.inputBox {
  margin-top: 10px;
  width: 100%;
  color: var(--textColor);
  display: inline-flex;
  flex-direction: column;
  position: relative;
  min-width: 0;
  padding: 0;
  border: 0;
  vertical-align: top;
}

.inputContainer {
  border-radius: 4px;
  padding-right: 14px;
  font-family: Roboto, Helvetica, Arial, sans-serif;
  font-weight: 400;
  font-size: 1rem;
  line-height: 1.4375em;
  letter-spacing: 0.00938em;
  color: var(--textColor);
  box-sizing: border-box;
  position: relative;
  cursor: text;
  display: inline-flex;
  align-items: center;
}

.inputField {
  color: var(--textColor);
  border-color: var(--inputOutline);
  padding: 5px 0 10px 14px;
  font: inherit;
  letter-spacing: inherit;
  border: 0;
  box-sizing: content-box;
  background: none;
  height: 1.4375em;
  margin: 0;
  display: block;
  min-width: 0;
  width: 100%;
}

.startIconContainer {
  color: var(--textColor);
  cursor: pointer;
  display: flex;
  height: 0.01em;
  max-height: 2em;
  align-items: center;
  white-space: nowrap;
  margin-left: 8px;
  margin-bottom: 5px;
  z-index: 20;

  svg {
    width: 1em;
    height: 1em;
    display: inline-block;
    flex-shrink: 0;
    font-size: 1.5rem;
    color: #fff;

    path {
      fill: var(--iconButtonColor);
    }

    &:hover {
      opacity: 0.8;
    }
  }
}

.endIconContainer {
  color: var(--textColor);
  cursor: pointer;
  display: flex;
  height: 0.01em;
  max-height: 2em;
  align-items: center;
  white-space: nowrap;
  margin-left: 8px;
  margin-bottom: 5px;
  z-index: 20;

  svg {
    width: 1em;
    height: 1em;
    display: inline-block;
    flex-shrink: 0;
    font-size: 1.5rem;
    color: #fff;

    path {
      fill: var(--iconButtonColor);
    }

    &:hover {
      opacity: 0.8;
    }
  }
}

.linkFieldset {
  border-color: var(--inputOutline);
  text-align: left;
  position: absolute;
  inset: -5px 0 0;
  margin: 0;
  padding: 0 8px;
  pointer-events: none;
  border-radius: 4px;
  border-style: solid;
  border-width: 1px;
  overflow: hidden;
  min-width: 0%;
}

.noBorder {
  border: none;
}

.linkLegend {
  margin: 0;
  padding: 0;
}
