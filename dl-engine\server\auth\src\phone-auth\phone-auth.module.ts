/**
 * DL-Engine 手机号认证模块
 */

import { Module } from '@nestjs/common'
import { TypeOrmModule } from '@nestjs/typeorm'
import { PhoneAuthController } from './phone-auth.controller'
import { PhoneAuthService } from './phone-auth.service'
import { PhoneVerification } from './entities/phone-verification.entity'
import { User } from '../users/entities/user.entity'
import { SmsModule } from '../sms/sms.module'
import { RedisModule } from '../redis/redis.module'

@Module({
  imports: [
    TypeOrmModule.forFeature([PhoneVerification, User]),
    SmsModule,
    RedisModule
  ],
  controllers: [PhoneAuthController],
  providers: [PhoneAuthService],
  exports: [PhoneAuthService]
})
export class PhoneAuthModule {}
