/**
 * DL-Engine API Gateway 代理服务
 * 
 * 核心功能：
 * - HTTP请求代理转发
 * - 负载均衡算法
 * - 请求重试机制
 * - 响应缓存
 * - 请求/响应转换
 */

import { Injectable } from '@nestjs/common'
import { Request, Response } from 'express'
import { createProxyMiddleware, Options } from 'http-proxy-middleware'
import { RoutingService } from '../routing/routing.service'
import { LoadBalancerService } from '../load-balancer/load-balancer.service'
import { LoggerService } from '../common/logger.service'
import { ConfigService } from '../config/config.service'

export interface ProxyOptions {
  onProxyReq?: (proxyReq: any, req: Request, res: Response) => void
  onProxyRes?: (proxyRes: any, req: Request, res: Response) => void
  onError?: (err: Error, req: Request, res: Response) => void
}

@Injectable()
export class ProxyService {
  private proxyCache = new Map<string, any>()

  constructor(
    private readonly routingService: RoutingService,
    private readonly loadBalancerService: LoadBalancerService,
    private readonly configService: ConfigService,
    private readonly logger: LoggerService
  ) {}

  /**
   * 代理请求到目标服务
   */
  async proxyRequest(req: Request, res: Response, options: ProxyOptions = {}) {
    const startTime = Date.now()
    
    try {
      // 查找匹配的路由
      const route = this.routingService.findMatchingRoute(req.path, req.method)
      
      if (!route) {
        return this.sendNotFoundResponse(res, req.path)
      }

      if (!route.enabled) {
        return this.sendServiceUnavailableResponse(res, 'Route is disabled')
      }

      // 获取目标服务实例
      const targetInstance = await this.loadBalancerService.selectInstance(route.service)
      
      if (!targetInstance) {
        return this.sendServiceUnavailableResponse(res, 'No healthy service instances available')
      }

      // 构建代理配置
      const proxyConfig = this.buildProxyConfig(route, targetInstance, options)
      
      // 创建代理中间件
      const proxy = createProxyMiddleware(proxyConfig)
      
      // 执行代理
      await new Promise<void>((resolve, reject) => {
        proxy(req, res, (err) => {
          if (err) {
            reject(err)
          } else {
            resolve()
          }
        })
      })

      // 记录成功指标
      const duration = Date.now() - startTime
      this.routingService.recordRouteMetrics(route.id, duration, true)

    } catch (error) {
      // 记录失败指标
      const duration = Date.now() - startTime
      if (route) {
        this.routingService.recordRouteMetrics(route.id, duration, false)
      }
      
      this.logger.error(`Proxy request failed: ${error.message}`)
      
      if (options.onError) {
        options.onError(error, req, res)
      } else {
        this.sendInternalErrorResponse(res, error.message)
      }
    }
  }

  /**
   * 构建代理配置
   */
  private buildProxyConfig(route: any, targetInstance: any, options: ProxyOptions): Options {
    const serviceConfig = this.configService.getService(route.service)
    
    return {
      target: targetInstance.url,
      changeOrigin: true,
      timeout: serviceConfig?.timeout || 30000,
      
      // 路径重写
      pathRewrite: (path: string) => {
        // 移除路由前缀，保留实际API路径
        const routePrefix = route.path.replace('/*', '')
        if (path.startsWith(routePrefix)) {
          return path.substring(routePrefix.length) || '/'
        }
        return path
      },

      // 请求预处理
      onProxyReq: (proxyReq, req, res) => {
        // 添加代理头信息
        proxyReq.setHeader('X-Forwarded-For', req.ip)
        proxyReq.setHeader('X-Forwarded-Proto', req.protocol)
        proxyReq.setHeader('X-Forwarded-Host', req.get('host'))
        proxyReq.setHeader('X-Gateway-Route-Id', route.id)
        proxyReq.setHeader('X-Gateway-Service', route.service)
        
        // 处理请求体
        if (req.body && (req.method === 'POST' || req.method === 'PUT' || req.method === 'PATCH')) {
          const bodyData = JSON.stringify(req.body)
          proxyReq.setHeader('Content-Type', 'application/json')
          proxyReq.setHeader('Content-Length', Buffer.byteLength(bodyData))
          proxyReq.write(bodyData)
        }

        if (options.onProxyReq) {
          options.onProxyReq(proxyReq, req, res)
        }
      },

      // 响应后处理
      onProxyRes: (proxyRes, req, res) => {
        // 添加响应头
        proxyRes.headers['X-Gateway-Service'] = route.service
        proxyRes.headers['X-Gateway-Instance'] = targetInstance.id
        proxyRes.headers['X-Response-Time'] = Date.now() - startTime

        if (options.onProxyRes) {
          options.onProxyRes(proxyRes, req, res)
        }
      },

      // 错误处理
      onError: (err, req, res) => {
        this.logger.error(`Proxy error for ${route.service}: ${err.message}`)
        
        // 标记实例为不健康
        this.loadBalancerService.markInstanceUnhealthy(route.service, targetInstance.id)
        
        if (options.onError) {
          options.onError(err, req, res)
        }
      },

      // 重试配置
      retry: serviceConfig?.retries || 0,
      retryDelay: 1000,
      retryCondition: (error) => {
        // 只对网络错误和5xx错误重试
        return error.code === 'ECONNRESET' || 
               error.code === 'ECONNREFUSED' || 
               (error.response && error.response.status >= 500)
      }
    }
  }

  /**
   * 发送404响应
   */
  private sendNotFoundResponse(res: Response, path: string) {
    res.status(404).json({
      error: 'Not Found',
      message: `No route found for ${path}`,
      timestamp: new Date().toISOString(),
      path
    })
  }

  /**
   * 发送503响应
   */
  private sendServiceUnavailableResponse(res: Response, message: string) {
    res.status(503).json({
      error: 'Service Unavailable',
      message,
      timestamp: new Date().toISOString()
    })
  }

  /**
   * 发送500响应
   */
  private sendInternalErrorResponse(res: Response, message: string) {
    res.status(500).json({
      error: 'Internal Server Error',
      message: this.configService.isDevelopment() ? message : 'An unexpected error occurred',
      timestamp: new Date().toISOString()
    })
  }

  /**
   * 清理代理缓存
   */
  clearCache() {
    this.proxyCache.clear()
    this.logger.log('Proxy cache cleared')
  }

  /**
   * 获取代理统计信息
   */
  getProxyStats() {
    return {
      cacheSize: this.proxyCache.size,
      timestamp: new Date().toISOString()
    }
  }
}
