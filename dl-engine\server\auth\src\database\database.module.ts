/**
 * DL-Engine 认证服务数据库模块
 */

import { Module } from '@nestjs/common'
import { TypeOrmModule } from '@nestjs/typeorm'
import { ConfigService } from '../config/config.service'
import { User } from '../users/entities/user.entity'
import { PhoneVerification } from '../phone-auth/entities/phone-verification.entity'

@Module({
  imports: [
    TypeOrmModule.forRootAsync({
      useFactory: (configService: ConfigService) => ({
        ...configService.getDatabaseConfig(),
        entities: [User, PhoneVerification],
        migrations: ['dist/migrations/*.js'],
        migrationsRun: false,
        autoLoadEntities: true
      }),
      inject: [ConfigService]
    })
  ]
})
export class DatabaseModule {}
