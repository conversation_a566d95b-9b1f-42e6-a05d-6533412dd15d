/**
 * 层次结构面板组件
 * 
 * 显示场景中所有实体的层次结构
 */

import React, { useCallback, useMemo, useState } from 'react'
import { useHookstate } from '@hookstate/core'
import { useTranslation } from 'react-i18next'
import { Tree, Input, Dropdown, Menu, Modal } from 'antd'
import { 
  EyeOutlined, 
  EyeInvisibleOutlined, 
  LockOutlined, 
  UnlockOutlined,
  MoreOutlined,
  SearchOutlined,
  PlusOutlined,
  DeleteOutlined,
  CopyOutlined,
  ScissorOutlined
} from '@ant-design/icons'
import { EditorState } from '../services/EditorState'
import { Entity, ComponentType } from '../types'
import { DLPanel, DLButton, DLInput, DLIcon } from '@dl-engine/ui'

const { TreeNode } = Tree
const { Search } = Input

/**
 * 树节点数据
 */
interface TreeNodeData {
  key: string
  title: string
  entity: Entity
  children?: TreeNodeData[]
  visible: boolean
  locked: boolean
}

/**
 * 层次结构面板属性
 */
export interface HierarchyPanelProps {
  /** 面板宽度 */
  width?: number
  /** 是否可折叠 */
  collapsible?: boolean
  /** 自定义类名 */
  className?: string
}

/**
 * 层次结构面板组件
 */
const HierarchyPanel: React.FC<HierarchyPanelProps> = ({
  width = 250,
  collapsible = true,
  className
}) => {
  const { t } = useTranslation()
  const editorState = useHookstate(EditorState)
  const [searchValue, setSearchValue] = useState('')
  const [expandedKeys, setExpandedKeys] = useState<string[]>([])
  const [autoExpandParent, setAutoExpandParent] = useState(true)

  const entities = editorState.scene.entities.get()
  const selection = editorState.selection.get()

  /**
   * 构建树形数据
   */
  const treeData = useMemo(() => {
    const buildTree = (parentId?: string): TreeNodeData[] => {
      return Object.values(entities)
        .filter(entity => entity.parentId === parentId)
        .map(entity => ({
          key: entity.id,
          title: entity.name,
          entity,
          visible: entity.visible !== false,
          locked: entity.locked === true,
          children: buildTree(entity.id)
        }))
        .sort((a, b) => a.title.localeCompare(b.title))
    }

    return buildTree()
  }, [entities])

  /**
   * 过滤树形数据
   */
  const filteredTreeData = useMemo(() => {
    if (!searchValue) return treeData

    const filterTree = (nodes: TreeNodeData[]): TreeNodeData[] => {
      return nodes.reduce((acc, node) => {
        const matchesSearch = node.title.toLowerCase().includes(searchValue.toLowerCase())
        const filteredChildren = filterTree(node.children || [])
        
        if (matchesSearch || filteredChildren.length > 0) {
          acc.push({
            ...node,
            children: filteredChildren
          })
        }
        
        return acc
      }, [] as TreeNodeData[])
    }

    return filterTree(treeData)
  }, [treeData, searchValue])

  /**
   * 获取所有匹配的节点键
   */
  const getMatchedKeys = useCallback((value: string): string[] => {
    const keys: string[] = []
    
    const findKeys = (nodes: TreeNodeData[]) => {
      nodes.forEach(node => {
        if (node.title.toLowerCase().includes(value.toLowerCase())) {
          keys.push(node.key)
        }
        if (node.children) {
          findKeys(node.children)
        }
      })
    }
    
    findKeys(treeData)
    return keys
  }, [treeData])

  /**
   * 处理搜索
   */
  const handleSearch = useCallback((value: string) => {
    setSearchValue(value)
    if (value) {
      const matchedKeys = getMatchedKeys(value)
      setExpandedKeys(matchedKeys)
      setAutoExpandParent(true)
    } else {
      setExpandedKeys([])
      setAutoExpandParent(false)
    }
  }, [getMatchedKeys])

  /**
   * 处理节点选择
   */
  const handleSelect = useCallback((selectedKeys: React.Key[], info: any) => {
    const entityIds = selectedKeys as string[]
    editorState.selection.selectedEntities.set(entityIds)
  }, [editorState])

  /**
   * 处理节点展开
   */
  const handleExpand = useCallback((expandedKeys: React.Key[]) => {
    setExpandedKeys(expandedKeys as string[])
    setAutoExpandParent(false)
  }, [])

  /**
   * 切换实体可见性
   */
  const toggleVisibility = useCallback((entityId: string, event: React.MouseEvent) => {
    event.stopPropagation()
    const entity = entities[entityId]
    if (entity) {
      editorState.scene.entities[entityId].visible.set(!entity.visible)
    }
  }, [entities, editorState])

  /**
   * 切换实体锁定状态
   */
  const toggleLock = useCallback((entityId: string, event: React.MouseEvent) => {
    event.stopPropagation()
    const entity = entities[entityId]
    if (entity) {
      editorState.scene.entities[entityId].locked.set(!entity.locked)
    }
  }, [entities, editorState])

  /**
   * 创建新实体
   */
  const createEntity = useCallback((parentId?: string) => {
    const newEntity: Entity = {
      id: `entity_${Date.now()}`,
      name: t('editor.entity.newEntity'),
      parentId,
      visible: true,
      locked: false,
      components: {
        [ComponentType.TRANSFORM]: {
          type: ComponentType.TRANSFORM,
          position: { x: 0, y: 0, z: 0 },
          rotation: { x: 0, y: 0, z: 0 },
          scale: { x: 1, y: 1, z: 1 }
        }
      }
    }
    
    editorState.scene.entities[newEntity.id].set(newEntity)
    editorState.selection.selectedEntities.set([newEntity.id])
  }, [editorState, t])

  /**
   * 删除实体
   */
  const deleteEntity = useCallback((entityId: string) => {
    Modal.confirm({
      title: t('editor.entity.deleteConfirm'),
      content: t('editor.entity.deleteConfirmContent'),
      onOk: () => {
        // 递归删除子实体
        const deleteRecursive = (id: string) => {
          const children = Object.values(entities).filter(e => e.parentId === id)
          children.forEach(child => deleteRecursive(child.id))
          editorState.scene.entities[id].set(undefined)
        }
        
        deleteRecursive(entityId)
        
        // 从选择中移除
        const selectedEntities = selection.selectedEntities.filter(id => id !== entityId)
        editorState.selection.selectedEntities.set(selectedEntities)
      }
    })
  }, [entities, editorState, selection, t])

  /**
   * 复制实体
   */
  const duplicateEntity = useCallback((entityId: string) => {
    const entity = entities[entityId]
    if (entity) {
      const newEntity: Entity = {
        ...entity,
        id: `entity_${Date.now()}`,
        name: `${entity.name} Copy`
      }
      
      editorState.scene.entities[newEntity.id].set(newEntity)
      editorState.selection.selectedEntities.set([newEntity.id])
    }
  }, [entities, editorState])

  /**
   * 渲染节点标题
   */
  const renderNodeTitle = (nodeData: TreeNodeData) => {
    const { entity, visible, locked } = nodeData
    
    return (
      <div className="flex items-center justify-between w-full group">
        <span className="flex-1 truncate">{entity.name}</span>
        
        <div className="flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
          {/* 可见性切换 */}
          <DLButton
            type="text"
            size="small"
            icon={visible ? <EyeOutlined /> : <EyeInvisibleOutlined />}
            onClick={(e) => toggleVisibility(entity.id, e)}
            className={visible ? 'text-blue-500' : 'text-gray-400'}
          />
          
          {/* 锁定切换 */}
          <DLButton
            type="text"
            size="small"
            icon={locked ? <LockOutlined /> : <UnlockOutlined />}
            onClick={(e) => toggleLock(entity.id, e)}
            className={locked ? 'text-red-500' : 'text-gray-400'}
          />
          
          {/* 更多操作 */}
          <Dropdown
            overlay={
              <Menu>
                <Menu.Item 
                  key="add-child"
                  icon={<PlusOutlined />}
                  onClick={() => createEntity(entity.id)}
                >
                  {t('editor.entity.addChild')}
                </Menu.Item>
                <Menu.Item 
                  key="duplicate"
                  icon={<CopyOutlined />}
                  onClick={() => duplicateEntity(entity.id)}
                >
                  {t('editor.entity.duplicate')}
                </Menu.Item>
                <Menu.Divider />
                <Menu.Item 
                  key="delete"
                  icon={<DeleteOutlined />}
                  danger
                  onClick={() => deleteEntity(entity.id)}
                >
                  {t('editor.entity.delete')}
                </Menu.Item>
              </Menu>
            }
            trigger={['click']}
          >
            <DLButton
              type="text"
              size="small"
              icon={<MoreOutlined />}
              onClick={(e) => e.stopPropagation()}
            />
          </Dropdown>
        </div>
      </div>
    )
  }

  /**
   * 渲染树节点
   */
  const renderTreeNodes = (nodes: TreeNodeData[]) => {
    return nodes.map(node => (
      <TreeNode
        key={node.key}
        title={renderNodeTitle(node)}
        className={`
          ${!node.visible ? 'opacity-50' : ''}
          ${node.locked ? 'pointer-events-none' : ''}
        `}
      >
        {node.children && renderTreeNodes(node.children)}
      </TreeNode>
    ))
  }

  return (
    <DLPanel
      title={t('editor.panels.hierarchy')}
      className={className}
      style={{ width }}
      collapsible={collapsible}
      extra={
        <DLButton
          type="text"
          size="small"
          icon={<PlusOutlined />}
          onClick={() => createEntity()}
          tooltip={t('editor.entity.create')}
        />
      }
    >
      {/* 搜索框 */}
      <div className="p-2 border-b">
        <Search
          placeholder={t('editor.hierarchy.search')}
          value={searchValue}
          onChange={(e) => handleSearch(e.target.value)}
          allowClear
        />
      </div>
      
      {/* 树形结构 */}
      <div className="flex-1 overflow-auto">
        <Tree
          showLine
          multiple
          selectedKeys={selection.selectedEntities}
          expandedKeys={expandedKeys}
          autoExpandParent={autoExpandParent}
          onSelect={handleSelect}
          onExpand={handleExpand}
          className="hierarchy-tree"
        >
          {renderTreeNodes(filteredTreeData)}
        </Tree>
      </div>
    </DLPanel>
  )
}

// 添加样式
const hierarchyStyles = `
.hierarchy-tree {
  .ant-tree-node-content-wrapper {
    width: 100%;
  }

  .ant-tree-title {
    width: 100%;
  }

  .ant-tree-node-selected {
    background-color: rgba(24, 144, 255, 0.1) !important;
  }

  .ant-tree-node-content-wrapper:hover {
    background-color: rgba(255, 255, 255, 0.05);
  }
}
`

// 注入样式
if (typeof document !== 'undefined') {
  const styleId = 'hierarchy-panel-styles'
  if (!document.getElementById(styleId)) {
    const style = document.createElement('style')
    style.id = styleId
    style.textContent = hierarchyStyles
    document.head.appendChild(style)
  }
}

export default HierarchyPanel
