{"name": "@ir-engine/client-core", "version": "1.0.3", "repository": {"type": "git", "url": "git://github.com/ir-engine/ir-engine.git"}, "engines": {"node": ">= 22.11.0"}, "publishConfig": {"access": "public"}, "main": "lib/client-core.umd.js", "module": "lib/client-core.es.js", "types": "lib/index.d.ts", "npmClient": "npm", "scripts": {"check-errors": "tsc --noemit && npx cycle-import-check src || true", "test": "cross-env TEST=true vitest run --config=../../vitest.client.config.ts"}, "resolutions": {"@types/react": "18.2.0", "react": "18.2.0"}, "peerDependencies": {"@types/react": "18.2.0", "react": "18.2.0"}, "dependencies": {"@feathersjs/authentication-client": "5.0.5", "@feathersjs/client": "5.0.5", "@feathersjs/primus-client": "4.5.15", "@feathersjs/rest-client": "5.0.5", "@ir-engine/common": "^1.0.3", "@ir-engine/engine": "^1.0.3", "@ir-engine/hyperflux": "^1.0.3", "@ir-engine/ui": "^1.0.3", "@storybook/react": "^8.6.14", "@uiw/react-color-sketch": "^2.1.1", "axios": "1.3.4", "comlink": "4.4.1", "cross-env": "7.0.3", "hark": "^1.2.3", "history": "^5.3.0", "i18next": "21.6.16", "image-palette-core": "0.2.2", "lodash": "4.17.21", "mediasoup-client": "3.6.82", "moment": "2.29.4", "notistack": "^3.0.1", "primus": "^8.0.6", "primus-client": "^7.3.4", "qrcode.react": "^3.1.0", "react": "18.2.0", "react-i18next": "11.16.6", "react-joystick-component": "6.0.0", "react-router-dom": "6.9.0", "typescript": "5.6.3", "uuid": "9.0.0"}, "devDependencies": {"@types/hark": "1.2.2", "@types/node": "18.15.5", "@types/react": "18.2.0", "@types/three": "0.176.0", "css-modules-require-hook": "4.2.3", "esbuild": "0.17.12", "msw": "^2.9.0", "react-dom": "18.2.0", "rimraf": "4.4.0", "sass": "1.59.3", "trace-unhandled": "2.0.1"}, "license": "CPAL", "gitHead": "2313453697ca7c6b8d36b3b166b5a6445fe1c851"}