:root {
  --lato: 'Lato', sans-serif;
  --red: #f44336;
  --pink: #e91e63;
  --purple: #9c27b0;
  --deepPurple: #673ab7;
  --indigo: #3f51b5;
  --blue: #006eff;
  --lightBlue: #03a9f4;
  --cyan: #00bcd4;
  --teal: #009688;
  --green: #4caf50;
  --lightGreen: #8bc34a;
  --lime: #cddc39;
  --yellow: #ffeb3b;
  --amber: #ffc107;
  --orange: #ff9800;
  --deepOrange: #ff5722;
  --brown: #795548;
  --blueHover: #4d93f1;
  --bluePressed: #0554bc;
  --disabled: #222;
  --disabledText: grey;
  --deemphasized: grey;
  --toolbar: rgb(0 0 0 / 0%);
  --toolbar2: rgb(0 0 0 / 0%);
  --header: #1b1b1b;
  --white: #fff;
  --purpleButtonColor: #5f5ff1;
  --facebookBlue: #4267b2;
  --lightRed: #ef5350;
  --mediumBlue: #3838b2;
  --shadow15: 0 4px 4px rgb(0 0 0 / 15%);
  --shadow30: 0 4px 4px rgb(0 0 0 / 30%);
  --borderStyle: 1px solid #5d646c;
  --borderStyleLight: 1px solid #b9b9b9;
  --background: rgb(0 0 0 / 0%);
  --border: #5d646c;
  --background2: #15171b;
}

:root[data-theme='light'] {
  --textColor: #585858;
  --navbarBackground: #e7e7e7;
  --sidebarBackground: #e1e1e1;
  --sidebarSelectedBackground: #acacac;
  --mainBackground: #fff;
  --panelBackground: #c0c0c0;
  --panelCards: #dadae2;
  --panelCardHoverOutline: #555;
  --panelCardIcon: #4d4d4d;
  --textHeading: #696969;
  --textSubheading: #797979;
  --textDescription: #898989;
  --iconButtonColor: #585858;
  --iconButtonHoverColor: #c9c9c9;
  --iconButtonBackground: #e1e1e1;
  --iconButtonSelectedBackground: #a3a3a3;
  --buttonOutlined: #969696;
  --buttonFilled: #969696;
  --buttonGradientStart: #d1d1d1;
  --buttonGradientEnd: #969696;
  --buttonTextColor: #585858;
  --scrollbarThumbXAxisStart: #d1d1d1;
  --scrollbarThumbXAxisEnd: #969696;
  --scrollbarThumbYAxisStart: #d1d1d1;
  --scrollbarThumbYAxisEnd: #969696;
  --scrollbarCorner: rgb(255 255 255 / 0%);
  --inputOutline: #b4b4b4;
  --inputOutlineRgb: 180, 180, 180;
  --inputBackground: #d4d4d4;
  --primaryHighlight: rgb(81 81 255);
  --dropdownMenuBackground: #c0c0c0;
  --dropdownMenuHoverBackground: #b8b8b8;
  --dropdownMenuSelectedBackground: #acacac;
  --drawerBackground: #c6c6c6;
  --popupBackground: #c6c6c6;
  --tableHeaderBackground: #c0c0c0;
  --tableCellBackground: #e1e1e1;
  --tableFooterBackground: #c0c0c0;
  --dockBackground: #bdbdbda6;
}

:root[data-theme='dark'] {
  --textColor: #fff;
  --navbarBackground: rgb(78 78 78 / 100%);
  --sidebarBackground: rgb(80 80 80 / 100%);
  --sidebarSelectedBackground: rgb(156 156 156 / 100%);
  --mainBackground: rgb(53 53 53 / 100%);
  --panelBackground: rgb(73 73 73 / 100%);
  --panelCards: rgb(178 178 178 / 100%);
  --panelCardHoverOutline: rgb(180 180 180 / 100%);
  --panelCardIcon: rgb(39 39 39 / 100%);
  --textHeading: #fff;
  --textSubheading: #fff;
  --textDescription: #fff;
  --iconButtonColor: #fff;
  --iconButtonHoverColor: rgb(148 148 148 / 100%);
  --iconButtonBackground: rgb(163 163 163 / 100%);
  --iconButtonSelectedBackground: rgb(146 146 146 / 100%);
  --buttonOutlined: rgb(93 93 93 / 100%);
  --buttonFilled: rgb(97 97 97 / 100%);
  --buttonGradientStart: rgb(127 127 127 / 100%);
  --buttonGradientEnd: rgb(95 95 95 / 100%);
  --buttonTextColor: #fff;
  --scrollbarThumbXAxisStart: rgb(124 124 124 / 100%);
  --scrollbarThumbXAxisEnd: rgb(82 82 82 / 100%);
  --scrollbarThumbYAxisStart: rgb(141 141 141 / 100%);
  --scrollbarThumbYAxisEnd: rgb(97 97 97 / 100%);
  --scrollbarCorner: rgb(255 255 255 / 0%);
  --inputOutline: #fff;
  --inputOutlineRgb: 255, 255, 255;
  --inputBackground: rgb(80 80 80 / 100%);
  --primaryHighlight: rgb(81 81 255);
  --dropdownMenuBackground: rgb(61 61 61 / 100%);
  --dropdownMenuHoverBackground: rgb(88 88 88 / 100%);
  --dropdownMenuSelectedBackground: rgb(95 95 95 / 100%);
  --drawerBackground: rgb(66 66 66 / 100%);
  --popupBackground: rgb(14 15 17 / 100%);
  --tableHeaderBackground: rgb(76 76 76 / 100%);
  --tableCellBackground: rgb(104 104 104 / 100%);
  --tableFooterBackground: rgb(71 71 71 / 100%);
  --dockBackground: rgb(53 53 53 / 93%);
}

:root[data-theme='vaporwave'] {
  --textColor: #fff;
  --navbarBackground: rgb(31 27 72 / 85%);
  --sidebarBackground: rgb(31 27 72 / 100%);
  --sidebarSelectedBackground: #5f5ff1;
  --mainBackground: #02022d;
  --panelBackground: #1f1b48;
  --panelCards: #3c3c6f;
  --panelCardHoverOutline: #9898ff;
  --panelCardIcon: #1f1b48;
  --textHeading: #fff;
  --textSubheading: #fff;
  --textDescription: #fff;
  --iconButtonColor: #fff;
  --iconButtonHoverColor: #7171f0;
  --iconButtonBackground: #9898ff;
  --iconButtonSelectedBackground: #4d4df2;
  --buttonOutlined: #3c3c6f;
  --buttonFilled: #3c3c6f;
  --buttonGradientStart: #5236ff;
  --buttonGradientEnd: #c20560;
  --buttonTextColor: #fff;
  --scrollbarThumbXAxisStart: #5236ff;
  --scrollbarThumbXAxisEnd: #c20560;
  --scrollbarThumbYAxisStart: #5236ff;
  --scrollbarThumbYAxisEnd: #c20560;
  --scrollbarCorner: rgb(255 255 255 / 0%);
  --inputOutline: #fff;
  --inputOutlineRgb: 255, 255, 255;
  --inputBackground: #3c3c6f;
  --primaryHighlight: rgb(81 81 255);
  --dropdownMenuBackground: #1f1b48;
  --dropdownMenuHoverBackground: #2a2567;
  --dropdownMenuSelectedBackground: #5f5ff1;
  --drawerBackground: #1f1b48;
  --popupBackground: #2c2c5f;
  --tableHeaderBackground: #1f1b48;
  --tableCellBackground: #3c3c6f;
  --tableFooterBackground: #1f1b48;
  --dockBackground: rgb(73 66 152 / 85%);
}

::-webkit-scrollbar {
  width: 0.5rem;
}

::-webkit-scrollbar-track {
  box-shadow: inset 0 0 1px grey;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(var(--scrollbarThumbYAxisStart), var(--scrollbarThumbYAxisEnd));
  border-radius: 10px;
}

::-webkit-scrollbar-thumb:horizontal {
  background: linear-gradient(90deg, var(--scrollbarThumbXAxisStart), var(--scrollbarThumbXAxisEnd));
}

::-webkit-scrollbar-corner {
  background-color: var(--scrollbarCorner);
}

body {
  overflow: hidden;
  color: var(--textColor);
  overscroll-behavior: none contain;
  background-color: var(--surface-0);
}

vite-error-overlay {
  pointer-events: auto;
}

#root {
  height: 100%;

  .fullscreen {
    height: 100%;
  }

  #engine-container {
    height: 100%;
  }
}

iframe#launcher {
  pointer-events: all;
}