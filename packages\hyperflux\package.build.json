{"name": "@ir-engine/hyperflux", "version": "1.0.3", "main": "index.js", "exports": {".": "./index.js", "./src/*": "./src/*"}, "files": ["./index.js", "./src"], "types": "./index.d.ts", "description": "Agent Centric Reactive Data Management for Infinite Reality Engine", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git://github.com/ir-engine/ir-engine.git"}, "author": "", "license": "CPAL", "bugs": {"url": "https://github.com/ir-engine/ir-engine/issues"}, "dependencies": {"@hookstate/core": "4.0.1", "@hookstate/identifiable": "^4.0.0", "react": "18.2.0", "react-reconciler": "0.29.0", "ts-matches": "5.3.0", "ts-toolbelt": "^9.6.0", "uuid": "9.0.0"}, "overrides": {"@hookstate/identifiable": {"@hookstate/core": "npm:@hookstate/core@4.0.1"}}}