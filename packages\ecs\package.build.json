{"name": "@ir-engine/ecs", "version": "1.0.3", "main": "index.js", "description": "Agent Centric Reactive Data Management for Infinite Reality Engine", "exports": {".": "./index.js", "./src/*": "./src/*"}, "files": ["./index.js", "./src"], "types": "./index.d.ts", "repository": {"type": "git", "url": "git://github.com/ir-engine/ir-engine.git"}, "author": "Infinite Reality Engine", "license": "CPAL", "bugs": {"url": "https://github.com/ir-engine/ir-engine/issues"}, "dependencies": {"@ir-engine/hyperflux": "^1.0.3", "bitecs": "github:NateTheGreatt/bitECS#rc-0-4-0", "uuid": "9.0.0"}}