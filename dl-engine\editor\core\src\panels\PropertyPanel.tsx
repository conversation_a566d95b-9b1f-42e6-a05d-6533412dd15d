/**
 * 属性面板组件
 * 
 * 显示和编辑选中对象的属性
 */

import React, { useCallback, useMemo } from 'react'
import { useHookstate } from '@hookstate/core'
import { useTranslation } from 'react-i18next'
import { Collapse, Divider } from 'antd'
import { EditorState } from '../services/EditorState'
import { Entity, Component, ComponentType } from '../types'
import { 
  DLPanel, 
  DLInput, 
  DLInputNumber, 
  DLSelect, 
  DLSwitch, 
  DLColorPicker,
  DLButton,
  DLDivider
} from '@dl-engine/ui'
import TransformComponent from './components/TransformComponent'
import MeshComponent from './components/MeshComponent'
import LightComponent from './components/LightComponent'
import CameraComponent from './components/CameraComponent'
import MaterialComponent from './components/MaterialComponent'

const { Panel } = Collapse

/**
 * 属性面板属性
 */
export interface PropertyPanelProps {
  /** 面板宽度 */
  width?: number
  /** 是否可折叠 */
  collapsible?: boolean
  /** 自定义类名 */
  className?: string
}

/**
 * 属性面板组件
 */
const PropertyPanel: React.FC<PropertyPanelProps> = ({
  width = 300,
  collapsible = true,
  className
}) => {
  const { t } = useTranslation()
  const editorState = useHookstate(EditorState)
  const selection = editorState.selection.get()

  /**
   * 获取选中的实体
   */
  const selectedEntities = useMemo(() => {
    return selection.selectedEntities.map(id => 
      editorState.scene.entities.get()[id]
    ).filter(Boolean)
  }, [selection.selectedEntities, editorState.scene.entities])

  /**
   * 获取当前编辑的实体
   */
  const currentEntity = useMemo(() => {
    return selectedEntities.length === 1 ? selectedEntities[0] : null
  }, [selectedEntities])

  /**
   * 更新实体属性
   */
  const updateEntityProperty = useCallback((
    entityId: string,
    property: string,
    value: any
  ) => {
    const entities = editorState.scene.entities.get()
    const entity = entities[entityId]
    if (entity) {
      const updatedEntity = {
        ...entity,
        [property]: value
      }
      editorState.scene.entities[entityId].set(updatedEntity)
    }
  }, [editorState])

  /**
   * 更新组件属性
   */
  const updateComponentProperty = useCallback((
    entityId: string,
    componentType: ComponentType,
    property: string,
    value: any
  ) => {
    const entities = editorState.scene.entities.get()
    const entity = entities[entityId]
    if (entity && entity.components[componentType]) {
      const component = { ...entity.components[componentType] }
      component[property] = value
      
      const updatedEntity = {
        ...entity,
        components: {
          ...entity.components,
          [componentType]: component
        }
      }
      editorState.scene.entities[entityId].set(updatedEntity)
    }
  }, [editorState])

  /**
   * 添加组件
   */
  const addComponent = useCallback((
    entityId: string,
    componentType: ComponentType
  ) => {
    const entities = editorState.scene.entities.get()
    const entity = entities[entityId]
    if (entity && !entity.components[componentType]) {
      const defaultComponent = createDefaultComponent(componentType)
      const updatedEntity = {
        ...entity,
        components: {
          ...entity.components,
          [componentType]: defaultComponent
        }
      }
      editorState.scene.entities[entityId].set(updatedEntity)
    }
  }, [editorState])

  /**
   * 移除组件
   */
  const removeComponent = useCallback((
    entityId: string,
    componentType: ComponentType
  ) => {
    const entities = editorState.scene.entities.get()
    const entity = entities[entityId]
    if (entity && entity.components[componentType]) {
      const components = { ...entity.components }
      delete components[componentType]
      
      const updatedEntity = {
        ...entity,
        components
      }
      editorState.scene.entities[entityId].set(updatedEntity)
    }
  }, [editorState])

  /**
   * 创建默认组件
   */
  const createDefaultComponent = (componentType: ComponentType): Component => {
    switch (componentType) {
      case ComponentType.TRANSFORM:
        return {
          type: ComponentType.TRANSFORM,
          position: { x: 0, y: 0, z: 0 },
          rotation: { x: 0, y: 0, z: 0 },
          scale: { x: 1, y: 1, z: 1 }
        }
      case ComponentType.MESH:
        return {
          type: ComponentType.MESH,
          geometry: 'box',
          material: 'standard'
        }
      case ComponentType.LIGHT:
        return {
          type: ComponentType.LIGHT,
          lightType: 'directional',
          color: '#ffffff',
          intensity: 1
        }
      case ComponentType.CAMERA:
        return {
          type: ComponentType.CAMERA,
          cameraType: 'perspective',
          fov: 75,
          near: 0.1,
          far: 1000
        }
      default:
        return { type: componentType }
    }
  }

  /**
   * 渲染实体基本信息
   */
  const renderEntityInfo = () => {
    if (!currentEntity) return null

    return (
      <Panel header={t('editor.entity.basicInfo')} key="entity-info">
        <div className="space-y-3">
          <DLInput
            label={t('editor.entity.name')}
            value={currentEntity.name}
            onChange={(e) => updateEntityProperty(currentEntity.id, 'name', e.target.value)}
          />
          
          <DLInput
            label={t('editor.entity.tag')}
            value={currentEntity.tag || ''}
            onChange={(e) => updateEntityProperty(currentEntity.id, 'tag', e.target.value)}
          />
          
          <DLSwitch
            label={t('editor.entity.visible')}
            checked={currentEntity.visible !== false}
            onChange={(checked) => updateEntityProperty(currentEntity.id, 'visible', checked)}
          />
          
          <DLSwitch
            label={t('editor.entity.static')}
            checked={currentEntity.static === true}
            onChange={(checked) => updateEntityProperty(currentEntity.id, 'static', checked)}
          />
        </div>
      </Panel>
    )
  }

  /**
   * 渲染组件
   */
  const renderComponent = (componentType: ComponentType, component: Component) => {
    if (!currentEntity) return null

    const componentName = t(`editor.components.${componentType}`)
    
    return (
      <Panel 
        header={componentName} 
        key={componentType}
        extra={
          <DLButton
            type="text"
            size="small"
            danger
            onClick={(e) => {
              e.stopPropagation()
              removeComponent(currentEntity.id, componentType)
            }}
          >
            {t('common.remove')}
          </DLButton>
        }
      >
        {componentType === ComponentType.TRANSFORM && (
          <TransformComponent
            component={component}
            onChange={(property, value) => 
              updateComponentProperty(currentEntity.id, componentType, property, value)
            }
          />
        )}
        
        {componentType === ComponentType.MESH && (
          <MeshComponent
            component={component}
            onChange={(property, value) => 
              updateComponentProperty(currentEntity.id, componentType, property, value)
            }
          />
        )}
        
        {componentType === ComponentType.LIGHT && (
          <LightComponent
            component={component}
            onChange={(property, value) => 
              updateComponentProperty(currentEntity.id, componentType, property, value)
            }
          />
        )}
        
        {componentType === ComponentType.CAMERA && (
          <CameraComponent
            component={component}
            onChange={(property, value) => 
              updateComponentProperty(currentEntity.id, componentType, property, value)
            }
          />
        )}
        
        {componentType === ComponentType.MATERIAL && (
          <MaterialComponent
            component={component}
            onChange={(property, value) => 
              updateComponentProperty(currentEntity.id, componentType, property, value)
            }
          />
        )}
      </Panel>
    )
  }

  /**
   * 渲染添加组件按钮
   */
  const renderAddComponent = () => {
    if (!currentEntity) return null

    const availableComponents = Object.values(ComponentType).filter(
      type => !currentEntity.components[type]
    )

    if (availableComponents.length === 0) return null

    return (
      <div className="p-3">
        <DLSelect
          placeholder={t('editor.components.addComponent')}
          style={{ width: '100%' }}
          onChange={(value) => {
            addComponent(currentEntity.id, value as ComponentType)
          }}
          options={availableComponents.map(type => ({
            label: t(`editor.components.${type}`),
            value: type
          }))}
        />
      </div>
    )
  }

  /**
   * 渲染多选信息
   */
  const renderMultiSelection = () => {
    return (
      <div className="p-4 text-center text-gray-500">
        <div>{t('editor.selection.multipleSelected', { count: selectedEntities.length })}</div>
        <div className="mt-2 text-sm">
          {t('editor.selection.multipleSelectedHint')}
        </div>
      </div>
    )
  }

  /**
   * 渲染空选择
   */
  const renderEmptySelection = () => {
    return (
      <div className="p-4 text-center text-gray-500">
        <div>{t('editor.selection.noSelection')}</div>
        <div className="mt-2 text-sm">
          {t('editor.selection.noSelectionHint')}
        </div>
      </div>
    )
  }

  return (
    <DLPanel
      title={t('editor.panels.properties')}
      className={className}
      style={{ width }}
      collapsible={collapsible}
    >
      {selectedEntities.length === 0 && renderEmptySelection()}
      
      {selectedEntities.length > 1 && renderMultiSelection()}
      
      {currentEntity && (
        <Collapse 
          defaultActiveKey={['entity-info', ComponentType.TRANSFORM]}
          ghost
        >
          {renderEntityInfo()}
          
          {Object.entries(currentEntity.components).map(([type, component]) =>
            renderComponent(type as ComponentType, component)
          )}
        </Collapse>
      )}
      
      {currentEntity && renderAddComponent()}
    </DLPanel>
  )
}

export default PropertyPanel
