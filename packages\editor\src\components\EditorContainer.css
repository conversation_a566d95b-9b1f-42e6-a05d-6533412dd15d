/** references https://github.com/ticlo/rc-dock/blob/master/dist/rc-dock.css */

.dock-top .dock-bar {
  border-bottom: 1px solid rgba(0, 0, 0, 0.2);
  background: transparent !important;
}

.dock-panel {
  border: none;
}

.dock-panel-max-btn::before {
  border-color: var(--text-primary) !important;
}

.dock.dock-top {
  background: var(--surface-0);
}

.dock-nav-list {
  justify-content: center;
  align-items: center;
}

.dock-top .dock-bar {
  border-bottom: none !important;
}

.dock-tab {
  border-top-left-radius: 5px;
  border-top-right-radius: 5px;
  background: transparent !important;
  margin: 0;
  border-bottom: none !important;
  color: var(--text-secondary) !important;
}

.dock-tab.dock-tab-active {
  background: var(--ui-select-background) !important;
  color: var(--text-primary) !important;
}

.dock-tab:not(.dock-tab-active):hover {
  background: var(--bg-surface-input) !important;
}

.dock-tab > div {
  padding: 0 !important;
}

.dock-ink-bar {
  display: none;  
}

.dock-tab-close-btn{
  display: none;
}

.dock-bar.drag-initiator{
  padding: 0;
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
input[type=number] {
  -moz-appearance: textfield;
}