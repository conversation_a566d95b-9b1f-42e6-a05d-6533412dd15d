{"name": "@dl-engine/server-gateway", "version": "1.0.0", "description": "DL-Engine API Gateway Service - 统一的API网关服务", "main": "dist/index.js", "type": "module", "scripts": {"build": "tsc", "dev": "tsx watch src/index.ts", "start": "node dist/index.js", "test": "vitest", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix"}, "dependencies": {"@nestjs/common": "^10.0.0", "@nestjs/core": "^10.0.0", "@nestjs/platform-express": "^10.0.0", "@nestjs/swagger": "^7.0.0", "@nestjs/throttler": "^5.0.0", "@nestjs/jwt": "^10.0.0", "@nestjs/passport": "^10.0.0", "express": "^4.18.0", "helmet": "^7.0.0", "cors": "^2.8.5", "compression": "^1.7.4", "express-rate-limit": "^7.0.0", "http-proxy-middleware": "^2.0.6", "redis": "^4.6.0", "winston": "^3.10.0", "class-validator": "^0.14.0", "class-transformer": "^0.5.1", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.0"}, "devDependencies": {"@types/node": "^20.0.0", "@types/express": "^4.17.0", "@types/cors": "^2.8.0", "@types/compression": "^1.7.0", "typescript": "^5.6.3", "tsx": "^4.0.0", "vitest": "^1.0.0", "eslint": "^8.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0"}, "keywords": ["dl-engine", "api-gateway", "microservices", "education", "3d", "vr", "ar"], "author": "DL-Engine Team", "license": "MIT"}