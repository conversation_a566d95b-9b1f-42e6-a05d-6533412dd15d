/*
CPAL-1.0 License

The contents of this file are subject to the Common Public Attribution License
Version 1.0. (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at
https://github.com/ir-engine/ir-engine/blob/dev/LICENSE.
The License is based on the Mozilla Public License Version 1.1, but Sections 14
and 15 have been added to cover use of software over a computer network and 
provide for limited attribution for the Original Developer. In addition, 
Exhibit A has been modified to be consistent with Exhibit B.

Software distributed under the License is distributed on an "AS IS" basis,
WITHOUT WARRANTY OF ANY KIND, either express or implied. See the License for the
specific language governing rights and limitations under the License.

The Original Code is Infinite Reality Engine.

The Original Developer is the Initial Developer. The Initial Developer of the
Original Code is the Infinite Reality Engine team.

All portions of the code written by the Infinite Reality Engine team are Copyright © 2021-2025
Infinite Reality Engine. All Rights Reserved.
*/

import React, { useId, useLayoutEffect, useRef, useState } from 'react'
import { twMerge } from 'tailwind-merge'
import { HelpIconSm } from '../../../icons'
import Tooltip from '../Tooltip'

export const heights = {
  xs: 'h-6 py-0.5 px-2',
  l: 'h-8 py-1.5 px-2',
  xl: 'h-10 py-2.5 px-2'
} as const

export interface InputProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'className'> {
  height?: keyof typeof heights

  /**
   * Optional React node to display at the start (left) of the s field.
   * Typically used for icons or other UI elements that provide additional context.
   */
  startComponent?: React.ReactNode

  /**
   * Optional React node to display at the end (right) of the input field.
   * Typically used for icons, buttons, or other UI elements that provide actions or additional information.
   */
  endComponent?: React.ReactNode

  /**
   * Specifies the validation state of the input field, affecting its outline color and the color of helper text.
   * - `success` indicates a successful input.
   * - `error` indicates an error in the input.
   */
  state?: 'success' | 'error'

  /**
   * Optional helper text that provides additional information about the input field.
   * When set, this will only be displayed when a valid `state` (`success` or `error`) is set.
   * The color of the helper text is determined by the current state.
   */
  helperText?: string

  fullWidth?: boolean

  labelProps?: {
    text: string
    position: 'top' | 'left'
    infoText?: string
  }
}

const Input = (
  {
    height = 'l',
    startComponent,
    endComponent,
    state,
    helperText,
    labelProps,
    required,
    id,
    fullWidth,
    disabled,
    readOnly,
    autoComplete = 'off',
    ...props
  }: InputProps,
  ref: React.ForwardedRef<HTMLInputElement>
) => {
  const tempId = useId()
  const inputId = id || tempId

  const labelRef = useRef<HTMLLabelElement>(null)

  const [helperOffset, setHelperOffset] = useState('')
  useLayoutEffect(() => {
    const updateHelperTextPosition = () => {
      if (labelProps?.position === 'left' && labelRef.current) {
        setHelperOffset(`${labelRef.current.offsetWidth + 8}px`)
      } else {
        setHelperOffset('')
      }
    }

    updateHelperTextPosition()

    window.addEventListener('resize', updateHelperTextPosition)
    return () => {
      window.removeEventListener('resize', updateHelperTextPosition)
    }
  }, [labelProps])

  return (
    <div className={`flex flex-col gap-y-2 ${fullWidth ? 'w-full' : 'w-fit'}`}>
      <div
        className={`flex ${labelProps?.position === 'top' && 'flex-col gap-y-2'} ${
          labelProps?.position === 'left' && 'flex-row items-center gap-x-2'
        }`}
      >
        {labelProps?.text && (
          <label htmlFor={inputId} className="block text-xs font-medium" ref={labelRef}>
            <div className="flex flex-row items-center gap-x-1.5">
              <div className="flex flex-row items-center gap-x-0.5">
                {required && <span className="text-sm text-ui-error">*</span>}
                <span className="whitespace-nowrap text-xs text-text-secondary">{labelProps.text}</span>
              </div>

              {labelProps?.infoText && (
                <Tooltip content={labelProps.infoText}>
                  <HelpIconSm className="text-text-tertiary" />
                </Tooltip>
              )}
            </div>
          </label>
        )}

        <div
          className={twMerge(
            'flex w-full items-center gap-x-2 rounded-md border-[0.5px] border-ui-outline',
            'text-xs placeholder-text-tertiary transition-colors duration-300 dark:bg-ui-background',
            heights[height],
            disabled
              ? 'border-ui-inactive-outline bg-ui-inactive-background text-text-inactive'
              : 'border-ui-outline bg-ui-background text-text-tertiary hover:border-ui-hover-outline hover:bg-ui-hover-background has-[:focus]:border-ui-primary has-[:focus]:bg-ui-select-background has-[:focus]:text-text-primary',
            state === 'success' ? 'border-ui-success' : '',
            state === 'error' ? 'border-ui-error' : ''
          )}
        >
          <input
            spellCheck={false}
            className={twMerge(
              'placeholder-text-[#616161] dark:placeholder-text-text-tertiary',
              'text-[#616161] dark:text-text-tertiary',
              'peer order-2 h-full w-full bg-inherit pt-0.5 outline-none autofill:bg-inherit'
            )}
            ref={ref}
            id={inputId}
            disabled={disabled}
            readOnly={readOnly}
            autoComplete={autoComplete}
            {...props}
          />
          {startComponent && (
            <div className="order-1 flex items-center justify-center text-text-tertiary">{startComponent}</div>
          )}
          {endComponent && (
            <div className="order-3 flex items-center justify-center text-text-tertiary">{endComponent}</div>
          )}
        </div>
      </div>

      {helperText && (
        <span
          className={`text-xs ${state === 'success' && 'text-ui-success'} ${state === 'error' && 'text-text-error'}`}
          style={{
            translate: helperOffset
          }}
        >
          {helperText}
        </span>
      )}
    </div>
  )
}

export default React.forwardRef(Input)
