/*
CPAL-1.0 License

The contents of this file are subject to the Common Public Attribution License
Version 1.0. (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at
https://github.com/ir-engine/ir-engine/blob/dev/LICENSE.
The License is based on the Mozilla Public License Version 1.1, but Sections 14
and 15 have been added to cover use of software over a computer network and
provide for limited attribution for the Original Developer. In addition,
Exhibit A has been modified to be consistent with Exhibit B.

Software distributed under the License is distributed on an "AS IS" basis,
WITHOUT WARRANTY OF ANY KIND, either express or implied. See the License for the
specific language governing rights and limitations under the License.

The Original Code is Infinite Reality Engine.

The Original Developer is the Initial Developer. The Initial Developer of the
Original Code is the Infinite Reality Engine team.

All portions of the code written by the Infinite Reality Engine team are Copyright © 2021-2025
Infinite Reality Engine. All Rights Reserved.
*/

import { useHookstate } from '@hookstate/core'
import { AuthStrategiesType } from '../common/initialAuthState'
import { AuthService } from '../user/services/AuthService'
import { useAuthSettings } from './useAuthSetting'

export const getRedirectUrl = (redirectUrl?: string) => {
  const currentUrl = new URL(window.location.href)
  const targetUrl = redirectUrl ? new URL(redirectUrl, currentUrl.origin) : currentUrl
  targetUrl.search = currentUrl.search
  return targetUrl.toString()
}

export const useMagicLink = () => {
  const pending = useHookstate(false)
  const sent = useHookstate(false)
  const authSetting = useAuthSettings()

  const handleMagicLink = async (email: string, isSignUp: boolean, username?: string) => {
    pending.set(true)

    if (!isSignUp) {
      const isExistingEmail = await AuthService.validateUser(email)
      if (!isExistingEmail) {
        pending.set(false)
        return
      }
    }

    const redirectURL = new URL(location.href)
    if (username) {
      redirectURL.searchParams.append('username', username)
    }
    try {
      await AuthService.createMagicLink(email, authSetting as AuthStrategiesType, 'email', redirectURL.toString())
    } finally {
      pending.set(false)
    }
  }

  return { pending, handleMagicLink, sent }
}
