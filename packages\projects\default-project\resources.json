{"assets/animations/default_skeleton.vrm": {"type": "asset", "tags": ["Model"], "dependencies": [], "name": "default_skeleton.vrm", "thumbnailKey": "projects/ir-engine/default-project/public/thumbnails/default-projectassetsanimationsdefault_skeleton.vrm-thumbnail.png", "thumbnailMode": "automatic"}, "assets/animations/emotes.glb": {"type": "asset", "tags": ["Model"], "dependencies": [], "name": "emotes.glb"}, "assets/animations/locomotion.glb": {"type": "asset", "tags": ["Model"], "dependencies": [], "name": "locomotion.glb", "thumbnailKey": "projects/ir-engine/default-project/public/thumbnails/default-projectassetsanimationslocomotion.glb-thumbnail.png", "thumbnailMode": "automatic"}, "assets/animations/optional/seated.fbx": {"type": "asset", "tags": ["Model"], "dependencies": [], "name": "seated.fbx"}, "assets/apartment_skybox.jpg": {"type": "asset", "tags": ["Image"], "dependencies": [], "name": "apartment_skybox.jpg", "thumbnailKey": "projects/ir-engine/default-project/public/thumbnails/default-projectassetsapartment_skybox.jpg-thumbnail.png", "thumbnailMode": "automatic"}, "assets/apartment-CubemapBake.png": {"type": "asset", "tags": ["Image"], "dependencies": [], "name": "apartment-CubemapBake.png", "thumbnailKey": "projects/ir-engine/default-project/public/thumbnails/assets_apartment-CubemapBake.png-thumbnail.png", "thumbnailMode": "automatic"}, "assets/apartment.glb": {"type": "asset", "tags": ["Model"], "dependencies": [], "name": "apartment.glb", "thumbnailKey": "projects/ir-engine/default-project/public/thumbnails/default-projectassetsapartment.glb-thumbnail.png", "thumbnailMode": "automatic"}, "assets/cloud.png": {"type": "asset", "tags": ["Image"], "dependencies": [], "name": "cloud.png", "thumbnailKey": "projects/ir-engine/default-project/public/thumbnails/default-projectassetscloud.png-thumbnail.png", "thumbnailMode": "automatic"}, "assets/collisioncube.glb": {"type": "asset", "tags": ["Model"], "dependencies": [], "name": "collisioncube.glb", "thumbnailKey": "projects/ir-engine/default-project/public/thumbnails/default-projectassetscollisioncube.glb-thumbnail.png", "thumbnailMode": "automatic"}, "assets/controllers/left_controller.glb": {"type": "asset", "tags": ["Model"], "dependencies": [], "name": "left_controller.glb", "thumbnailKey": "projects/ir-engine/default-project/public/thumbnails/default-projectassetscontrollersleft_controller.glb-thumbnail.png", "thumbnailMode": "automatic"}, "assets/controllers/left.glb": {"type": "asset", "tags": ["Model"], "dependencies": [], "name": "left.glb", "thumbnailKey": "projects/ir-engine/default-project/public/thumbnails/default-projectassetscontrollersleft.glb-thumbnail.png", "thumbnailMode": "automatic"}, "assets/controllers/right_controller.glb": {"type": "asset", "tags": ["Model"], "dependencies": [], "name": "right_controller.glb"}, "assets/controllers/right.glb": {"type": "asset", "tags": ["Model"], "dependencies": [], "name": "right.glb", "thumbnailKey": "projects/ir-engine/default-project/public/thumbnails/default-projectassetscontrollersright.glb-thumbnail.png", "thumbnailMode": "automatic"}, "assets/default-silhouette.svg": {"type": "file", "tags": ["unknown"], "dependencies": [], "name": "default-silhouette.svg"}, "assets/drop-shadow.ktx2": {"type": "asset", "tags": ["Image"], "dependencies": [], "name": "drop-shadow.ktx2", "thumbnailKey": "projects/ir-engine/default-project/public/thumbnails/default-projectassetsdrop-shadow.ktx2-thumbnail.png", "thumbnailMode": "automatic"}, "assets/galaxyTexture.jpg": {"type": "asset", "tags": ["Image"], "dependencies": [], "name": "galaxyTexture.jpg", "thumbnailKey": "projects/ir-engine/default-project/public/thumbnails/default-projectassetsgalaxyTexture.jpg-thumbnail.png", "thumbnailMode": "automatic"}, "assets/generic_midday_02.ktx2": {"type": "file", "tags": ["Image"], "dependencies": [], "name": "generic_midday_02.ktx2", "thumbnailKey": "projects/ir-engine/default-project/public/thumbnails/assets_generic_midday_02.ktx2-thumbnail.png", "thumbnailMode": "automatic"}, "assets/generic_midday_02.png": {"type": "asset", "tags": ["Image"], "dependencies": [], "name": "generic_midday_02.png", "thumbnailKey": "projects/ir-engine/default-project/public/thumbnails/assets_generic_midday_02.png-thumbnail.png", "thumbnailMode": "automatic"}, "assets/keycard.glb": {"type": "asset", "tags": ["Model"], "dependencies": [], "name": "keycard.glb", "thumbnailKey": "projects/ir-engine/default-project/public/thumbnails/default-projectassetskeycard.glb-thumbnail.png", "thumbnailMode": "automatic"}, "assets/platform.glb": {"type": "asset", "tags": ["Model"], "dependencies": [], "name": "platform.glb", "thumbnailKey": "projects/ir-engine/default-project/public/thumbnails/assets_platform.glb-thumbnail.png", "thumbnailMode": "automatic"}, "assets/portal_frame.glb": {"type": "asset", "tags": ["Model"], "dependencies": [], "name": "portal_frame.glb", "thumbnailKey": "projects/ir-engine/default-project/public/thumbnails/default-projectassetsportal_frame.glb-thumbnail.png", "thumbnailMode": "automatic"}, "assets/prefabs/3d-model.prefab.gltf": {"type": "asset", "tags": ["De<PERSON>ult Prefab"], "dependencies": [], "name": "3d-model.prefab.gltf", "thumbnailKey": "projects/ir-engine/default-project/public/thumbnails/Component_thumb_placeholder.png", "thumbnailMode": "custom"}, "assets/prefabs/ambient-light.prefab.gltf": {"type": "asset", "tags": ["De<PERSON>ult Prefab"], "dependencies": [], "name": "ambient-light.prefab.gltf", "thumbnailKey": "projects/ir-engine/default-project/public/thumbnails/Component_thumb_placeholder.png", "thumbnailMode": "custom"}, "assets/prefabs/body.prefab.gltf": {"type": "asset", "tags": ["De<PERSON>ult Prefab"], "dependencies": [], "name": "body.prefab.gltf", "thumbnailKey": "projects/ir-engine/default-project/public/thumbnails/Component_thumb_placeholder.png", "thumbnailMode": "custom"}, "assets/prefabs/box-collider.prefab.gltf": {"type": "asset", "tags": ["De<PERSON>ult Prefab"], "dependencies": [], "name": "box-collider.prefab.gltf", "thumbnailKey": "projects/ir-engine/default-project/public/thumbnails/Component_thumb_placeholder.png", "thumbnailMode": "custom"}, "assets/prefabs/camera.prefab.gltf": {"type": "asset", "tags": ["De<PERSON>ult Prefab"], "dependencies": [], "name": "camera.prefab.gltf", "thumbnailKey": "projects/ir-engine/default-project/public/thumbnails/Component_thumb_placeholder.png", "thumbnailMode": "custom"}, "assets/prefabs/cylinder-collider.prefab.gltf": {"type": "asset", "tags": ["De<PERSON>ult Prefab"], "dependencies": [], "name": "cylinder-collider.prefab.gltf", "thumbnailKey": "projects/ir-engine/default-project/public/thumbnails/Component_thumb_placeholder.png", "thumbnailMode": "custom"}, "assets/prefabs/directional-light.prefab.gltf": {"type": "asset", "tags": ["De<PERSON>ult Prefab"], "dependencies": [], "name": "directional-light.prefab.gltf", "thumbnailKey": "projects/ir-engine/default-project/public/thumbnails/Component_thumb_placeholder.png", "thumbnailMode": "custom"}, "assets/prefabs/fog.prefab.gltf": {"type": "asset", "tags": ["De<PERSON>ult Prefab"], "dependencies": [], "name": "fog.prefab.gltf", "thumbnailKey": "projects/ir-engine/default-project/public/thumbnails/Component_thumb_placeholder.png", "thumbnailMode": "custom"}, "assets/prefabs/geo.prefab.gltf": {"type": "asset", "tags": ["De<PERSON>ult Prefab"], "dependencies": [], "name": "geo.prefab.gltf", "thumbnailKey": "projects/ir-engine/default-project/public/thumbnails/Component_thumb_placeholder.png", "thumbnailMode": "custom"}, "assets/prefabs/ground-plane.prefab.gltf": {"type": "asset", "tags": ["De<PERSON>ult Prefab"], "dependencies": [], "name": "ground-plane.prefab.gltf", "thumbnailKey": "projects/ir-engine/default-project/public/thumbnails/Component_thumb_placeholder.png", "thumbnailMode": "custom"}, "assets/prefabs/hemisphere-light.prefab.gltf": {"type": "asset", "tags": ["De<PERSON>ult Prefab"], "dependencies": [], "name": "hemisphere-light.prefab.gltf", "thumbnailKey": "projects/ir-engine/default-project/public/thumbnails/Component_thumb_placeholder.png", "thumbnailMode": "custom"}, "assets/prefabs/image.prefab.gltf": {"type": "asset", "tags": ["De<PERSON>ult Prefab"], "dependencies": [], "name": "image.prefab.gltf", "thumbnailKey": "projects/ir-engine/default-project/public/thumbnails/Component_thumb_placeholder.png", "thumbnailMode": "custom"}, "assets/prefabs/mesh-collider.prefab.gltf": {"type": "asset", "tags": ["De<PERSON>ult Prefab"], "dependencies": [], "name": "mesh-collider.prefab.gltf", "thumbnailKey": "projects/ir-engine/default-project/public/thumbnails/Component_thumb_placeholder.png", "thumbnailMode": "custom"}, "assets/prefabs/model-variants.prefab.gltf": {"type": "asset", "tags": ["Prefab"], "dependencies": [], "name": "model-variants.prefab.gltf"}, "assets/prefabs/point-light.prefab.gltf": {"type": "asset", "tags": ["De<PERSON>ult Prefab"], "dependencies": [], "name": "point-light.prefab.gltf", "thumbnailKey": "projects/ir-engine/default-project/public/thumbnails/Component_thumb_placeholder.png", "thumbnailMode": "custom"}, "assets/prefabs/postprocessing.prefab.gltf": {"type": "asset", "tags": ["De<PERSON>ult Prefab"], "dependencies": [], "name": "postprocessing.prefab.gltf", "thumbnailKey": "projects/ir-engine/default-project/public/thumbnails/Component_thumb_placeholder.png", "thumbnailMode": "custom"}, "assets/prefabs/skybox.prefab.gltf": {"type": "asset", "tags": ["De<PERSON>ult Prefab"], "dependencies": [], "name": "skybox.prefab.gltf", "thumbnailKey": "projects/ir-engine/default-project/public/thumbnails/Component_thumb_placeholder.png", "thumbnailMode": "custom"}, "assets/prefabs/sphere-collider.prefab.gltf": {"type": "asset", "tags": ["De<PERSON>ult Prefab"], "dependencies": [], "name": "sphere-collider.prefab.gltf", "thumbnailKey": "projects/ir-engine/default-project/public/thumbnails/Component_thumb_placeholder.png", "thumbnailMode": "custom"}, "assets/prefabs/spot-light.prefab.gltf": {"type": "asset", "tags": ["De<PERSON>ult Prefab"], "dependencies": [], "name": "spot-light.prefab.gltf", "thumbnailKey": "projects/ir-engine/default-project/public/thumbnails/Component_thumb_placeholder.png", "thumbnailMode": "custom"}, "assets/prefabs/text.prefab.gltf": {"type": "asset", "tags": ["De<PERSON>ult Prefab"], "dependencies": [], "name": "text.prefab.gltf", "thumbnailKey": "projects/ir-engine/default-project/public/thumbnails/Component_thumb_placeholder.png", "thumbnailMode": "custom"}, "assets/prefabs/title.prefab.gltf": {"type": "asset", "tags": ["De<PERSON>ult Prefab"], "dependencies": [], "name": "title.prefab.gltf", "thumbnailKey": "projects/ir-engine/default-project/public/thumbnails/Component_thumb_placeholder.png", "thumbnailMode": "custom"}, "assets/prefabs/video.prefab.gltf": {"type": "asset", "tags": ["De<PERSON>ult Prefab"], "dependencies": [], "name": "video.prefab.gltf", "thumbnailKey": "projects/ir-engine/default-project/public/thumbnails/Component_thumb_placeholder.png", "thumbnailMode": "custom"}, "assets/rings.glb": {"type": "asset", "tags": ["Model"], "dependencies": [], "name": "rings.glb", "thumbnailKey": "projects/ir-engine/default-project/public/thumbnails/assets_rings.glb-thumbnail.png", "thumbnailMode": "automatic"}, "assets/sample_etc1s.ktx2": {"type": "asset", "tags": ["Image"], "dependencies": [], "name": "sample_etc1s.ktx2", "thumbnailKey": "projects/ir-engine/default-project/public/thumbnails/default-projectassetssample_etc1s.ktx2-thumbnail.png", "thumbnailMode": "automatic"}, "assets/SampleAudio.mp3": {"type": "asset", "tags": ["Audio"], "dependencies": [], "name": "SampleAudio.mp3"}, "assets/SampleVideo.mp4": {"type": "asset", "tags": ["Video"], "dependencies": [], "name": "SampleVideo.mp4", "thumbnailKey": "projects/ir-engine/default-project/public/thumbnails/default-projectassetsSampleVideo.mp4-thumbnail.png", "thumbnailMode": "automatic"}, "assets/sky_skybox.jpg": {"type": "asset", "tags": ["Image"], "dependencies": [], "name": "sky_skybox.jpg", "thumbnailKey": "projects/ir-engine/default-project/public/thumbnails/default-projectassetssky_skybox.jpg-thumbnail.png", "thumbnailMode": "automatic"}, "assets/Skybase.glb": {"type": "asset", "tags": ["Model"], "dependencies": [], "name": "Skybase.glb", "thumbnailKey": "projects/ir-engine/default-project/public/thumbnails/default-projectassetsSkybase.glb-thumbnail.png", "thumbnailMode": "automatic"}, "assets/skyboxsun25deg/negx.jpg": {"type": "asset", "tags": ["Image"], "dependencies": [], "name": "negx.jpg", "thumbnailKey": "projects/ir-engine/default-project/public/thumbnails/default-projectassetsskyboxsun25degnegx.jpg-thumbnail.png", "thumbnailMode": "automatic"}, "assets/skyboxsun25deg/negy.jpg": {"type": "asset", "tags": ["Image"], "dependencies": [], "name": "negy.jpg"}, "assets/skyboxsun25deg/negz.jpg": {"type": "asset", "tags": ["Image"], "dependencies": [], "name": "negz.jpg", "thumbnailKey": "projects/ir-engine/default-project/public/thumbnails/default-projectassetsskyboxsun25degnegz.jpg-thumbnail.png", "thumbnailMode": "automatic"}, "assets/skyboxsun25deg/posx.jpg": {"type": "asset", "tags": ["Image"], "dependencies": [], "name": "posx.jpg", "thumbnailKey": "projects/ir-engine/default-project/public/thumbnails/default-projectassetsskyboxsun25degposx.jpg-thumbnail.png", "thumbnailMode": "automatic"}, "assets/skyboxsun25deg/posy.jpg": {"type": "asset", "tags": ["Image"], "dependencies": [], "name": "posy.jpg", "thumbnailKey": "projects/ir-engine/default-project/public/thumbnails/default-projectassetsskyboxsun25degposy.jpg-thumbnail.png", "thumbnailMode": "automatic"}, "assets/skyboxsun25deg/posz.jpg": {"type": "asset", "tags": ["Image"], "dependencies": [], "name": "posz.jpg"}, "assets/UV.png": {"type": "asset", "tags": ["Image"], "dependencies": [], "name": "UV.png", "thumbnailKey": "projects/ir-engine/default-project/public/thumbnails/default-projectassetsUV.png-thumbnail.png", "thumbnailMode": "automatic"}, "public/scenes/apartment-New-EnvMap_Bake.ktx2": {"type": "asset", "tags": ["Image"], "dependencies": [], "name": "apartment-New-EnvMap_Bake.ktx2"}, "public/scenes/apartment-Portal.ktx2": {"type": "asset", "tags": ["Image"], "dependencies": [], "name": "apartment-Portal.ktx2", "thumbnailKey": "projects/ir-engine/default-project/public/thumbnails/public_scenes_apartment-Portal.ktx2-thumbnail.png", "thumbnailMode": "automatic"}, "public/scenes/apartment.envmap.ktx2": {"type": "asset", "tags": ["Image"], "dependencies": [], "name": "apartment.envmap.ktx2", "thumbnailKey": "projects/ir-engine/default-project/public/thumbnails/public_scenes_apartment.envmap.ktx2-thumbnail.png", "thumbnailMode": "automatic"}, "public/scenes/apartment.gltf": {"type": "scene", "tags": ["Model"], "dependencies": [], "name": "apartment.gltf", "thumbnailKey": "projects/ir-engine/default-project/public/scenes/apartment.thumbnail.jpg"}, "public/scenes/apartment.loadingscreen.ktx2": {"type": "asset", "tags": ["Image"], "dependencies": [], "name": "apartment.loadingscreen.ktx2", "thumbnailKey": "projects/ir-engine/default-project/public/thumbnails/public_scenes_apartment.loadingscreen.ktx2-thumbnail.png", "thumbnailMode": "automatic"}, "public/scenes/default.envmap.ktx2": {"type": "asset", "tags": ["Image"], "dependencies": [], "name": "default.envmap.ktx2", "thumbnailKey": "projects/ir-engine/default-project/public/thumbnails/public_scenes_default.envmap.ktx2-thumbnail.png", "thumbnailMode": "automatic"}, "public/scenes/default.gltf": {"type": "scene", "tags": ["Model"], "dependencies": [], "name": "default.gltf", "thumbnailKey": "projects/ir-engine/default-project/public/scenes/default.thumbnail.jpg"}, "public/scenes/default.loadingscreen.ktx2": {"type": "asset", "tags": ["Image"], "dependencies": [], "name": "default.loadingscreen.ktx2", "thumbnailKey": "projects/ir-engine/default-project/public/thumbnails/public_scenes_default.loadingscreen.ktx2-thumbnail.png", "thumbnailMode": "automatic"}, "public/scenes/sky-station-Portal--_Sky Station Exterior.ktx2": {"type": "asset", "tags": ["Image"], "dependencies": [], "name": "sky-station-Portal--_Sky Station Exterior.ktx2"}, "public/scenes/sky-station-Portal--_Sky Station Interior.ktx2": {"type": "asset", "tags": ["Image"], "dependencies": [], "name": "sky-station-Portal--_Sky Station Interior.ktx2"}, "public/scenes/sky-station-Portal--_to Apartment.ktx2": {"type": "asset", "tags": ["Image"], "dependencies": [], "name": "sky-station-Portal--_to Apartment.ktx2"}, "public/scenes/sky-station.envmap.ktx2": {"type": "asset", "tags": ["Image"], "dependencies": [], "name": "sky-station.envmap.ktx2", "thumbnailKey": "projects/ir-engine/default-project/public/thumbnails/public_scenes_sky-station.envmap.ktx2-thumbnail.png", "thumbnailMode": "automatic"}, "public/scenes/sky-station.gltf": {"type": "scene", "tags": ["Model"], "dependencies": [], "name": "sky-station.gltf", "thumbnailKey": "projects/ir-engine/default-project/public/scenes/sky-station.thumbnail.jpg"}, "public/scenes/sky-station.loadingscreen.ktx2": {"type": "asset", "tags": ["Image"], "dependencies": [], "name": "sky-station.loadingscreen.ktx2", "thumbnailKey": "projects/ir-engine/default-project/public/thumbnails/public_scenes_sky-station.loadingscreen.ktx2-thumbnail.png", "thumbnailMode": "automatic"}, "assets/avatars/irRobot.vrm": {"type": "asset", "tags": ["Model"], "dependencies": ["projects/ir-engine/default-project/assets/avatars/irRobot.png"], "name": "irRobot.vrm"}, "assets/noise-offset.material.gltf": {"type": "asset", "tags": ["Material"], "dependencies": [], "name": "noise-offset.material.gltf"}, "public/scenes/sponza.gltf": {"type": "scene", "tags": ["Model"], "dependencies": [], "name": "sponza.gltf", "thumbnailKey": "projects/ir-engine/default-project/public/scenes/sponza.thumbnail.jpg"}}