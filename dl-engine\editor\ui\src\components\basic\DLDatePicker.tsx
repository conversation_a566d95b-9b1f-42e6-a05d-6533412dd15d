/**
 * DL-Engine 日期选择器组件
 * 
 * 基于 Ant Design DatePicker 的增强版本
 */

import React, { useCallback } from 'react'
import { DatePicker, DatePickerProps, TimePicker, TimePickerProps } from 'antd'
import { useTranslation } from 'react-i18next'
import classNames from 'classnames'
import dayjs, { Dayjs } from 'dayjs'
import 'dayjs/locale/zh-cn'
import DLTooltip from './DLTooltip'

// 设置dayjs中文语言
dayjs.locale('zh-cn')

/**
 * DL日期选择器属性
 */
export interface DLDatePickerProps extends Omit<DatePickerProps, 'onChange'> {
  /** 标签文本 */
  label?: string
  /** 工具提示 */
  tooltip?: string
  /** 工具提示位置 */
  tooltipPlacement?: 'top' | 'bottom' | 'left' | 'right'
  /** 是否显示标签 */
  showLabel?: boolean
  /** 标签位置 */
  labelPosition?: 'top' | 'left'
  /** 日期格式 */
  format?: string
  /** 是否显示时间 */
  showTime?: boolean | TimePickerProps
  /** 是否显示今天按钮 */
  showToday?: boolean
  /** 是否显示清除按钮 */
  allowClear?: boolean
  /** 值变化回调 */
  onChange?: (date: Dayjs | null, dateString: string) => void
  /** 自定义类名 */
  className?: string
}

/**
 * DL日期选择器组件
 */
const DLDatePicker: React.FC<DLDatePickerProps> = ({
  label,
  tooltip,
  tooltipPlacement = 'top',
  showLabel = true,
  labelPosition = 'top',
  format = 'YYYY-MM-DD',
  showTime = false,
  showToday = true,
  allowClear = true,
  onChange,
  className,
  ...props
}) => {
  const { t } = useTranslation()

  /**
   * 处理值变化
   */
  const handleChange = useCallback((date: Dayjs | null, dateString: string) => {
    onChange?.(date, dateString)
  }, [onChange])

  /**
   * 渲染标签
   */
  const renderLabel = () => {
    if (!showLabel || !label) return null

    return (
      <label className="dl-datepicker-label">
        {label}
      </label>
    )
  }

  /**
   * 日期选择器类名
   */
  const datePickerClassName = classNames(
    'dl-datepicker',
    {
      'dl-datepicker--with-time': showTime,
      'dl-datepicker--label-left': labelPosition === 'left',
      'dl-datepicker--no-label': !showLabel || !label
    },
    className
  )

  /**
   * 渲染日期选择器
   */
  const renderDatePicker = () => {
    const datePickerProps = {
      ...props,
      format: showTime ? `${format} HH:mm:ss` : format,
      showTime,
      showToday,
      allowClear,
      onChange: handleChange,
      className: 'dl-datepicker-input',
      placeholder: props.placeholder || (showTime ? t('common.selectDateTime') : t('common.selectDate'))
    }

    return <DatePicker {...datePickerProps} />
  }

  /**
   * 渲染完整组件
   */
  const renderComponent = () => {
    if (labelPosition === 'left') {
      return (
        <div className={datePickerClassName}>
          {renderLabel()}
          {renderDatePicker()}
        </div>
      )
    }

    return (
      <div className={datePickerClassName}>
        {renderLabel()}
        {renderDatePicker()}
      </div>
    )
  }

  // 如果有工具提示，包装在 Tooltip 中
  if (tooltip) {
    return (
      <DLTooltip title={tooltip} placement={tooltipPlacement}>
        {renderComponent()}
      </DLTooltip>
    )
  }

  return renderComponent()
}

// 范围日期选择器
const DLRangePicker: React.FC<{
  label?: string
  tooltip?: string
  tooltipPlacement?: 'top' | 'bottom' | 'left' | 'right'
  showLabel?: boolean
  labelPosition?: 'top' | 'left'
  format?: string
  showTime?: boolean | TimePickerProps
  allowClear?: boolean
  onChange?: (dates: [Dayjs | null, Dayjs | null] | null, dateStrings: [string, string]) => void
  className?: string
} & Omit<DatePickerProps, 'onChange'>> = ({
  label,
  tooltip,
  tooltipPlacement = 'top',
  showLabel = true,
  labelPosition = 'top',
  format = 'YYYY-MM-DD',
  showTime = false,
  allowClear = true,
  onChange,
  className,
  ...props
}) => {
  const { t } = useTranslation()

  /**
   * 处理值变化
   */
  const handleChange = useCallback((dates: any, dateStrings: [string, string]) => {
    onChange?.(dates, dateStrings)
  }, [onChange])

  /**
   * 渲染标签
   */
  const renderLabel = () => {
    if (!showLabel || !label) return null

    return (
      <label className="dl-datepicker-label">
        {label}
      </label>
    )
  }

  /**
   * 范围选择器类名
   */
  const rangePickerClassName = classNames(
    'dl-datepicker dl-datepicker--range',
    {
      'dl-datepicker--with-time': showTime,
      'dl-datepicker--label-left': labelPosition === 'left',
      'dl-datepicker--no-label': !showLabel || !label
    },
    className
  )

  /**
   * 渲染范围选择器
   */
  const renderRangePicker = () => {
    const rangePickerProps = {
      ...props,
      format: showTime ? `${format} HH:mm:ss` : format,
      showTime,
      allowClear,
      onChange: handleChange,
      className: 'dl-datepicker-input',
      placeholder: [
        props.placeholder?.[0] || t('common.startDate'),
        props.placeholder?.[1] || t('common.endDate')
      ]
    }

    return <DatePicker.RangePicker {...rangePickerProps} />
  }

  /**
   * 渲染完整组件
   */
  const renderComponent = () => {
    if (labelPosition === 'left') {
      return (
        <div className={rangePickerClassName}>
          {renderLabel()}
          {renderRangePicker()}
        </div>
      )
    }

    return (
      <div className={rangePickerClassName}>
        {renderLabel()}
        {renderRangePicker()}
      </div>
    )
  }

  // 如果有工具提示，包装在 Tooltip 中
  if (tooltip) {
    return (
      <DLTooltip title={tooltip} placement={tooltipPlacement}>
        {renderComponent()}
      </DLTooltip>
    )
  }

  return renderComponent()
}

// 时间选择器
const DLTimePicker: React.FC<{
  label?: string
  tooltip?: string
  tooltipPlacement?: 'top' | 'bottom' | 'left' | 'right'
  showLabel?: boolean
  labelPosition?: 'top' | 'left'
  format?: string
  use12Hours?: boolean
  allowClear?: boolean
  onChange?: (time: Dayjs | null, timeString: string) => void
  className?: string
} & Omit<TimePickerProps, 'onChange'>> = ({
  label,
  tooltip,
  tooltipPlacement = 'top',
  showLabel = true,
  labelPosition = 'top',
  format = 'HH:mm:ss',
  use12Hours = false,
  allowClear = true,
  onChange,
  className,
  ...props
}) => {
  const { t } = useTranslation()

  /**
   * 处理值变化
   */
  const handleChange = useCallback((time: Dayjs | null, timeString: string) => {
    onChange?.(time, timeString)
  }, [onChange])

  /**
   * 渲染标签
   */
  const renderLabel = () => {
    if (!showLabel || !label) return null

    return (
      <label className="dl-datepicker-label">
        {label}
      </label>
    )
  }

  /**
   * 时间选择器类名
   */
  const timePickerClassName = classNames(
    'dl-datepicker dl-datepicker--time',
    {
      'dl-datepicker--12hours': use12Hours,
      'dl-datepicker--label-left': labelPosition === 'left',
      'dl-datepicker--no-label': !showLabel || !label
    },
    className
  )

  /**
   * 渲染时间选择器
   */
  const renderTimePicker = () => {
    const timePickerProps = {
      ...props,
      format: use12Hours ? 'h:mm:ss A' : format,
      use12Hours,
      allowClear,
      onChange: handleChange,
      className: 'dl-datepicker-input',
      placeholder: props.placeholder || t('common.selectTime')
    }

    return <TimePicker {...timePickerProps} />
  }

  /**
   * 渲染完整组件
   */
  const renderComponent = () => {
    if (labelPosition === 'left') {
      return (
        <div className={timePickerClassName}>
          {renderLabel()}
          {renderTimePicker()}
        </div>
      )
    }

    return (
      <div className={timePickerClassName}>
        {renderLabel()}
        {renderTimePicker()}
      </div>
    )
  }

  // 如果有工具提示，包装在 Tooltip 中
  if (tooltip) {
    return (
      <DLTooltip title={tooltip} placement={tooltipPlacement}>
        {renderComponent()}
      </DLTooltip>
    )
  }

  return renderComponent()
}

// 导出组件
DLDatePicker.RangePicker = DLRangePicker
DLDatePicker.TimePicker = DLTimePicker

export default DLDatePicker
