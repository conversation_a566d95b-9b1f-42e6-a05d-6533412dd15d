/**
 * 插件市场
 * 
 * 提供插件发现、下载、安装功能
 */

import { EventEmitter } from 'events'
import { PluginMetadata, PluginConfig } from './PluginAPI'

/**
 * 市场插件信息
 */
export interface MarketplacePlugin {
  /** 插件ID */
  id: string
  /** 插件名称 */
  name: string
  /** 插件描述 */
  description: string
  /** 版本 */
  version: string
  /** 作者 */
  author: string
  /** 标签 */
  tags: string[]
  /** 分类 */
  category: string
  /** 下载次数 */
  downloads: number
  /** 评分 */
  rating: number
  /** 评论数 */
  reviews: number
  /** 截图 */
  screenshots: string[]
  /** 文档链接 */
  documentation?: string
  /** 源码链接 */
  repository?: string
  /** 许可证 */
  license: string
  /** 发布时间 */
  publishedAt: string
  /** 更新时间 */
  updatedAt: string
  /** 是否免费 */
  isFree: boolean
  /** 价格 */
  price?: number
  /** 下载URL */
  downloadUrl: string
  /** 依赖 */
  dependencies: string[]
  /** 兼容性 */
  compatibility: {
    minVersion: string
    maxVersion?: string
  }
}

/**
 * 搜索过滤器
 */
export interface SearchFilter {
  /** 关键词 */
  query?: string
  /** 分类 */
  category?: string
  /** 标签 */
  tags?: string[]
  /** 是否免费 */
  isFree?: boolean
  /** 最小评分 */
  minRating?: number
  /** 排序方式 */
  sortBy?: 'name' | 'downloads' | 'rating' | 'updated' | 'published'
  /** 排序方向 */
  sortOrder?: 'asc' | 'desc'
  /** 页码 */
  page?: number
  /** 每页数量 */
  pageSize?: number
}

/**
 * 搜索结果
 */
export interface SearchResult {
  /** 插件列表 */
  plugins: MarketplacePlugin[]
  /** 总数 */
  total: number
  /** 当前页 */
  page: number
  /** 每页数量 */
  pageSize: number
  /** 总页数 */
  totalPages: number
}

/**
 * 下载进度
 */
export interface DownloadProgress {
  /** 插件ID */
  pluginId: string
  /** 已下载字节数 */
  loaded: number
  /** 总字节数 */
  total: number
  /** 进度百分比 */
  progress: number
  /** 下载速度 */
  speed: number
}

/**
 * 插件市场类
 */
export class PluginMarketplace extends EventEmitter {
  private apiBaseUrl: string
  private apiKey?: string
  private cache: Map<string, MarketplacePlugin> = new Map()
  private cacheExpiry: Map<string, number> = new Map()
  private downloadTasks: Map<string, AbortController> = new Map()

  constructor(apiBaseUrl: string, apiKey?: string) {
    super()
    this.apiBaseUrl = apiBaseUrl.replace(/\/$/, '')
    this.apiKey = apiKey
  }

  /**
   * 搜索插件
   */
  async searchPlugins(filter: SearchFilter = {}): Promise<SearchResult> {
    const params = new URLSearchParams()
    
    if (filter.query) params.append('q', filter.query)
    if (filter.category) params.append('category', filter.category)
    if (filter.tags) params.append('tags', filter.tags.join(','))
    if (filter.isFree !== undefined) params.append('free', filter.isFree.toString())
    if (filter.minRating) params.append('min_rating', filter.minRating.toString())
    if (filter.sortBy) params.append('sort', filter.sortBy)
    if (filter.sortOrder) params.append('order', filter.sortOrder)
    if (filter.page) params.append('page', filter.page.toString())
    if (filter.pageSize) params.append('size', filter.pageSize.toString())

    const response = await this.makeRequest(`/plugins/search?${params}`)
    const result = await response.json()

    // 缓存结果
    result.plugins.forEach((plugin: MarketplacePlugin) => {
      this.cache.set(plugin.id, plugin)
      this.cacheExpiry.set(plugin.id, Date.now() + 5 * 60 * 1000) // 5分钟缓存
    })

    return result
  }

  /**
   * 获取插件详情
   */
  async getPlugin(pluginId: string): Promise<MarketplacePlugin> {
    // 检查缓存
    const cached = this.cache.get(pluginId)
    const expiry = this.cacheExpiry.get(pluginId)
    
    if (cached && expiry && Date.now() < expiry) {
      return cached
    }

    const response = await this.makeRequest(`/plugins/${pluginId}`)
    const plugin = await response.json()

    // 更新缓存
    this.cache.set(pluginId, plugin)
    this.cacheExpiry.set(pluginId, Date.now() + 5 * 60 * 1000)

    return plugin
  }

  /**
   * 获取分类列表
   */
  async getCategories(): Promise<string[]> {
    const response = await this.makeRequest('/categories')
    return response.json()
  }

  /**
   * 获取热门插件
   */
  async getFeaturedPlugins(limit = 10): Promise<MarketplacePlugin[]> {
    const response = await this.makeRequest(`/plugins/featured?limit=${limit}`)
    return response.json()
  }

  /**
   * 下载插件
   */
  async downloadPlugin(
    pluginId: string,
    onProgress?: (progress: DownloadProgress) => void
  ): Promise<ArrayBuffer> {
    const plugin = await this.getPlugin(pluginId)
    const abortController = new AbortController()
    this.downloadTasks.set(pluginId, abortController)

    try {
      const response = await fetch(plugin.downloadUrl, {
        signal: abortController.signal,
        headers: this.getHeaders()
      })

      if (!response.ok) {
        throw new Error(`下载失败: ${response.statusText}`)
      }

      const contentLength = response.headers.get('content-length')
      const total = contentLength ? parseInt(contentLength, 10) : 0
      let loaded = 0

      const reader = response.body?.getReader()
      if (!reader) {
        throw new Error('无法读取响应流')
      }

      const chunks: Uint8Array[] = []
      const startTime = Date.now()

      while (true) {
        const { done, value } = await reader.read()
        
        if (done) break

        chunks.push(value)
        loaded += value.length

        if (onProgress && total > 0) {
          const progress = (loaded / total) * 100
          const elapsed = Date.now() - startTime
          const speed = loaded / (elapsed / 1000)

          onProgress({
            pluginId,
            loaded,
            total,
            progress,
            speed
          })
        }
      }

      // 合并所有块
      const totalLength = chunks.reduce((sum, chunk) => sum + chunk.length, 0)
      const result = new Uint8Array(totalLength)
      let offset = 0

      for (const chunk of chunks) {
        result.set(chunk, offset)
        offset += chunk.length
      }

      return result.buffer
    } finally {
      this.downloadTasks.delete(pluginId)
    }
  }

  /**
   * 取消下载
   */
  cancelDownload(pluginId: string): void {
    const abortController = this.downloadTasks.get(pluginId)
    if (abortController) {
      abortController.abort()
      this.downloadTasks.delete(pluginId)
    }
  }

  /**
   * 提交评论
   */
  async submitReview(
    pluginId: string,
    rating: number,
    comment: string
  ): Promise<void> {
    await this.makeRequest(`/plugins/${pluginId}/reviews`, {
      method: 'POST',
      body: JSON.stringify({ rating, comment })
    })
  }

  /**
   * 获取评论
   */
  async getReviews(pluginId: string, page = 1, pageSize = 10): Promise<any> {
    const response = await this.makeRequest(
      `/plugins/${pluginId}/reviews?page=${page}&size=${pageSize}`
    )
    return response.json()
  }

  /**
   * 清理缓存
   */
  clearCache(): void {
    this.cache.clear()
    this.cacheExpiry.clear()
  }

  /**
   * 发起HTTP请求
   */
  private async makeRequest(path: string, options: RequestInit = {}): Promise<Response> {
    const url = `${this.apiBaseUrl}${path}`
    
    const response = await fetch(url, {
      ...options,
      headers: {
        ...this.getHeaders(),
        ...options.headers
      }
    })

    if (!response.ok) {
      throw new Error(`请求失败: ${response.status} ${response.statusText}`)
    }

    return response
  }

  /**
   * 获取请求头
   */
  private getHeaders(): Record<string, string> {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json'
    }

    if (this.apiKey) {
      headers['Authorization'] = `Bearer ${this.apiKey}`
    }

    return headers
  }
}

export default PluginMarketplace
