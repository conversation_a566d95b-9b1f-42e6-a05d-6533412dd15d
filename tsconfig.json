{"compilerOptions": {"target": "ESNext", "useDefineForClassFields": true, "module": "CommonJS", "lib": ["ESNext", "DOM", "DOM.Iterable"], "skipLibCheck": true, "moduleResolution": "node", "allowImportingTsExtensions": true, "isolatedModules": true, "moduleDetection": "force", "noEmit": true, "allowJs": true, "forceConsistentCasingInFileNames": true, "strict": false, "strictNullChecks": true, "strictBindCallApply": true, "noImplicitAny": false, "noUnusedLocals": false, "resolveJsonModule": true, "esModuleInterop": true, "sourceMap": true, "jsx": "react", "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "emitDecoratorMetadata": true, "declaration": false, "types": ["@types/node"]}, "include": ["./__global.d.ts"]}