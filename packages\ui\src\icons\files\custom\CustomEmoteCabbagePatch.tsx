/*
CPAL-1.0 License

The contents of this file are subject to the Common Public Attribution License
Version 1.0. (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at
https://github.com/ir-engine/ir-engine/blob/dev/LICENSE.
The License is based on the Mozilla Public License Version 1.1, but Sections 14
and 15 have been added to cover use of software over a computer network and
provide for limited attribution for the Original Developer. In addition,
Exhibit A has been modified to be consistent with Exhibit B.

Software distributed under the License is distributed on an "AS IS" basis,
WITHOUT WARRANTY OF ANY KIND, either express or implied. See the License for the
specific language governing rights and limitations under the License.

The Original Code is Infinite Reality Engine.

The Original Developer is the Initial Developer. The Initial Developer of the
Original Code is the Infinite Reality Engine team.

All portions of the code written by the Infinite Reality Engine team are Copyright © 2021-2025
Infinite Reality Engine. All Rights Reserved.
*/

import React, { forwardRef, Ref, SVGProps } from 'react'

const Icon = (props: SVGProps<SVGSVGElement>, ref: Ref<SVGSVGElement>) => (
  <svg
    width="1em"
    height="1em"
    viewBox="0 0 27 23"
    xmlns="http://www.w3.org/2000/svg"
    role="img"
    fill="none"
    stroke="none"
    ref={ref}
    {...props}
  >
    <path
      d="M17.3799 7.89373V12.0585C17.9575 13.7913 18.8391 17.5001 17.0759 20.1449C16.8023 20.5704 16.1639 20.6312 15.7687 20.2665C15.4647 19.9929 15.4343 19.5369 15.6471 19.2025C16.8327 17.3785 16.3159 14.5817 15.6471 12.5145L13.8232 17.8345C13.6103 18.5033 12.7592 18.7465 12.2119 18.2905L9.35434 15.7977C8.98957 15.4937 8.95914 14.9769 9.26318 14.6121C9.56715 14.2473 10.1144 14.2169 10.4488 14.5209L12.4248 16.1929L13.3975 12.5449V8.07618C12.5159 8.04575 11.5431 8.01532 10.6312 7.98495C9.96235 7.95452 9.44556 7.37694 9.50636 6.67778L9.90155 2.02659C10.0231 1.69219 10.388 1.47939 10.7224 1.47939C11.2695 1.47939 11.7255 2.02659 11.5736 2.57379L11.2695 5.0362C11.2087 5.46176 11.5736 5.85695 11.9991 5.85695H17.0151C17.2583 5.85695 17.4711 5.7658 17.5927 5.58334C18.2919 4.67136 18.7479 3.21216 18.9911 2.14816C19.1735 1.60099 19.5383 1.47939 19.9335 1.47939C20.4199 1.54019 20.7543 2.02659 20.6631 2.513C20.3287 4.03299 19.4471 7.01218 17.3799 7.89373Z"
      fill="#F7F8FA"
    />
    <path
      d="M13.6408 4.06359C13.1543 3.33399 13.0632 2.51317 13.4584 1.81399C13.7928 1.1452 14.492 0.75 15.2216 0.75C16.316 0.75 17.228 1.662 17.228 2.75641C17.228 3.48601 16.8023 4.27641 16.1943 4.6716C15.8904 4.88435 15.556 4.97557 15.2216 4.97557C14.9784 4.97557 14.7656 4.91478 14.5224 4.82355C14.1575 4.6716 13.8536 4.42836 13.6408 4.06359Z"
      fill="#F7F8FA"
    />
    <path
      d="M22.9971 22.2885H19.6114V21.6167L21.2093 19.9137C21.4287 19.674 21.5904 19.4648 21.6944 19.2861C21.8007 19.1074 21.8538 18.9378 21.8538 18.7772C21.8538 18.5578 21.7985 18.3859 21.6876 18.2615C21.5768 18.1349 21.4185 18.0716 21.2127 18.0716C20.991 18.0716 20.8157 18.1484 20.6868 18.3022C20.5602 18.4538 20.4968 18.6539 20.4968 18.9028H19.513C19.513 18.6019 19.5842 18.3271 19.7267 18.0783C19.8715 17.8296 20.075 17.635 20.3374 17.4948C20.5997 17.3523 20.8972 17.2811 21.2296 17.2811C21.7385 17.2811 22.1332 17.4033 22.4136 17.6475C22.6963 17.8918 22.8377 18.2367 22.8377 18.6822C22.8377 18.9264 22.7744 19.1752 22.6477 19.4286C22.521 19.6819 22.3039 19.977 21.9963 20.314L20.8734 21.498H22.9971V22.2885Z"
      fill="#F7F8FA"
    />
    <path
      d="M8.07132 11.8666C7.63728 11.8666 7.2466 11.6459 6.94277 11.2927C6.76914 11.0721 6.63892 10.763 6.63892 10.4982C6.63892 10.4982 6.63892 10.454 6.63892 10.4099V6.26044L5.38015 7.31985C5.20652 7.4964 4.90268 7.45228 4.77246 7.27573C4.64224 7.09912 4.64224 6.79014 4.85927 6.65772L6.81257 5.02441C6.94277 4.89198 7.1164 4.89198 7.29003 4.98029C7.46365 5.06853 7.55043 5.20095 7.55043 5.37757V9.12971C7.59386 9.12971 7.63728 9.08559 7.72406 9.08559C8.1581 8.95316 8.63563 9.04147 8.98281 9.30633C9.33007 9.57118 9.54712 10.0126 9.54712 10.4541C9.5037 11.2045 8.85261 11.8666 8.07132 11.8666Z"
      fill="#F7F8FA"
    />
  </svg>
)
const ForwardRef = forwardRef(Icon)
export default ForwardRef
