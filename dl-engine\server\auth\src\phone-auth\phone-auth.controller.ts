/**
 * DL-Engine 手机号认证控制器
 * 
 * 功能：
 * - 手机号注册
 * - 手机号登录
 * - 验证码发送
 * - 验证码验证
 * - 手机号绑定/解绑
 */

import { Controller, Post, Body, Get, Query, UseGuards, Req } from '@nestjs/common'
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger'
import { ThrottlerGuard } from '@nestjs/throttler'
import { PhoneAuthService } from './phone-auth.service'
import { JwtAuthGuard } from '../jwt-auth/jwt-auth.guard'
import { 
  SendSmsDto, 
  VerifyPhoneDto, 
  PhoneLoginDto, 
  PhoneRegisterDto,
  BindPhoneDto,
  UnbindPhoneDto
} from './dto/phone-auth.dto'

@ApiTags('phone')
@Controller('api/auth/phone')
@UseGuards(ThrottlerGuard)
export class PhoneAuthController {
  constructor(private readonly phoneAuthService: PhoneAuthService) {}

  @Post('send-sms')
  @ApiOperation({ summary: '发送短信验证码', description: '向指定手机号发送验证码' })
  @ApiResponse({ status: 200, description: '验证码发送成功' })
  @ApiResponse({ status: 400, description: '请求参数错误' })
  @ApiResponse({ status: 429, description: '请求过于频繁' })
  async sendSms(@Body() sendSmsDto: SendSmsDto) {
    return this.phoneAuthService.sendSms(sendSmsDto)
  }

  @Post('verify')
  @ApiOperation({ summary: '验证手机号码', description: '验证手机号和验证码' })
  @ApiResponse({ status: 200, description: '验证成功' })
  @ApiResponse({ status: 400, description: '验证码错误或已过期' })
  async verifyPhone(@Body() verifyPhoneDto: VerifyPhoneDto) {
    return this.phoneAuthService.verifyPhone(verifyPhoneDto)
  }

  @Post('register')
  @ApiOperation({ summary: '手机号注册', description: '使用手机号和验证码注册新用户' })
  @ApiResponse({ status: 201, description: '注册成功' })
  @ApiResponse({ status: 400, description: '注册失败' })
  @ApiResponse({ status: 409, description: '手机号已存在' })
  async register(@Body() phoneRegisterDto: PhoneRegisterDto) {
    return this.phoneAuthService.register(phoneRegisterDto)
  }

  @Post('login')
  @ApiOperation({ summary: '手机号登录', description: '使用手机号和验证码登录' })
  @ApiResponse({ status: 200, description: '登录成功' })
  @ApiResponse({ status: 400, description: '登录失败' })
  @ApiResponse({ status: 401, description: '验证码错误' })
  @ApiResponse({ status: 404, description: '用户不存在' })
  async login(@Body() phoneLoginDto: PhoneLoginDto) {
    return this.phoneAuthService.login(phoneLoginDto)
  }

  @Post('bind')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '绑定手机号', description: '为已登录用户绑定手机号' })
  @ApiResponse({ status: 200, description: '绑定成功' })
  @ApiResponse({ status: 400, description: '绑定失败' })
  @ApiResponse({ status: 409, description: '手机号已被其他用户绑定' })
  async bindPhone(@Body() bindPhoneDto: BindPhoneDto, @Req() req: any) {
    return this.phoneAuthService.bindPhone(req.user.id, bindPhoneDto)
  }

  @Post('unbind')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '解绑手机号', description: '解绑当前用户的手机号' })
  @ApiResponse({ status: 200, description: '解绑成功' })
  @ApiResponse({ status: 400, description: '解绑失败' })
  async unbindPhone(@Body() unbindPhoneDto: UnbindPhoneDto, @Req() req: any) {
    return this.phoneAuthService.unbindPhone(req.user.id, unbindPhoneDto)
  }

  @Get('check')
  @ApiOperation({ summary: '检查手机号状态', description: '检查手机号是否已注册' })
  @ApiResponse({ status: 200, description: '检查结果' })
  async checkPhone(@Query('phone') phone: string, @Query('countryCode') countryCode?: string) {
    return this.phoneAuthService.checkPhone(phone, countryCode)
  }

  @Get('countries')
  @ApiOperation({ summary: '获取支持的国家代码', description: '获取支持短信验证的国家代码列表' })
  @ApiResponse({ status: 200, description: '国家代码列表' })
  async getSupportedCountries() {
    return this.phoneAuthService.getSupportedCountries()
  }

  @Get('stats')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '获取短信统计', description: '获取当前用户的短信发送统计' })
  @ApiResponse({ status: 200, description: '短信统计信息' })
  async getSmsStats(@Req() req: any) {
    return this.phoneAuthService.getSmsStats(req.user.id)
  }
}
