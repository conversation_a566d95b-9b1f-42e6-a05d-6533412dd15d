/**
 * DL-Engine 认证服务健康检查控制器
 */

import { Controller, Get } from '@nestjs/common'
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger'
import { HealthService } from './health.service'

@ApiTags('health')
@Controller('health')
export class HealthController {
  constructor(private readonly healthService: HealthService) {}

  @Get()
  @ApiOperation({ summary: '获取认证服务健康状态' })
  @ApiResponse({ status: 200, description: '健康状态信息' })
  async getHealth() {
    return this.healthService.getHealth()
  }

  @Get('detailed')
  @ApiOperation({ summary: '获取详细健康状态' })
  @ApiResponse({ status: 200, description: '详细健康状态信息' })
  async getDetailedHealth() {
    return this.healthService.getDetailedHealth()
  }
}
