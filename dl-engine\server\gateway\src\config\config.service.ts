/**
 * DL-Engine API Gateway 配置服务
 * 
 * 管理所有网关配置：
 * - 服务器配置
 * - 路由配置
 * - 安全配置
 * - 监控配置
 * - 微服务配置
 */

import { Injectable } from '@nestjs/common'

export interface ServiceConfig {
  name: string
  url: string
  healthCheck: string
  timeout: number
  retries: number
  weight: number
  enabled: boolean
}

export interface RouteConfig {
  path: string
  method: string
  service: string
  middleware: string[]
  rateLimit?: {
    windowMs: number
    max: number
  }
  auth?: {
    required: boolean
    roles?: string[]
  }
}

@Injectable()
export class ConfigService {
  private readonly config = {
    server: {
      port: parseInt(process.env.GATEWAY_PORT || '3030'),
      host: process.env.GATEWAY_HOST || '0.0.0.0',
      environment: process.env.NODE_ENV || 'development'
    },
    
    cors: {
      allowedOrigins: (process.env.ALLOWED_ORIGINS || 'http://localhost:3000,https://localhost:3001').split(','),
      credentials: true
    },

    redis: {
      host: process.env.REDIS_HOST || 'localhost',
      port: parseInt(process.env.REDIS_PORT || '6379'),
      password: process.env.REDIS_PASSWORD,
      db: parseInt(process.env.REDIS_DB || '0')
    },

    jwt: {
      secret: process.env.JWT_SECRET || 'dl-engine-gateway-secret',
      expiresIn: process.env.JWT_EXPIRES_IN || '24h'
    },

    monitoring: {
      enabled: process.env.MONITORING_ENABLED === 'true',
      metricsPath: '/metrics',
      healthPath: '/health'
    },

    // 微服务配置
    services: {
      auth: {
        name: 'auth-service',
        url: process.env.AUTH_SERVICE_URL || 'http://localhost:3031',
        healthCheck: '/health',
        timeout: 5000,
        retries: 3,
        weight: 1,
        enabled: true
      },
      api: {
        name: 'api-service',
        url: process.env.API_SERVICE_URL || 'http://localhost:3032',
        healthCheck: '/health',
        timeout: 10000,
        retries: 3,
        weight: 1,
        enabled: true
      },
      instance: {
        name: 'instance-service',
        url: process.env.INSTANCE_SERVICE_URL || 'http://localhost:3033',
        healthCheck: '/health',
        timeout: 15000,
        retries: 2,
        weight: 1,
        enabled: true
      },
      media: {
        name: 'media-service',
        url: process.env.MEDIA_SERVICE_URL || 'http://localhost:3034',
        healthCheck: '/health',
        timeout: 30000,
        retries: 2,
        weight: 1,
        enabled: true
      },
      ai: {
        name: 'ai-service',
        url: process.env.AI_SERVICE_URL || 'http://localhost:3035',
        healthCheck: '/health',
        timeout: 60000,
        retries: 1,
        weight: 1,
        enabled: true
      }
    } as Record<string, ServiceConfig>,

    // 路由配置
    routes: [
      // 认证相关路由
      {
        path: '/api/auth/*',
        method: 'ALL',
        service: 'auth',
        middleware: ['cors', 'rateLimit'],
        rateLimit: { windowMs: 60000, max: 10 }
      },
      
      // 用户管理路由
      {
        path: '/api/users/*',
        method: 'ALL',
        service: 'api',
        middleware: ['cors', 'auth', 'rateLimit'],
        auth: { required: true }
      },
      
      // 项目管理路由
      {
        path: '/api/projects/*',
        method: 'ALL',
        service: 'api',
        middleware: ['cors', 'auth', 'rateLimit'],
        auth: { required: true }
      },
      
      // 场景管理路由
      {
        path: '/api/scenes/*',
        method: 'ALL',
        service: 'api',
        middleware: ['cors', 'auth', 'rateLimit'],
        auth: { required: true }
      },
      
      // 实例服务路由
      {
        path: '/api/instances/*',
        method: 'ALL',
        service: 'instance',
        middleware: ['cors', 'auth', 'rateLimit'],
        auth: { required: true }
      },
      
      // 媒体服务路由
      {
        path: '/api/media/*',
        method: 'ALL',
        service: 'media',
        middleware: ['cors', 'auth', 'rateLimit'],
        auth: { required: true }
      },
      
      // AI服务路由
      {
        path: '/api/ai/*',
        method: 'ALL',
        service: 'ai',
        middleware: ['cors', 'auth', 'rateLimit'],
        auth: { required: true, roles: ['user', 'admin'] }
      },
      
      // 管理员路由
      {
        path: '/api/admin/*',
        method: 'ALL',
        service: 'api',
        middleware: ['cors', 'auth', 'rateLimit'],
        auth: { required: true, roles: ['admin'] }
      }
    ] as RouteConfig[]
  }

  getPort(): number {
    return this.config.server.port
  }

  getHost(): string {
    return this.config.server.host
  }

  getEnvironment(): string {
    return this.config.server.environment
  }

  getAllowedOrigins(): string[] {
    return this.config.cors.allowedOrigins
  }

  getRedisConfig() {
    return this.config.redis
  }

  getJwtConfig() {
    return this.config.jwt
  }

  getMonitoringConfig() {
    return this.config.monitoring
  }

  getServices(): Record<string, ServiceConfig> {
    return this.config.services
  }

  getService(name: string): ServiceConfig | undefined {
    return this.config.services[name]
  }

  getRoutes(): RouteConfig[] {
    return this.config.routes
  }

  getRoutesByService(serviceName: string): RouteConfig[] {
    return this.config.routes.filter(route => route.service === serviceName)
  }

  isDevelopment(): boolean {
    return this.config.server.environment === 'development'
  }

  isProduction(): boolean {
    return this.config.server.environment === 'production'
  }
}
