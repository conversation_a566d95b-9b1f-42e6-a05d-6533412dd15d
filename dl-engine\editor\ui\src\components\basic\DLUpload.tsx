/**
 * DL-Engine 文件上传组件
 * 
 * 基于 Ant Design Upload 的增强版本
 */

import React, { useCallback, useState } from 'react'
import { Upload, UploadProps, Button, message } from 'antd'
import { useTranslation } from 'react-i18next'
import { UploadOutlined, InboxOutlined, PlusOutlined, LoadingOutlined } from '@ant-design/icons'
import classNames from 'classnames'
import DLButton from './DLButton'
import DLIcon from './DLIcon'
import DLProgress from './DLProgress'

/**
 * DL文件上传属性
 */
export interface DLUploadProps extends Omit<UploadProps, 'onChange'> {
  /** 上传类型 */
  uploadType?: 'button' | 'drag' | 'avatar' | 'picture-card'
  /** 标签文本 */
  label?: string
  /** 上传按钮文本 */
  buttonText?: string
  /** 拖拽提示文本 */
  dragText?: string
  /** 拖拽子提示文本 */
  dragHint?: string
  /** 最大文件大小（MB） */
  maxSize?: number
  /** 允许的文件类型 */
  allowedTypes?: string[]
  /** 是否显示上传进度 */
  showProgress?: boolean
  /** 是否显示文件列表 */
  showFileList?: boolean
  /** 文件变化回调 */
  onChange?: (fileList: any[]) => void
  /** 上传前验证回调 */
  beforeUpload?: (file: File) => boolean | Promise<boolean>
  /** 自定义类名 */
  className?: string
}

/**
 * DL文件上传组件
 */
const DLUpload: React.FC<DLUploadProps> = ({
  uploadType = 'button',
  label,
  buttonText,
  dragText,
  dragHint,
  maxSize = 10,
  allowedTypes = [],
  showProgress = true,
  showFileList = true,
  onChange,
  beforeUpload,
  className,
  children,
  ...props
}) => {
  const { t } = useTranslation()
  const [uploading, setUploading] = useState(false)
  const [progress, setProgress] = useState(0)

  /**
   * 处理文件变化
   */
  const handleChange = useCallback((info: any) => {
    const { fileList, file } = info

    // 更新上传状态
    if (file.status === 'uploading') {
      setUploading(true)
      setProgress(file.percent || 0)
    } else if (file.status === 'done') {
      setUploading(false)
      setProgress(100)
      message.success(`${file.name} 上传成功`)
    } else if (file.status === 'error') {
      setUploading(false)
      setProgress(0)
      message.error(`${file.name} 上传失败`)
    }

    onChange?.(fileList)
  }, [onChange])

  /**
   * 上传前验证
   */
  const handleBeforeUpload = useCallback((file: File) => {
    // 检查文件大小
    if (maxSize && file.size / 1024 / 1024 > maxSize) {
      message.error(`文件大小不能超过 ${maxSize}MB`)
      return false
    }

    // 检查文件类型
    if (allowedTypes.length > 0) {
      const fileType = file.type
      const fileName = file.name
      const fileExtension = fileName.substring(fileName.lastIndexOf('.') + 1).toLowerCase()
      
      const isValidType = allowedTypes.some(type => {
        if (type.startsWith('.')) {
          return type.substring(1).toLowerCase() === fileExtension
        }
        return fileType.includes(type)
      })

      if (!isValidType) {
        message.error(`只支持 ${allowedTypes.join(', ')} 格式的文件`)
        return false
      }
    }

    // 自定义验证
    if (beforeUpload) {
      return beforeUpload(file)
    }

    return true
  }, [maxSize, allowedTypes, beforeUpload])

  /**
   * 渲染标签
   */
  const renderLabel = () => {
    if (!label) return null

    return (
      <label className="dl-upload-label">
        {label}
      </label>
    )
  }

  /**
   * 渲染上传按钮
   */
  const renderUploadButton = () => {
    if (uploadType === 'button') {
      return (
        <DLButton
          icon={uploading ? <LoadingOutlined /> : <UploadOutlined />}
          loading={uploading}
          disabled={uploading}
        >
          {buttonText || t('common.upload')}
        </DLButton>
      )
    }

    if (uploadType === 'picture-card') {
      return (
        <div className="dl-upload-picture-card-button">
          {uploading ? <LoadingOutlined /> : <PlusOutlined />}
          <div style={{ marginTop: 8 }}>
            {buttonText || t('common.upload')}
          </div>
        </div>
      )
    }

    if (uploadType === 'avatar') {
      return (
        <div className="dl-upload-avatar-button">
          {uploading ? <LoadingOutlined /> : <UploadOutlined />}
          <div>{buttonText || t('common.upload')}</div>
        </div>
      )
    }

    return children
  }

  /**
   * 渲染拖拽区域
   */
  const renderDragArea = () => {
    return (
      <div className="dl-upload-drag-content">
        <p className="dl-upload-drag-icon">
          <InboxOutlined />
        </p>
        <p className="dl-upload-drag-text">
          {dragText || t('common.dragToUpload')}
        </p>
        <p className="dl-upload-drag-hint">
          {dragHint || t('common.dragHint')}
        </p>
      </div>
    )
  }

  /**
   * 渲染进度条
   */
  const renderProgress = () => {
    if (!showProgress || !uploading) return null

    return (
      <div className="dl-upload-progress">
        <DLProgress
          percent={progress}
          status="active"
          showPercent={true}
          size="small"
        />
      </div>
    )
  }

  /**
   * 上传组件类名
   */
  const uploadClassName = classNames(
    'dl-upload',
    `dl-upload--${uploadType}`,
    {
      'dl-upload--uploading': uploading,
      'dl-upload--with-label': !!label
    },
    className
  )

  /**
   * 上传组件属性
   */
  const uploadProps = {
    ...props,
    onChange: handleChange,
    beforeUpload: handleBeforeUpload,
    showUploadList: showFileList,
    className: 'dl-upload-component'
  }

  /**
   * 渲染上传组件
   */
  const renderUpload = () => {
    if (uploadType === 'drag') {
      return (
        <Upload.Dragger {...uploadProps}>
          {renderDragArea()}
        </Upload.Dragger>
      )
    }

    return (
      <Upload {...uploadProps}>
        {renderUploadButton()}
      </Upload>
    )
  }

  return (
    <div className={uploadClassName}>
      {renderLabel()}
      {renderUpload()}
      {renderProgress()}
    </div>
  )
}

// 添加样式
const uploadStyles = `
.dl-upload {
  .dl-upload-label {
    display: block;
    margin-bottom: 8px;
    color: #ffffff;
    font-size: 14px;
    font-weight: 500;
  }

  .dl-upload-component {
    .ant-upload {
      background-color: #2d2d30;
      border-color: #3c3c3c;
    }

    .ant-upload:hover {
      border-color: #1890ff;
    }

    .ant-upload-btn {
      color: #ffffff;
    }
  }

  .dl-upload-picture-card-button {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #888888;
    font-size: 16px;
  }

  .dl-upload-avatar-button {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100px;
    height: 100px;
    border: 1px dashed #3c3c3c;
    border-radius: 6px;
    background-color: #2d2d30;
    color: #888888;
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .dl-upload-avatar-button:hover {
    border-color: #1890ff;
    color: #1890ff;
  }

  .dl-upload-drag-content {
    padding: 40px 20px;
    text-align: center;
  }

  .dl-upload-drag-icon {
    font-size: 48px;
    color: #1890ff;
    margin-bottom: 16px;
  }

  .dl-upload-drag-text {
    font-size: 16px;
    color: #ffffff;
    margin-bottom: 8px;
  }

  .dl-upload-drag-hint {
    font-size: 14px;
    color: #888888;
  }

  .dl-upload-progress {
    margin-top: 12px;
  }

  &.dl-upload--uploading {
    .ant-upload {
      pointer-events: none;
      opacity: 0.6;
    }
  }

  &.dl-upload--with-label {
    .dl-upload-component {
      margin-top: 4px;
    }
  }
}

.ant-upload-list {
  .ant-upload-list-item {
    background-color: #2d2d30;
    border-color: #3c3c3c;
  }

  .ant-upload-list-item-name {
    color: #ffffff;
  }

  .ant-upload-list-item-info {
    .ant-upload-list-item-progress {
      .ant-progress-bg {
        background-color: #1890ff;
      }
    }
  }
}
`

// 注入样式
if (typeof document !== 'undefined') {
  const styleId = 'dl-upload-styles'
  if (!document.getElementById(styleId)) {
    const style = document.createElement('style')
    style.id = styleId
    style.textContent = uploadStyles
    document.head.appendChild(style)
  }
}

// 导出组件
DLUpload.Dragger = Upload.Dragger

export default DLUpload
