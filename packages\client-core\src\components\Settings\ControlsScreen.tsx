/*
CPAL-1.0 License

The contents of this file are subject to the Common Public Attribution License
Version 1.0. (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at
https://github.com/ir-engine/ir-engine/blob/dev/LICENSE.
The License is based on the Mozilla Public License Version 1.1, but Sections 14
and 15 have been added to cover use of software over a computer network and
provide for limited attribution for the Original Developer. In addition,
Exhibit A has been modified to be consistent with Exhibit B.

Software distributed under the License is distributed on an "AS IS" basis,
WITHOUT WARRANTY OF ANY KIND, either express or implied. See the License for the
specific language governing rights and limitations under the License.

The Original Code is Infinite Reality Engine.

The Original Developer is the Initial Developer. The Initial Developer of the
Original Code is the Infinite Reality Engine team.

All portions of the code written by the Infinite Reality Engine team are Copyright © 2021-2025
Infinite Reality Engine. All Rights Reserved.
*/

import { useMutableState } from '@ir-engine/hyperflux'
import { isMobile } from '@ir-engine/spatial/src/common/functions/isMobile'
import { XRState } from '@ir-engine/spatial/src/xr/XRState'
import React from 'react'
import ControllerMappingImage from '../../user/menus/images/controller-mapping.png'
import KeyboardMappingImage from '../../user/menus/images/keyboard-mapping.png'
import MouseMappingImage from '../../user/menus/images/mouse-mapping.png'
import { Inner } from '../Glass/ToolbarAndSidebar'

export default function ControlsScreen() {
  const xrSupportedModes = useMutableState(XRState).supportedSessionModes
  const xrSupported = xrSupportedModes['immersive-ar'].value || xrSupportedModes['immersive-vr'].value

  return (
    <Inner className="flex flex-col">
      {!isMobile && !xrSupported && (
        <>
          <img
            src={KeyboardMappingImage}
            alt="Desktop Controls"
            className="mx-auto"
            data-testid="keyboard-controls-image"
          />
          <div className="mx-auto grid grid-cols-2">
            <img
              src={ControllerMappingImage}
              alt="Controller Controls"
              className="col-span-1"
              data-testid="controller-controls-image"
            />
            <img
              src={MouseMappingImage}
              alt="Controller Controls"
              className="col-span-1"
              data-testid="mouse-controls-image"
            />
          </div>
        </>
      )}
      {isMobile && <img src={ControllerMappingImage} alt="Mobile Controls" />}
    </Inner>
  )
}
