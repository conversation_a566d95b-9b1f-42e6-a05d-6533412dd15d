/*
CPAL-1.0 License

The contents of this file are subject to the Common Public Attribution License
Version 1.0. (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at
https://github.com/ir-engine/ir-engine/blob/dev/LICENSE.
The License is based on the Mozilla Public License Version 1.1, but Sections 14
and 15 have been added to cover use of software over a computer network and
provide for limited attribution for the Original Developer. In addition,
Exhibit A has been modified to be consistent with Exhibit B.

Software distributed under the License is distributed on an "AS IS" basis,
WITHOUT WARRANTY OF ANY KIND, either express or implied. See the License for the
specific language governing rights and limitations under the License.

The Original Code is Infinite Reality Engine.

The Original Developer is the Initial Developer. The Initial Developer of the
Original Code is the Infinite Reality Engine team.

All portions of the code written by the Infinite Reality Engine team are Copyright © 2021-2025
Infinite Reality Engine. All Rights Reserved.
*/

import { useEffect } from 'react'
import { Mesh, MeshStandardMaterial } from 'three'

import { useEntityContext } from '@ir-engine/ecs'
import {
  defineComponent,
  removeComponent,
  setComponent,
  useComponent,
  useOptionalComponent
} from '@ir-engine/ecs/src/ComponentFunctions'
import { S } from '@ir-engine/ecs/src/schemas/JSONSchemas'
import { Geometry } from '@ir-engine/spatial/src/common/constants/Geometry'
import { MeshComponent } from '@ir-engine/spatial/src/renderer/components/MeshComponent'
import { DoubleSide } from 'three'
import { GeometryType, GeometryTypeToFactory } from '../constants/GeometryTypeEnum'

const createGeometry = (geometryType: GeometryType, geometryParams: Record<string, any>): Geometry => {
  const factory = GeometryTypeToFactory[geometryType]
  const geometry = factory(geometryParams)
  return geometry
}

export const PrimitiveGeometryComponent = defineComponent({
  name: 'PrimitiveGeometryComponent',
  jsonID: 'EE_primitive_geometry',

  schema: S.Object({
    geometryType: S.Enum(GeometryType, {
      $comment:
        "A string enum, ie. one of the following values: 'BoxGeometry', 'SphereGeometry', 'CylinderGeometry', 'CapsuleGeometry', 'PlaneGeometry', 'CircleGeometry', 'RingGeometry', 'TorusGeometry', 'DodecahedronGeometry', 'IcosahedronGeometry', 'OctahedronGeometry', 'TetrahedronGeometry', 'TorusKnotGeometry'",
      default: GeometryType.BoxGeometry
    }),
    geometryParams: S.Record(S.String(), S.Any())
  }),

  reactor: () => {
    const entity = useEntityContext()
    const geometryComponent = useComponent(entity, PrimitiveGeometryComponent)
    useEffect(() => {
      setComponent(
        entity,
        MeshComponent,
        new Mesh(
          createGeometry(geometryComponent.geometryType.value, geometryComponent.geometryParams.value),
          new MeshStandardMaterial({ side: DoubleSide })
        )
      )
      return () => {
        removeComponent(entity, MeshComponent)
      }
    }, [])

    const mesh = useOptionalComponent(entity, MeshComponent)

    useEffect(() => {
      if (!mesh) return
      mesh.geometry.set(createGeometry(geometryComponent.geometryType.value, geometryComponent.geometryParams.value))
    }, [!!mesh, geometryComponent.geometryType, geometryComponent.geometryParams])

    return null
  }
})
