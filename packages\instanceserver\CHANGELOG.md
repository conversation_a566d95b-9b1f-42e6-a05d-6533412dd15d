# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.



**Note:** Version bump only for package @etherealengine/instanceserver







**Note:** Version bump only for package @etherealengine/instanceserver







**Note:** Version bump only for package @etherealengine/instanceserver







**Note:** Version bump only for package @etherealengine/instanceserver







**Note:** Version bump only for package @etherealengine/instanceserver







**Note:** Version bump only for package @etherealengine/instanceserver







**Note:** Version bump only for package @etherealengine/instanceserver







**Note:** Version bump only for package @xrengine/instanceserver







**Note:** Version bump only for package @xrengine/instanceserver







**Note:** Version bump only for package @xrengine/instanceserver







**Note:** Version bump only for package @xrengine/instanceserver







**Note:** Version bump only for package @xrengine/instanceserver







**Note:** Version bump only for package @xrengine/instanceserver







**Note:** Version bump only for package @xrengine/instanceserver







**Note:** Version bump only for package @xrengine/instanceserver







**Note:** Version bump only for package @xrengine/instanceserver







**Note:** Version bump only for package @xrengine/instanceserver







**Note:** Version bump only for package @xrengine/instanceserver







**Note:** Version bump only for package @xrengine/instanceserver







**Note:** Version bump only for package @xrengine/instanceserver







**Note:** Version bump only for package @xrengine/instanceserver







**Note:** Version bump only for package @xrengine/instanceserver







**Note:** Version bump only for package @xrengine/gameserver







**Note:** Version bump only for package @xrengine/gameserver







**Note:** Version bump only for package @xrengine/gameserver







**Note:** Version bump only for package @xrengine/gameserver





## [0.5.2](https://github.com/xrfoundation/XREngine/compare/v0.5.1...v0.5.2) (2022-04-07)

**Note:** Version bump only for package @xrengine/gameserver







**Note:** Version bump only for package @xrengine/gameserver







**Note:** Version bump only for package @xrengine/gameserver







**Note:** Version bump only for package @xrengine/gameserver







**Note:** Version bump only for package @xrengine/gameserver







**Note:** Version bump only for package @xrengine/gameserver







**Note:** Version bump only for package @xrengine/gameserver







**Note:** Version bump only for package @xrengine/gameserver







**Note:** Version bump only for package @xrengine/gameserver







**Note:** Version bump only for package @xrengine/gameserver







**Note:** Version bump only for package @xrengine/gameserver







**Note:** Version bump only for package @xrengine/gameserver







**Note:** Version bump only for package @xrengine/gameserver







**Note:** Version bump only for package @xrengine/gameserver







**Note:** Version bump only for package @xrengine/gameserver







**Note:** Version bump only for package @xrengine/gameserver







**Note:** Version bump only for package @xrengine/gameserver







**Note:** Version bump only for package @xrengine/gameserver







**Note:** Version bump only for package @xrengine/gameserver







**Note:** Version bump only for package @xrengine/gameserver







**Note:** Version bump only for package @xrengine/gameserver







**Note:** Version bump only for package @xrengine/gameserver







**Note:** Version bump only for package @xrengine/gameserver







**Note:** Version bump only for package @xrengine/gameserver







**Note:** Version bump only for package @xrengine/gameserver







**Note:** Version bump only for package @xrengine/gameserver







**Note:** Version bump only for package @xrengine/gameserver







**Note:** Version bump only for package @xrengine/gameserver







**Note:** Version bump only for package @xrengine/gameserver







**Note:** Version bump only for package @xrengine/gameserver







**Note:** Version bump only for package @xrengine/gameserver





## 0.2.36 (2021-06-25)

**Note:** Version bump only for package @xrengine/gameserver





## 0.2.35 (2021-06-25)

**Note:** Version bump only for package @xrengine/gameserver





## 0.2.34 (2021-06-25)

**Note:** Version bump only for package @xrengine/gameserver





## 0.2.33 (2021-06-25)

**Note:** Version bump only for package @xrengine/gameserver





## 0.2.32 (2021-06-25)

**Note:** Version bump only for package @xrengine/gameserver





## 0.2.31 (2021-06-25)

**Note:** Version bump only for package @xrengine/gameserver





## 0.2.30 (2021-06-25)

**Note:** Version bump only for package @xrengine/gameserver





## [0.2.29](https://github.com/barankyle/xr3ngine/compare/v0.2.28...v0.2.29) (2021-06-24)

**Note:** Version bump only for package @xrengine/gameserver





## [0.2.28](https://github.com/barankyle/xr3ngine/compare/v0.2.27...v0.2.28) (2021-06-22)

**Note:** Version bump only for package @xrengine/gameserver





## [0.2.27](https://github.com/barankyle/xrengine/compare/v0.2.26...v0.2.27) (2021-05-13)

**Note:** Version bump only for package @xrengine/gameserver





## [0.2.26](https://github.com/barankyle/xrengine/compare/v0.2.24...v0.2.26) (2021-05-12)

**Note:** Version bump only for package @xrengine/gameserver





## [0.2.25](https://github.com/barankyle/xrengine/compare/v0.2.24...v0.2.25) (2021-05-12)

**Note:** Version bump only for package @xrengine/gameserver





## [0.2.24](https://github.com/barankyle/xrengine/compare/v0.2.23...v0.2.24) (2021-05-12)

**Note:** Version bump only for package @xrengine/gameserver





## [0.2.23](https://github.com/barankyle/xrengine/compare/v0.2.22...v0.2.23) (2021-05-12)

**Note:** Version bump only for package @xrengine/gameserver





## [0.2.22](https://github.com/xrengine/xrengine/compare/v0.2.21...v0.2.22) (2021-05-05)

**Note:** Version bump only for package @xrengine/gameserver





## [0.2.21](https://github.com/barankyle/xrengine/compare/v0.2.20...v0.2.21) (2021-05-05)

**Note:** Version bump only for package @xrengine/gameserver





## [0.2.20](https://github.com/barankyle/xrengine/compare/v0.2.18...v0.2.20) (2021-05-04)


### Bug Fixes

* **deps:** update dependency @feathersjs/hooks to v0.6.4 ([df63c37](https://github.com/barankyle/xrengine/commit/df63c37dcf4eb61a8e9ed4bdcfa2053d60164d8b))
* **deps:** update dependency aws-sdk to v2.885.0 ([b99dbc0](https://github.com/barankyle/xrengine/commit/b99dbc0a7ba9aa44ae49c88bd89dc1161a25a7e1))
* **deps:** update dependency aws-sdk to v2.886.0 ([b37bc42](https://github.com/barankyle/xrengine/commit/b37bc42fd77d765bc1a947ff097cef2360e3bbac))
* **deps:** update dependency aws-sdk to v2.887.0 ([1fc3094](https://github.com/barankyle/xrengine/commit/1fc3094cd008466281f8410ebf0b69e3c23ba4c9))
* **deps:** update dependency aws-sdk to v2.888.0 ([7e040cb](https://github.com/barankyle/xrengine/commit/7e040cbe484c74602def81e26f28cb55264ed177))
* **deps:** update dependency aws-sdk to v2.892.0 ([06db15c](https://github.com/barankyle/xrengine/commit/06db15cc17ade5417feafeda07fc3ee77fbe3d3f))
* **deps:** update dependency feathers-blob to v2.4.0 ([40afa5e](https://github.com/barankyle/xrengine/commit/40afa5e33767fc9d1c1d8baad7820028478a71cc))
* **deps:** update dependency js-yaml to v4.1.0 ([cf84acd](https://github.com/barankyle/xrengine/commit/cf84acd3dbfc606c5be67d26abbb445270eb8536))





## 0.2.18 (2021-04-22)


### Bug Fixes

* **deps:** update dependency @feathersjs/hooks to v0.6.4 ([e4afbb4](https://github.com/XRFoundation/XREngine/commit/e4afbb4e1f3f085855393eea997453c6002aaedb))
* **deps:** update dependency aws-sdk to v2.885.0 ([db05194](https://github.com/XRFoundation/XREngine/commit/db05194e8e61a0d54af54cdbaa6e50fd3f4f8b72))
* **deps:** update dependency aws-sdk to v2.886.0 ([1ba75d0](https://github.com/XRFoundation/XREngine/commit/1ba75d008a82b37eaf57b60b7ce442dde92be8c5))
* **deps:** update dependency aws-sdk to v2.887.0 ([367d16a](https://github.com/XRFoundation/XREngine/commit/367d16a9a7c5eee2deee16ae7cf4df8a1697490d))
* **deps:** update dependency aws-sdk to v2.888.0 ([c20639e](https://github.com/XRFoundation/XREngine/commit/c20639e23a8946e0484be289ddd258ebc7da88f4))
* **deps:** update dependency feathers-blob to v2.4.0 ([ae7314c](https://github.com/XRFoundation/XREngine/commit/ae7314c2066bdf20181060c49f75680d97fb8300))
* **deps:** update dependency js-yaml to v4.1.0 ([90e9372](https://github.com/XRFoundation/XREngine/commit/90e937244efdb3470de266f34c40d5cfb504acb5))





## [0.2.11](https://github.com/XRFoundation/XREngine/compare/v0.2.10...v0.2.11) (2021-04-08)

**Note:** Version bump only for package @xrengine/gameserver
