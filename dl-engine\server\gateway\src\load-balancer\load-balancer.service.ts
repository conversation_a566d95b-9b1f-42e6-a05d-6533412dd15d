/**
 * DL-Engine API Gateway 负载均衡服务
 * 
 * 支持多种负载均衡算法：
 * - 轮询 (Round Robin)
 * - 加权轮询 (Weighted Round Robin)
 * - 最少连接 (Least Connections)
 * - 随机 (Random)
 * - 一致性哈希 (Consistent Hash)
 */

import { Injectable } from '@nestjs/common'
import { ConfigService } from '../config/config.service'
import { LoggerService } from '../common/logger.service'

export interface ServiceInstance {
  id: string
  name: string
  url: string
  weight: number
  health: 'healthy' | 'unhealthy' | 'unknown'
  connections: number
  lastCheck: Date
  metadata?: Record<string, any>
}

export type LoadBalancingStrategy = 'round-robin' | 'weighted-round-robin' | 'least-connections' | 'random' | 'consistent-hash'

@Injectable()
export class LoadBalancerService {
  private services: Map<string, ServiceInstance[]> = new Map()
  private roundRobinCounters: Map<string, number> = new Map()
  private strategy: LoadBalancingStrategy = 'weighted-round-robin'

  constructor(
    private readonly configService: ConfigService,
    private readonly logger: LoggerService
  ) {
    this.initializeServices()
  }

  /**
   * 初始化服务实例
   */
  private initializeServices() {
    const services = this.configService.getServices()
    
    Object.entries(services).forEach(([name, config]) => {
      if (config.enabled) {
        const instance: ServiceInstance = {
          id: `${name}-1`,
          name,
          url: config.url,
          weight: config.weight || 1,
          health: 'unknown',
          connections: 0,
          lastCheck: new Date(),
          metadata: {
            timeout: config.timeout,
            retries: config.retries
          }
        }
        this.services.set(name, [instance])
        this.roundRobinCounters.set(name, 0)
      }
    })

    this.logger.log(`Initialized load balancer with ${this.services.size} services`)
  }

  /**
   * 选择服务实例
   */
  async selectInstance(serviceName: string): Promise<ServiceInstance | null> {
    const instances = this.services.get(serviceName)
    
    if (!instances || instances.length === 0) {
      this.logger.warn(`No instances found for service: ${serviceName}`)
      return null
    }

    // 过滤健康的实例
    const healthyInstances = instances.filter(instance => instance.health === 'healthy')
    
    if (healthyInstances.length === 0) {
      this.logger.warn(`No healthy instances found for service: ${serviceName}`)
      return null
    }

    // 根据策略选择实例
    let selectedInstance: ServiceInstance | null = null

    switch (this.strategy) {
      case 'round-robin':
        selectedInstance = this.selectRoundRobin(serviceName, healthyInstances)
        break
      case 'weighted-round-robin':
        selectedInstance = this.selectWeightedRoundRobin(serviceName, healthyInstances)
        break
      case 'least-connections':
        selectedInstance = this.selectLeastConnections(healthyInstances)
        break
      case 'random':
        selectedInstance = this.selectRandom(healthyInstances)
        break
      case 'consistent-hash':
        selectedInstance = this.selectConsistentHash(serviceName, healthyInstances)
        break
      default:
        selectedInstance = this.selectRoundRobin(serviceName, healthyInstances)
    }

    if (selectedInstance) {
      selectedInstance.connections++
      this.logger.debug(`Selected instance ${selectedInstance.id} for service ${serviceName}`)
    }

    return selectedInstance
  }

  /**
   * 轮询算法
   */
  private selectRoundRobin(serviceName: string, instances: ServiceInstance[]): ServiceInstance {
    const counter = this.roundRobinCounters.get(serviceName) || 0
    const selectedIndex = counter % instances.length
    this.roundRobinCounters.set(serviceName, counter + 1)
    return instances[selectedIndex]
  }

  /**
   * 加权轮询算法
   */
  private selectWeightedRoundRobin(serviceName: string, instances: ServiceInstance[]): ServiceInstance {
    // 计算总权重
    const totalWeight = instances.reduce((sum, instance) => sum + instance.weight, 0)
    
    if (totalWeight === 0) {
      return this.selectRoundRobin(serviceName, instances)
    }

    // 生成加权实例列表
    const weightedInstances: ServiceInstance[] = []
    instances.forEach(instance => {
      for (let i = 0; i < instance.weight; i++) {
        weightedInstances.push(instance)
      }
    })

    return this.selectRoundRobin(serviceName, weightedInstances)
  }

  /**
   * 最少连接算法
   */
  private selectLeastConnections(instances: ServiceInstance[]): ServiceInstance {
    return instances.reduce((min, current) => 
      current.connections < min.connections ? current : min
    )
  }

  /**
   * 随机算法
   */
  private selectRandom(instances: ServiceInstance[]): ServiceInstance {
    const randomIndex = Math.floor(Math.random() * instances.length)
    return instances[randomIndex]
  }

  /**
   * 一致性哈希算法
   */
  private selectConsistentHash(serviceName: string, instances: ServiceInstance[]): ServiceInstance {
    // 简单的哈希实现
    const hash = this.simpleHash(serviceName)
    const index = hash % instances.length
    return instances[index]
  }

  /**
   * 简单哈希函数
   */
  private simpleHash(str: string): number {
    let hash = 0
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // 转换为32位整数
    }
    return Math.abs(hash)
  }

  /**
   * 释放连接
   */
  releaseConnection(serviceName: string, instanceId: string) {
    const instances = this.services.get(serviceName)
    if (instances) {
      const instance = instances.find(i => i.id === instanceId)
      if (instance && instance.connections > 0) {
        instance.connections--
      }
    }
  }

  /**
   * 标记实例为不健康
   */
  markInstanceUnhealthy(serviceName: string, instanceId: string) {
    const instances = this.services.get(serviceName)
    if (instances) {
      const instance = instances.find(i => i.id === instanceId)
      if (instance) {
        instance.health = 'unhealthy'
        instance.lastCheck = new Date()
        this.logger.warn(`Marked instance ${instanceId} of service ${serviceName} as unhealthy`)
      }
    }
  }

  /**
   * 标记实例为健康
   */
  markInstanceHealthy(serviceName: string, instanceId: string) {
    const instances = this.services.get(serviceName)
    if (instances) {
      const instance = instances.find(i => i.id === instanceId)
      if (instance) {
        instance.health = 'healthy'
        instance.lastCheck = new Date()
        this.logger.log(`Marked instance ${instanceId} of service ${serviceName} as healthy`)
      }
    }
  }

  /**
   * 添加服务实例
   */
  addInstance(serviceName: string, instance: Omit<ServiceInstance, 'connections' | 'lastCheck'>) {
    const instances = this.services.get(serviceName) || []
    const newInstance: ServiceInstance = {
      ...instance,
      connections: 0,
      lastCheck: new Date()
    }
    
    instances.push(newInstance)
    this.services.set(serviceName, instances)
    
    this.logger.log(`Added instance ${instance.id} to service ${serviceName}`)
  }

  /**
   * 移除服务实例
   */
  removeInstance(serviceName: string, instanceId: string) {
    const instances = this.services.get(serviceName)
    if (instances) {
      const filteredInstances = instances.filter(i => i.id !== instanceId)
      this.services.set(serviceName, filteredInstances)
      this.logger.log(`Removed instance ${instanceId} from service ${serviceName}`)
    }
  }

  /**
   * 获取服务实例列表
   */
  getInstances(serviceName: string): ServiceInstance[] {
    return this.services.get(serviceName) || []
  }

  /**
   * 获取所有服务状态
   */
  getAllServicesStatus() {
    const status: Record<string, any> = {}
    
    this.services.forEach((instances, serviceName) => {
      const healthyCount = instances.filter(i => i.health === 'healthy').length
      const totalConnections = instances.reduce((sum, i) => sum + i.connections, 0)
      
      status[serviceName] = {
        totalInstances: instances.length,
        healthyInstances: healthyCount,
        totalConnections,
        instances: instances.map(i => ({
          id: i.id,
          url: i.url,
          health: i.health,
          connections: i.connections,
          weight: i.weight,
          lastCheck: i.lastCheck
        }))
      }
    })
    
    return status
  }

  /**
   * 设置负载均衡策略
   */
  setStrategy(strategy: LoadBalancingStrategy) {
    this.strategy = strategy
    this.logger.log(`Load balancing strategy changed to: ${strategy}`)
  }

  /**
   * 获取当前策略
   */
  getStrategy(): LoadBalancingStrategy {
    return this.strategy
  }

  /**
   * 重置连接计数
   */
  resetConnections() {
    this.services.forEach(instances => {
      instances.forEach(instance => {
        instance.connections = 0
      })
    })
    this.logger.log('Reset all connection counts')
  }
}
