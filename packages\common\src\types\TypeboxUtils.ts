/*
CPAL-1.0 License

The contents of this file are subject to the Common Public Attribution License
Version 1.0. (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at
https://github.com/ir-engine/ir-engine/blob/dev/LICENSE.
The License is based on the Mozilla Public License Version 1.1, but Sections 14
and 15 have been added to cover use of software over a computer network and 
provide for limited attribution for the Original Developer. In addition, 
Exhibit A has been modified to be consistent with Exhibit B.

Software distributed under the License is distributed on an "AS IS" basis,
WITHOUT WARRANTY OF ANY KIND, either express or implied. See the License for the
specific language governing rights and limitations under the License.

The Original Code is Infinite Reality Engine.

The Original Developer is the Initial Developer. The Initial Developer of the
Original Code is the Infinite Reality Engine team.

All portions of the code written by the Infinite Reality Engine team are Copyright © 2021-2025
Infinite Reality Engine. All Rights Reserved.
*/

import { Kind, ObjectOptions, Static, StringFormatOption, StringOptions, TSchema, Type } from '@feathersjs/typebox'

export interface TTypedString<T extends string, Format extends string = string> extends TSchema, StringOptions<Format> {
  [Kind]: 'String'
  static: T
  type: T
}

export const TypedString = <T extends string>(options?: StringOptions<StringFormatOption | T>) => {
  return Type.String(options) as TTypedString<T, T>
}

export interface TTypedRecord<
  S extends string,
  K extends TTypedString<S> = TTypedString<S>,
  T extends TSchema = TSchema
> extends TSchema {
  [Kind]: 'Record'
  static: Record<Static<K>, Static<T, this['params']>>
  type: 'object'
  patternProperties: {
    [pattern: string]: T
  }
  additionalProperties: false
}

export const TypedRecord = <K extends TTypedString<S>, T extends TSchema, S extends string>(
  key: K,
  schema: T,
  options?: ObjectOptions
) => {
  return Type.Record(key as any, schema, options) as any as TTypedRecord<S, K, T>
}
