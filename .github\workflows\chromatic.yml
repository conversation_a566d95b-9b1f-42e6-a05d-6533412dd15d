name: Chromatic

on:
  pull_request:
    paths:
      - '**/*.tsx'
  workflow_dispatch:
    inputs:
      reason:
        description: 'Reason for running Chromatic'
        required: false
        default: 'Manual trigger'

jobs:
  check-and-publish:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout Code
        uses: actions/checkout@v3
        with:
          fetch-depth: 0

      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: 22

      - name: Install dependencies
        run: npm i

      - name: Run stories check script
        id: check_stories
        run: |
          npx tsx packages/ui/scripts/check-modified-files.ts
        continue-on-error: true

      - name: Publish to Chromatic
        if: steps.check_stories.outcome == 'success'
        uses: chromaui/action@v1
        with:
          projectToken: ${{ secrets.CHROMATIC_TOKEN }}
          autoAcceptChanges: 'dev'
