services:
  db:
    image: mariadb:10.7
    container_name: ir-engine_db
    environment:
      MYSQL_ROOT_PASSWORD: ir-engine-root
      MYSQL_DATABASE: ir-engine
      MYSQL_USER: server
      MYSQL_PASSWORD: password
    ports:
      - '3306:3306'
  redis:
    image: redis
    container_name: ir-engine_redis
    command: redis-server
    ports:
      - '6379:6379'
  postgres:
    image: pgvector/pgvector:pg16
    profiles: ["vectordb"] # Only starts in vectordb profile
    container_name: ir-engine_vector_db
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DATABASE: vector-db
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data

  ollama:
    image: ollama/ollama:latest
    profiles: ["vectordb"] # Only starts in vectordb profile
    entrypoint:
      [
        "/bin/bash",
        "-c",
        "ollama serve & sleep 5 && ollama pull mxbai-embed-large && wait",
      ]
    environment:
      - OLLAMA_KEEP_ALIVE="24h"
    volumes:
      - ollama_storage:/root/.ollama
    ports:
      - "11434:11434"
    healthcheck:
      test: ["CMD-SHELL", "ollama list | grep -q mxbai-embed-large"] # Check if model is pulled
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s # Give Ollama time to start and pull models
    restart: unless-stopped
  
volumes:
  postgres_data:
  
  ollama_storage:
