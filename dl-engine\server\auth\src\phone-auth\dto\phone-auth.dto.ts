/**
 * DL-Engine 手机号认证数据传输对象
 */

import { IsString, IsOptional, IsPhoneNumber, IsIn, Length, Matches, IsNotEmpty } from 'class-validator'
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'

export class SendSmsDto {
  @ApiProperty({ description: '手机号码', example: '13800138000' })
  @IsString()
  @IsNotEmpty()
  @Matches(/^1[3-9]\d{9}$/, { message: '请输入有效的中国大陆手机号码' })
  phone: string

  @ApiPropertyOptional({ description: '国家代码', example: '+86', default: '+86' })
  @IsOptional()
  @IsString()
  @IsIn(['+86', '+1', '+44', '+81', '+82', '+65', '+852', '+853', '+886'])
  countryCode?: string

  @ApiPropertyOptional({ 
    description: '验证码用途', 
    example: 'login',
    enum: ['login', 'register', 'bind', 'unbind', 'reset'],
    default: 'login'
  })
  @IsOptional()
  @IsString()
  @IsIn(['login', 'register', 'bind', 'unbind', 'reset'])
  purpose?: string
}

export class VerifyPhoneDto {
  @ApiProperty({ description: '手机号码', example: '13800138000' })
  @IsString()
  @IsNotEmpty()
  @Matches(/^1[3-9]\d{9}$/, { message: '请输入有效的中国大陆手机号码' })
  phone: string

  @ApiPropertyOptional({ description: '国家代码', example: '+86', default: '+86' })
  @IsOptional()
  @IsString()
  @IsIn(['+86', '+1', '+44', '+81', '+82', '+65', '+852', '+853', '+886'])
  countryCode?: string

  @ApiProperty({ description: '验证码', example: '123456' })
  @IsString()
  @IsNotEmpty()
  @Length(6, 6, { message: '验证码必须是6位数字' })
  @Matches(/^\d{6}$/, { message: '验证码必须是6位数字' })
  code: string

  @ApiPropertyOptional({ 
    description: '验证码用途', 
    example: 'login',
    enum: ['login', 'register', 'bind', 'unbind', 'reset'],
    default: 'login'
  })
  @IsOptional()
  @IsString()
  @IsIn(['login', 'register', 'bind', 'unbind', 'reset'])
  purpose?: string
}

export class PhoneRegisterDto {
  @ApiProperty({ description: '手机号码', example: '13800138000' })
  @IsString()
  @IsNotEmpty()
  @Matches(/^1[3-9]\d{9}$/, { message: '请输入有效的中国大陆手机号码' })
  phone: string

  @ApiPropertyOptional({ description: '国家代码', example: '+86', default: '+86' })
  @IsOptional()
  @IsString()
  @IsIn(['+86', '+1', '+44', '+81', '+82', '+65', '+852', '+853', '+886'])
  countryCode?: string

  @ApiProperty({ description: '验证码', example: '123456' })
  @IsString()
  @IsNotEmpty()
  @Length(6, 6, { message: '验证码必须是6位数字' })
  @Matches(/^\d{6}$/, { message: '验证码必须是6位数字' })
  code: string

  @ApiPropertyOptional({ description: '用户昵称', example: '学习者小明' })
  @IsOptional()
  @IsString()
  @Length(1, 50, { message: '昵称长度必须在1-50个字符之间' })
  nickname?: string

  @ApiPropertyOptional({ description: '密码（可选）', example: 'Password123!' })
  @IsOptional()
  @IsString()
  @Length(8, 128, { message: '密码长度必须在8-128个字符之间' })
  @Matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/, {
    message: '密码必须包含至少一个大写字母、一个小写字母和一个数字'
  })
  password?: string
}

export class PhoneLoginDto {
  @ApiProperty({ description: '手机号码', example: '13800138000' })
  @IsString()
  @IsNotEmpty()
  @Matches(/^1[3-9]\d{9}$/, { message: '请输入有效的中国大陆手机号码' })
  phone: string

  @ApiPropertyOptional({ description: '国家代码', example: '+86', default: '+86' })
  @IsOptional()
  @IsString()
  @IsIn(['+86', '+1', '+44', '+81', '+82', '+65', '+852', '+853', '+886'])
  countryCode?: string

  @ApiProperty({ description: '验证码', example: '123456' })
  @IsString()
  @IsNotEmpty()
  @Length(6, 6, { message: '验证码必须是6位数字' })
  @Matches(/^\d{6}$/, { message: '验证码必须是6位数字' })
  code: string
}

export class BindPhoneDto {
  @ApiProperty({ description: '手机号码', example: '13800138000' })
  @IsString()
  @IsNotEmpty()
  @Matches(/^1[3-9]\d{9}$/, { message: '请输入有效的中国大陆手机号码' })
  phone: string

  @ApiPropertyOptional({ description: '国家代码', example: '+86', default: '+86' })
  @IsOptional()
  @IsString()
  @IsIn(['+86', '+1', '+44', '+81', '+82', '+65', '+852', '+853', '+886'])
  countryCode?: string

  @ApiProperty({ description: '验证码', example: '123456' })
  @IsString()
  @IsNotEmpty()
  @Length(6, 6, { message: '验证码必须是6位数字' })
  @Matches(/^\d{6}$/, { message: '验证码必须是6位数字' })
  code: string
}

export class UnbindPhoneDto {
  @ApiProperty({ description: '验证码', example: '123456' })
  @IsString()
  @IsNotEmpty()
  @Length(6, 6, { message: '验证码必须是6位数字' })
  @Matches(/^\d{6}$/, { message: '验证码必须是6位数字' })
  code: string
}
