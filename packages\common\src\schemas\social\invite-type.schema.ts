/*
CPAL-1.0 License

The contents of this file are subject to the Common Public Attribution License
Version 1.0. (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at
https://github.com/ir-engine/ir-engine/blob/dev/LICENSE.
The License is based on the Mozilla Public License Version 1.1, but Sections 14
and 15 have been added to cover use of software over a computer network and 
provide for limited attribution for the Original Developer. In addition, 
Exhibit A has been modified to be consistent with Exhibit B.

Software distributed under the License is distributed on an "AS IS" basis,
WITHOUT WARRANTY OF ANY KIND, either express or implied. See the License for the
specific language governing rights and limitations under the License.

The Original Code is Infinite Reality Engine.

The Original Developer is the Initial Developer. The Initial Developer of the
Original Code is the Infinite Reality Engine team.

All portions of the code written by the Infinite Reality Engine team are Copyright © 2021-2025
Infinite Reality Engine. All Rights Reserved.
*/

// For more information about this file see https://dove.feathersjs.com/guides/cli/service.schemas.html
import type { Static } from '@feathersjs/typebox'
import { getValidator, querySyntax, Type } from '@feathersjs/typebox'

import { dataValidator, queryValidator } from '../validators'

export const inviteTypePath = 'invite-type'

export const inviteTypeMethods = ['find', 'get'] as const

export const inviteTypes = ['friend', 'channel', 'location', 'instance', 'new-user']

// Main data model schema
export const inviteTypeSchema = Type.Object(
  {
    type: Type.String()
  },
  { $id: 'InviteType', additionalProperties: false }
)
export interface InviteTypeType extends Static<typeof inviteTypeSchema> {}

// Schema for creating new entries
export const inviteTypeDataSchema = Type.Pick(inviteTypeSchema, ['type'], {
  $id: 'InviteTypeData'
})
export interface InviteTypeData extends Static<typeof inviteTypeDataSchema> {}

// Schema for updating existing entries
export const inviteTypePatchSchema = Type.Partial(inviteTypeSchema, {
  $id: 'InviteTypePatch'
})
export interface InviteTypePatch extends Static<typeof inviteTypePatchSchema> {}

// Schema for allowed query properties
export const inviteTypeQueryProperties = Type.Pick(inviteTypeSchema, ['type'])
export const inviteTypeQuerySchema = Type.Intersect(
  [
    querySyntax(inviteTypeQueryProperties),
    // Add additional query properties here
    Type.Object({}, { additionalProperties: false })
  ],
  { additionalProperties: false }
)
export interface InviteTypeQuery extends Static<typeof inviteTypeQuerySchema> {}

export const inviteTypeValidator = /* @__PURE__ */ getValidator(inviteTypeSchema, dataValidator)
export const inviteTypeDataValidator = /* @__PURE__ */ getValidator(inviteTypeDataSchema, dataValidator)
export const inviteTypePatchValidator = /* @__PURE__ */ getValidator(inviteTypePatchSchema, dataValidator)
export const inviteTypeQueryValidator = /* @__PURE__ */ getValidator(inviteTypeQuerySchema, queryValidator)
