{"name": "@ir-engine/ecs", "version": "1.0.3", "main": "index.ts", "description": "Fast and reactive ECS for the Infinite Reality Engine", "publishConfig": {"access": "public"}, "scripts": {"check-errors": "tsc --noemit && npx cycle-import-check src || true", "test": "cross-env TEST=true vitest run --config=../../vitest.client.config.ts", "test-coverage": "npm run test-coverage-generate ; npm run test-coverage-launch", "test-coverage-generate": "npm run test -- --coverage --silent", "test-coverage-launch": "vite preview --open --outDir coverage", "build": "ts-node --swc build.ts", "build-publish": "npm run build && cd dist && npm publish --access public --dry-run"}, "repository": {"type": "git", "url": "git://github.com/ir-engine/ir-engine.git"}, "author": "Infinite Reality Engine", "license": "CPAL", "dependencies": {"@ir-engine/hyperflux": "^1.0.3", "bitecs": "github:NateTheGreatt/bitECS#rc-0-4-0", "uuid": "9.0.0"}, "devDependencies": {"@types/node": "18.15.5", "rimraf": "4.4.0"}, "gitHead": "2313453697ca7c6b8d36b3b166b5a6445fe1c851"}