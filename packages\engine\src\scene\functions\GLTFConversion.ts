/*
CPAL-1.0 License

The contents of this file are subject to the Common Public Attribution License
Version 1.0. (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at
https://github.com/ir-engine/ir-engine/blob/dev/LICENSE.
The License is based on the Mozilla Public License Version 1.1, but Sections 14
and 15 have been added to cover use of software over a computer network and 
provide for limited attribution for the Original Developer. In addition, 
Exhibit A has been modified to be consistent with Exhibit B.

Software distributed under the License is distributed on an "AS IS" basis,
WITHOUT WARRANTY OF ANY KIND, either express or implied. See the License for the
specific language governing rights and limitations under the License.

The Original Code is Infinite Reality Engine.

The Original Developer is the Initial Developer. The Initial Developer of the
Original Code is the Infinite Reality Engine team.

All portions of the code written by the Infinite Reality Engine team are Copyright © 2021-2025
Infinite Reality Engine. All Rights Reserved.
*/

import { GLTF } from '@gltf-transform/core'
import { Object3D } from 'three'

import { getState } from '@ir-engine/hyperflux'
import { pathIndentifiers } from '../../assets/functions/parseSceneJSON'
import { DomainConfigState } from '../../assets/state/DomainConfigState'

export const getCacheRegex = (fileServer: string) => {
  return new RegExp(`${fileServer}\/projects`)
}

/**
 * Handles encoding and decoding scene path symbols from gltfs
 * @param gltf
 * @param mode 'encode' or 'decode'
 */
export const handleScenePaths = (gltf: GLTF.IGLTF, mode: 'encode' | 'decode') => {
  const cloudDomain = getState(DomainConfigState).cloudDomain
  const cacheRe = getCacheRegex(cloudDomain)
  const symbolRe = /__\$project\$__/
  const frontier = [...(gltf.scenes ?? []), ...(gltf.nodes ?? [])]
  while (frontier.length > 0) {
    const elt = frontier.pop()
    if (typeof elt === 'object' && elt !== null) {
      for (const [k, v] of Object.entries(elt)) {
        if (!!v && typeof v === 'object' && !(v as Object3D).isObject3D) {
          frontier.push(v)
        }
        if (mode === 'encode') {
          if (typeof v === 'string' && cacheRe.test(v)) {
            elt[k] = v.replace(cacheRe, pathIndentifiers.sceneRelative)
          }
        }
        if (mode === 'decode') {
          if (typeof v === 'string' && symbolRe.test(v)) {
            elt[k] = v.replace(symbolRe, `${cloudDomain}/projects`)
          }
        }
      }
    }
  }
  return gltf
}
