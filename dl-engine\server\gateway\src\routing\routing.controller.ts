/**
 * DL-Engine API Gateway 路由控制器
 * 
 * 功能：
 * - 动态路由管理
 * - 路由规则配置
 * - 路由健康检查
 * - 路由性能监控
 */

import { Controller, Get, Post, Put, Delete, Body, Param, Query } from '@nestjs/common'
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger'
import { RoutingService } from './routing.service'
import { CreateRouteDto, UpdateRouteDto, RouteQueryDto } from './dto/route.dto'

@ApiTags('routing')
@ApiBearerAuth()
@Controller('api/gateway/routes')
export class RoutingController {
  constructor(private readonly routingService: RoutingService) {}

  @Get()
  @ApiOperation({ summary: '获取所有路由配置' })
  @ApiResponse({ status: 200, description: '路由配置列表' })
  async getAllRoutes(@Query() query: RouteQueryDto) {
    return this.routingService.getAllRoutes(query)
  }

  @Get(':id')
  @ApiOperation({ summary: '获取指定路由配置' })
  @ApiResponse({ status: 200, description: '路由配置详情' })
  @ApiResponse({ status: 404, description: '路由不存在' })
  async getRoute(@Param('id') id: string) {
    return this.routingService.getRoute(id)
  }

  @Post()
  @ApiOperation({ summary: '创建新路由配置' })
  @ApiResponse({ status: 201, description: '路由创建成功' })
  @ApiResponse({ status: 400, description: '请求参数错误' })
  async createRoute(@Body() createRouteDto: CreateRouteDto) {
    return this.routingService.createRoute(createRouteDto)
  }

  @Put(':id')
  @ApiOperation({ summary: '更新路由配置' })
  @ApiResponse({ status: 200, description: '路由更新成功' })
  @ApiResponse({ status: 404, description: '路由不存在' })
  async updateRoute(@Param('id') id: string, @Body() updateRouteDto: UpdateRouteDto) {
    return this.routingService.updateRoute(id, updateRouteDto)
  }

  @Delete(':id')
  @ApiOperation({ summary: '删除路由配置' })
  @ApiResponse({ status: 200, description: '路由删除成功' })
  @ApiResponse({ status: 404, description: '路由不存在' })
  async deleteRoute(@Param('id') id: string) {
    return this.routingService.deleteRoute(id)
  }

  @Get(':id/health')
  @ApiOperation({ summary: '检查路由健康状态' })
  @ApiResponse({ status: 200, description: '路由健康状态' })
  async checkRouteHealth(@Param('id') id: string) {
    return this.routingService.checkRouteHealth(id)
  }

  @Get(':id/metrics')
  @ApiOperation({ summary: '获取路由性能指标' })
  @ApiResponse({ status: 200, description: '路由性能指标' })
  async getRouteMetrics(@Param('id') id: string, @Query('timeRange') timeRange?: string) {
    return this.routingService.getRouteMetrics(id, timeRange)
  }

  @Post(':id/enable')
  @ApiOperation({ summary: '启用路由' })
  @ApiResponse({ status: 200, description: '路由启用成功' })
  async enableRoute(@Param('id') id: string) {
    return this.routingService.enableRoute(id)
  }

  @Post(':id/disable')
  @ApiOperation({ summary: '禁用路由' })
  @ApiResponse({ status: 200, description: '路由禁用成功' })
  async disableRoute(@Param('id') id: string) {
    return this.routingService.disableRoute(id)
  }

  @Get('services/discovery')
  @ApiOperation({ summary: '服务发现' })
  @ApiResponse({ status: 200, description: '可用服务列表' })
  async discoverServices() {
    return this.routingService.discoverServices()
  }

  @Post('reload')
  @ApiOperation({ summary: '重新加载路由配置' })
  @ApiResponse({ status: 200, description: '路由配置重新加载成功' })
  async reloadRoutes() {
    return this.routingService.reloadRoutes()
  }
}
