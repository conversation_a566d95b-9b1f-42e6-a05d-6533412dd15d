/*
CPAL-1.0 License

The contents of this file are subject to the Common Public Attribution License
Version 1.0. (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at
https://github.com/ir-engine/ir-engine/blob/dev/LICENSE.
The License is based on the Mozilla Public License Version 1.1, but Sections 14
and 15 have been added to cover use of software over a computer network and 
provide for limited attribution for the Original Developer. In addition, 
Exhibit A has been modified to be consistent with Exhibit B.

Software distributed under the License is distributed on an "AS IS" basis,
WITHOUT WARRANTY OF ANY KIND, either express or implied. See the License for the
specific language governing rights and limitations under the License.

The Original Code is Infinite Reality Engine.

The Original Developer is the Initial Developer. The Initial Developer of the
Original Code is the Infinite Reality Engine team.

All portions of the code written by the Infinite Reality Engine team are Copyright © 2021-2025
Infinite Reality Engine. All Rights Reserved.
*/

import { MeshBasicMaterial, Quaternion, Vector3 } from 'three'

import { isClient } from '@ir-engine/common/src/utils/getEnvironment'
import {
  Engine,
  EngineState,
  EntityTreeComponent,
  getChildrenWithComponents,
  getMutableComponent,
  InputSystemGroup,
  UndefinedEntity
} from '@ir-engine/ecs'
import { getComponent, getOptionalComponent, setComponent } from '@ir-engine/ecs/src/ComponentFunctions'
import { ECSState } from '@ir-engine/ecs/src/ECSState'
import { Entity } from '@ir-engine/ecs/src/Entity'
import { defineQuery, QueryReactor } from '@ir-engine/ecs/src/QueryFunctions'
import { defineSystem } from '@ir-engine/ecs/src/SystemFunctions'
import { MediaComponent } from '@ir-engine/engine/src/scene/components/MediaComponent'
import { getState } from '@ir-engine/hyperflux'
import { createTransitionState } from '@ir-engine/spatial/src/common/functions/createTransitionState'
import { NameComponent } from '@ir-engine/spatial/src/common/NameComponent'
import { InputComponent } from '@ir-engine/spatial/src/input/components/InputComponent'
import { InputSourceComponent } from '@ir-engine/spatial/src/input/components/InputSourceComponent'
import { InputState } from '@ir-engine/spatial/src/input/state/InputState'
import { TransformComponent } from '@ir-engine/spatial/src/transform/components/TransformComponent'
import { XRUIComponent } from '@ir-engine/spatial/src/xrui/components/XRUIComponent'
import { WebLayer3D } from '@ir-engine/xrui'

import React, { useEffect } from 'react'
import { createMediaControlsView } from './ui/MediaControlsUI'

const controlsUiPosVec3 = new Vector3()
let clicking = false
const MediaFadeTransitions = new Map<Entity, ReturnType<typeof createTransitionState>>()
const mediaQuery = defineQuery([MediaComponent])

export const createMediaControlsUI = (entity: Entity, aspectRatio: number = 1) => {
  const mediaTransform = getComponent(entity, TransformComponent)

  const uiFront = createMediaControlsView(entity)
  setComponent(uiFront.entity, EntityTreeComponent, { parentEntity: Engine.instance.originEntity })
  setComponent(uiFront.entity, NameComponent, 'mediacontrols-ui-frontside-' + entity)
  setComponent(uiFront.entity, TransformComponent, { rotation: mediaTransform.rotation })
  uiFront.container.rootLayer.traverseLayersPreOrder((layer: WebLayer3D) => {
    const mat = layer.contentMesh.material as MeshBasicMaterial
    mat.transparent = true
  })

  const rotationQuaternion = new Quaternion()
  rotationQuaternion.setFromAxisAngle(new Vector3(0, 1, 0), Math.PI) // 180 degrees in radians
  const backRotation = mediaTransform.rotation.clone().multiply(rotationQuaternion)

  const uiBack = createMediaControlsView(entity)
  setComponent(uiBack.entity, EntityTreeComponent, { parentEntity: uiFront.entity })
  setComponent(uiBack.entity, NameComponent, 'mediacontrols-ui-backside-' + entity)
  setComponent(uiBack.entity, TransformComponent, { rotation: backRotation })
  uiBack.container.rootLayer.traverseLayersPreOrder((layer: WebLayer3D) => {
    const mat = layer.contentMesh.material as MeshBasicMaterial
    mat.transparent = true
  })

  return uiFront
}

const onUpdate = (entity: Entity) => {
  const mediaComponent = getMutableComponent(entity, MediaComponent)
  if (!mediaComponent.controls.value) return
  const xrui = getOptionalComponent(mediaComponent.xruiEntity.value, XRUIComponent)

  if (!xrui) return
  const xruiChildren = getChildrenWithComponents(mediaComponent.xruiEntity.value, [XRUIComponent]).map((entity) =>
    getComponent(entity, XRUIComponent)
  )
  const xruiList = [xrui, ...xruiChildren]
  const transition = MediaFadeTransitions.get(entity)!
  const buttonLayers = xruiList.map((xrui) => xrui.rootLayer.querySelector('#button'))

  const inputComponent = getComponent(entity, InputComponent)
  const inputSourceEntity = inputComponent?.inputSources[0]

  //inputsource and entity 0 = hover
  //inputsource and entity 3 = clicking HERE
  //noinput and entity 3 = clicking somewhere else or still clicking
  //noinputsource and entity 0 = no hover, no click
  const capturingEntity = getState(InputState).capturingEntity

  if (inputSourceEntity) {
    const inputSource = getOptionalComponent(inputSourceEntity, InputSourceComponent)

    if (capturingEntity === entity) {
      const buttons = inputSource?.buttons
      clicking = !!buttons //clicking on our boundingbox this frame

      mediaComponent.paused.set(!mediaComponent.paused.value)
    }
  }

  const hover = inputSourceEntity && capturingEntity === UndefinedEntity
  const showUI = hover || clicking

  //fires one frame late to prevent mouse up frame issue
  if (clicking && !inputSourceEntity && capturingEntity === UndefinedEntity) {
    clicking = false
  }
  if (showUI) {
    transition.setState('IN')
  } else {
    transition.setState('OUT')
  }
  const uiTransform = getComponent(mediaComponent.xruiEntity.value, TransformComponent)
  const transform = getComponent(entity, TransformComponent)

  controlsUiPosVec3.copy(mediaComponent.uiOffset.value) //used to add - might be nice to allow for some pre-placed anchor positions
  controlsUiPosVec3.add(transform.position)
  uiTransform.position.copy(controlsUiPosVec3)

  const deltaSeconds = getState(ECSState).deltaSeconds
  transition.update(deltaSeconds, (opacity) => {
    ;``
    buttonLayers.forEach((buttonLayer) => {
      buttonLayer?.scale.setScalar(0.9 + 0.1 * opacity * opacity)
    })
    xruiList.forEach((xrui) => {
      xrui.rootLayer.traverseLayersPreOrder((layer: WebLayer3D) => {
        const mat = layer.contentMesh.material as MeshBasicMaterial
        mat.opacity = opacity
      })
    })
  })
}

const execute = () => {
  if (getState(EngineState).isEditing || !isClient) return

  for (const entity of mediaQuery()) {
    onUpdate(entity)
  }
}

const MediaXRUIReactor = ({ entity }: { entity: Entity }) => {
  useEffect(() => {
    const mediaComponent = getComponent(entity, MediaComponent)
    if (!mediaComponent.controls) return

    const transition = createTransitionState(0.25, 'IN')
    MediaFadeTransitions.set(entity, transition)
    mediaComponent.xruiEntity = createMediaControlsUI(entity).entity
    setComponent(mediaComponent.xruiEntity, EntityTreeComponent, { parentEntity: Engine.instance.originEntity })

    return () => {
      if (MediaFadeTransitions.has(entity)) MediaFadeTransitions.delete(entity)
    }
  }, [])
  return null
}

export const MediaControlSystem = defineSystem({
  uuid: 'ee.engine.MediaControlSystem',
  insert: { after: InputSystemGroup },
  execute,
  reactor: () => <QueryReactor Components={[MediaComponent]} ChildEntityReactor={MediaXRUIReactor} />
})
