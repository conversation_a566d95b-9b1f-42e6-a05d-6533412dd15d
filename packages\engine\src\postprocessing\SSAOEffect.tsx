/*
CPAL-1.0 License

The contents of this file are subject to the Common Public Attribution License
Version 1.0. (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at
https://github.com/ir-engine/ir-engine/blob/dev/LICENSE.
The License is based on the Mozilla Public License Version 1.1, but Sections 14
and 15 have been added to cover use of software over a computer network and
provide for limited attribution for the Original Developer. In addition,
Exhibit A has been modified to be consistent with Exhibit B.

Software distributed under the License is distributed on an "AS IS" basis,
WITHOUT WARRANTY OF ANY KIND, either express or implied. See the License for the
specific language governing rights and limitations under the License.

The Original Code is Infinite Reality Engine.

The Original Developer is the Initial Developer. The Initial Developer of the
Original Code is the Infinite Reality Engine team.

All portions of the code written by the Infinite Reality Engine team are Copyright © 2021-2025
Infinite Reality Engine. All Rights Reserved.
*/

import { Entity, getComponent } from '@ir-engine/ecs'
import { getMutableState, getState, none } from '@ir-engine/hyperflux'
import { CameraComponent } from '@ir-engine/spatial/src/camera/components/CameraComponent'
import { RendererComponent } from '@ir-engine/spatial/src/renderer/components/RendererComponent'
import { EffectReactorProps, PostProcessingEffectState } from '@ir-engine/spatial/src/renderer/effects/EffectRegistry'
import { BlendFunction, DepthDownsamplingPass, KernelSize, Resolution, SSAOEffect } from 'postprocessing'
import React, { useEffect } from 'react'
import { ArrayCamera } from 'three'
import { CustomNormalPass } from './passes/CustomNormalPass'
import { PropertyTypes } from './PostProcessingRegister'

declare module 'postprocessing' {
  interface EffectComposer {
    SSAOEffect: SSAOEffect
  }
}

const effectKey = 'SSAOEffect'

export const SSAOEffectProcessReactor: React.FC<EffectReactorProps> = (props: {
  isActive
  rendererEntity: Entity
  effectData
  effects
  scene
  passes
}) => {
  const { isActive, rendererEntity, effectData, effects, scene, passes } = props
  const effectState = getState(PostProcessingEffectState)

  useEffect(() => {
    if (effectData[effectKey].value) return
    effectData[effectKey].set(effectState[effectKey].defaultValues)
  }, [])

  useEffect(() => {
    if (!isActive?.value) {
      if (effects[effectKey].value) effects[effectKey].set(none)
      return
    }

    const camera = getComponent(rendererEntity, CameraComponent)

    const customNormalPass = RendererComponent.registerPass(rendererEntity, CustomNormalPass, (rendererEntity) => {
      const camera = getComponent(rendererEntity, CameraComponent)
      return new CustomNormalPass(scene, camera)
    })

    const depthDownSamplingPass = RendererComponent.registerPass(
      rendererEntity,
      DepthDownsamplingPass,
      (rendererEntity) => {
        const customNormalPass = RendererComponent.getPass(rendererEntity, CustomNormalPass)
        return new DepthDownsamplingPass({
          normalBuffer: customNormalPass.texture,
          resolutionScale: 0.5
        })
      }
    )

    const eff = new SSAOEffect(camera as ArrayCamera, customNormalPass.texture, {
      ...effectData[effectKey].value,
      normalDepthBuffer: depthDownSamplingPass.texture
    })
    effects[effectKey].set(eff)
    return () => {
      effects[effectKey].set(none)
      RendererComponent.unregisterPass(rendererEntity, DepthDownsamplingPass)
      RendererComponent.unregisterPass(rendererEntity, CustomNormalPass)
    }
  }, [isActive])

  return null
}

export const ssaoAddToEffectRegistry = () => {
  // registers the effect

  getMutableState(PostProcessingEffectState).merge({
    [effectKey]: {
      reactor: SSAOEffectProcessReactor,
      defaultValues: {
        isActive: false,
        blendFunction: BlendFunction.MULTIPLY,
        distanceScaling: true,
        depthAwareUpsampling: true,
        normalDepthBuffer: undefined,
        samples: 9,
        rings: 7,
        // worldDistanceThreshold: 0.97,
        // worldDistanceFalloff: 0.03,
        // worldProximityThreshold: 0.0005,
        // worldProximityFalloff: 0.001,
        distanceThreshold: 0.97, // Render up to a distance of ~20 world units
        distanceFalloff: 0.03, // with an additional ~2.5 units of falloff.
        rangeThreshold: 0.0005,
        rangeFalloff: 0.001,
        minRadiusScale: 0.1,
        luminanceInfluence: 0.7,
        bias: 0.025,
        radius: 0.1825,
        intensity: 1.0,
        fade: 0.01,
        color: undefined,
        resolutionScale: 1.0,
        resolutionX: Resolution.AUTO_SIZE,
        resolutionY: Resolution.AUTO_SIZE,
        width: Resolution.AUTO_SIZE,
        height: Resolution.AUTO_SIZE,
        blur: false,
        kernelSize: KernelSize.SMALL
      },
      schema: {
        blendFunction: { propertyType: PropertyTypes.BlendFunction, name: 'Blend Function' },
        distanceScaling: { propertyType: PropertyTypes.Boolean, name: 'Distance Scaling' },
        depthAwareUpsampling: { propertyType: PropertyTypes.Boolean, name: 'Depth Aware Upsampling' },
        samples: { propertyType: PropertyTypes.Number, name: 'Samples', min: 1, max: 32, step: 1 },
        rings: { propertyType: PropertyTypes.Number, name: 'Rings', min: -1, max: 1, step: 0.01 },

        rangeThreshold: {
          propertyType: PropertyTypes.Number,
          name: 'Range Threshold',
          min: -1,
          max: 1,
          step: 0.0001
        }, // Occlusion proximity of ~0.3 world units
        rangeFalloff: { propertyType: PropertyTypes.Number, name: 'Range Falloff', min: -1, max: 1, step: 0.0001 }, // with ~0.1 units of falloff.
        // Render up to a distance of ~20 world units.
        distanceThreshold: {
          propertyType: PropertyTypes.Number,
          name: 'Distance Threshold',
          min: -1,
          max: 1,
          step: 0.01
        },
        luminanceInfluence: {
          propertyType: PropertyTypes.Number,
          name: 'Luminance Influence',
          min: -1,
          max: 1,
          step: 0.01
        },
        // with an additional ~2.5 units of falloff.
        distanceFalloff: {
          propertyType: PropertyTypes.Number,
          name: 'Distance Falloff',
          min: -1,
          max: 1,
          step: 0.001
        },
        minRadiusScale: { propertyType: PropertyTypes.Number, name: 'Min Radius Scale', min: -1, max: 1, step: 0.01 },
        bias: { propertyType: PropertyTypes.Number, name: 'Bias', min: -1, max: 1, step: 0.01 },
        radius: { propertyType: PropertyTypes.Number, name: 'Radius', min: -1, max: 1, step: 0.01 },
        intensity: { propertyType: PropertyTypes.Number, name: 'Intensity', min: 0, max: 10, step: 0.01 },
        fade: { propertyType: PropertyTypes.Number, name: 'Fade', min: -1, max: 1, step: 0.01 },
        resolutionScale: {
          propertyType: PropertyTypes.Number,
          name: 'Resolution Scale',
          min: -10,
          max: 10,
          step: 0.01
        },
        blur: { propertyType: PropertyTypes.Boolean, name: 'Blur' },
        kernelSize: { propertyType: PropertyTypes.KernelSize, name: 'Kernel Size', min: 1, max: 5, step: 1 }
      }
    }
  })
}
