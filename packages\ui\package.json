{"name": "@ir-engine/ui", "version": "1.0.3", "description": "A declarative way to build 3D/VR/AR/XR apps that scale. Built with tooling web developers will love, using conventions that game developers and creative coders will find straightforward familiar.", "homepage": "https://github.com/ir-engine/ir-engine#readme", "author": {"name": "Infinite Reality Engine Collective", "email": "<EMAIL>", "url": "http://github.com/ir-engine"}, "repository": {"type": "git", "url": "git://github.com/ir-engine/ir-engine.git"}, "bugs": {"url": "https://github.com/ir-engine/ir-engine/issues"}, "engines": {"node": ">= 22.11.0"}, "publishConfig": {"access": "public"}, "main": "src/index.ts", "scripts": {"check-errors": "tsc --noemit && npx cycle-import-check src || true", "test": "exit 0", "test:generate-output": "jest --json --outputFile=./tests/jest-test-results.json || true", "test:watch": "jest -u --json --watchAll --outputFile=./tests/jest-test-results.json || true", "prebuild:storybook": "npm run test:generate-output", "build:storybook": "storybook build -c .storybook -o build/", "predeploy": "npm run build:storybook", "deploy": "gh-pages -d build/", "storybook": "./scripts/start-storybook.sh", "build-storybook": "storybook build", "copy-icona": "./scripts/copy-icona-json.sh", "build-icons": "npx tsx scripts/build-icons.ts", "chromatic": "chromatic --exit-zero-on-changes"}, "dependencies": {"@ir-engine/common": "^1.0.3", "@ir-engine/engine": "^1.0.3", "@ir-engine/hyperflux": "^1.0.3", "@ir-engine/projects": "^1.0.3", "@pixiv/three-vrm": "^2.0.6", "autoprefixer": "^10.4.14", "country-state-city": "^3.2.1", "fuse.js": "^7.0.0", "re-resizable": "^6.9.9", "react-icons": "5.0.1", "react-scrubber": "^2.0.0", "reactjs-popup": "^2.0.6", "tailwind-merge": "^1.13.2", "tailwindcss": "^3.3.2", "typescript": "5.6.3"}, "devDependencies": {"@chromatic-com/playwright": "^0.12.0", "@jest/globals": "^29.5.0", "@storybook/addon-a11y": "^8.0.5", "@storybook/addon-actions": "^8.0.5", "@storybook/addon-essentials": "^8.0.5", "@storybook/addon-interactions": "^8.0.5", "@storybook/addon-jest": "^8.0.5", "@storybook/addon-links": "^8.0.5", "@storybook/addon-onboarding": "^8.0.5", "@storybook/addon-styling": "^1.3.7", "@storybook/addon-toolbars": "^8.0.5", "@storybook/blocks": "^8.0.5", "@storybook/react": "^8.0.5", "@storybook/react-docgen-typescript-plugin": "^1.0.1", "@storybook/react-vite": "^8.0.5", "@storybook/test": "^8.0.5", "@svgr/core": "^8.1.0", "@svgr/plugin-jsx": "^8.1.0", "@svgr/plugin-svgo": "^8.1.0", "@testing-library/jest-dom": "^6.6.3", "@types/enzyme": "^3.10.12", "@types/enzyme-adapter-react-16": "^1.0.6", "@types/jest": "^29.5.1", "@types/node": "18.15.5", "@types/react": "18.2.0", "@types/three": "0.176.0", "chromatic": "^11.28.0", "crypto-browserify": "^3.12.0", "enzyme": "^3.11.0", "enzyme-adapter-react-16": "^1.15.7", "enzyme-to-json": "^3.6.2", "jest": "^29.4.1", "jest-enzyme": "^7.1.2", "jest-scss-transform": "^1.0.3", "msw": "^2.8.2", "msw-storybook-addon": "^2.0.4", "path-browserify": "^1.0.1", "postcss": "^8.4.23", "react": "18.2.0", "react-dom": "18.2.0", "sass": "1.59.3", "storybook": "^8.0.5", "storybook-addon-react-router-v6": "^2.0.15", "storybook-addon-remix-react-router": "^3.1.0", "storybook-addon-sass-postcss": "^0.2.0", "stream-browserify": "^3.0.0", "ts-jest": "^29.0.5"}, "license": "CPAL", "gitHead": "2313453697ca7c6b8d36b3b166b5a6445fe1c851", "msw": {"workerDirectory": ["public"]}}