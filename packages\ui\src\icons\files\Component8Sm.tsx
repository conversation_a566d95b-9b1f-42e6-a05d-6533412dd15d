/*
CPAL-1.0 License

The contents of this file are subject to the Common Public Attribution License
Version 1.0. (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at
https://github.com/ir-engine/ir-engine/blob/dev/LICENSE.
The License is based on the Mozilla Public License Version 1.1, but Sections 14
and 15 have been added to cover use of software over a computer network and 
provide for limited attribution for the Original Developer. In addition, 
Exhibit A has been modified to be consistent with Exhibit B.

Software distributed under the License is distributed on an "AS IS" basis,
WITHOUT WARRANTY OF ANY KIND, either express or implied. See the License for the
specific language governing rights and limitations under the License.

The Original Code is Infinite Reality Engine.

The Original Developer is the Initial Developer. The Initial Developer of the
Original Code is the Infinite Reality Engine team.

All portions of the code written by the Infinite Reality Engine team are Copyright © 2021-2025
Infinite Reality Engine. All Rights Reserved.
*/

import type { SVGProps } from 'react'
import * as React from 'react'
import { Ref, forwardRef } from 'react'
const Component8Sm = (props: SVGProps<SVGSVGElement>, ref: Ref<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="1em"
    height="1em"
    fill="none"
    viewBox="0 0 20 18"
    role="img"
    stroke="currentColor"
    ref={ref}
    {...props}
  >
    <path
      stroke="currentColor"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={1.5}
      d="M10 13.8h.008M7.44 1h5.12c1.344 0 2.016 0 2.53.262a2.4 2.4 0 0 1 1.049 1.048c.261.514.261 1.186.261 2.53v8.32c0 1.344 0 2.016-.261 2.53a2.4 2.4 0 0 1-1.05 1.048C14.577 17 13.905 17 12.56 17H7.44c-1.344 0-2.016 0-2.53-.262a2.4 2.4 0 0 1-1.048-1.048c-.262-.514-.262-1.186-.262-2.53V4.84c0-1.344 0-2.016.262-2.53A2.4 2.4 0 0 1 4.91 1.262C5.424 1 6.096 1 7.44 1m2.96 12.8a.4.4 0 1 0-.8 0 .4.4 0 0 0 .8 0m2.8-6.4a3.2 3.2 0 1 0-6.4 0 3.2 3.2 0 0 0 6.4 0"
    />
  </svg>
)
const ForwardRef = forwardRef(Component8Sm)
export default ForwardRef
