/**
 * DL-Engine 认证服务主入口
 * 
 * 功能特性：
 * - 手机号码登录系统
 * - 短信验证码服务
 * - JWT令牌管理
 * - OAuth第三方登录
 * - 权限管理系统
 * - 中文优先界面
 */

import { NestFactory } from '@nestjs/core'
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger'
import { ValidationPipe } from '@nestjs/common'
import helmet from 'helmet'
import compression from 'compression'
import { AppModule } from './app.module'
import { LoggerService } from './common/logger.service'
import { ConfigService } from './config/config.service'

async function bootstrap() {
  const app = await NestFactory.create(AppModule, {
    logger: new LoggerService()
  })

  const configService = app.get(ConfigService)
  const logger = app.get(LoggerService)

  // 安全中间件
  app.use(helmet({
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        styleSrc: ["'self'", "'unsafe-inline'"],
        scriptSrc: ["'self'"],
        imgSrc: ["'self'", "data:", "https:"],
        connectSrc: ["'self'"]
      }
    }
  }))

  // 压缩中间件
  app.use(compression())

  // CORS配置
  app.enableCors({
    origin: configService.getAllowedOrigins(),
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
  })

  // 全局验证管道
  app.useGlobalPipes(new ValidationPipe({
    transform: true,
    whitelist: true,
    forbidNonWhitelisted: true
  }))

  // API文档配置
  const config = new DocumentBuilder()
    .setTitle('DL-Engine 认证服务')
    .setDescription('数字化学习引擎认证和权限管理服务')
    .setVersion('1.0.0')
    .addBearerAuth()
    .addTag('auth', '认证管理')
    .addTag('phone', '手机号登录')
    .addTag('sms', '短信验证')
    .addTag('jwt', 'JWT令牌')
    .addTag('oauth', 'OAuth登录')
    .addTag('permissions', '权限管理')
    .addTag('users', '用户管理')
    .build()

  const document = SwaggerModule.createDocument(app, config)
  SwaggerModule.setup('api/docs', app, document, {
    customSiteTitle: 'DL-Engine 认证服务文档',
    customCss: '.swagger-ui .topbar { display: none }',
    customfavIcon: '/favicon.ico'
  })

  // 启动服务器
  const port = configService.getPort()
  await app.listen(port)

  logger.log(`🔐 DL-Engine 认证服务已启动，端口: ${port}`)
  logger.log(`📚 API文档地址: http://localhost:${port}/api/docs`)
  logger.log(`🔍 健康检查: http://localhost:${port}/health`)
  logger.log(`📱 支持手机号登录和短信验证`)
}

// 错误处理
process.on('unhandledRejection', (reason, promise) => {
  console.error('未处理的Promise拒绝:', promise, '原因:', reason)
  process.exit(1)
})

process.on('uncaughtException', (error) => {
  console.error('未捕获的异常:', error)
  process.exit(1)
})

bootstrap().catch(error => {
  console.error('启动应用失败:', error)
  process.exit(1)
})
