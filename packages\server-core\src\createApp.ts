/*
CPAL-1.0 License

The contents of this file are subject to the Common Public Attribution License
Version 1.0. (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at
https://github.com/ir-engine/ir-engine/blob/dev/LICENSE.
The License is based on the Mozilla Public License Version 1.1, but Sections 14
and 15 have been added to cover use of software over a computer network and
provide for limited attribution for the Original Developer. In addition,
Exhibit A has been modified to be consistent with Exhibit B.

Software distributed under the License is distributed on an "AS IS" basis,
WITHOUT WARRANTY OF ANY KIND, either express or implied. See the License for the
specific language governing rights and limitations under the License.

The Original Code is Infinite Reality Engine.

The Original Developer is the Initial Developer. The Initial Developer of the
Original Code is the Infinite Reality Engine team.

All portions of the code written by the Infinite Reality Engine team are Copyright © 2021-2025
Infinite Reality Engine. All Rights Reserved.
*/

// Do not delete json and urlencoded, they are used even if some IDEs show them as unused

import { feathers } from '@feathersjs/feathers'
import { bodyParser, errorHandler, koa, rest } from '@feathersjs/koa'
import { AppsV1Api, BatchV1<PERSON><PERSON>, CoreV1Api, CustomObjectsApi, KubeConfig } from '@kubernetes/client-node'
import { EventEmitter } from 'events'
// Do not delete, this is used even if some IDEs show it as unused
import swagger from 'feathers-swagger'
import sync from 'feathers-sync'
import { parse, stringify } from 'flatted'
import compress from 'koa-compress'
import cors from 'koa-cors'
import helmet from 'koa-helmet'
import healthcheck from 'koa-simple-healthcheck'

import { API } from '@ir-engine/common'
import commonConfig from '@ir-engine/common/src/config'
import { pipeLogs } from '@ir-engine/common/src/logger'
import { pipe } from '@ir-engine/common/src/utils/pipe'
import { createEngine } from '@ir-engine/ecs/src/Engine'
import { createHyperStore, getMutableState } from '@ir-engine/hyperflux'

import { DomainConfigState } from '@ir-engine/engine/src/assets/state/DomainConfigState'
import { Application } from '../declarations'
import packagejson from '../package.json'
import { logger } from './ServerLogger'
import { ServerMode, ServerState, ServerTypeMode } from './ServerState'
import { default as appConfig } from './appconfig'
import authenticate from './hooks/authenticate'
import { logError } from './hooks/log-error'
import persistHeaders from './hooks/persist-headers'
import { createDefaultStorageProvider } from './media/storageprovider/storageprovider'
import monitoringServices from './monitoring'
import mysql from './mysql'
import postgres from './postgres'
import services from './services'
import authentication from './user/authentication'
import primus from './util/primus'

export const configureOpenAPI = () => (app: Application) => {
  app.configure(
    swagger({
      ui: swagger.swaggerUI({
        docsPath: '/openapi'
      }),
      specs: {
        info: {
          title: 'Infinite Reality Engine API Surface',
          description: 'APIs for the Infinite Reality Engine application',
          version: packagejson.version
        },
        schemes: ['https'],
        components: {
          securitySchemes: {
            BearerAuth: {
              type: 'http',
              scheme: 'bearer'
            }
          }
        },
        security: [{ BearerAuth: [] }]
      },
      idType: 'string',
      ignore: {
        paths: ['oauth', 'knex_migrations']
      }
    })
  )
  return app
}

export const configurePrimus =
  (instanceserver = false) =>
  (app: Application) => {
    const origin = [
      'https://' + appConfig.server.clientHost,
      'capacitor://' + appConfig.server.clientHost,
      'ionic://' + appConfig.server.clientHost
    ]
    if (!instanceserver) origin.push('https://localhost:3001')

    // Get metrics service if it exists
    const metricsService = app.get('metricsService')
    const metricsEnabled = process.env.PROMETHEUS_METRICS_ENABLED === 'true'

    app.configure(
      primus(
        {
          transformer: 'websockets',
          origins: origin,
          methods: ['OPTIONS', 'GET', 'POST'],
          pingInterval: commonConfig.websocket.pingInterval,
          pingTimeout: commonConfig.websocket.pingTimeout,
          headers: '*',
          credentials: true
        },
        (primus) => {
          primus.use((message, socket, next) => {
            ;(message as any).feathers.socketQuery = message.query
            ;(message as any).socketQuery = message.query
            ;(message as any).feathers.forwarded = message.forwarded
            next()
          })

          // Add event handlers for tracking WebSocket connections and messages if metrics service exists
          if (metricsService && metricsEnabled) {
            primus.on('connection', (spark) => {
              metricsService.trackWebSocketConnection('connected')

              // Track WebSocket messages
              spark.on('data', (data) => {
                metricsService.trackWebSocketMessage(
                  'incoming',
                  typeof data === 'object' ? data.type || 'unknown' : 'unknown'
                )
              })

              // Track outgoing messages
              spark.on('outgoing::data', (data) => {
                metricsService.trackWebSocketMessage(
                  'outgoing',
                  typeof data === 'object' ? data.type || 'unknown' : 'unknown'
                )
              })
            })

            primus.on('disconnection', () => {
              metricsService.trackWebSocketConnection('disconnected')
            })

            logger.info('WebSocket metrics tracking enabled for Primus')
          }
        }
      )
    )
    return app
  }

export const configureRedis = () => (app: Application) => {
  if (appConfig.redis.enabled) {
    // https://github.com/feathersjs-ecosystem/feathers-sync/issues/140#issuecomment-810144263
    app.configure(
      sync({
        uri: appConfig.redis.password
          ? `redis://:${appConfig.redis.password}@${appConfig.redis.address}:${appConfig.redis.port}`
          : `redis://${appConfig.redis.address}:${appConfig.redis.port}`,
        serialize: stringify,
        deserialize: parse
      })
    )
    app.sync.ready.then(() => {
      logger.info('Feathers-sync started.')
    })
  }
  return app
}

export const configureK8s = () => (app: Application) => {
  if (appConfig.kubernetes.enabled) {
    const kc = new KubeConfig()
    kc.loadFromDefault()
    const serverState = getMutableState(ServerState)

    const k8AgonesClient = kc.makeApiClient(CustomObjectsApi)
    const k8DefaultClient = kc.makeApiClient(CoreV1Api)
    const k8AppsClient = kc.makeApiClient(AppsV1Api)
    const k8BatchClient = kc.makeApiClient(BatchV1Api)

    serverState.merge({
      k8AppsClient,
      k8BatchClient,
      k8DefaultClient,
      k8AgonesClient
    })
  }
  return app
}

export const configureMonitoring = () => (app: Application) => {
  // Set app name for monitoring
  const serviceName = appConfig.monitoring?.metrics ? 'ir-engine-api' : 'ir-engine-api'
  app.set('name', serviceName)

  // Configure monitoring services
  monitoringServices.forEach((service) => app.configure(service()))

  return app
}

export const serverPipe = pipe(
  configureOpenAPI(),
  configureMonitoring(),
  configurePrimus(),
  configureRedis(),
  configureK8s()
) as (app: Application) => Application

export const serverJobPipe = pipe(configurePrimus(), configureK8s()) as (app: Application) => Application

export const serverJobRedisPipe = pipe(configurePrimus(), configureRedis(), configureK8s()) as (
  app: Application
) => Application

export const createFeathersKoaApp = async (
  serverMode: ServerTypeMode = ServerMode.API,
  configurationPipe = serverPipe
): Promise<Application> => {
  createEngine(createHyperStore())

  getMutableState(DomainConfigState).merge({
    publicDomain: appConfig.client.dist,
    cloudDomain: commonConfig.client.fileServer,
    proxyDomain: commonConfig.client.cors.proxyUrl
  })

  const serverState = getMutableState(ServerState)
  serverState.serverMode.set(serverMode)

  createDefaultStorageProvider()

  const app = koa(feathers()) as Application
  API.instance = app

  app.set('nextReadyEmitter', new EventEmitter())

  // Feathers authentication-oauth will only append the port in production, but then it will also
  // hard-code http as the protocol, so manually mashing host + port together if in local.
  app.set(
    'host',
    (appConfig.server.local as any) === '1' || appConfig.server.local === true
      ? appConfig.server.hostname + ':' + appConfig.server.port
      : appConfig.server.hostname
  )
  app.set('port', appConfig.server.port)

  app.set('paginate', appConfig.server.paginate)
  app.set('authentication', appConfig.authentication)
  app.use(healthcheck())
  app.use(
    cors({
      origin: '*',
      credentials: true
    })
  )
  configurationPipe(app)
  // Feathers authentication-oauth will use http for its redirect_uri if this is 'dev'.
  // Doesn't appear anything else uses it.
  app.set('env', 'production')
  app.configure(mysql)

  if (appConfig.vectordb.enabled) {
    app.configure(postgres)
  }

  // Enable security, CORS, compression, favicon and body parsing
  app.use(errorHandler()) // in koa no option to pass logger object its a async function instead and must be set first
  app.use(helmet())

  app.use(compress())
  app.use(
    bodyParser({
      includeUnparsed: true
    })
  )

  app.proxy = true
  app.use(async (ctx, next) => {
    const clientIp = ctx.request.ip

    ctx.feathers = {
      ...ctx.feathers,
      forwarded: {
        ip: clientIp
      }
    }

    await next()
  })

  app.configure(rest())
  // app.use(function (req, res, next) {
  //   ;(req as any).feathers.req = req
  //   ;(req as any).feathers.res = res
  //   next()
  // })

  // Configure other middleware (see `middleware/index.js`)
  app.configure(authentication)

  // Set up our services (see `services/index.js`)
  await services(app)

  // Store headers across internal service calls
  app.hooks({
    around: {
      all: [logError, persistHeaders, authenticate]
    }
  })

  pipeLogs(API.instance)

  return app
}

export const tearDownAPI = async () => {
  if (API.instance) {
    if ((API.instance as any).server) await API.instance.teardown()

    const knex = (API.instance as any).get?.('knexClient')
    if (knex) await knex.destroy()

    const vectorDb = (API.instance as any).get?.('vectorDbClient')
    if (vectorDb) await vectorDb.destroy()
  }
}
