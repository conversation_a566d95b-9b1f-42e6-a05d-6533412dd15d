/*
CPAL-1.0 License

The contents of this file are subject to the Common Public Attribution License
Version 1.0. (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at
https://github.com/ir-engine/ir-engine/blob/dev/LICENSE.
The License is based on the Mozilla Public License Version 1.1, but Sections 14
and 15 have been added to cover use of software over a computer network and 
provide for limited attribution for the Original Developer. In addition, 
Exhibit A has been modified to be consistent with Exhibit B.

Software distributed under the License is distributed on an "AS IS" basis,
WITHOUT WARRANTY OF ANY KIND, either express or implied. See the License for the
specific language governing rights and limitations under the License.

The Original Code is Infinite Reality Engine.

The Original Developer is the Initial Developer. The Initial Developer of the
Original Code is the Infinite Reality Engine team.

All portions of the code written by the Infinite Reality Engine team are Copyright © 2021-2025
Infinite Reality Engine. All Rights Reserved.
*/

export type ManifestJson = {
  name: string
  /**
   * The version of this project
   * @example "0.0.1"
   */
  version: string
  /**
   * The version of the engine this project version is compatible with.
   * @example "1.0.0"
   */
  engineVersion: string
  /**
   * A short description of the project
   * @example "A simple project"
   */
  description?: string
  repoEmpty?: boolean
  /**
   * An optional thumbnail image
   * @example "https://example.com/thumbnail.jpg"
   */
  thumbnail?: string
  /**
   * The dependencies of this project. Specify other projects that are to be installed alongside this one.
   * Can be either a string in the format 'namespace/project-name' or an object with detailed configuration.
   * @example ["orgname/reponame", { "name": "orgname/another-repo", "commitHash": "abc123", "branch": "main" }]
   * The solo string or name can also be in the format `git+<url>.git
   */
  dependencies?: (
    | string
    | {
        name: string
        commit?: string
        tag?: string
        branch?: string
      }
  )[]
}
