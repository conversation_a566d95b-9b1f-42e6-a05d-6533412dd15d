/*
CPAL-1.0 License

The contents of this file are subject to the Common Public Attribution License
Version 1.0. (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at
https://github.com/ir-engine/ir-engine/blob/dev/LICENSE.
The License is based on the Mozilla Public License Version 1.1, but Sections 14
and 15 have been added to cover use of software over a computer network and 
provide for limited attribution for the Original Developer. In addition, 
Exhibit A has been modified to be consistent with Exhibit B.

Software distributed under the License is distributed on an "AS IS" basis,
WITHOUT WARRANTY OF ANY KIND, either express or implied. See the License for the
specific language governing rights and limitations under the License.

The Original Code is Infinite Reality Engine.

The Original Developer is the Initial Developer. The Initial Developer of the
Original Code is the Infinite Reality Engine team.

All portions of the code written by the Infinite Reality Engine team are Copyright © 2021-2025
Infinite Reality Engine. All Rights Reserved.
*/

import type { SVGProps } from 'react'
import * as React from 'react'
import { Ref, forwardRef } from 'react'
const Component9Sm = (props: SVGProps<SVGSVGElement>, ref: Ref<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="1em"
    height="1em"
    fill="none"
    viewBox="0 0 18 18"
    role="img"
    stroke="currentColor"
    ref={ref}
    {...props}
  >
    <g stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5}>
      <path d="M2.027 13a7.99 7.99 0 0 0 10.92 2.927A7.99 7.99 0 0 0 16.9 9.84M15.873 5a7.99 7.99 0 0 0-10.92-2.927A7.99 7.99 0 0 0 1 8.16" />
      <path d="M13.535 7.785a4.693 4.693 0 0 1-3.319 5.748m3.319-5.748a4.693 4.693 0 0 0-5.748-3.318m5.748 3.318 1.133-.303m-4.452 6.051a4.693 4.693 0 0 1-5.748-3.318m5.748 3.318.304 1.134m-6.052-4.452a4.693 4.693 0 0 1 3.319-5.748m-3.319 5.748-1.133.303m4.452-6.051-.304-1.134m3.219 5.211a1.76 1.76 0 1 1-3.4.911 1.76 1.76 0 0 1 3.4-.91" />
    </g>
  </svg>
)
const ForwardRef = forwardRef(Component9Sm)
export default ForwardRef
