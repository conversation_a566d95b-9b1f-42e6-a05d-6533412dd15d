/*
CPAL-1.0 License

The contents of this file are subject to the Common Public Attribution License
Version 1.0. (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at
https://github.com/ir-engine/ir-engine/blob/dev/LICENSE.
The License is based on the Mozilla Public License Version 1.1, but Sections 14
and 15 have been added to cover use of software over a computer network and
provide for limited attribution for the Original Developer. In addition,
Exhibit A has been modified to be consistent with Exhibit B.

Software distributed under the License is distributed on an "AS IS" basis,
WITHOUT WARRANTY OF ANY KIND, either express or implied. See the License for the
specific language governing rights and limitations under the License.

The Original Code is Infinite Reality Engine.

The Original Developer is the Initial Developer. The Initial Developer of the
Original Code is the Infinite Reality Engine team.

All portions of the code written by the Infinite Reality Engine team are Copyright © 2021-2025
Infinite Reality Engine. All Rights Reserved.
*/

import fs from 'fs'
import path from 'path'

import { KnexSeed } from '@ir-engine/common/src/interfaces/KnexSeed'
import { ProjectConfigInterface } from '@ir-engine/projects/ProjectConfigInterface'

import { integrationsSeeds } from './integrations/seeder-config'
import { mediaSeeds } from './media/seeder-config'
import { networkingSeeds } from './networking/seeder-config'
import { projectSeeds } from './projects/seeder-config'
import { routeSeeds } from './route/seeder-config'
import { scopeSeeds } from './scope/seeder-config'
import { settingSeeds } from './setting/seeder-config'
import { socialSeeds } from './social/seeder-config'
import { userSeeds } from './user/seeder-config'

const installedProjects = fs.existsSync(path.resolve(__dirname, '../../projects/projects'))
  ? async () => {
      const projects = fs
        .readdirSync(path.resolve(__dirname, '../../projects/projects'), { withFileTypes: true })
        .filter((orgDir) => orgDir.isDirectory())
        .map((orgDir) => {
          return fs
            .readdirSync(path.resolve(__dirname, '../../projects/projects', orgDir.name), { withFileTypes: true })
            .filter((projectDir) => projectDir.isDirectory())
            .map((projectDir) => `${orgDir.name}/${projectDir.name}`)
        })
        .flat()
      const seederDirs = (
        await Promise.all(
          projects.map(async (projectName) => {
            try {
              const configPath = `../../projects/projects/${projectName}/xrengine.config.ts`
              const config: ProjectConfigInterface = (await import(configPath)).default
              if (!config.databaseSeed) return null
              return path.join(projectName, config.databaseSeed)
            } catch (e) {
              // console.log(e)
            }
          })
        )
      ).filter((hasServices) => !!hasServices)
      return (
        await Promise.all(
          seederDirs.map(async (seedDir) => (await import(`../../projects/projects/${seedDir}`)).default)
        )
      ).flat()
    }
  : async () => []

export const knexSeeds = (): Promise<Array<KnexSeed>> => {
  return installedProjects().then((installedProjectSeeds: KnexSeed[]) => {
    return [
      ...routeSeeds,
      ...settingSeeds,
      ...scopeSeeds,
      ...userSeeds,
      ...socialSeeds,
      ...projectSeeds,
      ...mediaSeeds,
      ...networkingSeeds,
      ...integrationsSeeds,
      ...installedProjectSeeds
    ]
  })
}
