/*
CPAL-1.0 License

The contents of this file are subject to the Common Public Attribution License
Version 1.0. (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at
https://github.com/ir-engine/ir-engine/blob/dev/LICENSE.
The License is based on the Mozilla Public License Version 1.1, but Sections 14
and 15 have been added to cover use of software over a computer network and 
provide for limited attribution for the Original Developer. In addition, 
Exhibit A has been modified to be consistent with Exhibit B.

Software distributed under the License is distributed on an "AS IS" basis,
WITHOUT WARRANTY OF ANY KIND, either express or implied. See the License for the
specific language governing rights and limitations under the License.

The Original Code is Infinite Reality Engine.

The Original Developer is the Initial Developer. The Initial Developer of the
Original Code is the Infinite Reality Engine team.

All portions of the code written by the Infinite Reality Engine team are Copyright © 2021-2025
Infinite Reality Engine. All Rights Reserved.
*/
import { useMemo } from 'react'
import { Edge, Node } from 'reactflow'
import { v4 as uuidv4 } from 'uuid'

import { useMutableState } from '@ir-engine/hyperflux'
import { GraphTemplate, VisualScriptState } from '@ir-engine/visual-script'

import { useSelectionHandler } from './useSelectionHandler'
import { useVisualScriptFlow } from './useVisualScriptFlow'

type SelectionHandler = ReturnType<typeof useSelectionHandler>
type VisualScriptFlow = ReturnType<typeof useVisualScriptFlow>
export const useTemplateHandler = ({
  selectedNodes,
  selectedEdges,
  pasteNodes,
  onNodesChange
}: Pick<SelectionHandler, 'pasteNodes'> &
  Pick<VisualScriptFlow, 'onNodesChange'> & {
    selectedNodes: Node[]
    selectedEdges: Edge[]
  }) => {
  const visualScriptState = useMutableState(VisualScriptState)

  const createGraphTemplate = (nodes: Node[], edges: Edge[]): GraphTemplate => ({
    id: uuidv4(),
    name: 'New template ' + Math.random().toString(36).slice(-6),
    nodes,
    edges
  })

  const handleAddTemplate = useMemo(
    () =>
      (nodes: Node[] = selectedNodes, edges: Edge[] = selectedEdges) => {
        try {
          visualScriptState.templates.set((currentTemplates) => [
            ...currentTemplates,
            createGraphTemplate(nodes, edges)
          ])
        } catch (error) {
          console.error('Error adding template:', error)
        }
      },
    [selectedNodes, selectedEdges]
  )

  const handleEditTemplate = (editedTemplate: GraphTemplate) => {
    try {
      visualScriptState.templates.set((currentTemplates) => {
        const filterList = currentTemplates.filter((template) => template.id !== editedTemplate.id)
        return [...filterList, editedTemplate]
      })
    } catch (error) {
      console.error('Error editing template:', error)
    }
  }

  const handleDeleteTemplate = (deleteTemplate: GraphTemplate) => {
    try {
      visualScriptState.templates.set((currentTemplates) =>
        currentTemplates.filter((template) => template.id !== deleteTemplate.id)
      )
    } catch (error) {
      console.error('Error deleting template:', error)
    }
  }

  const handleApplyTemplate = (template: GraphTemplate) => {
    try {
      console.log('DEBUG ', template.name)
      pasteNodes(template.nodes, template.edges, true, template.name)
    } catch (error) {
      console.error('Error applying template:', error)
    }
  }

  return { handleAddTemplate, handleEditTemplate, handleDeleteTemplate, handleApplyTemplate }
}
