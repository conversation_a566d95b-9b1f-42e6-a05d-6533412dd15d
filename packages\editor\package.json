{"name": "@ir-engine/editor", "version": "1.0.3", "main": "lib/editor.umd.js", "module": "lib/editor.es.js", "types": "lib/index.d.ts", "repository": {"type": "git", "url": "git://github.com/ir-engine/ir-engine.git"}, "engines": {"node": ">= 22.11.0"}, "publishConfig": {"access": "public"}, "npmClient": "npm", "scripts": {"check-errors": "tsc --noemit && npx cycle-import-check src || true", "test": "cross-env TEST=true vitest run --config=../../vitest.client.config.ts"}, "resolutions": {"@types/react": "18.2.0", "react": "18.2.0"}, "peerDependencies": {"@types/react": "18.2.0", "react": "18.2.0", "xatlasjs": "0.2.0"}, "dependencies": {"@dimforge/rapier3d-compat": "0.11.2", "@ir-engine/client-core": "^1.0.3", "@ir-engine/common": "^1.0.3", "@ir-engine/engine": "^1.0.3", "@ir-engine/hyperflux": "^1.0.3", "@ir-engine/ui": "^1.0.3", "@ir-engine/visual-script": "^1.0.3", "i18next": "21.6.16", "lodash": "4.17.21", "postprocessing": "6.37.3", "rc-dock": "3.2.18", "react": "18.2.0", "react-dnd": "16.0.1", "react-dnd-html5-backend": "16.0.1", "react-dom": "18.2.0", "react-hotkeys-hook": "4.3.8", "react-i18next": "11.16.6", "react-json-view": "^1.21.3", "react-router-dom": "6.9.0", "react-window": "1.8.8", "reactflow": "^11.10.1", "typescript": "5.6.3", "uuid": "9.0.0", "xatlas-three": "^0.2.1"}, "devDependencies": {"@types/node": "18.15.5", "@types/react": "18.2.0", "@types/react-dom": "18.2.0", "@types/react-window": "^1.8.8", "@types/three": "0.176.0", "@types/uuid": "^9.0.1", "rimraf": "4.4.0", "trace-unhandled": "2.0.1"}, "license": "CPAL", "gitHead": "2313453697ca7c6b8d36b3b166b5a6445fe1c851"}