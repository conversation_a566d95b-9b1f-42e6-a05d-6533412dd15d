/*
CPAL-1.0 License

The contents of this file are subject to the Common Public Attribution License
Version 1.0. (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at
https://github.com/ir-engine/ir-engine/blob/dev/LICENSE.
The License is based on the Mozilla Public License Version 1.1, but Sections 14
and 15 have been added to cover use of software over a computer network and 
provide for limited attribution for the Original Developer. In addition, 
Exhibit A has been modified to be consistent with Exhibit B.

Software distributed under the License is distributed on an "AS IS" basis,
WITHOUT WARRANTY OF ANY KIND, either express or implied. See the License for the
specific language governing rights and limitations under the License.

The Original Code is Infinite Reality Engine.

The Original Developer is the Initial Developer. The Initial Developer of the
Original Code is the Infinite Reality Engine team.

All portions of the code written by the Infinite Reality Engine team are Copyright © 2021-2025
Infinite Reality Engine. All Rights Reserved.
*/

import type { SVGProps } from 'react'
import * as React from 'react'
import { Ref, forwardRef } from 'react'
const EyeOffMd = (props: SVGProps<SVGSVGElement>, ref: Ref<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="1em"
    height="1em"
    fill="none"
    viewBox="0 0 20 20"
    role="img"
    stroke="currentColor"
    ref={ref}
    {...props}
  >
    <path
      stroke="currentColor"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={1.75}
      d="M8.952 4.244q.51-.076 1.048-.077c4.255 0 7.046 3.754 7.984 5.239.113.18.17.27.202.408a1 1 0 0 1 0 .372c-.032.139-.089.23-.203.41-.25.396-.631.952-1.136 1.554M5.604 5.596c-1.802 1.222-3.025 2.92-3.586 3.808-.114.18-.171.271-.203.41a1 1 0 0 0 0 .372c.032.139.088.229.202.408.938 1.485 3.73 5.24 7.983 5.24 1.716 0 3.193-.611 4.407-1.437M2.5 2.5l15 15M8.233 8.232a2.5 2.5 0 0 0 3.536 3.536"
    />
  </svg>
)
const ForwardRef = forwardRef(EyeOffMd)
export default ForwardRef
