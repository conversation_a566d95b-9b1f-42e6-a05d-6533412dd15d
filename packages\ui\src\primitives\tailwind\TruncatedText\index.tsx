/*
CPAL-1.0 License

The contents of this file are subject to the Common Public Attribution License
Version 1.0. (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at
https://github.com/ir-engine/ir-engine/blob/dev/LICENSE.
The License is based on the Mozilla Public License Version 1.1, but Sections 14
and 15 have been added to cover use of software over a computer network and
provide for limited attribution for the Original Developer. In addition,
Exhibit A has been modified to be consistent with Exhibit B.

Software distributed under the License is distributed on an "AS IS" basis,
WITHOUT WARRANTY OF ANY KIND, either express or implied. See the License for the
specific language governing rights and limitations under the License.

The Original Code is Infinite Reality Engine.

The Original Developer is the Initial Developer. The Initial Developer of the
Original Code is the Infinite Reality Engine team.

All portions of the code written by the Infinite Reality Engine team are Copyright © 2021-2025
Infinite Reality Engine. All Rights Reserved.
*/

import React, { AnchorHTMLAttributes, FC } from 'react'
import CopyText from '../CopyText'
import Tooltip from '../Tooltip'

type TruncateTextOptions = {
  truncatorChar?: string
  visibleChars?: number
  truncatorPosition?: 'start' | 'middle' | 'end'
}

export const truncateText = (text: string, options: TruncateTextOptions = {}) => {
  const { visibleChars = 3, truncatorPosition = 'middle', truncatorChar = '...' } = options

  if (text.length <= visibleChars * 2) return text

  const truncators = {
    start: () => `${truncatorChar}${text.slice(-visibleChars)}`,
    middle: () => `${text.slice(0, visibleChars)}${truncatorChar}${text.slice(-visibleChars)}`,
    end: () => `${text.slice(0, visibleChars)}${truncatorChar}`
  }

  return truncators[truncatorPosition]()
}

type TruncatedTextProps = {
  text: string
  variant?: 'copy' | 'text'
} & TruncateTextOptions

const TruncatedText: FC<TruncatedTextProps> = (props) => {
  const { variant = 'text', text, visibleChars = 3, truncatorPosition = 'middle', truncatorChar = '...' } = props

  const variants = {
    copy: (
      <span className="flex items-center">
        <Tooltip content={text}>
          <>{truncateText(text, { visibleChars, truncatorPosition, truncatorChar })}</>
        </Tooltip>
        <CopyText text={text} className="ml-2" />
      </span>
    ),
    text: (
      <span className="flex items-center">
        {truncateText(text, { visibleChars, truncatorPosition, truncatorChar })}
      </span>
    )
  }
  return variants[variant]
}

type TruncatedLinkProps = {
  text: string
  copyable?: boolean
} & TruncateTextOptions &
  AnchorHTMLAttributes<HTMLAnchorElement>

export const TruncatedLink: FC<TruncatedLinkProps> = (props) => {
  const { visibleChars = 3, truncatorPosition = 'middle', truncatorChar = '...', copyable = true, text, href } = props

  if (copyable) {
    return (
      <a href={href} target="_blank" rel="noopener noreferrer">
        <TruncatedText
          text={text}
          visibleChars={visibleChars}
          truncatorPosition={truncatorPosition}
          truncatorChar={truncatorChar}
        />
      </a>
    )
  }
  return (
    <a href={href} target="_blank" rel="noopener noreferrer">
      {truncateText(text, { visibleChars, truncatorPosition: truncatorPosition })}
    </a>
  )
}

export default TruncatedText
