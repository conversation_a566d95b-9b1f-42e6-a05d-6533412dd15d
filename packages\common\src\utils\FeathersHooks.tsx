/*
CPAL-1.0 License

The contents of this file are subject to the Common Public Attribution License
Version 1.0. (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at
https://github.com/ir-engine/ir-engine/blob/dev/LICENSE.
The License is based on the Mozilla Public License Version 1.1, but Sections 14
and 15 have been added to cover use of software over a computer network and 
provide for limited attribution for the Original Developer. In addition, 
Exhibit A has been modified to be consistent with Exhibit B.

Software distributed under the License is distributed on an "AS IS" basis,
WITHOUT WARRANTY OF ANY KIND, either express or implied. See the License for the
specific language governing rights and limitations under the License.

The Original Code is Infinite Reality Engine.

The Original Developer is the Initial Developer. The Initial Developer of the
Original Code is the Infinite Reality Engine team.

All portions of the code written by the Infinite Reality Engine team are Copyright © 2021-2025
Infinite Reality Engine. All Rights Reserved.
*/

/**
 * This is a modified version of figbird. https://humaans.github.io/figbird/
 * It now runs on hookstate instead of redux, and has been modified to work with
 */

/**
 * API:
 * useFind(serviceName, options) => { data, status, refetch, isFetching, error }
 * useGet(serviceName, id, options) => { data, status, refetch, isFetching, error }
 * useMutation(serviceName) => { create, update, patch, remove, status, data, error }
 */

import { Params, Query } from '@feathersjs/feathers'
import React, { useCallback, useEffect, useLayoutEffect, useMemo, useRef } from 'react'
import { ServiceTypes } from '../../declarations'

import {
  defineState,
  getMutableState,
  getState,
  isDev,
  NO_PROXY,
  OpaqueType,
  State,
  useHookstate,
  useImmediateEffect,
  useMutableState
} from '@ir-engine/hyperflux'
import { API } from '../API'

export type Methods = 'find' | 'get' | 'create' | 'update' | 'patch' | 'remove'

export type MethodArgs = {
  find: [Params, any?]
  get: [string, any?]
  create: [any, any?]
  update: [string, any, any?]
  patch: [string, any, any?]
  remove: [string, any?]
}

type QueryHash = OpaqueType<'QueryHash'> & string

type Paginated<T> = {
  data: T[]
  total: number
  limit: number
  skip: number
}

type ArrayOrPaginated<T> = T[] | Paginated<T>

type ArrayOrPaginatedType<T> = T extends ArrayOrPaginated<infer R> ? R[] : never

export const FeathersState = defineState({
  name: 'ee.engine.FeathersState',
  initial: () =>
    ({}) as Record<
      keyof ServiceTypes,
      Record<
        QueryHash,
        {
          fetch: () => void
          query: any
          refs: number
          response: unknown
          status: 'pending' | 'success' | 'error'
          error: string
          requestTime: number
          resolvedTime: number | null
          $stack?: string[]
        }
      >
    >,

  reactor: () => {
    const feathersState = useMutableState(FeathersState)
    return (
      <>
        {feathersState.keys.map((serviceName: keyof ServiceTypes) => (
          <FeathersChildReactor key={serviceName} serviceName={serviceName} />
        ))}
      </>
    )
  }
})

const FeathersChildReactor = (props: { serviceName: keyof ServiceTypes }) => {
  const fetch = () => {
    const feathersState = getState(FeathersState)
    for (const queryId in feathersState[props.serviceName]) {
      if (feathersState[props.serviceName][queryId].refs > 0) feathersState[props.serviceName][queryId].fetch()
    }
  }

  useRealtime(props.serviceName, fetch)

  return null
}

type Args = MethodArgs[Methods]

export const useService = <S extends keyof ServiceTypes, M extends Methods>(
  serviceName: S,
  method: M,
  ...args: Args
) => {
  const fetchRef = useRef<() => void>()
  fetchRef.current = () => {
    const state = getMutableState(FeathersState)[serviceName][queryId]
    if (method === 'get' && (!args || args[0] == null || args[0] === '')) {
      state.merge({
        status: 'error',
        error: 'Get method requires an id or query object'
      })
      return
    }
    state.merge({
      status: 'pending',
      requestTime: Date.now(),
      error: ''
    })
    if (isDev) {
      const trace = { stack: '' }
      Error.captureStackTrace?.(trace, fetch)
      const stack = trace.stack.split('\n')
      stack.shift()
      state.merge({ $stack: stack })
    }
    // prettier-ignore
    return API.instance.service(serviceName)[method](...args)
      .then((res) => {
        //console.log(`API: ${serviceName}.${method}`, ...args, res)
        state.merge({
          response: res,
          status: 'success',
          error: '',
          resolvedTime: Date.now()
        })
      })
      .catch((error) => {
        console.error(`Error in service: ${serviceName}, method: ${method}, args: ${JSON.stringify(args)}`, error)
        state.merge({
          status: 'error',
          error: error.message,
          resolvedTime: Date.now()
        })
      })
  }

  const fetch = useCallback(() => {
    fetchRef.current?.()
  }, [fetchRef])

  const queryParams = {
    serviceName,
    method,
    args
  }

  const queryId = `${method.substring(0, 1)}:${hashObject(queryParams)}` as QueryHash

  const feathersState = getMutableState(FeathersState)
  if (!feathersState.get(NO_PROXY)[serviceName]) feathersState[serviceName].set({})
  if (!feathersState.get(NO_PROXY)[serviceName][queryId]) {
    feathersState[serviceName].merge({
      [queryId]: {
        fetch,
        query: queryParams,
        refs: 0,
        response: null,
        status: 'pending',
        requestTime: 0,
        resolvedTime: null,
        error: ''
      }
    })
    fetch()
  }

  // use immediate effect to get the stack trace of the react context, then add it to the state
  useImmediateEffect(() => {
    const state = getMutableState(FeathersState)[serviceName][queryId]
    if (state.get(NO_PROXY).error) {
      state.merge({
        fetch,
        query: queryParams,
        response: null,
        status: 'pending',
        error: ''
      })
      fetch()
    }
    state.refs.set(state.refs.value + 1)
    return () => {
      state.refs.set(state.refs.value - 1)
    }
  }, [queryId])

  const state = useHookstate(getMutableState(FeathersState)[serviceName][queryId]).get(NO_PROXY)
  const data = state.response as Awaited<ReturnType<ServiceTypes[S][M]>> | undefined
  const error = state.error
  const status = state.status

  return useMemo(
    () => ({
      data,
      status,
      error,
      refetch: fetch
    }),
    [data, status, error, fetch]
  )
}

export const useGet = <S extends keyof ServiceTypes>(serviceName: S, id: string | undefined, params: Params = {}) => {
  return useService(serviceName, 'get', id, params)
}

export type PaginationQuery = Partial<PaginationProps> & Query

export const useFind = <S extends keyof ServiceTypes>(serviceName: S, params: Params<PaginationQuery> = {}) => {
  const paginate = usePaginate(params.query)

  let requestParams
  if (params.query?.paginate === false || params.query?.$paginate === false) {
    requestParams = {
      ...params,
      query: {
        ...params.query
      }
    }
  } else {
    requestParams = {
      ...params,
      query: {
        ...params.query,
        ...paginate.query
      }
    }
  }

  const response = useService(serviceName, 'find', requestParams)

  const data = response?.data
    ? Array.isArray(response.data)
      ? response.data
      : response.data.data
      ? response.data.data
      : response.data
    : []
  const total: number = response?.data && !Array.isArray(response.data) ? response.data.total : 0

  return {
    ...response,
    total,
    setSort: paginate.setSort,
    setLimit: paginate.setLimit,
    setPage: paginate.setPage,
    search: paginate.search,
    page: paginate.page,
    skip: paginate.query.$skip,
    limit: paginate.query.$limit,
    sort: paginate.query.$sort,
    data: data as Readonly<ArrayOrPaginatedType<(typeof response)['data']>>
  }
}

const forceRefetch = (serviceName: keyof ServiceTypes) => {
  const feathersState = getState(FeathersState)
  if (!feathersState[serviceName]) return
  for (const queryId in feathersState[serviceName]) {
    feathersState[serviceName][queryId].fetch()
  }
}

const created = ({ serviceName, item }) => {
  forceRefetch(serviceName)
}

const updated = ({ serviceName, item }) => {
  forceRefetch(serviceName)
}

const removed = ({ serviceName, item }) => {
  forceRefetch(serviceName)
}

type CreateMethodParameters<S extends keyof ServiceTypes> = ServiceTypes[S]['create']
type UpdateMethodParameters<S extends keyof ServiceTypes> = ServiceTypes[S]['update']
type PatchMethodParameters<S extends keyof ServiceTypes> = ServiceTypes[S]['patch']
type RemoveMethodParameters<S extends keyof ServiceTypes> = ServiceTypes[S]['remove']

/**
 * Simple mutation hook exposing crud methods
 * of any feathers service. The resulting state
 * of calling these operations needs to be handled
 * by the caller. as you create/update/patch/remove
 * entities using this helper, the entities cache gets updated
 */
export function useMutation<S extends keyof ServiceTypes>(serviceName: S, forceRefetch = true) {
  const state = useHookstate({
    status: 'idle',
    data: null as unknown | null,
    error: null as string | null
  })

  const create = useMethod(
    'create',
    forceRefetch ? created : undefined,
    serviceName,
    state
  ) as CreateMethodParameters<S>
  const update = useMethod(
    'update',
    forceRefetch ? updated : undefined,
    serviceName,
    state
  ) as UpdateMethodParameters<S>
  const patch = useMethod('patch', forceRefetch ? updated : undefined, serviceName, state) as PatchMethodParameters<S>
  const remove = useMethod(
    'remove',
    forceRefetch ? removed : undefined,
    serviceName,
    state
  ) as RemoveMethodParameters<S>

  return useMemo(
    () => ({
      create,
      update,
      patch,
      remove,
      data: state.data.value,
      status: state.status.value,
      error: state.value.error
    }),
    [create, update, patch, remove, state]
  )
}

function useMethod(
  method: Methods,
  action: undefined | ((props: { serviceName: keyof ServiceTypes; item: any }) => void),
  serviceName: keyof ServiceTypes,
  state: State<any>
) {
  return useCallback(
    (...args) => {
      const service = API.instance.service(serviceName)
      state.merge({ status: 'loading', loading: true, data: null, error: null })
      return service[method](...args)
        .then((item) => {
          action && action({ serviceName, item })
          state.merge({ status: 'success', loading: false, data: item })
          return item
        })
        .catch((err) => {
          state.merge({ status: 'error', loading: false, error: err })
          throw err
        })
    },
    [serviceName, method, action]
  )
}

export function hashObject(obj) {
  let hash = 0
  const str = JSON.stringify(obj)

  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i)
    hash = (hash << 5) - hash + char
    hash = hash & hash // Convert to 32bit integer
  }

  return hash
}

/**
 * An internal hook that will listen to realtime updates to a service
 * and update the cache as changes happen.
 */
export function useRealtime(
  serviceName: keyof ServiceTypes,
  refetch: (data: any, eventType: 'created' | 'updated' | 'patched' | 'removed') => void
) {
  useLayoutEffect(() => {
    const service = API.instance.service(serviceName)

    const handleCreated = (data: any) => refetch(data, 'created')
    const handleUpdated = (data: any) => refetch(data, 'updated')
    const handlePatched = (data: any) => refetch(data, 'patched')
    const handleRemoved = (data: any) => refetch(data, 'removed')

    service.on('created', handleCreated)
    service.on('updated', handleUpdated)
    service.on('patched', handlePatched)
    service.on('removed', handleRemoved)

    return () => {
      service.off('created', handleCreated)
      service.off('updated', handleUpdated)
      service.off('patched', handlePatched)
      service.off('removed', handleRemoved)
    }
  }, [serviceName])
}

export type FeathersOrder = -1 | 0 | 1

type PaginationProps = {
  $skip: number
  $limit: number
  $sort: Record<string, FeathersOrder>
}

function resetPaginationProps(defaultProps: Partial<PaginationProps>) {
  return {
    $skip: defaultProps.$skip ?? 0,
    $limit: defaultProps.$limit ?? 10,
    $sort: defaultProps.$sort ?? {}
  } as PaginationProps
}

export function usePaginate(defaultProps = {} as Partial<PaginationProps>) {
  const store = useHookstate(resetPaginationProps(defaultProps))

  const query = store.get(NO_PROXY)
  const storedPagination = useHookstate({ stored: false, query })

  const setSort = (sort: Record<string, FeathersOrder>) => {
    store.$sort.set(sort)
  }

  const setLimit = (limit: number) => {
    store.$limit.set(limit)
  }

  const setPage = (page: number) => {
    store.$skip.set(page * store.$limit.value)
  }

  const reset = () => {
    store.set(resetPaginationProps(defaultProps))
  }

  const _storePagination = () => {
    if (storedPagination.stored.value) return
    storedPagination.set({ stored: true, query: structuredClone(query) })
    reset()
  }

  const _restorePagination = () => {
    if (!storedPagination.stored.value) return
    store.set(structuredClone(storedPagination.get(NO_PROXY).query))
    storedPagination.merge({ stored: false })
  }

  const search = (searchQuery?: object) => {
    if (searchQuery) {
      _storePagination()
      store.merge(searchQuery)
    } else {
      _restorePagination()
    }
  }

  return {
    query,
    page: Math.floor(store.$skip.value / store.$limit.value),
    setSort,
    setLimit,
    setPage,
    search
  }
}

/**
 * Mutates the query object to store the pagination props, and add the search query
 */
export const useSearch = (query: ReturnType<typeof useFind>, searchQuery: object, active: any) => {
  useEffect(() => {
    if (active) {
      query.search(searchQuery)
    } else {
      query.search()
    }
  }, [active])
}
