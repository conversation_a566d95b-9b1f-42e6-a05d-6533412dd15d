/*
CPAL-1.0 License

The contents of this file are subject to the Common Public Attribution License
Version 1.0. (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at
https://github.com/ir-engine/ir-engine/blob/dev/LICENSE.
The License is based on the Mozilla Public License Version 1.1, but Sections 14
and 15 have been added to cover use of software over a computer network and 
provide for limited attribution for the Original Developer. In addition, 
Exhibit A has been modified to be consistent with Exhibit B.

Software distributed under the License is distributed on an "AS IS" basis,
WITHOUT WARRANTY OF ANY KIND, either express or implied. See the License for the
specific language governing rights and limitations under the License.

The Original Code is Infinite Reality Engine.

The Original Developer is the Initial Developer. The Initial Developer of the
Original Code is the Infinite Reality Engine team.

All portions of the code written by the Infinite Reality Engine team are Copyright © 2021-2025
Infinite Reality Engine. All Rights Reserved.
*/
import { isApple } from '@ir-engine/common/src/utils/getDeviceStats'
import { Entity, EntityTreeComponent, getComponent, getOptionalComponent } from '@ir-engine/ecs'
import { GLTFComponent } from '@ir-engine/engine/src/gltf/GLTFComponent'
import { getState } from '@ir-engine/hyperflux'
import { NameComponent } from '@ir-engine/spatial/src/common/NameComponent'
import { EditorState } from '../services/EditorServices'

export function isEntityGlb(entity: Entity): boolean {
  const gltfComponent = getOptionalComponent(entity, GLTFComponent)
  if (!gltfComponent) return false
  const trimmedFilename = gltfComponent?.src.split('?')[0]
  return trimmedFilename !== undefined && trimmedFilename.endsWith('.glb')
}

export function getStepSize(event, smallStep, mediumStep, largeStep) {
  if (event.altKey) {
    return smallStep
  } else if (event.shiftKey) {
    return largeStep
  }
  return mediumStep
}

export const cmdOrCtrlString = isApple() ? 'meta' : 'ctrl'

export const getIncreamentedName = (requestedName: string, parentEntity = getState(EditorState).rootEntity) => {
  const siblings = getComponent(parentEntity, EntityTreeComponent).children
  let counter = 0
  siblings.forEach((child) => {
    const name = getComponent(child, NameComponent)
    const match = name.match(/^(.*?)\s+(\d+)?$/)
    const adjustedName = match ? match[1] : name
    if (adjustedName === requestedName || name === requestedName) {
      counter++
    }
  })
  if (counter > 0) {
    requestedName = `${requestedName} ${counter}`
  }
  return requestedName
}
