/*
CPAL-1.0 License

The contents of this file are subject to the Common Public Attribution License
Version 1.0. (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at
https://github.com/ir-engine/ir-engine/blob/dev/LICENSE.
The License is based on the Mozilla Public License Version 1.1, but Sections 14
and 15 have been added to cover use of software over a computer network and 
provide for limited attribution for the Original Developer. In addition, 
Exhibit A has been modified to be consistent with Exhibit B.

Software distributed under the License is distributed on an "AS IS" basis,
WITHOUT WARRANTY OF ANY KIND, either express or implied. See the License for the
specific language governing rights and limitations under the License.

The Original Code is Infinite Reality Engine.

The Original Developer is the Initial Developer. The Initial Developer of the
Original Code is the Infinite Reality Engine team.

All portions of the code written by the Infinite Reality Engine team are Copyright © 2021-2025
Infinite Reality Engine. All Rights Reserved.
*/

import { describe } from 'vitest'

// import { AuthState, AuthAction, avatarFetchedReceptor } from '../../src/user/services/AuthService' // make browser globals defined

// import { createEngine } from '@ir-engine/ecs/src/Engine'
// import { getMutableState } from '@ir-engine/hyperflux
// import { Downgraded } from '@ir-engine/hyperflux'

// ;(globalThis as any).document = {}
// ;(globalThis as any).navigator = {}
// ;(globalThis as any).window = {}

describe('Auth Service', () => {
  // beforeEach(() => {
  //   createEngine()
  // })
  // describe('Auth Receptors', () => {
  //   it('avatarFetchedReceptor', () => {
  //     // mock
  //     const mockAuthState = getMutableState(AuthState)
  //     const mockData = {
  //       id: 'c7456310-48d5-11ec-8706-c7fb367d91f0',
  //       key: 'avatars/assets/CyberbotGreen.glb',
  //       name: 'CyberbotGreen',
  //       url: 'https://host.name/avatars/assets/CyberbotGreen.glb',
  //       userId: null
  //     } as any
  //     const mockAction = AuthAction.updateAvatarListAction({ avatarList: [mockData] })
  //     // logic
  //     avatarFetchedReceptor(mockAuthState, mockAction)
  //     const dataResult = mockAuthState.attach(Downgraded).value
  //     // test
  //     assert.equal(mockAuthState.avatarList.length, 1)
  //     assert.deepStrictEqual(dataResult.avatarList[0], { avatar: mockData }) // Fails...
  //   })
  // })
})
