/**
 * DL-Engine API Gateway 健康检查服务
 */

import { Injectable } from '@nestjs/common'
import { LoadBalancerService } from '../load-balancer/load-balancer.service'
import { ConfigService } from '../config/config.service'

@Injectable()
export class HealthService {
  constructor(
    private readonly loadBalancerService: LoadBalancerService,
    private readonly configService: ConfigService
  ) {}

  async getHealth() {
    const servicesStatus = this.loadBalancerService.getAllServicesStatus()
    const totalServices = Object.keys(servicesStatus).length
    const healthyServices = Object.values(servicesStatus).filter(
      (service: any) => service.healthyInstances > 0
    ).length

    return {
      status: healthyServices === totalServices ? 'healthy' : 'degraded',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      version: '1.0.0',
      services: {
        total: totalServices,
        healthy: healthyServices,
        degraded: totalServices - healthyServices
      }
    }
  }

  async getDetailedHealth() {
    const servicesStatus = this.loadBalancerService.getAllServicesStatus()
    const basicHealth = await this.getHealth()

    return {
      ...basicHealth,
      services: servicesStatus,
      system: {
        memory: process.memoryUsage(),
        cpu: process.cpuUsage(),
        platform: process.platform,
        nodeVersion: process.version
      },
      configuration: {
        environment: this.configService.getEnvironment(),
        loadBalancingStrategy: this.loadBalancerService.getStrategy()
      }
    }
  }
}
