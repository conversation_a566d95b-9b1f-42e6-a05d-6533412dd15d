{"printWidth": 120, "tabWidth": 2, "useTabs": false, "semi": false, "singleQuote": true, "quoteProps": "as-needed", "jsxSingleQuote": false, "trailingComma": "none", "bracketSpacing": true, "bracketSameLine": false, "arrowParens": "always", "proseWrap": "never", "endOfLine": "lf", "importOrder": ["<THIRD_PARTY_MODULES>", "", "^@ir-engine/(.*)$", "", "^[./]"], "plugins": ["prettier-plugin-organize-imports", "@ianvs/prettier-plugin-sort-imports", "prettier-plugin-tailwindcss"], "tailwindFunctions": ["twMerge"]}