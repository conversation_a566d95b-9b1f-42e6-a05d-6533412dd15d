# etherealengine-matchmaking

##Start minikube:
First, you'll need to start minikube with the `ingress` addon. This will allow the Open Match Helm chart to install
an ingress that will route incoming traffic to Open Match's frontend service over a configured hostname.
```bash
minikube start --addons ingress
```

If minikube has already been created, you can enable the ingress by running `minikube addons enable ingress`

## Install Helm
Follow the [instructions](https://helm.sh/docs/intro/install/) for installing Helm on your OS.

## Install Open Match with local open-match Helm chart
A Helm chart containing all of the Open Match resources needed for Ethereal Engine matchmaking is located
in `packages/ops/open-match`. You can install it by running
`helm install --set frontend.host=<hostname> open-match packages/ops/open-match`

`<hostname>` is a hostname that the frontend service will be reachable at, e.g. `local-matchmaking.etherealengine.io`.
You can set it to be whatever you want. After you do, you'll need to edit your computer's hostfile to point this
hostname to the IP address of minikube.

### Point hostname in hostfile to minikube's IP
Run `minikube ip`. This will give you minikube's IP address.
Next, edit the file `hosts` in the folder `/etc` (or whatever your hostfile is called if not on Linux); you may need to execute `sudo`
privileges to edit it. You'll need to add a line like this:

`<minikube ip>  <hostname>`, e.g. 

`**************   local-matchmaking.etherealengine.io`

Make sure to save the file after you've added this line. This will tell your machine to route traffic to that hostname
to minikube. When combined with the Ingress that the Open Match Helm chart installs, this will then route the traffic
to the frontend service. Be aware that the Ingress is only routing traffic coming in on the path /v1/frontendservice.

* note: sometimes on localhost redis synchronisation is not working correctly,
it's usually leads to "Ticket not found" on getting ticket assignment even if ticket was just created. 
if this happens then run `kubectl scale -n open-match statefulset open-match-redis-node --replicas=1` to limit redis pod to one.  

## Build director and matchfunction Docker images
NOTE: If you are building and testing this locally, you can skip to the section "Build and deploy locally in one command".
This section and the couple after it lay out in more detail what options there are for building and deploying,
primarily for deploying to a non-local cluster. They are both still valid for local deployment to minikube if you want 
to do things more manually, though.

Navigate to packages/matchmaking and build the director and matchfunction images.
```bash
cd packages/matchmaking/

#this line for localhost development
eval $(minikube docker-env)

./build-all-pods.sh
```

This will build the images with no registry by default, e.g. the image names will just be `etherealengine-matchmaking-director`
and `etherealengine-matchmaking-matchfunction`. If you wish to build them to a registry,
set the `REGISTRY` environment variable, e.g. `REGISTRY=example-reg ./build-all-pods.sh`

If you want to automatically push the images to the registry they've been built towards, add `push` as a parameter:
`REGISTRY=example-reg ./build-all-pods.sh push` Otherwise, you'll have to manually push the images to the right repo.
If you are building this locally on minikube, you do not need to push the images anywhere; running `eval $(minikube docker-env)`
builds the images in minikube's Docker environment, which means it already has access to those images and does not
need to source them from an external Docker registry.

Each of the build scripts will generate a timestamp and use that as the tag for the images that are built. For example,
an image would be generated with a full name of `etherealengine-matchmaking-director:23-12-21T12-55-03`.
You can see the tags in the build logs, where they're printed out like the following:
`BUILDING matchfunction as etherealengine-matchmaking-matchfunction:23-12-21T14-16-09`

## Install matchmaking via Helm chart
You'll next install the matchmaking deployment using the Helm chart in packages/ops/etherealengine-matchmaking. Make sure that 
the path at the end of the following command is relative to the directory you're still in; it was written assuming
you're in packages/matchmaking after just building the images, and will be different if you're in the repo root or
some other directory.

The file referenced below as path/to/matchmaking.values.yaml is a simple configuration file. There is a template for it in
`packages/ops/configs/local.matchmaking.template.values.yaml`. You should make a copy of this template and remove the 
word 'template' from the file name. If you are going to push these images to an external registry, i.e. you're not just
building them locally for minikube, then you'll need to add `<REGISTRY>/` to the start of both `repository`settings.
For example, if pushing the image to the Docker Hub repo `etherealengine-test`, you would make director.image.repository
`etherealengine-test/etherealengine-matchmaking-director` and matchfunction.image.repository `etherealengine-test/etherealengine-matchmaking-matchfunction`

If you are installing this on minikube, you don't need to specify a registry. By running `eval $(minikube dockerenv)`, 
you're building the images into minikube's Docker environment.

When the values.yaml/Helm config file is ready, run the following command:
```
helm install -f path/to/matchmaking.yaml --set director.image.tag=<TAG>,matchfunction.image.tag=<TAG> <release>-matchmaking ../ops/etherealengine-matchmaking
```
where `<TAG>` is the timestamp tag that can be found on in the builder logs.

`<release>` should be `local` if running on minikube, and `dev`/`prod` if deploying this to a dev/prod production
environment.

After 30 seconds or so, the matchmaking services should be running.

## Updating director or matchfunction
When changes to the director or matchfunction services are made, you'll need to re-build them and re-deploy them.

Navigate to `packages/matchmaking`. You can run `RELEASE=<RELEASE> ./build-all-pods.sh <push>` if you want to rebuild both.
If you only want to rebuild one, run `RELEASE=<RELEASE> ./build-pod.sh <director/matchfunction> <push>`. `push` is only
needed if you are pushing to an external registry, and not needed if you're building to minikube's Docker environment.

Once the new image(s) have been built (and optionally pushed), run 
`helm upgrade --reuse-values --set director.image.tag=<TAG>,matchfunction.image.tag=<TAG> <release>-matchmaking ../ops/etherealengine-matchmaking`
This will update the matchmaking deployment with the newly timestamp-tagged images. If you only updated and rebuilt
one of the services, and did so by just running `build-pod.sh` instead of `build-all-pods.sh`, then you must omit the
`--set <service>.image.tag=<TAG>` for the service that didn't get rebuilt, since it will not have a new image tag.
For example, if you just updated the matchfunction service, run
`helm upgrade --reuse-values --set matchfunction.image.tag=<TAG> <release>-matchmaking ../ops/etherealengine-matchmaking`

## Build and deploy locally in one command
The script `matchmaking/build-all-and-refresh-pods-local.sh` can build and deploy the director and matchfunction locally
in one go. Just run

`HELM_CONFIG=<path/to/matchfunction.values.yaml> ./build-all-and-refresh-pods-local.sh`
where `<path/to/matchfunction.values.yaml>` is an absolute or relative path to the Helm config file, e.g.
`HELM_CONFIG="/home/<USER>/Documents/matchmaking.values.yaml" ./build-all-and-refresh-pods-local.sh`
This will build the two services, timestamp-tag them, then automatically run `helm upgrade --install` with the
timestamp-tags and the config file, which will install the deployment if it's not already installed and update the tag
if it is, forcing the deployment to download the new versions of the services.

## Uninstall Open Match and etherealengine-matchmaking
To uninstall etherealengine-matchmaing, run `helm uninstall <release>-matchmaking`
To uninstall Open Match, from `helm uninstall open-match`



# misc

===============================================================

Detailed instructions on open match: [Install Core Open Match](https://open-match.dev/site/docs/installation/yaml/#install-core-open-match)

###hardcoded
game modes hardcoded:
`open-match-custom-pods/director/profile.go:24`

team size is probably here:
`open-match-custom-pods/matchfunction/mmf/matchfunction.go` in `ticketsPerPoolPerMatch`

###cleanup
delete custom pods
```bash
helm uninstall local-matchmaking
```

delete open-match
```bash
helm uninstall open-match
```