/*
CPAL-1.0 License

The contents of this file are subject to the Common Public Attribution License
Version 1.0. (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at
https://github.com/ir-engine/ir-engine/blob/dev/LICENSE.
The License is based on the Mozilla Public License Version 1.1, but Sections 14
and 15 have been added to cover use of software over a computer network and
provide for limited attribution for the Original Developer. In addition,
Exhibit A has been modified to be consistent with Exhibit B.

Software distributed under the License is distributed on an "AS IS" basis,
WITHOUT WARRANTY OF ANY KIND, either express or implied. See the License for the
specific language governing rights and limitations under the License.

The Original Code is Infinite Reality Engine.

The Original Developer is the Initial Developer. The Initial Developer of the
Original Code is the Infinite Reality Engine team.

All portions of the code written by the Infinite Reality Engine team are Copyright © 2021-2025
Infinite Reality Engine. All Rights Reserved.
*/

import React, { forwardRef, Ref, SVGProps } from 'react'

const Icon = (props: SVGProps<SVGSVGElement>, ref: Ref<SVGSVGElement>) => (
  <svg
    width="1em"
    height="1em"
    viewBox="0 0 22 23"
    xmlns="http://www.w3.org/2000/svg"
    role="img"
    fill="none"
    stroke="none"
    ref={ref}
    {...props}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M4.47039 8.23333C4.10672 8.79903 3.84407 9.41187 3.69592 10.0516C3.82387 10.1459 3.93836 10.2604 4.03264 10.3951C4.23468 10.6847 4.32896 11.0551 4.25488 11.4322L4.05285 12.5906L6.53115 7.08846C6.69278 6.73826 6.97563 6.48909 7.30562 6.36113C7.575 6.26011 7.87805 6.23991 8.16764 6.32072C8.18784 6.1995 8.18784 6.07155 8.16764 5.95033C8.1205 5.69441 7.97234 5.45197 7.73663 5.29034C7.50765 5.13545 7.23154 5.08157 6.96889 5.12871C6.71298 5.17586 6.47054 5.32401 6.30891 5.55972L4.47712 8.23333H4.47039ZM18.6533 2.71776C18.8823 2.52919 19.2257 2.55613 19.421 2.7851C19.6096 3.01407 19.5826 3.35753 19.3537 3.5461L17.8182 4.83913C17.5892 5.0277 17.2458 5.00076 17.0572 4.77178C16.8619 4.54281 16.8956 4.19935 17.1245 4.01078L18.6533 2.71776ZM18.1684 7.29723C17.8653 7.27029 17.6431 7.00764 17.67 6.71133C17.697 6.40827 17.9596 6.18603 18.2559 6.21297L20.2561 6.38133C20.5524 6.40827 20.7814 6.67092 20.7545 6.97397C20.7275 7.27029 20.4649 7.49253 20.1686 7.46559L18.1684 7.29723ZM14.9291 1.24963C14.956 0.953311 15.2187 0.731072 15.5217 0.751275C15.8181 0.778213 16.0403 1.04086 16.0134 1.33718L15.845 3.33733C15.8181 3.64038 15.5554 3.86262 15.2591 3.83569C14.956 3.80875 14.7338 3.5461 14.7607 3.24978L14.9291 1.24963ZM14.7001 12.4963C14.8146 12.2404 14.8146 11.9575 14.7271 11.7083C14.6328 11.4659 14.4442 11.2504 14.1883 11.1359C13.9324 11.0214 13.6495 11.0214 13.4004 11.1157C13.1579 11.21 12.9424 11.3918 12.8279 11.6477L11.4271 14.7523C11.3867 14.8399 11.279 14.8803 11.1914 14.8399C11.0972 14.7995 11.0567 14.6917 11.0972 14.6042L12.4979 11.4996L13.1444 10.0719C13.2589 9.80921 13.2589 9.53309 13.1647 9.28391C13.0771 9.03473 12.8885 8.82596 12.6259 8.71148C12.37 8.59699 12.0939 8.59699 11.8447 8.68454C11.5955 8.77882 11.3867 8.96739 11.2723 9.2233L9.22496 13.7556C9.18455 13.8499 9.0768 13.8903 8.98925 13.8499C8.89497 13.8095 8.85456 13.7018 8.89497 13.6075L10.9423 9.07514L11.1376 8.6374C11.252 8.38149 11.252 8.09864 11.1578 7.85619C11.0635 7.60702 10.8816 7.39825 10.619 7.28376C10.3631 7.16254 10.0802 7.16254 9.8378 7.25682C9.58862 7.3511 9.37985 7.53967 9.26536 7.79558L9.06333 8.23333L7.02277 12.7657C6.98236 12.8532 6.87461 12.8936 6.78033 12.8532C6.69278 12.8128 6.65237 12.7051 6.69278 12.6175L8.73334 8.07843C8.85456 7.82252 8.85456 7.53967 8.76028 7.29723C8.66599 7.04805 8.47743 6.83928 8.22151 6.72479C7.9656 6.60357 7.68275 6.60357 7.43358 6.69786C7.19113 6.79214 6.98236 6.98071 6.86114 7.23662L3.77673 14.0789C3.73632 14.1732 3.62857 14.2136 3.53429 14.1732C3.45347 14.1328 3.41307 14.0452 3.43327 13.9644L3.90469 11.3649C3.95183 11.0888 3.88448 10.8126 3.73632 10.5971C3.58143 10.3816 3.35245 10.2267 3.06961 10.1729C2.79349 10.1257 2.51737 10.1931 2.30187 10.3412C2.08637 10.4894 1.93147 10.7251 1.8776 11.0012L1.30516 14.1934V14.2001C1.11659 15.4325 1.3321 16.6784 1.88433 17.7694C2.47023 18.9075 3.43327 19.8706 4.68589 20.4363C6.15402 21.103 7.76357 21.103 9.17108 20.571C10.5786 20.0389 11.7773 18.9749 12.4441 17.5068L13.4206 15.3315L14.7001 12.4963ZM16.0268 10.651L15.0368 11.5198C15.0503 11.54 15.0571 11.5602 15.0638 11.5804C15.1917 11.9171 15.1917 12.2942 15.0301 12.6444L13.8179 15.345L15.0503 14.2473L17.3872 12.1932C17.596 12.0047 17.7105 11.7487 17.7307 11.4861C17.7441 11.2235 17.6633 10.9541 17.4747 10.7386C17.2862 10.5298 17.0303 10.4153 16.7676 10.3951C16.505 10.3816 16.2356 10.4625 16.0268 10.651ZM15.6025 7.79558L13.5957 9.5735C13.6091 9.789 13.5687 10.0112 13.4744 10.22L13.2118 10.7992C13.232 10.7924 13.2522 10.779 13.2724 10.7722C13.6091 10.651 13.9863 10.651 14.3365 10.8059C14.5452 10.9002 14.7136 11.0349 14.8483 11.2033L15.7844 10.3749L16.9629 9.33779C17.1717 9.14922 17.2929 8.89331 17.3064 8.63066C17.3199 8.36802 17.239 8.09864 17.0505 7.88987C16.8686 7.67436 16.606 7.55988 16.3433 7.54641C16.0807 7.5262 15.8113 7.61375 15.6025 7.79558ZM11.5888 8.11884C11.5955 8.21312 11.5888 8.31414 11.5753 8.40843C11.6224 8.38822 11.6696 8.36802 11.7167 8.34781C12.0467 8.21986 12.4306 8.21986 12.7808 8.38149C13.1242 8.53638 13.3802 8.81923 13.5081 9.15596V9.16269L15.3601 7.5262L15.717 7.20968C15.9325 7.02111 16.047 6.7652 16.0605 6.50255C16.0807 6.23991 15.9999 5.97053 15.8113 5.76176C15.6228 5.54625 15.3668 5.43177 15.1042 5.4183C14.8415 5.39809 14.5722 5.48564 14.3567 5.66748L11.5888 8.11884ZM9.70984 6.9201C10.0398 6.79214 10.4237 6.79214 10.7739 6.95377C11.1174 7.10866 11.3665 7.38478 11.4945 7.71477L13.764 5.71462C13.9728 5.52605 14.0873 5.27014 14.1075 5.00749C14.121 4.74485 14.0401 4.47547 13.8516 4.2667C13.663 4.05119 13.4071 3.9367 13.1444 3.9165C12.8818 3.90303 12.6124 3.98384 12.4037 4.17241L9.0768 7.10866C9.08353 7.12887 9.09027 7.14907 9.097 7.16927C9.11721 7.21641 9.13067 7.26356 9.14414 7.3107C9.29904 7.1356 9.49434 7.00091 9.70984 6.9201Z"
      fill="#F7F8FA"
    />
    <path
      d="M2.85351 18.2598C2.88862 18.3402 2.90013 18.4763 2.87655 18.5318L2.16893 20.1989L7.09167 22.2885L7.63418 21.0104C7.68135 20.8993 7.76363 20.8686 7.84591 20.838L2.85351 18.2598Z"
      fill="#F7F8FA"
    />
  </svg>
)

const ForwardRef = forwardRef(Icon)
export default ForwardRef
