/**
 * DL-Engine API Gateway 健康检查模块
 */

import { Module } from '@nestjs/common'
import { HealthController } from './health.controller'
import { HealthService } from './health.service'
import { LoadBalancerModule } from '../load-balancer/load-balancer.module'

@Module({
  imports: [LoadBalancerModule],
  controllers: [HealthController],
  providers: [HealthService],
  exports: [HealthService]
})
export class HealthModule {}
