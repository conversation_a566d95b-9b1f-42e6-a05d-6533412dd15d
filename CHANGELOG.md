# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.



**Note:** Version bump only for package etherealengine







**Note:** Version bump only for package etherealengine







**Note:** Version bump only for package etherealengine







**Note:** Version bump only for package etherealengine







**Note:** Version bump only for package etherealengine







**Note:** Version bump only for package etherealengine







**Note:** Version bump only for package etherealengine







**Note:** Version bump only for package xrengine







**Note:** Version bump only for package xrengine







**Note:** Version bump only for package xrengine







**Note:** Version bump only for package xrengine







**Note:** Version bump only for package xrengine







**Note:** Version bump only for package xrengine







**Note:** Version bump only for package xrengine







**Note:** Version bump only for package xrengine







**Note:** Version bump only for package xrengine







**Note:** Version bump only for package xrengine







**Note:** Version bump only for package xrengine







**Note:** Version bump only for package xrengine







**Note:** Version bump only for package xrengine







**Note:** Version bump only for package xrengine







**Note:** Version bump only for package xrengine







**Note:** Version bump only for package xrengine







**Note:** Version bump only for package xrengine







**Note:** Version bump only for package xrengine







**Note:** Version bump only for package xrengine





## [0.5.2](https://github.com/xrfoundation/XREngine/compare/v0.5.1...v0.5.2) (2022-04-07)


### Reverts

* Revert "Fix some interactive system errors while scene is loading" ([126fef5](https://github.com/xrfoundation/XREngine/commit/126fef5e884e4c4cf7087ad0c49d7ba0b90ddca0))







**Note:** Version bump only for package xrengine







**Note:** Version bump only for package xrengine







**Note:** Version bump only for package xrengine







**Note:** Version bump only for package xrengine







**Note:** Version bump only for package xrengine







**Note:** Version bump only for package xrengine







**Note:** Version bump only for package xrengine







**Note:** Version bump only for package xrengine







**Note:** Version bump only for package xrengine







**Note:** Version bump only for package xrengine







**Note:** Version bump only for package xrengine







**Note:** Version bump only for package xrengine







**Note:** Version bump only for package xrengine







**Note:** Version bump only for package xrengine







**Note:** Version bump only for package xrengine







**Note:** Version bump only for package xrengine







**Note:** Version bump only for package xrengine







**Note:** Version bump only for package xrengine







**Note:** Version bump only for package xrengine







**Note:** Version bump only for package xrengine







**Note:** Version bump only for package xrengine







**Note:** Version bump only for package xrengine







**Note:** Version bump only for package xrengine







**Note:** Version bump only for package xrengine







**Note:** Version bump only for package xrengine







**Note:** Version bump only for package xrengine







**Note:** Version bump only for package xrengine







**Note:** Version bump only for package xrengine







**Note:** Version bump only for package xrengine







**Note:** Version bump only for package xrengine





## 0.2.36 (2021-06-25)

**Note:** Version bump only for package xrengine





## 0.2.35 (2021-06-25)

**Note:** Version bump only for package xrengine





## 0.2.34 (2021-06-25)

**Note:** Version bump only for package xrengine





## 0.2.33 (2021-06-25)

**Note:** Version bump only for package xrengine





## 0.2.32 (2021-06-25)

**Note:** Version bump only for package xrengine





## 0.2.31 (2021-06-25)

**Note:** Version bump only for package xrengine





## 0.2.30 (2021-06-25)

**Note:** Version bump only for package xrengine





## [0.2.29](https://github.com/barankyle/xr3ngine/compare/v0.2.28...v0.2.29) (2021-06-24)

**Note:** Version bump only for package xrengine





## [0.2.28](https://github.com/barankyle/xr3ngine/compare/v0.2.27...v0.2.28) (2021-06-22)

**Note:** Version bump only for package xrengine





## [0.2.27](https://github.com/barankyle/xrengine/compare/v0.2.26...v0.2.27) (2021-05-13)

**Note:** Version bump only for package xrengine





## [0.2.26](https://github.com/barankyle/xrengine/compare/v0.2.24...v0.2.26) (2021-05-12)

**Note:** Version bump only for package xrengine





## [0.2.25](https://github.com/barankyle/xrengine/compare/v0.2.24...v0.2.25) (2021-05-12)

**Note:** Version bump only for package xrengine





## [0.2.24](https://github.com/barankyle/xrengine/compare/v0.2.23...v0.2.24) (2021-05-12)

**Note:** Version bump only for package xrengine





## [0.2.23](https://github.com/barankyle/xrengine/compare/v0.2.22...v0.2.23) (2021-05-12)

**Note:** Version bump only for package xrengine





## [0.2.22](https://github.com/xrengine/xrengine/compare/v0.2.21...v0.2.22) (2021-05-05)

**Note:** Version bump only for package xrengine





## [0.2.21](https://github.com/barankyle/xrengine/compare/v0.2.20...v0.2.21) (2021-05-05)

**Note:** Version bump only for package xrengine





## [0.2.20](https://github.com/barankyle/xrengine/compare/v0.2.18...v0.2.20) (2021-05-04)


### Bug Fixes

* **deps:** pin dependencies ([ac0be70](https://github.com/barankyle/xrengine/commit/ac0be70b9194c3809e74ba8875529c091d084014))
* **deps:** pin dependency terser-webpack-plugin to 5.1.1 ([0b8aa13](https://github.com/barankyle/xrengine/commit/0b8aa13533bd9e0515af589b5fce1203939f16f2))
* **deps:** update capacitor monorepo to v2.4.7 ([9793e4e](https://github.com/barankyle/xrengine/commit/9793e4e8608bcd34b52e9dcfd81c853151662b7e))
* **deps:** update dependency @feathersjs/hooks to v0.6.4 ([df63c37](https://github.com/barankyle/xrengine/commit/df63c37dcf4eb61a8e9ed4bdcfa2053d60164d8b))
* **deps:** update dependency @material-ui/data-grid to v4.0.0-alpha.25 ([0b31f6f](https://github.com/barankyle/xrengine/commit/0b31f6f31de9dfe90c9b384e5aad1126bd483b40))
* **deps:** update dependency aws-sdk to v2.885.0 ([b99dbc0](https://github.com/barankyle/xrengine/commit/b99dbc0a7ba9aa44ae49c88bd89dc1161a25a7e1))
* **deps:** update dependency aws-sdk to v2.886.0 ([b37bc42](https://github.com/barankyle/xrengine/commit/b37bc42fd77d765bc1a947ff097cef2360e3bbac))
* **deps:** update dependency aws-sdk to v2.887.0 ([1fc3094](https://github.com/barankyle/xrengine/commit/1fc3094cd008466281f8410ebf0b69e3c23ba4c9))
* **deps:** update dependency aws-sdk to v2.888.0 ([7e040cb](https://github.com/barankyle/xrengine/commit/7e040cbe484c74602def81e26f28cb55264ed177))
* **deps:** update dependency aws-sdk to v2.892.0 ([06db15c](https://github.com/barankyle/xrengine/commit/06db15cc17ade5417feafeda07fc3ee77fbe3d3f))
* **deps:** update dependency concurrently to v6.0.2 ([2ed83cb](https://github.com/barankyle/xrengine/commit/2ed83cb943881bf0d9bc850ec5541f8983047c41))
* **deps:** update dependency feathers-blob to v2.4.0 ([40afa5e](https://github.com/barankyle/xrengine/commit/40afa5e33767fc9d1c1d8baad7820028478a71cc))
* **deps:** update dependency js-yaml to v4.1.0 ([cf84acd](https://github.com/barankyle/xrengine/commit/cf84acd3dbfc606c5be67d26abbb445270eb8536))
* **deps:** update dependency react-apexcharts to v1.3.9 ([a5461a6](https://github.com/barankyle/xrengine/commit/a5461a66b727f8d5cbf9480ad1142dd56642433e))
* **deps:** update dependency react-i18next to v11.8.13 ([8fd41b7](https://github.com/barankyle/xrengine/commit/8fd41b7bb47a07fadc9558cab7dea60d1d2031f9))
* **deps:** update dependency react-modal to v3.13.1 ([293901c](https://github.com/barankyle/xrengine/commit/293901c94afa9dc883d17ffd22e9b3577dab88d6))
* **deps:** update dependency react-redux to v7.2.4 ([8bc2601](https://github.com/barankyle/xrengine/commit/8bc26013abb25ce0c07a96006d7d03d9e4d84665))
* **deps:** update dependency redux to v4.1.0 ([fa7ccd9](https://github.com/barankyle/xrengine/commit/fa7ccd9e2fce1df39a8537c2ba93f5e0d77834b1))
* **deps:** update dependency url-toolkit to v2.2.2 ([9d5aa72](https://github.com/barankyle/xrengine/commit/9d5aa72ab4ad254eb60d08822a90c7dd22a9a5fd))
* **deps:** update dependency vite to v2.2.2 ([9141bf3](https://github.com/barankyle/xrengine/commit/9141bf38bfc5ae48ae5489916a6485baeb6194cb))
* **deps:** update docusaurus monorepo to v2.0.0-alpha.ffe8b6106 ([62cf058](https://github.com/barankyle/xrengine/commit/62cf0584d6a0d4ea7339b50380e2425b38d1c38c))
* **deps:** update nivo monorepo to v0.68.0 ([e4e44b8](https://github.com/barankyle/xrengine/commit/e4e44b860e2c34e368f47c071f72c3f140abfb7b))
* clearEventQueues assignes new array ref, and breaks some event listeners that are using that references ([6195fa3](https://github.com/barankyle/xrengine/commit/6195fa3b9d3e8d93db362730f3dcaf7703f9c09b))
* Dockerfile-dev to reduce vulnerabilities ([4ac9f77](https://github.com/barankyle/xrengine/commit/4ac9f776eac61b4e827496d913b6c39fdaeac9b1))
* missing style.scss ([8f94a8d](https://github.com/barankyle/xrengine/commit/8f94a8d1c55f619ab9c339aa25205b719598ef0f))
* removed "input/touchEvents" in "test" call ([cb2a2f2](https://github.com/barankyle/xrengine/commit/cb2a2f28f67b12ab0dca701a78351c832ee7fcbf))


### Reverts

* Revert "updates buffer size instead of recreating" ([4f2a31c](https://github.com/barankyle/xrengine/commit/4f2a31cf4ba032b76bd24a010ac6d8dcdc58d458))
* Revert "push the tag and latest" ([e73b192](https://github.com/barankyle/xrengine/commit/e73b1928144dc187ff44828d6447a4d66a9e33db))





## 0.2.18 (2021-04-22)


### Bug Fixes

* **deps:** pin dependency terser-webpack-plugin to 5.1.1 ([c8264fd](https://github.com/XRFoundation/XREngine/commit/c8264fd16ff4779b456f8f16bffbae6cc5396ccf))
* **deps:** update capacitor monorepo to v2.4.7 ([f97197f](https://github.com/XRFoundation/XREngine/commit/f97197f837ff50d5bb6fa3af8dc0b33627f576d5))
* **deps:** update dependency @feathersjs/hooks to v0.6.4 ([e4afbb4](https://github.com/XRFoundation/XREngine/commit/e4afbb4e1f3f085855393eea997453c6002aaedb))
* **deps:** update dependency @material-ui/data-grid to v4.0.0-alpha.25 ([242408b](https://github.com/XRFoundation/XREngine/commit/242408ba69153b303b233810c1d47b22251097b5))
* **deps:** update dependency aws-sdk to v2.885.0 ([db05194](https://github.com/XRFoundation/XREngine/commit/db05194e8e61a0d54af54cdbaa6e50fd3f4f8b72))
* **deps:** update dependency aws-sdk to v2.886.0 ([1ba75d0](https://github.com/XRFoundation/XREngine/commit/1ba75d008a82b37eaf57b60b7ce442dde92be8c5))
* **deps:** update dependency aws-sdk to v2.887.0 ([367d16a](https://github.com/XRFoundation/XREngine/commit/367d16a9a7c5eee2deee16ae7cf4df8a1697490d))
* **deps:** update dependency aws-sdk to v2.888.0 ([c20639e](https://github.com/XRFoundation/XREngine/commit/c20639e23a8946e0484be289ddd258ebc7da88f4))
* **deps:** update dependency concurrently to v6.0.2 ([d3db328](https://github.com/XRFoundation/XREngine/commit/d3db32874399d5a6ce3f157188cfb8ce7ff798c0))
* **deps:** update dependency feathers-blob to v2.4.0 ([ae7314c](https://github.com/XRFoundation/XREngine/commit/ae7314c2066bdf20181060c49f75680d97fb8300))
* **deps:** update dependency js-yaml to v4.1.0 ([90e9372](https://github.com/XRFoundation/XREngine/commit/90e937244efdb3470de266f34c40d5cfb504acb5))
* **deps:** update dependency react-apexcharts to v1.3.9 ([bf68837](https://github.com/XRFoundation/XREngine/commit/bf688372b3b78a0234d6b805d700f7f888ff07e9))
* **deps:** update dependency react-i18next to v11.8.13 ([309d94d](https://github.com/XRFoundation/XREngine/commit/309d94dc136028d50ac5f01efba62c61e5735e41))
* **deps:** update dependency react-modal to v3.13.1 ([279a21f](https://github.com/XRFoundation/XREngine/commit/279a21ff38bb067b634ff811ef0faf2197850800))
* **deps:** update docusaurus monorepo to v2.0.0-alpha.ffe8b6106 ([fdda629](https://github.com/XRFoundation/XREngine/commit/fdda629c5b7b2a04ad80fc46a90054f769e73f27))
* clearEventQueues assignes new array ref, and breaks some event listeners that are using that references ([f19058a](https://github.com/XRFoundation/XREngine/commit/f19058aa08e42d0836b1be1e5584d9ba72c053d3))
* Dockerfile-dev to reduce vulnerabilities ([42749c8](https://github.com/XRFoundation/XREngine/commit/42749c858dc954bf3d023f9bd07f67d3f6d2add0))
* missing style.scss ([22a556c](https://github.com/XRFoundation/XREngine/commit/22a556c1c92e1936e51dd8b116afbc9c03416f83))
* removed "input/touchEvents" in "test" call ([1e80b16](https://github.com/XRFoundation/XREngine/commit/1e80b16602beeacf6a2ed2d5768a05a971e07d6e))


### Reverts

* Revert "Removed mobx from harmony module." This reverts commit 1c36c1b605a444cde9935a5abb6eb3e06d39804d. ([9da17ff](https://github.com/XRFoundation/XREngine/commit/9da17ffef59540a26e73bf3c9ce004674f276ff1))
* Revert "-temporary set avatar (change avatar is BROKEN); - animation load - NOT READY;" ([f62a4ad](https://github.com/XRFoundation/XREngine/commit/f62a4ad131bbf1b27e96858fc4829cea6ec32044))
* Revert "updates buffer size instead of recreating" ([0bb8cff](https://github.com/XRFoundation/XREngine/commit/0bb8cff02b3403cce25335e856b752edf88c5145))
* Revert "push the tag and latest" ([9840e3c](https://github.com/XRFoundation/XREngine/commit/9840e3cd016ed38df1c588e7d2db04ebf8e327e1))





## [0.2.11](https://github.com/XRFoundation/XREngine/compare/v0.2.10...v0.2.11) (2021-04-08)

**Note:** Version bump only for package xrengine





## [0.2.10](https://github.com/XRFoundation/XREngine/compare/v0.2.9...v0.2.10) (2021-03-31)

**Note:** Version bump only for package xrengine





## [0.2.9](https://github.com/XRFoundation/XREngine/compare/v0.2.8...v0.2.9) (2021-03-31)

**Note:** Version bump only for package xrengine





## [0.2.7](https://github.com/XRFoundation/XREngine/compare/v0.2.6...v0.2.7) (2021-03-31)

**Note:** Version bump only for package xrengine





## [0.2.6](https://github.com/XRFoundation/XREngine/compare/v0.2.5...v0.2.6) (2021-03-31)

**Note:** Version bump only for package xrengine





## 0.2.4 (2021-03-31)


### Bug Fixes

* clearEventQueues assignes new array ref, and breaks some event listeners that are using that references ([f19058a](https://github.com/XRFoundation/XREngine/commit/f19058aa08e42d0836b1be1e5584d9ba72c053d3))
* missing style.scss ([22a556c](https://github.com/XRFoundation/XREngine/commit/22a556c1c92e1936e51dd8b116afbc9c03416f83))
* removed "input/touchEvents" in "test" call ([1e80b16](https://github.com/XRFoundation/XREngine/commit/1e80b16602beeacf6a2ed2d5768a05a971e07d6e))


### Reverts

* Revert "Removed mobx from harmony module." This reverts commit 1c36c1b605a444cde9935a5abb6eb3e06d39804d. ([9da17ff](https://github.com/XRFoundation/XREngine/commit/9da17ffef59540a26e73bf3c9ce004674f276ff1))
* Revert "-temporary set avatar (change avatar is BROKEN); - animation load - NOT READY;" ([f62a4ad](https://github.com/XRFoundation/XREngine/commit/f62a4ad131bbf1b27e96858fc4829cea6ec32044))
* Revert "updates buffer size instead of recreating" ([0bb8cff](https://github.com/XRFoundation/XREngine/commit/0bb8cff02b3403cce25335e856b752edf88c5145))
* Revert "push the tag and latest" ([9840e3c](https://github.com/XRFoundation/XREngine/commit/9840e3cd016ed38df1c588e7d2db04ebf8e327e1))





## 0.2.3 (2021-03-31)


### Bug Fixes

* clearEventQueues assignes new array ref, and breaks some event listeners that are using that references ([f19058a](https://github.com/XRFoundation/XREngine/commit/f19058aa08e42d0836b1be1e5584d9ba72c053d3))
* missing style.scss ([22a556c](https://github.com/XRFoundation/XREngine/commit/22a556c1c92e1936e51dd8b116afbc9c03416f83))
* removed "input/touchEvents" in "test" call ([1e80b16](https://github.com/XRFoundation/XREngine/commit/1e80b16602beeacf6a2ed2d5768a05a971e07d6e))


### Reverts

* Revert "Removed mobx from harmony module." This reverts commit 1c36c1b605a444cde9935a5abb6eb3e06d39804d. ([9da17ff](https://github.com/XRFoundation/XREngine/commit/9da17ffef59540a26e73bf3c9ce004674f276ff1))
* Revert "-temporary set avatar (change avatar is BROKEN); - animation load - NOT READY;" ([f62a4ad](https://github.com/XRFoundation/XREngine/commit/f62a4ad131bbf1b27e96858fc4829cea6ec32044))
* Revert "updates buffer size instead of recreating" ([0bb8cff](https://github.com/XRFoundation/XREngine/commit/0bb8cff02b3403cce25335e856b752edf88c5145))
* Revert "push the tag and latest" ([9840e3c](https://github.com/XRFoundation/XREngine/commit/9840e3cd016ed38df1c588e7d2db04ebf8e327e1))
