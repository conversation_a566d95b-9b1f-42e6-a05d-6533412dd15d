/*
CPAL-1.0 License

The contents of this file are subject to the Common Public Attribution License
Version 1.0. (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at
https://github.com/ir-engine/ir-engine/blob/dev/LICENSE.
The License is based on the Mozilla Public License Version 1.1, but Sections 14
and 15 have been added to cover use of software over a computer network and
provide for limited attribution for the Original Developer. In addition,
Exhibit A has been modified to be consistent with Exhibit B.

Software distributed under the License is distributed on an "AS IS" basis,
WITHOUT WARRANTY OF ANY KIND, either express or implied. See the License for the
specific language governing rights and limitations under the License.

The Original Code is Infinite Reality Engine.

The Original Developer is the Initial Developer. The Initial Developer of the
Original Code is the Infinite Reality Engine team.

All portions of the code written by the Infinite Reality Engine team are Copyright © 2021-2025
Infinite Reality Engine. All Rights Reserved.
*/

import { Engine, getComponent, getOptionalMutableComponent, hasComponent } from '@ir-engine/ecs'
import { getState, none, useMutableState } from '@ir-engine/hyperflux'
import { ReferenceSpaceState } from '@ir-engine/spatial'
import { destroySpatialViewer, initializeSpatialViewer } from '@ir-engine/spatial/src/initializeEngine'
import { useEffect, useRef } from 'react'
import { RendererComponent } from '../components/RendererComponent'

export const useEngineCanvas = (ref: React.RefObject<HTMLElement> | null) => {
  const canvasRef = useRef(document.getElementById('engine-renderer-canvas') as HTMLCanvasElement | null)

  useEffect(() => {
    if (!ref) return
    const parent = ref.current as HTMLElement
    if (!parent) return
    const canvas = (document.getElementById('engine-renderer-canvas') as HTMLCanvasElement) ?? canvasRef.current

    const originalParent = canvas.parentElement!
    canvas.hidden = false

    parent.appendChild(canvas)

    const observer = new ResizeObserver(() => {
      getComponent(getState(ReferenceSpaceState).viewerEntity, RendererComponent).needsResize = true
    })

    observer.observe(parent)

    return () => {
      observer.disconnect()
      const canvas = document.getElementById('engine-renderer-canvas') as HTMLCanvasElement
      if (!parent.contains(canvas)) return
      parent.removeChild(canvas)
      originalParent.appendChild(canvas)
      canvas.hidden = true
    }
  }, [ref?.current])

  useEffect(() => {
    const canvas = document.getElementById('engine-renderer-canvas') as HTMLCanvasElement
    canvasRef.current = canvas
    initializeSpatialViewer(canvas)
    return () => {
      if (!Engine.instance) return
      canvasRef.current = null
      destroySpatialViewer()
    }
  }, [])

  /**
   * Since the viewer and XR reference spaces can technically exist without the other,
   * we need to reactively update the core renderer's scenes
   */
  const { viewerEntity, originEntity, localFloorEntity } = useMutableState(ReferenceSpaceState).value

  useEffect(() => {
    if (!viewerEntity || !originEntity) return

    const rendererComponent = getOptionalMutableComponent(viewerEntity, RendererComponent)
    if (!rendererComponent) return

    rendererComponent.scenes.merge([originEntity])

    return () => {
      if (!Engine.instance) return
      if (!hasComponent(viewerEntity, RendererComponent)) return
      const index = rendererComponent.scenes.value.indexOf(originEntity)
      rendererComponent.scenes[index].set(none)
    }
  }, [viewerEntity, originEntity])

  useEffect(() => {
    if (!viewerEntity || !localFloorEntity) return

    const rendererComponent = getOptionalMutableComponent(viewerEntity, RendererComponent)
    if (!rendererComponent) return

    rendererComponent.scenes.merge([localFloorEntity])

    return () => {
      if (!Engine.instance) return
      if (!hasComponent(viewerEntity, RendererComponent)) return
      const index = rendererComponent.scenes.value.indexOf(localFloorEntity)
      rendererComponent.scenes[index].set(none)
    }
  }, [viewerEntity, localFloorEntity])
}

export const useRemoveEngineCanvas = () => {
  useEffect(() => {
    const canvas = document.getElementById('engine-renderer-canvas')!
    const parent = canvas.parentElement
    parent?.removeChild(canvas)

    return () => {
      parent?.appendChild(canvas)
    }
  }, [])

  return null
}
