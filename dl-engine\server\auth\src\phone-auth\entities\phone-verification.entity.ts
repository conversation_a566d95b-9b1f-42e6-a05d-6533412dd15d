/**
 * DL-Engine 手机号验证记录实体
 */

import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, Index } from 'typeorm'

@Entity('phone_verifications')
@Index(['phone', 'purpose'])
@Index(['createdAt'])
export class PhoneVerification {
  @PrimaryGeneratedColumn('uuid')
  id: string

  @Column({ type: 'varchar', length: 20, comment: '手机号码（包含国家代码）' })
  phone: string

  @Column({ type: 'varchar', length: 10, comment: '验证码' })
  code: string

  @Column({ 
    type: 'enum', 
    enum: ['login', 'register', 'bind', 'unbind', 'reset'],
    default: 'login',
    comment: '验证码用途'
  })
  purpose: string

  @Column({ type: 'datetime', comment: '验证码过期时间' })
  expiresAt: Date

  @Column({ type: 'boolean', default: false, comment: '是否已使用' })
  used: boolean

  @Column({ type: 'datetime', nullable: true, comment: '使用时间' })
  usedAt: Date

  @Column({ type: 'varchar', length: 45, nullable: true, comment: '发送IP地址' })
  ipAddress: string

  @Column({ type: 'varchar', length: 500, nullable: true, comment: '用户代理' })
  userAgent: string

  @CreateDateColumn({ comment: '创建时间' })
  createdAt: Date
}
