.drawerBox {
  display: flex;
  position: fixed;
  top: 5px;
  z-index: 5;
  width: 100%;
  justify-content: center;

  .iconContainer {
    background: rgb(0 0 0 / 20%);
    border-radius: 50%;
    margin: 5px;
    height: 50px;
    width: 50px;
    cursor: pointer;
    position: relative;
    border: none;
    color: white;
    padding: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    pointer-events: auto;
    
    &::before {
      content: " ";
      position: absolute;
      width: 50px;
      height: 50px;
      display: block;
      transition: all 0.15s cubic-bezier(0.18, 0.89, 0.32, 1.28);
      background: var(--primaryHighlight);
      top: 0;
      left: 0;
      z-index: -1;
      border-radius: 50%;
      transform: scale(0);
    }

    &:hover::before {
      transform: scale(1);
    }

    &.on svg {
      fill: var(--primaryHighlight) !important;
      color: var(--primaryHighlight) !important;
    }

    > svg {
      height: 50%;
      width: 50%;
    }
  }

  @media (max-width: 450px) {
    justify-content: flex-middle;

    .iconContainer {
      width: 40px;
      height: 40px;

      &::before {
        width: 40px;
        height: 40px;
      }
    }
  }

  @media (max-width: 395px) {
    top: 60px;
    width: auto;
    flex-direction: column;
  }
}

.loader {
  display: flex;
  padding: 0;
  align-items: center;
  text-align: center;
  margin: 5px;
}