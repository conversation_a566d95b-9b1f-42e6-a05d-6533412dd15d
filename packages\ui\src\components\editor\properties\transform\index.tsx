/*
CPAL-1.0 License

The contents of this file are subject to the Common Public Attribution License
Version 1.0. (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at
https://github.com/ir-engine/ir-engine/blob/dev/LICENSE.
The License is based on the Mozilla Public License Version 1.1, but Sections 14
and 15 have been added to cover use of software over a computer network and 
provide for limited attribution for the Original Developer. In addition, 
Exhibit A has been modified to be consistent with Exhibit B.

Software distributed under the License is distributed on an "AS IS" basis,
WITHOUT WARRANTY OF ANY KIND, either express or implied. See the License for the
specific language governing rights and limitations under the License.

The Original Code is Infinite Reality Engine.

The Original Developer is the Initial Developer. The Initial Developer of the
Original Code is the Infinite Reality Engine team.

All portions of the code written by the Infinite Reality Engine team are Copyright © 2021-2025
Infinite Reality Engine. All Rights Reserved.
*/

import React from 'react'
import { useTranslation } from 'react-i18next'
import { Quaternion, Vector3 } from 'three'

import {
  getComponent,
  hasComponent,
  removeComponent,
  setComponent,
  useComponent,
  useOptionalComponent
} from '@ir-engine/ecs/src/ComponentFunctions'
import { SceneDynamicLoadComponent } from '@ir-engine/engine/src/scene/components/SceneDynamicLoadComponent'
import { getMutableState, getState, NO_PROXY, useHookstate } from '@ir-engine/hyperflux'

import { LuMove3D } from 'react-icons/lu'

import { commitProperty, EditorComponentType, updateProperty } from '@ir-engine/editor/src/components/properties/Util'
import { ObjectGridSnapState } from '@ir-engine/editor/src/systems/ObjectGridSnapSystem'

import { EditorControlFunctions } from '@ir-engine/editor/src/functions/EditorControlFunctions'
import { EditorHelperState } from '@ir-engine/editor/src/services/EditorHelperState'
import { EntityHierarchyLockState } from '@ir-engine/editor/src/services/EntityHierarchyLockState'
import { SelectionState } from '@ir-engine/editor/src/services/SelectionServices'
import { TransformComponent } from '@ir-engine/spatial'
import { TransformSpace } from '@ir-engine/spatial/src/common/constants/TransformConstants'

import { entityExists, PresentationSystemGroup, useExecute } from '@ir-engine/ecs'
import { AuthoringState } from '@ir-engine/engine/src/authoring/AuthoringState'
import { Checkbox } from '@ir-engine/ui'
import ComponentDropdown from '../../ComponentDropdown'
import EulerInput from '../../input/Euler'
import InputGroup from '../../input/Group'
import NumericInput from '../../input/Numeric'
import Vector3Input from '../../input/Vector3'
import { TransformUniformScaleState } from './TransformUniformScaleState'

/**
 * TransformPropertyGroup component is used to render editor view to customize properties.
 */
export const TransformPropertyGroup: EditorComponentType = (props) => {
  const { t } = useTranslation()

  const locked = useHookstate(getMutableState(EntityHierarchyLockState).lockedEntities).value[props.entity] ?? false
  const hasDynamicLoad = useOptionalComponent(props.entity, SceneDynamicLoadComponent)
  const transformComponent = useComponent(props.entity, TransformComponent)
  const transformSpace = useHookstate(getMutableState(EditorHelperState).transformSpace)

  const position = useHookstate(transformComponent.position.get(NO_PROXY))
  const rotation = useHookstate(transformComponent.rotation.get(NO_PROXY))
  const scale = useHookstate(transformComponent.scale.get(NO_PROXY))

  useExecute(
    () => {
      if (!entityExists(props.entity) || !hasComponent(props.entity, TransformComponent)) return

      const transformComponent = getComponent(props.entity, TransformComponent)
      const updatedPostion = transformComponent.position
      const updatedRotation = transformComponent.rotation
      const updatedScale = transformComponent.scale

      if (!position.value.equals(updatedPostion)) {
        position.set(updatedPostion)
      }
      if (!rotation.value.equals(updatedRotation)) {
        rotation.set(updatedRotation)
      }
      if (!scale.value.equals(updatedScale)) {
        scale.set(updatedScale)
      }
    },
    { after: PresentationSystemGroup }
  )

  if (transformSpace.value === TransformSpace.world)
    transformComponent.matrixWorld.value.decompose(position.value, rotation.value, scale.value)

  const onRelease = () => {
    const bboxSnapState = getState(ObjectGridSnapState)
    if (bboxSnapState.enabled) {
      ObjectGridSnapState.apply()
    }
    const entities = SelectionState.getSelectedEntities()
    AuthoringState.snapshotEntities(entities)
  }

  const onChangeDynamicLoad = (value) => {
    const entities = SelectionState.getSelectedEntities()
    for (const entity of entities) {
      if (value === true) setComponent(entity, SceneDynamicLoadComponent)
      else removeComponent(entity, SceneDynamicLoadComponent)
    }
    AuthoringState.snapshotEntities(entities)
  }

  const onChangePosition = (value: Vector3) => {
    const selectedEntities = SelectionState.getSelectedEntities()
    EditorControlFunctions.positionObject(selectedEntities, [value])
  }

  const onChangeRotation = (value: Quaternion) => {
    const selectedEntities = SelectionState.getSelectedEntities()
    EditorControlFunctions.rotateObject(selectedEntities, [value])
  }

  const onChangeScale = (value: Vector3) => {
    const selectedEntities = SelectionState.getSelectedEntities()
    EditorControlFunctions.scaleObject(selectedEntities, [value], true)
  }

  const onToggleUniformScale = (updatedValue: boolean) => {
    updatedValue
      ? TransformUniformScaleState.addOrUpdateEntity(props.entity)
      : TransformUniformScaleState.removeEntry(props.entity)
  }

  const getUniformScale = (): boolean => {
    return TransformUniformScaleState.getEntityState(props.entity) ?? false
  }

  return (
    <ComponentDropdown
      name={t('editor:properties.transform.title')}
      description={t('editor:properties.transform.description')}
      Icon={TransformPropertyGroup.iconComponent}
      entity={props.entity}
    >
      <div className="flex w-full gap-x-2 py-1.5 pl-8 pr-3.5">
        <Checkbox
          checked={!!hasDynamicLoad}
          onChange={onChangeDynamicLoad}
          label={t('editor:properties.lbl-dynamicLoad')}
        />
        {hasDynamicLoad && (
          <InputGroup label="Distance">
            <NumericInput
              min={1}
              max={100}
              value={hasDynamicLoad.distance.value}
              onChange={updateProperty(SceneDynamicLoadComponent, 'distance')}
              onRelease={commitProperty(SceneDynamicLoadComponent, 'distance')}
            />
          </InputGroup>
        )}
      </div>
      <InputGroup name="Position" label={t('editor:properties.transform.lbl-position')} className="w-auto">
        <Vector3Input
          disabled={locked}
          smallStep={0.01}
          mediumStep={0.1}
          largeStep={1}
          value={position.value}
          onChange={onChangePosition}
          onRelease={onRelease}
        />
      </InputGroup>
      <InputGroup name="Rotation" label={t('editor:properties.transform.lbl-rotation')} className="w-auto">
        <EulerInput
          disabled={locked}
          quaternion={rotation.value}
          onChange={onChangeRotation}
          unit="°"
          onRelease={onRelease}
        />
      </InputGroup>
      <InputGroup name="Scale" label={t('editor:properties.transform.lbl-scale')} className="w-auto">
        <Vector3Input
          disabled={locked}
          uniformScaling={getUniformScale()}
          smallStep={0.01}
          mediumStep={0.1}
          largeStep={1}
          value={scale.value}
          onToggleUniformScale={onToggleUniformScale}
          onChange={onChangeScale}
          onRelease={onRelease}
        />
      </InputGroup>
    </ComponentDropdown>
  )
}

TransformPropertyGroup.iconComponent = LuMove3D

export default TransformPropertyGroup
