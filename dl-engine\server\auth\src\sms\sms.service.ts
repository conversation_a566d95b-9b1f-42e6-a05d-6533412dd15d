/**
 * DL-Engine 短信服务
 * 
 * 支持多个短信服务提供商：
 * - 阿里云短信服务
 * - 腾讯云短信服务
 * - 开发环境模拟发送
 */

import { Injectable } from '@nestjs/common'
import { ConfigService } from '../config/config.service'
import { LoggerService } from '../common/logger.service'

@Injectable()
export class SmsService {
  constructor(
    private readonly configService: ConfigService,
    private readonly logger: LoggerService
  ) {}

  /**
   * 发送验证码短信
   */
  async sendVerificationCode(phone: string, code: string, purpose: string): Promise<boolean> {
    const smsConfig = this.configService.getCurrentSmsConfig()
    
    if (this.configService.isDevelopment()) {
      return this.sendMockSms(phone, code, purpose)
    }

    switch (smsConfig.provider) {
      case 'aliyun':
        return this.sendAliyunSms(phone, code, purpose)
      case 'tencent':
        return this.sendTencentSms(phone, code, purpose)
      default:
        throw new Error(`Unsupported SMS provider: ${smsConfig.provider}`)
    }
  }

  /**
   * 开发环境模拟发送
   */
  private async sendMockSms(phone: string, code: string, purpose: string): Promise<boolean> {
    this.logger.log(`[MOCK SMS] 发送验证码到 ${phone}: ${code} (用途: ${purpose})`)
    
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    return true
  }

  /**
   * 阿里云短信服务
   */
  private async sendAliyunSms(phone: string, code: string, purpose: string): Promise<boolean> {
    try {
      const smsConfig = this.configService.getSmsConfig().aliyun
      
      // 这里应该集成阿里云SDK
      // 由于需要实际的AccessKey，这里提供示例代码结构
      
      const params = {
        PhoneNumbers: phone,
        SignName: smsConfig.signName,
        TemplateCode: smsConfig.templateCode,
        TemplateParam: JSON.stringify({ code })
      }

      this.logger.log(`发送阿里云短信到 ${phone}`)
      
      // 实际发送逻辑
      // const result = await aliyunClient.sendSms(params)
      
      return true
    } catch (error) {
      this.logger.error(`阿里云短信发送失败: ${error.message}`)
      throw error
    }
  }

  /**
   * 腾讯云短信服务
   */
  private async sendTencentSms(phone: string, code: string, purpose: string): Promise<boolean> {
    try {
      const smsConfig = this.configService.getSmsConfig().tencent
      
      // 这里应该集成腾讯云SDK
      // 由于需要实际的SecretId和SecretKey，这里提供示例代码结构
      
      const params = {
        PhoneNumberSet: [phone],
        SignName: smsConfig.signName,
        TemplateId: smsConfig.templateId,
        TemplateParamSet: [code]
      }

      this.logger.log(`发送腾讯云短信到 ${phone}`)
      
      // 实际发送逻辑
      // const result = await tencentClient.SendSms(params)
      
      return true
    } catch (error) {
      this.logger.error(`腾讯云短信发送失败: ${error.message}`)
      throw error
    }
  }

  /**
   * 获取短信模板内容
   */
  private getSmsTemplate(purpose: string): string {
    const templates = {
      login: '您的登录验证码是：{code}，5分钟内有效，请勿泄露。',
      register: '欢迎注册DL学习引擎！您的验证码是：{code}，5分钟内有效。',
      bind: '您正在绑定手机号，验证码是：{code}，5分钟内有效。',
      unbind: '您正在解绑手机号，验证码是：{code}，5分钟内有效。',
      reset: '您正在重置密码，验证码是：{code}，5分钟内有效。'
    }

    return templates[purpose] || templates.login
  }

  /**
   * 验证手机号格式
   */
  validatePhoneNumber(phone: string): boolean {
    // 中国大陆手机号验证
    if (phone.startsWith('+86')) {
      const number = phone.substring(3)
      return /^1[3-9]\d{9}$/.test(number)
    }
    
    // 其他国家手机号的基本验证
    return /^\+\d{1,4}\d{6,14}$/.test(phone)
  }

  /**
   * 格式化手机号
   */
  formatPhoneNumber(phone: string, countryCode: string = '+86'): string {
    if (phone.startsWith('+')) {
      return phone
    }
    
    return `${countryCode}${phone}`
  }
}
