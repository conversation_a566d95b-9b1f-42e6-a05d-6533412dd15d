/*
CPAL-1.0 License

The contents of this file are subject to the Common Public Attribution License
Version 1.0. (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at
https://github.com/ir-engine/ir-engine/blob/dev/LICENSE.
The License is based on the Mozilla Public License Version 1.1, but Sections 14
and 15 have been added to cover use of software over a computer network and 
provide for limited attribution for the Original Developer. In addition, 
Exhibit A has been modified to be consistent with Exhibit B.

Software distributed under the License is distributed on an "AS IS" basis,
WITHOUT WARRANTY OF ANY KIND, either express or implied. See the License for the
specific language governing rights and limitations under the License.

The Original Code is Infinite Reality Engine.

The Original Developer is the Initial Developer. The Initial Developer of the
Original Code is the Infinite Reality Engine team.

All portions of the code written by the Infinite Reality Engine team are Copyright © 2021-2025 
Infinite Reality Engine. All Rights Reserved.
*/

import appRootPath from 'app-root-path'
import fs from 'fs'
import path from 'path'

import { deleteFolderRecursive, writeFileSyncRecursive } from '@ir-engine/common/src/utils/fsHelperFunctions'

import { getStorageProvider } from '../../media/storageprovider/storageprovider'
import { getFileKeysRecursive } from '../../media/storageprovider/storageProviderUtils'
import logger from '../../ServerLogger'

/**
 * Downloads a specific project to the local file system from the storage provider cache
 * Then runs `npm install --legacy-peer-deps` inside the project to install it's dependencies
 * @param projectName
 * @param storageProviderName
 * @returns {Promise<boolean>}
 */
export const download = async (projectName: string, storageProviderName?: string) => {
  if (projectName === 'ir-engine/default-project') return

  const storageProvider = getStorageProvider(storageProviderName)
  try {
    logger.info(`[ProjectLoader]: Installing project "${projectName}"...`)
    let files = await getFileKeysRecursive(`projects/${projectName}/`)

    files = files.filter(
      (file) =>
        !file.startsWith(`/projects/${projectName}/assets/`) && !file.startsWith(`/projects/${projectName}/public/`)
    )
    logger.info('[ProjectLoader]: Found files:' + files)

    const localProjectDirectory = path.join(appRootPath.path, 'packages/projects/projects', projectName)
    if (fs.existsSync(localProjectDirectory)) {
      logger.info('[Project temp debug]: fs exists, deleting')
      deleteFolderRecursive(localProjectDirectory)
    }

    await Promise.all(
      files.map(async (filePath) => {
        logger.info(`[ProjectLoader]: - downloading "${filePath}"`)
        const fileResult = await storageProvider.getObject(filePath)
        if (fileResult.Body.length === 0) {
          logger.info(`[ProjectLoader]: WARNING file "${filePath}" is empty`)
          if (filePath[filePath.length - 1] === '/') return
        }
        writeFileSyncRecursive(path.join(appRootPath.path, 'packages/projects', filePath), fileResult.Body)
      })
    )

    logger.info(`[ProjectLoader]: Successfully downloaded and mounted project "${projectName}".`)
    // if (projectName !== 'ir-engine/default-project') {
    //   const npmInstallPromise = new Promise<void>((resolve) => {
    //     const npmInstallProcess = spawn('npm', ['install', '--legacy-peer-deps'], { cwd: localProjectDirectory })
    //     npmInstallProcess.once('exit', () => {
    //       logger.info('Finished npm installing %s', projectName)
    //       resolve()
    //     })
    //     npmInstallProcess.once('error', resolve)
    //     npmInstallProcess.once('disconnect', resolve)
    //     npmInstallProcess.stdout.on('data', (data) => logger.info(data.toString()))
    //     npmInstallProcess.stderr.on('data', (data) => logger.error(data.toString()))
    //   }).then((result) => logger.info(result))
    //   await Promise.race([
    //     npmInstallPromise,
    //     new Promise<void>((resolve) => {
    //       setTimeout(() => {
    //         logger.warn(`WARNING: npm installing ${projectName} took too long!`)
    //         resolve()
    //       }, 5 * 60 * 1000) // timeout after 5 minutes
    //     })
    //   ])
    // }
  } catch (e) {
    const errorMsg = `[ProjectLoader]: Failed to download project ${projectName} with error: ${e.message}`
    logger.error(e, errorMsg)
    throw e
  }
}
