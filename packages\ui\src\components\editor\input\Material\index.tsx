/*
CPAL-1.0 License

The contents of this file are subject to the Common Public Attribution License
Version 1.0. (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at
https://github.com/ir-engine/ir-engine/blob/dev/LICENSE.
The License is based on the Mozilla Public License Version 1.1, but Sections 14
and 15 have been added to cover use of software over a computer network and 
provide for limited attribution for the Original Developer. In addition, 
Exhibit A has been modified to be consistent with Exhibit B.

Software distributed under the License is distributed on an "AS IS" basis,
WITHOUT WARRANTY OF ANY KIND, either express or implied. See the License for the
specific language governing rights and limitations under the License.

The Original Code is Infinite Reality Engine.

The Original Developer is the Initial Developer. The Initial Developer of the
Original Code is the Infinite Reality Engine team.

All portions of the code written by the Infinite Reality Engine team are Copyright © 2021-2025
Infinite Reality Engine. All Rights Reserved.
*/

import React from 'react'
import { DropTargetMonitor, useDrop } from 'react-dnd'

import { EntityUUID } from '@ir-engine/ecs'
import { Entity } from '@ir-engine/ecs/src/Entity'
import { ItemTypes } from '@ir-engine/editor/src/constants/AssetTypes'
import { ControlledStringInput } from '../String'

export function MaterialInput<T extends { value: Entity; onChange: (val: EntityUUID) => void }>({
  value,
  onChange,
  ...rest
}: T) {
  function onDrop(item: { value?: Entity | Entity[] | undefined }, _monitor: DropTargetMonitor) {
    let element = item.value
    if (typeof element === 'undefined') return
    if (Array.isArray(value)) {
      element = element[0]
    }
    if (typeof element !== 'string') return
    onChange(element)
  }

  const [{ canDrop, isOver }, dropRef] = useDrop({
    accept: [ItemTypes.Material],
    drop: onDrop,
    collect: (monitor) => ({
      canDrop: monitor.canDrop(),
      isOver: monitor.isOver()
    })
  })

  return (
    <ControlledStringInput ref={dropRef} onChange={onChange} canDrop={isOver && canDrop} value={'' + value} {...rest} />
  )
}

MaterialInput.defaultProps = {}

export default MaterialInput
