
* @ir-engine/qa

/packages/client/ @ir-engine/napster-experiences 
/packages/client-core/ @ir-engine/napster-experiences 
/packages/ecs/ @HexaField @speigg
/packages/editor/ @ir-engine/napster-experiences 
/packages/engine/ @HexaField @AidanCaruso @<PERSON><PERSON><PERSON> @dinomut1
/packages/hyperflux/ @HexaField @speigg
/packages/matchmaking/ @HexaField @speigg @hanzlamateen @barankyle
/packages/common/ @HexaField @mshafiqmk @DanielBelmes
/packages/server/ @hanzlamateen @barankyle @jose-galvan @kattsushi @mikeplascdev
/packages/server-core/ @barankyle @MoizAdnan @hanzlamateen @hurairahmateen @jose-galvan @kattsushi @mikeplascdev
/packages/spatial/ @HexaField @speigg @AidanCaruso @MichaelEstes @MbfloydIR @SamMazerIR
/packages/taskserver/ @barankyle @hanzlamateen
/packages/ui/ @ir-engine/napster-experiences 
/packages/visual-script/ @SYBIOTE
/packages/xrui/ @HexaField @speigg

/packages/common/src/interfaces/ @hurairahmateen
/packages/client-core/src/admin/ @hanzlamateen
/packages/client-core/src/components/World/ @speigg
/packages/client-core/src/hooks/ @jose-galvan
/packages/editor/src/classes/ @SYBIOTE
/packages/editor/src/functions/ @SYBIOTE
/packages/engine/src/postprocessing/ @DanielBelmes @MbfloydIR @AidanCaruso
/packages/engine/src/mocap/ @AidanCaruso
/packages/engine/src/avatar/ @AidanCaruso
/packages/engine/src/assets/ @MichaelEstes @dinomut1 @speigg
/packages/engine/src/scene/ @dinomut1 @speigg @CITIZENDOT @AidanCaruso
/packages/engine/src/audio/ @speigg
/packages/engine/src/interaction/ @SamMazerIR @speigg
/packages/spatial/src/camera/ @SamMazerIR @DanielBelmes @speigg
/packages/spatial/src/input/ @SamMazerIR @DanielBelmes @speigg
/packages/spatial/src/resources/ @MichaelEstes
/packages/spatial/src/xrui/ @speigg
/packages/spatial/src/input/ @SamMazerIR @speigg
/packages/spatial/src/transform/ @SamMazerIR
/packages/spatial/src/camera/ @DanielBelmes @SamMazerIR @speigg
/packages/spatial/src/renderer/ @AidanCaruso @DanielBelmes @MichaelEstes
/scripts/ @barankyle @hanzlamateen @DanielBelmes @sfgarza
/patches/postprocessing+6.37.3.patch @AidanCaruso
/patches/three+0.176.0.patch @AidanCaruso
/dockerfiles/ @barankyle @sfgarza
/certs/ @DanielBelmes
/.github/ @DanielBelmes @barankyle @sfgarza
