/*
CPAL-1.0 License

The contents of this file are subject to the Common Public Attribution License
Version 1.0. (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at
https://github.com/ir-engine/ir-engine/blob/dev/LICENSE.
The License is based on the Mozilla Public License Version 1.1, but Sections 14
and 15 have been added to cover use of software over a computer network and 
provide for limited attribution for the Original Developer. In addition, 
Exhibit A has been modified to be consistent with Exhibit B.

Software distributed under the License is distributed on an "AS IS" basis,
WITHOUT WARRANTY OF ANY KIND, either express or implied. See the License for the
specific language governing rights and limitations under the License.

The Original Code is Infinite Reality Engine.

The Original Developer is the Initial Developer. The Initial Developer of the
Original Code is the Infinite Reality Engine team.

All portions of the code written by the Infinite Reality Engine team are Copyright © 2021-2025
Infinite Reality Engine. All Rights Reserved.
*/

import { NO_PROXY, none, startReactor, useForceUpdate, useHookstate, useImmediateEffect } from '@ir-engine/hyperflux'
import * as bitECS from 'bitecs'
import React, { useEffect } from 'react'
import {
  Component,
  ComponentType,
  defineComponent,
  entityExists,
  getComponent,
  getMutableComponent,
  getOptionalComponent,
  getOptionalMutableComponent,
  hasComponent,
  hasComponents,
  LayerComponents,
  Layers,
  removeEntity,
  setComponent,
  useHasComponents,
  useOptionalComponent
} from './ComponentFunctions'
import { Entity, UndefinedEntity } from './Entity'
import { QueryReactor, useQuery } from './QueryFunctions'
import { S } from './schemas/JSONSchemas'
import { UUIDComponent } from './UUIDComponent'

type EntityTreeSetType = {
  parentEntity: Entity
  childIndex?: number
}

/**
 * @description
 * Describes parent-child relationship between entities.
 * A root entity has it's parentEntity set to null.
 *
 * @param parentEntity _(Optional)_ The entity where this entity connects to the EntityTree
 * @param childIndex _(Optional)_ The position that this entity should be found at in the `@param parentEntity`.EntityTreeComponent.children list
 * @param children _(Internal)_ The list of entities that are connected to this entity in the EntityTree
 */
export const EntityTreeComponent = defineComponent({
  name: 'EntityTreeComponent',

  jsonID: 'IR_hierarchy',

  schema: S.Object({
    parentEntity: S.Entity({
      validate: (value, prev, entity) => {
        if (entity === value) {
          console.error('Entity cannot be its own parent: ' + entity)
          return false
        }

        return true
      }
    }),
    // automatically updated if parent exists
    childIndex: S.Optional(S.Number()),
    // automatically updated if parent exists
    children: S.Array(S.Entity(), { serialized: false })
  }),

  onSet: (entity, component, json?: Readonly<EntityTreeSetType>) => {
    if (!json) return

    if (entity === json.parentEntity) {
      throw new Error('Entity cannot be its own parent: ' + entity)
    }

    const currentParentEntity = component.parentEntity.value
    // If a previous parentEntity, remove this entity from its children
    if (currentParentEntity && currentParentEntity !== json.parentEntity) {
      if (entityExists(currentParentEntity)) {
        const oldParent = getOptionalMutableComponent(currentParentEntity, EntityTreeComponent)
        if (oldParent) {
          const parentChildIndex = oldParent.children.value.findIndex((child) => child === entity)
          const children = oldParent.children.get(NO_PROXY)
          oldParent.children.set([...children.slice(0, parentChildIndex), ...children.slice(parentChildIndex + 1)])
        }
      }
    }
    // set new data
    if (typeof json.parentEntity !== 'undefined') {
      component.parentEntity.set(json.parentEntity)
    }

    const parentEntity = component.parentEntity.value

    if (parentEntity && entityExists(parentEntity)) {
      if (!hasComponent(parentEntity, EntityTreeComponent)) setComponent(parentEntity, EntityTreeComponent)

      const parentState = getMutableComponent(parentEntity, EntityTreeComponent)
      const parent = getComponent(parentEntity, EntityTreeComponent)

      const prevChildIndex = parent.children.indexOf(entity)
      const isDifferentIndex = typeof json.childIndex === 'number' ? prevChildIndex !== json.childIndex : false

      if (isDifferentIndex && prevChildIndex !== -1) {
        parentState.children.set((prevChildren) => [
          ...prevChildren.slice(0, prevChildIndex),
          ...prevChildren.slice(prevChildIndex + 1)
        ])
      }

      if (isDifferentIndex || prevChildIndex === -1) {
        if (typeof json.childIndex !== 'undefined') {
          const childIndex =
            parentState.children.length > json.childIndex ? json.childIndex : parentState.children.length
          parentState.children.set((prevChildren) => [
            ...prevChildren.slice(0, childIndex),
            entity,
            ...prevChildren.slice(childIndex)
          ])
        } else {
          parentState.children.set([...parent.children, entity])
        }
      }

      component.childIndex.set(parent.children.indexOf(entity))
    }
  },

  onRemove: (entity, component) => {
    const parentEntity = component.parentEntity.value
    if (parentEntity && entityExists(parentEntity)) {
      if (hasComponent(parentEntity, EntityTreeComponent)) {
        const parentState = getMutableComponent(parentEntity, EntityTreeComponent)
        const parent = getComponent(parentEntity, EntityTreeComponent)
        const parentChildIndex = parent.children.findIndex((child) => child === entity)
        if (parentChildIndex > -1) parentState.children[parentChildIndex].set(none)
      }
    }
  }
})

/**
 * @description
 * Recursively call {@link removeEntity} on `@param entity` and all its children entities
 * Children entities will be traversed first
 *
 * @param entity The parent entity where traversal will start.
 */
export function removeEntityNodeRecursively(entity: Entity) {
  traverseEntityNodeChildFirst(entity, (nodeEntity) => {
    removeEntity(nodeEntity)
  })
}
/**
 * @deprecated Use {@link removeEntityNodeRecursively} instead */
export const destroyEntityTree = removeEntityNodeRecursively

/**
 * @description
 * Recursively call `@param cb` function on `@param entity` and all its children.
 * The tree will be traversed using the {@link EntityTreeComponent} of each entity found in the tree.
 * @note
 * Does not support removing the current `@param entity` node during traversal
 * The `@param cb` function will be called for `@param entity` first
 *
 * @param entity Entity Node where traversal will start
 * @param cb Callback function called for every entity of the tree
 * @param index Index of the current node (relative to its parent)
 */
export function traverseEntityNode(entity: Entity, cb: (entity: Entity, index: number) => void, index = 0): void {
  const entityTreeNode = getComponent(entity, EntityTreeComponent)
  if (!entityTreeNode) return

  cb(entity, index)

  if (!entityTreeNode.children.length) return
  for (let i = 0; i < entityTreeNode.children.length; i++) {
    const child = entityTreeNode.children[i]
    traverseEntityNode(child, cb, i)
  }
}

/**
 * @description
 * Recursively call `@param cb` function on `@param entity` and all its children.
 * The tree will be traversed using the {@link EntityTreeComponent} of each entity found in the tree.
 * @note
 * Supports removing the current `@param entity` node during traversal
 * The `@param cb` function will be called for `@param entity` at the end
 *
 * @param entity Entity Node where traversal will start
 * @param cb Callback function called for every entity of the tree
 * @param index Index of the current node (relative to its parent)
 */
export function traverseEntityNodeChildFirst(
  entity: Entity,
  cb: (entity: Entity, index: number) => void,
  index = 0
): void {
  const entityTreeNode = getOptionalComponent(entity, EntityTreeComponent)

  if (entityTreeNode) {
    const children = [...entityTreeNode.children]
    for (let i = 0; i < children.length; i++) {
      const child = children[i]
      traverseEntityNodeChildFirst(child, cb, i)
    }
  }

  cb(entity, index)
}

/**
 * @description
 * Traverse the children nodes of `@param entity` iteratively, and call `@param cb` on them when the requested conditions are matched.
 * Will return the array of all `@type R` returned by `@param cb` on each iteration.
 *
 * @param entity Entity Node where traversal will start
 * @param cb Callback function called on every entity found in the tree
 * @param pred An entity (and its children) won't be processed when the predicate function returns false for that entity
 * @param snubChildren When true: Do not traverse the children of a node when `@param pred` returns false
 * @param breakOnFind Whe true: Traversal will stop as soon as `@param pred` returns true for the first time. No children will be included in the result
 * @returns The list of `@type R` for all entities that matched the conditions.
 */
export function iterateEntityNode<R>(
  entity: Entity,
  cb: (entity: Entity, index: number) => R,
  pred: (entity: Entity) => boolean = (_e) => true,
  snubChildren = false,
  breakOnFind = false
): R[] {
  const frontier = [[entity]]
  const result: R[] = []
  while (frontier.length > 0) {
    const items = frontier.pop()!
    let idx = 0
    for (let i = 0; i < items.length; i++) {
      const item = items[i]
      if (pred(item)) {
        result.push(cb(item, idx))
        if (breakOnFind) return result
        idx += 1
        snubChildren &&
          frontier.push(
            getOptionalComponent(item, EntityTreeComponent)?.children?.filter((x) =>
              hasComponent(x, EntityTreeComponent)
            ) ?? []
          )
      }
      !snubChildren &&
        frontier.push(
          getOptionalComponent(item, EntityTreeComponent)?.children?.filter((x) =>
            hasComponent(x, EntityTreeComponent)
          ) ?? []
        )
    }
  }
  return result
}

/**
 * @description
 * Recursively calls `@param cb` on every parent entity in the `@param entity`'s {@link EntityTreeComponent}
 *
 * @param entity Entity Node where traversal will start
 * @param cb
 * Callback function that will be called for every traverse
 * Returning true from the cb will early stop traversal _(no matter if the currentEntity.EntityTreeComponent.parentEntity has any parents or not)_
 */
export function traverseEntityNodeParent(entity: Entity, cb: (parent: Entity) => true | void): void {
  const entityTreeNode = getOptionalComponent(entity, EntityTreeComponent)
  if (entityTreeNode?.parentEntity) {
    const earlyReturn = cb(entityTreeNode.parentEntity)
    if (earlyReturn === true) return
    traverseEntityNodeParent(entityTreeNode.parentEntity, cb)
  }
}

/**
 * @description
 * Finds the closest ancestor of `@param entity` that has all the `@param components` by walking up the entity's {@link EntityTreeComponent}
 *
 * @param entity Entity Node from which traversal will start
 * @param components Components to search for
 * @param closest _(@default true)_ - Whether to return the closest ancestor or the furthest ancestor
 * @param includeSelf _(@default true)_ - Whether to include the `@param entity` in the search or not
 * @returns The parent entity _(or itself when relevant)_
 * */
export function getAncestorWithComponents(
  entity: Entity,
  components: Component[],
  closest = true,
  includeSelf = true
): Entity {
  let result = hasComponents(entity, components) ? entity : UndefinedEntity
  if (includeSelf && closest && result) return result
  else if (!includeSelf) result = UndefinedEntity

  traverseEntityNodeParent(entity, (parentEntity: Entity) => {
    if (parentEntity === entity && !includeSelf) return
    if (hasComponents(parentEntity, components)) {
      result = parentEntity
      if (closest) return true // stop traversal
    }
  })
  return result
}

/**
 * @description Walks up the tree and returns an inclusive array of entities from the child to the ancestor (in that order) or an empty array if the ancestor is not found
 * @param childEntity The entity to start the search from
 * @param outEntities The array where the entities will be pushed
 * @param ancestorEntity The optional entity to stop the search at (leave UndefinedEntity to search all the way up)
 */
export function getTreeFromChildToAncestor(
  childEntity: Entity,
  outEntities: Entity[],
  ancestorEntity: Entity = UndefinedEntity
): boolean {
  outEntities.push(childEntity)
  if (ancestorEntity === childEntity) return true
  let found = false
  traverseEntityNodeParent(childEntity, (parent) => {
    if (ancestorEntity !== UndefinedEntity && parent === ancestorEntity) {
      found = true
      outEntities.push(parent)
      return true
    }
    outEntities.push(parent)
  })
  return found
}

/**
 * @description
 * Returns the array index of `@param entity` inside the `@param list` of {@link Entity} IDs
 * Useful for nodes that are not contained in the array but can have the same entity as one of the array elements
 *
 * @param list The list of {@link Entity} IDs where the `@param entity` will be searched for
 * @param entity The {@link Entity} ID to search for
 * @returns
 * The index of `@param entity` inside `@param list` when the entity was found
 * `-1` when the `@param entity` wasn't found
 * */
export function findIndexOfEntityNode(list: Entity[], entity: Entity): number {
  for (let id = 0; id < list.length; ++id) {
    if (entity === list[id]) return id
  }
  return -1
}

/**
 * @description
 * Returns whether or not the `@param child` is part of the `@param parent`'s {@link EntityTreeComponent} hierarchy
 *
 * @param child The Entity Node to search for
 * @param parent The Entity Node where the search will start
 * */
export function isDeepChildOf(child: Entity, parent: Entity): boolean {
  const childTreeNode = getOptionalComponent(child, EntityTreeComponent)
  if (!childTreeNode) return false
  if (childTreeNode.parentEntity === parent) return true
  return isDeepChildOf(childTreeNode.parentEntity, parent)
}

/**
 * @description
 * Returns an {@link Entity} list that contains all children of `@param entity` and all children of those children recursively.
 * Traversal will stop early when `@param pred` returns true for an entity. The entire tree will be traversed otherwise.
 *
 * @param entity Entity Node where traversal will start
 * @param pred An entity (and its children) won't be processed when the predicate function returns false for that entity
 * @returns The resulting {@link Entity} list of children entities that matched the conditions.
 * */
export function getNestedChildren(entity: Entity, pred?: (e: Entity) => boolean): Entity[] {
  const result: Entity[] = []
  iterateEntityNode(
    entity,
    (child) => {
      result.push(child)
    },
    pred,
    true
  )
  return result
}

/**
/**
 * @description
 * React Hook that returns the closest or furthest ancestor {@link Entity} of `@param entity` that has all of the `@param components`
 *
 * @param entity The {@link Entity} whose {@link EntityTreeComponent} will be traversed during the search.
 * @param components The list of Components that the child must have in order to be considered a match.
 * @param closest _(default: true)_ - Returns the closest entity when true. Returns the furthest entity otherwise.
 * @param includeSelf _(@default true)_ - Whether to include the `@param entity` in the search or not
 * @returns The ancestor {@link Entity} of `@param entity` that matched the conditions
 * */
export function useAncestorWithComponents(
  entity: Entity,
  components: ComponentType<any>[],
  closest: boolean = true,
  includeSelf: boolean = true
): Entity {
  const result = getAncestorWithComponents(entity, components, closest, includeSelf)
  const forceUpdate = useForceUpdate()

  const parentEntity = useOptionalComponent(entity, EntityTreeComponent)?.parentEntity
  const componentsString = components.map((component) => component.name).join()

  // hook into reactive changes up the tree to trigger a re-render of the parent when necessary
  useImmediateEffect(() => {
    let unmounted = false
    const ParentSubReactor = React.memo((props: { entity: Entity }) => {
      const tree = useOptionalComponent(props.entity, EntityTreeComponent)
      const matchesQuery = useHasComponents(props.entity, components)
      useImmediateEffect(() => {
        if (!unmounted) forceUpdate()
      }, [tree?.parentEntity?.value, matchesQuery])
      if (matchesQuery && closest) return null
      if (!tree?.parentEntity?.value) return null
      return <ParentSubReactor key={tree.parentEntity.value} entity={tree.parentEntity.value} />
    })

    const startEntity = includeSelf ? entity : parentEntity?.value ?? UndefinedEntity

    const root = startEntity
      ? startReactor(
          function useQueryReactor() {
            return <ParentSubReactor entity={startEntity} key={startEntity} />
          },
          'useAncestorWithComponents ' + entity + componentsString
        )
      : null

    return () => {
      unmounted = true
      root?.stop()
    }
  }, [entity, componentsString, includeSelf, parentEntity?.value])

  return result
}

/**
 * Returns all the entities that meet a query and belong to a particular source entity
 * - if you need entities from potentially deeply nested entities,
 *   use useChildrenWithComponents instead (but note it incurs a significant performance cost)
 */
export function useQueryBySource(
  sourceEntity: Entity,
  queryTerms: bitECS.QueryTerm[],
  layer = Layers.Simulation
): Entity[] {
  const query = useQuery([...queryTerms], layer)
  return query.filter((e) => hasComponent(e, UUIDComponent) && UUIDComponent.getSourceEntity(e) === sourceEntity)
}

/**
 * Returns a list of all ancestors for an entity
 * @param entity
 * @returns
 */
export function useAncestorTree(entity: Entity) {
  const ancestors = useHookstate([] as Entity[])

  useImmediateEffect(() => {
    let unmounted = false
    const ParentSubReactor = React.memo((props: { entity: Entity }) => {
      const tree = useOptionalComponent(props.entity, EntityTreeComponent)
      useEffect(() => {
        if (!tree) return
        // capture value to use in the cleanup function to prevent errors
        const parentEntityValue = tree.parentEntity.value
        ancestors.set((prev) => {
          if (prev.indexOf(parentEntityValue) < 0) prev.push(parentEntityValue)
          return prev
        })
        return () => {
          if (unmounted) return
          ancestors.set((prev) => prev.filter((e) => e !== parentEntityValue))
        }
      }, [tree?.parentEntity?.value])
      if (!tree?.parentEntity?.value) return null
      return <ParentSubReactor key={tree.parentEntity.value} entity={tree.parentEntity.value} />
    })

    const root = startReactor(function useQueryReactor() {
      return <ParentSubReactor entity={entity} key={entity} />
    }, 'useAncestorTree ' + entity)
    return () => {
      unmounted = true
      root.stop()
    }
  }, [entity])

  return ancestors.get(NO_PROXY) as Entity[]
}

/**
 * WARNING: This function is not optimized for performance. Do not use it for queries that return a lot of results.
 * Gets all the entities that meet a query and are deep children of the rootEntity
 * @param rootEntity
 * @param queryTerms
 * @param layer
 * @returns
 */
export function useChildrenWithComponents(
  rootEntity: Entity,
  queryTerms: bitECS.QueryTerm[],
  layer = Layers.Simulation
): Entity[] {
  const children = useHookstate([] as Entity[])

  useImmediateEffect(() => {
    let unmounted = false
    const ChildSubReactor = (props: { entity: Entity }) => {
      const ancestorTree = useAncestorTree(props.entity)
      /** @todo somehow wrapping this in a useMemo doesn't work */
      const includesRootEntity = ancestorTree.includes(rootEntity)

      useEffect(() => {
        if (!includesRootEntity) return
        children.set((prev) => {
          if (prev.indexOf(props.entity) < 0) prev.push(props.entity)
          return prev
        })
        return () => {
          if (!unmounted) {
            children.set((prev) => {
              const index = prev.indexOf(props.entity)
              prev.splice(index, 1)
              return prev
            })
          }
        }
      }, [includesRootEntity])
      return null
    }

    const root = startReactor(function useQueryReactor() {
      return <QueryReactor Components={[...queryTerms, LayerComponents[layer]]} ChildEntityReactor={ChildSubReactor} />
    }, 'useChildrenWithComponents ' + rootEntity)
    return () => {
      unmounted = true
      root.stop()
    }
  }, [rootEntity]) /** @todo expose bitECS.queryHash and include that here too */

  return children.get(NO_PROXY) as Entity[]
}

/**
 * @description
 * Returns an {@link Entity} array that will contain all child entities of `@param rootEntity` that have all of the `@param components`
 *
 * @param rootEntity The entity where traversal will start
 * @param components List of components that a child entity must have to be added to the result
 * @param result
 * _(optional)_
 * Array where the resulting entities will be added by `Array.push()`.
 * It will **not** be erased before traversal.
 * @returns An {@link Entity} array that contains the children that matched the condition
 * */
export function getChildrenWithComponents(
  rootEntity: Entity,
  components: ComponentType<any>[],
  result = [] as Entity[]
): Entity[] {
  const tree = getOptionalComponent(rootEntity, EntityTreeComponent)
  if (!tree?.children) return result

  for (const child of tree.children) {
    let valid = true
    for (const comp of components) {
      if (!hasComponent(child, comp)) valid = false
    }
    if (valid) result.push(child)
  }

  for (const childEntity of tree.children) getChildrenWithComponents(childEntity, components, result)

  return result
}

/**
 * @description
 * Returns whether or not `@param entity1` and `@param entity2` have a parent entity in common in their EntityTrees
 * EntityTree traversal will go up the tree searching for parents, and creating the list that will be compared.
 *
 * @param entity1 The first entity that will be compared
 * @param entity2 The second entity that will be compared
 * @returns True/False depending on whether they share a parent or not
 * */
export function haveCommonAncestor(entity1: Entity, entity2: Entity): boolean {
  const entity1Ancestors: Entity[] = []
  const entity2Ancestors: Entity[] = []

  traverseEntityNodeParent(entity1, (parent) => {
    entity1Ancestors.push(parent)
  })

  traverseEntityNodeParent(entity2, (parent) => {
    entity2Ancestors.push(parent)
  })

  for (const entity of entity1Ancestors) {
    if (entity2Ancestors.includes(entity)) return true
  }

  return false
}

/**
 * @description
 * Returns the filtered list of `@param entities` that are not ancestors of any other entities in the `@param result` array.
 *
 * @param entities The list of entities that will be searched for
 * @param result _(default: [])_ {@link Entity} list used to store the resulting list during the process
 * @returns The resulting filtered {@link Entity} list. Will be the same array as `@param result`
 * */
export function findRootAncestors(entities: Entity[], result: Entity[] = []): Entity[] {
  // Initially all entities are candidates
  for (const entity of entities) result.push(entity)

  // Check the input list against the initial candidates list,
  // and remove any invalid candidates from the output
  for (const entity of entities) {
    let validCandidate = true
    for (const candidate of result) {
      if (isAncestor(candidate, entity)) {
        validCandidate = false
        break
      }
    }
    if (!validCandidate) {
      const index = findIndexOfEntityNode(result, entity)
      if (index === -1) throw new Error('Object not found')
      result.splice(index, 1)
    }
  }

  return result
}

/**
 * @description
 * Returns whether `@param potentialChild` is part of the `@param parent`'s EntityTree or not.
 *
 * @param parent The entity whose EntityTree will be traversed during the search process.
 * @param potentialChild The child entity that will be searched for inside `@param parent`'s EntityTree.
 * @param includeSelf _(default: false)_ - Allow returning true when `@param parent` and `@param potentialChild` are the same entity.
 * */
export function isAncestor(parent: Entity, potentialChild: Entity, includeSelf = false) {
  if (!potentialChild || !parent) return false
  if (!includeSelf && parent === potentialChild) return false
  return traverseEarlyOut(parent, (child) => child === potentialChild)
}

/**
 * @description
 * Recursively call `@param cb` function on `@param entity` and all its children.
 * Traversal will stop as soon as `@param cb` returns true for the first time
 * @note The `@param cb` function will be called for `@param entity` first
 *
 * @param entity Entity Node where traversal will start
 * @param cb
 * Callback function that will be called for every traverse
 * Returning true from the cb will immediately stop traversal
 * */
export function traverseEarlyOut(entity: Entity, cb: (entity: Entity) => boolean): boolean {
  let stopTravel = cb(entity)

  if (stopTravel) return stopTravel

  const entityTreeComponent = getOptionalComponent(entity, EntityTreeComponent)
  const children = entityTreeComponent?.children
  if (!children) return stopTravel

  for (let id = 0; id < children.length; ++id) {
    const child = children[id]

    if (child) {
      stopTravel = traverseEarlyOut(child, cb)
      if (stopTravel) break
    }
  }

  return stopTravel
}
