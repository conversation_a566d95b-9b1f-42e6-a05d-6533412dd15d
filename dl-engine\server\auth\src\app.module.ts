/**
 * DL-Engine 认证服务应用模块
 * 
 * 集成所有认证相关模块：
 * - 手机号登录模块
 * - 短信验证模块
 * - JWT令牌模块
 * - OAuth登录模块
 * - 权限管理模块
 * - 用户管理模块
 */

import { Module } from '@nestjs/common'
import { TypeOrmModule } from '@nestjs/typeorm'
import { JwtModule } from '@nestjs/jwt'
import { PassportModule } from '@nestjs/passport'
import { ThrottlerModule } from '@nestjs/throttler'
import { ConfigModule } from './config/config.module'
import { ConfigService } from './config/config.service'
import { LoggerModule } from './common/logger.module'
import { DatabaseModule } from './database/database.module'
import { RedisModule } from './redis/redis.module'
import { HealthModule } from './health/health.module'
import { PhoneAuthModule } from './phone-auth/phone-auth.module'
import { SmsModule } from './sms/sms.module'
import { JwtAuthModule } from './jwt-auth/jwt-auth.module'
import { OAuthModule } from './oauth/oauth.module'
import { PermissionsModule } from './permissions/permissions.module'
import { UsersModule } from './users/users.module'
import { AuthModule } from './auth/auth.module'

@Module({
  imports: [
    // 配置模块 - 必须首先加载
    ConfigModule.forRoot(),

    // 日志模块
    LoggerModule,

    // 限流模块 - 防止暴力破解
    ThrottlerModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => [
        {
          name: 'short',
          ttl: 1000,  // 1秒
          limit: 3,   // 每秒最多3个请求
        },
        {
          name: 'medium',
          ttl: 60000, // 1分钟
          limit: 10,  // 每分钟最多10个请求
        },
        {
          name: 'long',
          ttl: 3600000, // 1小时
          limit: 100,   // 每小时最多100个请求
        }
      ],
      inject: [ConfigService]
    }),

    // 数据库模块
    DatabaseModule,

    // Redis模块
    RedisModule,

    // Passport模块
    PassportModule.register({ defaultStrategy: 'jwt' }),

    // JWT模块
    JwtModule.registerAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        secret: configService.getJwtSecret(),
        signOptions: {
          expiresIn: configService.getJwtExpiresIn(),
          issuer: 'dl-engine-auth',
          audience: 'dl-engine-users'
        }
      }),
      inject: [ConfigService]
    }),

    // 健康检查模块
    HealthModule,

    // 认证相关模块
    AuthModule,
    PhoneAuthModule,
    SmsModule,
    JwtAuthModule,
    OAuthModule,
    PermissionsModule,
    UsersModule
  ],
  controllers: [],
  providers: []
})
export class AppModule {}
