/*
CPAL-1.0 License

The contents of this file are subject to the Common Public Attribution License
Version 1.0. (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at
https://github.com/ir-engine/ir-engine/blob/dev/LICENSE.
The License is based on the Mozilla Public License Version 1.1, but Sections 14
and 15 have been added to cover use of software over a computer network and 
provide for limited attribution for the Original Developer. In addition, 
Exhibit A has been modified to be consistent with Exhibit B.

Software distributed under the License is distributed on an "AS IS" basis,
WITHOUT WARRANTY OF ANY KIND, either express or implied. See the License for the
specific language governing rights and limitations under the License.

The Original Code is Infinite Reality Engine.

The Original Developer is the Initial Developer. The Initial Developer of the
Original Code is the Infinite Reality Engine team.

All portions of the code written by the Infinite Reality Engine team are Copyright © 2021-2025
Infinite Reality Engine. All Rights Reserved.
*/

import type { SVGProps } from 'react'
import * as React from 'react'
import { Ref, forwardRef } from 'react'
const ShoppingCart = (props: SVGProps<SVGSVGElement>, ref: Ref<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="1em"
    height="1em"
    fill="none"
    viewBox="0 0 412 40"
    role="img"
    stroke="currentColor"
    ref={ref}
    {...props}
  >
    <path
      fill="#374151"
      d="m130.483 27.074-1.054.298a2 2 0 0 0-.293-.512 1.4 1.4 0 0 0-.517-.418q-.328-.164-.841-.164-.7 0-1.168.324-.462.318-.462.81 0 .438.318.691.318.254.994.423l1.134.278q1.024.25 1.526.76.502.508.502 1.308 0 .657-.378 1.173-.372.517-1.044.816-.671.298-1.561.298-1.168 0-1.934-.507-.765-.507-.969-1.482l1.113-.278q.16.617.602.925.447.308 1.168.308.82 0 1.303-.348.487-.353.487-.845a.9.9 0 0 0-.278-.666q-.279-.274-.855-.408l-1.273-.298q-1.049-.25-1.541-.77-.488-.528-.488-1.318 0-.646.363-1.144a2.5 2.5 0 0 1 1-.78 3.5 3.5 0 0 1 1.441-.284q1.134 0 1.78.497.651.498.925 1.313m3.079 1.332V33h-1.174V22.818h1.174v3.739h.099q.268-.592.805-.94.543-.353 1.442-.353.781 0 1.367.313.587.308.91.95.328.636.328 1.62V33h-1.173v-4.773q0-.909-.472-1.407-.468-.501-1.298-.502-.576 0-1.034.244-.452.244-.716.71-.258.468-.258 1.134m10.2 4.753q-1.034 0-1.815-.492a3.35 3.35 0 0 1-1.213-1.377q-.432-.886-.432-2.068 0-1.193.432-2.084a3.34 3.34 0 0 1 1.213-1.382q.781-.492 1.815-.492t1.81.492q.78.493 1.213 1.383.437.89.437 2.083 0 1.183-.437 2.068a3.3 3.3 0 0 1-1.213 1.377q-.776.492-1.81.492m0-1.054q.786 0 1.293-.403a2.37 2.37 0 0 0 .75-1.059q.244-.656.244-1.421t-.244-1.427a2.4 2.4 0 0 0-.75-1.07q-.507-.406-1.293-.407t-1.293.408a2.4 2.4 0 0 0-.75 1.069q-.244.66-.244 1.427 0 .765.244 1.421.243.656.75 1.06.508.402 1.293.402m5.251 3.759v-10.5h1.134v1.213h.139q.13-.2.358-.507.234-.315.666-.557.438-.249 1.183-.249.965 0 1.701.482.735.483 1.148 1.368t.413 2.088q0 1.213-.413 2.103-.412.885-1.143 1.372-.731.482-1.686.482-.735 0-1.178-.243a2.2 2.2 0 0 1-.681-.562 8 8 0 0 1-.368-.527h-.099v4.037zm1.154-6.682q0 .865.253 1.526.254.656.741 1.03.488.367 1.193.367.736 0 1.228-.388a2.37 2.37 0 0 0 .746-1.054q.253-.666.253-1.481 0-.805-.248-1.452a2.24 2.24 0 0 0-.741-1.029q-.492-.383-1.238-.383-.715 0-1.203.363-.487.358-.736 1.004-.248.642-.248 1.497m7.378 6.682v-10.5h1.133v1.213h.139q.13-.2.358-.507.234-.315.666-.557.438-.249 1.184-.249.964 0 1.7.482.735.483 1.148 1.368t.413 2.088q0 1.213-.413 2.103-.413.885-1.143 1.372-.73.482-1.685.482-.737 0-1.179-.243a2.2 2.2 0 0 1-.681-.562 8 8 0 0 1-.368-.527h-.099v4.037zm1.153-6.682q0 .865.254 1.526.253.656.74 1.03.488.367 1.193.367.736 0 1.228-.388a2.37 2.37 0 0 0 .746-1.054q.254-.666.254-1.481 0-.805-.249-1.452a2.24 2.24 0 0 0-.741-1.029q-.492-.383-1.238-.383-.715 0-1.203.363-.487.358-.735 1.004-.249.642-.249 1.497M166.076 33v-7.636h1.173V33zm.596-8.91a.83.83 0 0 1-.591-.233.75.75 0 0 1-.244-.561q0-.33.244-.562a.83.83 0 0 1 .591-.234q.344 0 .587.234a.74.74 0 0 1 .249.561.74.74 0 0 1-.249.562.82.82 0 0 1-.587.234m3.899 4.316V33h-1.173v-7.636h1.134v1.193h.099q.269-.582.815-.935.548-.358 1.412-.358.776 0 1.358.318.581.314.904.955.324.636.324 1.61V33h-1.174v-4.773q0-.9-.467-1.402-.468-.507-1.283-.507-.561 0-1.004.244a1.74 1.74 0 0 0-.691.71q-.254.468-.254 1.134m10.099 7.617q-.85 0-1.462-.219a3.1 3.1 0 0 1-1.019-.567 3.1 3.1 0 0 1-.641-.745l.934-.657q.16.21.403.477.245.274.666.473.428.204 1.119.204.924 0 1.526-.448.602-.447.602-1.402v-1.55h-.1a6 6 0 0 1-.368.516q-.233.303-.676.542-.437.234-1.183.234-.925 0-1.66-.438-.732-.437-1.159-1.272-.423-.835-.422-2.029 0-1.173.412-2.043.412-.875 1.149-1.353.735-.482 1.7-.482.746 0 1.183.249.443.243.676.557.239.307.368.507h.12v-1.213h1.133v7.855q0 .984-.447 1.6a2.55 2.55 0 0 1-1.194.91 4.5 4.5 0 0 1-1.66.294m-.04-4.196q.707 0 1.193-.323.487-.324.741-.93t.254-1.452q0-.825-.249-1.456a2.2 2.2 0 0 0-.736-.99q-.487-.358-1.203-.358-.745 0-1.243.378-.492.378-.741 1.014a3.9 3.9 0 0 0-.243 1.412q0 .796.248 1.407.254.607.746.955.498.342 1.233.343m9.864-3.739v1.094h-4.455v-1.094zm5.167 5.071q-1.074 0-1.85-.507a3.3 3.3 0 0 1-1.193-1.397q-.418-.89-.418-2.033 0-1.164.428-2.054a3.4 3.4 0 0 1 1.203-1.397q.775-.507 1.81-.507.805 0 1.451.299a2.8 2.8 0 0 1 1.059.835q.413.537.512 1.253h-1.173q-.134-.522-.597-.925-.456-.408-1.232-.408-.687 0-1.204.358-.512.354-.8 1-.283.641-.283 1.506 0 .885.278 1.541.284.656.795 1.02a2.06 2.06 0 0 0 1.214.362q.456 0 .83-.159.372-.16.631-.457.259-.3.368-.716h1.173a2.71 2.71 0 0 1-1.521 2.073q-.636.313-1.481.313m7.601 0q-.954 0-1.685-.482-.732-.487-1.144-1.372-.412-.891-.412-2.103 0-1.203.412-2.088t1.149-1.368q.735-.482 1.7-.482.746 0 1.178.249.438.243.666.557.234.307.363.507h.1v-1.213h1.173V33h-1.133v-1.173h-.14q-.129.209-.368.527a2.2 2.2 0 0 1-.681.562q-.442.243-1.178.243m.159-1.054q.707 0 1.193-.368.487-.372.741-1.029.254-.661.254-1.526 0-.856-.249-1.497-.249-.645-.736-1.004-.487-.363-1.203-.363-.745 0-1.243.383a2.3 2.3 0 0 0-.74 1.03q-.244.645-.244 1.45 0 .816.248 1.482.255.662.746 1.054.498.388 1.233.388m5.653.895v-7.636h1.133v1.153h.08q.208-.566.756-.92a2.2 2.2 0 0 1 1.233-.353q.128 0 .323.005.194.006.293.015v1.193a4 4 0 0 0-.273-.044 3 3 0 0 0-.443-.035q-.557 0-.994.233a1.712 1.712 0 0 0-.935 1.556V33zm9.008-7.636v.994h-3.957v-.994zm-2.804-1.83h1.174v7.279q0 .496.144.745a.74.74 0 0 0 .378.328q.233.08.492.08.194 0 .318-.02l.199-.04.239 1.054q-.12.045-.334.09-.214.05-.541.05-.498 0-.975-.214a1.93 1.93 0 0 1-.785-.652q-.309-.437-.309-1.103z"
    />
  </svg>
)
const ForwardRef = forwardRef(ShoppingCart)
export default ForwardRef
