/*
CPAL-1.0 License

The contents of this file are subject to the Common Public Attribution License
Version 1.0. (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at
https://github.com/ir-engine/ir-engine/blob/dev/LICENSE.
The License is based on the Mozilla Public License Version 1.1, but Sections 14
and 15 have been added to cover use of software over a computer network and
provide for limited attribution for the Original Developer. In addition,
Exhibit A has been modified to be consistent with Exhibit B.

Software distributed under the License is distributed on an "AS IS" basis,
WITHOUT WARRANTY OF ANY KIND, either express or implied. See the License for the
specific language governing rights and limitations under the License.

The Original Code is Infinite Reality Engine.

The Original Developer is the Initial Developer. The Initial Developer of the
Original Code is the Infinite Reality Engine team.

All portions of the code written by the Infinite Reality Engine team are Copyright © 2021-2025
Infinite Reality Engine. All Rights Reserved.
*/

import assert from 'assert'
import sinon from 'sinon'
import { afterEach, beforeEach, describe, it, vi } from 'vitest'

import {
  BoxGeometry,
  BufferGeometry,
  Color,
  ColorRepresentation,
  LineBasicMaterial,
  LineSegments,
  Material,
  MeshBasicMaterial,
  SphereGeometry
} from 'three'

import {
  createEntity,
  Entity,
  getComponent,
  getMutableComponent,
  hasComponent,
  removeComponent,
  removeEntity,
  setComponent,
  UndefinedEntity
} from '@ir-engine/ecs'
import { destroyEngine } from '@ir-engine/ecs/src/Engine'

import { createEngine } from '@ir-engine/ecs/src/Engine'

import { assertColor } from '../../../tests/util/assert'
import { NameComponent } from '../../common/NameComponent'
import { ObjectLayerMask, ObjectLayerMasks, ObjectLayers } from '../constants/ObjectLayers'
import { LineSegmentComponent } from './LineSegmentComponent'
import { ObjectComponent } from './ObjectComponent'
import { ObjectLayerComponents, ObjectLayerMaskComponent } from './ObjectLayerComponent'
import { VisibleComponent } from './VisibleComponent'

type LineSegmentComponentData = {
  name: string
  geometry: BufferGeometry
  material: Material
  color?: ColorRepresentation
  layerMask: ObjectLayerMask
  entity?: Entity
}

const LineSegmentComponentDefaults = {
  name: 'line-segment',
  geometry: null!,
  material: new LineBasicMaterial(),
  color: undefined,
  layerMask: ObjectLayerMasks.NodeHelper,
  entity: undefined
} as LineSegmentComponentData

function assertLineSegmentComponentEq(A: LineSegmentComponentData, B: LineSegmentComponentData) {
  if (A === null && B === null) return
  assert.equal(A.name, B.name)
  if (A.geometry === null && B.geometry === null) assert(true)
  else if (A.geometry === null) assert(false, 'Geometry of A is not equal to B. B has geometry, but A.geometry is null')
  else if (B.geometry === null) assert(false, 'Geometry of B is not equal to A. A has geometry, but B.geometry is null')
  else assert.deepEqual(A.geometry, B.geometry)
  assert.deepEqual(A.material, B.material)
  assertColor.eq(A.color!, B.color!)
  assert.equal(A.layerMask, B.layerMask)
  assert.equal(A.entity, B.entity)
}

describe('LineSegmentComponent', () => {
  describe('IDs', () => {
    it('should initialize the LineSegmentComponent.name field with the expected value', () => {
      assert.equal(LineSegmentComponent.name, 'LineSegmentComponent')
    })
  }) //:: IDs

  describe('onInit', () => {
    let testEntity = UndefinedEntity

    beforeEach(async () => {
      createEngine()
      testEntity = createEntity()
    })

    afterEach(() => {
      removeEntity(testEntity)
      return destroyEngine()
    })

    it('should initialize the component with the expected values', () => {
      const geometry = new BoxGeometry(1, 1, 1)
      const material = new MeshBasicMaterial({ color: 0xffff00 })
      const Expected = LineSegmentComponentDefaults
      Expected.geometry = geometry
      Expected.material = material
      setComponent(testEntity, LineSegmentComponent, { geometry: geometry, material: material })
      const data = getComponent(testEntity, LineSegmentComponent)
      assertLineSegmentComponentEq(data as LineSegmentComponentData, Expected)
    })
  }) //:: onInit

  describe('onSet', () => {
    let testEntity = UndefinedEntity

    beforeEach(async () => {
      createEngine()
      testEntity = createEntity()
    })

    afterEach(() => {
      removeEntity(testEntity)
      return destroyEngine()
    })

    it('should throw an error if the data assigned does not provide a valid `LineSegmentComponent.geometry` object', () => {
      const material = new MeshBasicMaterial({ color: 0xffff00 })
      assert.throws(() => setComponent(testEntity, LineSegmentComponent, { material: material }))
    })

    it('should change the values of an initialized LineSegmentComponent', () => {
      const geometry1 = new BoxGeometry(1, 1, 1)
      const material1 = new MeshBasicMaterial({ color: 0x111111 })
      const Expected = LineSegmentComponentDefaults
      Expected.geometry = geometry1
      Expected.material = material1
      setComponent(testEntity, LineSegmentComponent, { geometry: geometry1, material: material1 })
      const data = getComponent(testEntity, LineSegmentComponent)
      assertLineSegmentComponentEq(data as LineSegmentComponentData, Expected)

      const geometry2 = new BoxGeometry(2, 2, 2)
      const material2 = new MeshBasicMaterial({ color: 0x222222 })
      setComponent(testEntity, LineSegmentComponent, { geometry: geometry2, material: material2 })
      Expected.geometry = geometry2
      Expected.material = material2
      const result = getComponent(testEntity, LineSegmentComponent)
      assertLineSegmentComponentEq(result as LineSegmentComponentData, Expected)
    })
  }) //:: onSet

  describe('reactor', () => {
    beforeEach(() => {
      createEngine()
    })

    afterEach(() => {
      return destroyEngine()
    })

    it('should call addObjectToGroup(lineSegment) with the entity when it mounts', async () => {
      const testEntity = createEntity()
      assert.equal(hasComponent(testEntity, ObjectComponent), false)
      setComponent(testEntity, LineSegmentComponent, {
        geometry: new BoxGeometry(1, 1, 1),
        material: new MeshBasicMaterial({ color: 0x111111 })
      })
      await vi.waitFor(() => {
        assert.equal(hasComponent(testEntity, ObjectComponent), true)
      })
    })

    it('should set a VisibleComponent to the entity when it mounts', async () => {
      const testEntity = createEntity()
      assert.equal(hasComponent(testEntity, VisibleComponent), false)
      setComponent(testEntity, LineSegmentComponent, {
        geometry: new BoxGeometry(1, 1, 1),
        material: new MeshBasicMaterial({ color: 0x111111 })
      })
      await vi.waitFor(() => {
        assert.equal(hasComponent(testEntity, VisibleComponent), true)
      })
    })

    it('should call removeObjectFromGroup(lineSegment) with the entity when it unmounts', async () => {
      const testEntity = createEntity()
      assert.equal(hasComponent(testEntity, ObjectComponent), false)
      setComponent(testEntity, LineSegmentComponent, {
        geometry: new BoxGeometry(1, 1, 1),
        material: new MeshBasicMaterial({ color: 0x111111 })
      })
      await vi.waitFor(() => {
        assert.equal(hasComponent(testEntity, ObjectComponent), true)
      })
      removeComponent(testEntity, ObjectComponent)
      await vi.waitFor(() => {
        assert.equal(hasComponent(testEntity, ObjectComponent), false)
      })
    })

    it('should trigger when component.name changes', async () => {
      const testEntity = createEntity()
      const Expected = 'line-segment'
      assert.equal(hasComponent(testEntity, NameComponent), false)
      const geometry = new BoxGeometry(1, 1, 1)
      setComponent(testEntity, LineSegmentComponent, {
        geometry: geometry,
        material: new MeshBasicMaterial({ color: 0x111111 })
      })
      await vi.waitFor(() => {
        assert.equal(hasComponent(testEntity, NameComponent), true)
      })
      setComponent(testEntity, LineSegmentComponent, {
        name: Expected,
        geometry: geometry,
        material: new MeshBasicMaterial({ color: 0x111111 })
      })
      await vi.waitFor(() => {
        const result = getComponent(testEntity, NameComponent)
        assert.equal(result, Expected)
      })
    })

    it('should trigger when component.layerMask changes', async () => {
      const testEntity = createEntity()
      const Expected = (ObjectLayerMasks.Avatar | ObjectLayerMasks.Gizmos) as ObjectLayerMask
      assert.equal(hasComponent(testEntity, ObjectLayerMaskComponent), false)
      const geometry = new BoxGeometry(1, 1, 1)
      setComponent(testEntity, LineSegmentComponent, {
        geometry: geometry,
        material: new MeshBasicMaterial({ color: 0x111111 })
      })
      await vi.waitFor(() => {
        assert.equal(hasComponent(testEntity, ObjectLayerMaskComponent), true)
        assert.notEqual(ObjectLayerMaskComponent.mask[testEntity], Expected)
      })
      setComponent(testEntity, LineSegmentComponent, {
        layerMask: Expected,
        geometry: geometry,
        material: new MeshBasicMaterial({ color: 0x111111 })
      })
      await vi.waitFor(() => {
        assert.equal(ObjectLayerMaskComponent.mask[testEntity], Expected)
      })
    })

    it('should set the LineSegment layerMask correctly', async () => {
      const entity = createEntity()
      const geometry = new BoxGeometry(1, 1, 1)
      const material = new MeshBasicMaterial({ color: 0xffff00 })

      const layerMask = ObjectLayerMasks.NodeHelper
      const layer = ObjectLayers.NodeHelper

      setComponent(entity, LineSegmentComponent, {
        geometry: geometry,
        material: material,
        layerMask: layerMask
      })

      await vi.waitFor(() => {
        assert(hasComponent(entity, LineSegmentComponent))
        assert(hasComponent(entity, ObjectComponent))
        assert(hasComponent(entity, ObjectLayerMaskComponent))
        assert(hasComponent(entity, ObjectLayerComponents[layer]))
      })

      const object = getComponent(entity, ObjectComponent)
      const lineSegments = object as LineSegments
      assert(lineSegments.isLineSegments)
      assert(lineSegments.layers.mask === layerMask)
      removeEntity(entity)
    })

    it('should trigger when component.color changes', async () => {
      const testEntity = createEntity()
      const Expected = new Color('#123456')
      assert.equal(hasComponent(testEntity, NameComponent), false)
      const geometry = new BoxGeometry(1, 1, 1)
      const material = new MeshBasicMaterial({ color: 0x111111 })
      setComponent(testEntity, LineSegmentComponent, {
        geometry: geometry,
        material: material
      })
      await vi.waitFor(() => {
        assert.notDeepEqual(
          (getComponent(testEntity, LineSegmentComponent).material as MeshBasicMaterial).color,
          Expected
        )
      })
      setComponent(testEntity, LineSegmentComponent, {
        color: Expected,
        geometry: geometry,
        material: material
      })
      await vi.waitFor(() => {
        const result = (getComponent(testEntity, LineSegmentComponent).material as MeshBasicMaterial).color
        assert.deepEqual(result, Expected)
      })
    })

    it('should create a LineSegmentComponent correctly', async () => {
      const entity = createEntity()
      const geometry = new BoxGeometry(1, 1, 1)
      const material = new MeshBasicMaterial({ color: 0xffff00 })

      setComponent(entity, LineSegmentComponent, { geometry: geometry, material: material })

      await vi.waitFor(() => {
        assert(hasComponent(entity, LineSegmentComponent))
      })

      removeEntity(entity)

      await vi.waitFor(() => {
        assert(!hasComponent(entity, LineSegmentComponent))
      })
    })

    it('should update the LineSegmentComponent data correctly', async () => {
      const entity = createEntity()
      const geometry = new BoxGeometry(1, 1, 1)
      const material = new MeshBasicMaterial({ color: 0xffff00 })

      const spy = sinon.spy()
      geometry.dispose = spy
      material.dispose = spy

      const geometry2 = new SphereGeometry(0.5)
      const material2 = new LineBasicMaterial()

      geometry2.dispose = spy
      material2.dispose = spy

      setComponent(entity, LineSegmentComponent, { geometry: geometry, material: material })

      await vi.waitFor(() => {
        assert(hasComponent(entity, LineSegmentComponent))
        assert(hasComponent(entity, ObjectComponent))
      })

      const lineSegmentComponent = getMutableComponent(entity, LineSegmentComponent)
      lineSegmentComponent.geometry.set(geometry2)
      lineSegmentComponent.material.set(material2)

      await vi.waitFor(
        () => {
          sinon.assert.calledTwice(spy)
        },
        { timeout: 5000 }
      )
    })

    it('should remove the LineSegmentComponent resources when it is unmounted', async () => {
      const entity = createEntity()
      const geometry = new BoxGeometry(1, 1, 1)
      const material = new MeshBasicMaterial({ color: 0xffff00 })

      const spy = sinon.spy()
      geometry.dispose = spy
      material.dispose = spy

      setComponent(entity, LineSegmentComponent, { geometry: geometry, material: material })

      await vi.waitFor(() => {
        assert(hasComponent(entity, LineSegmentComponent))
        assert(hasComponent(entity, ObjectComponent))
      })

      removeComponent(entity, LineSegmentComponent)

      await vi.waitFor(
        () => {
          assert(!hasComponent(entity, LineSegmentComponent))
          sinon.assert.calledTwice(spy)
        },
        { timeout: 5000 }
      )

      removeEntity(entity)
    })
  }) //:: reactor
})
