/*
CPAL-1.0 License

The contents of this file are subject to the Common Public Attribution License
Version 1.0. (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at
https://github.com/ir-engine/ir-engine/blob/dev/LICENSE.
The License is based on the Mozilla Public License Version 1.1, but Sections 14
and 15 have been added to cover use of software over a computer network and 
provide for limited attribution for the Original Developer. In addition, 
Exhibit A has been modified to be consistent with Exhibit B.

Software distributed under the License is distributed on an "AS IS" basis,
WITHOUT WARRANTY OF ANY KIND, either express or implied. See the License for the
specific language governing rights and limitations under the License.

The Original Code is Infinite Reality Engine.

The Original Developer is the Initial Developer. The Initial Developer of the
Original Code is the Infinite Reality Engine team.

All portions of the code written by the Infinite Reality Engine team are Copyright © 2021-2025
Infinite Reality Engine. All Rights Reserved.
*/

import type { SVGProps } from 'react'
import * as React from 'react'
import { Ref, forwardRef } from 'react'
const Component9Lg = (props: SVGProps<SVGSVGElement>, ref: Ref<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="1em"
    height="1em"
    fill="none"
    viewBox="0 0 24 25"
    role="img"
    stroke="currentColor"
    ref={ref}
    {...props}
  >
    <g stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth={2}>
      <path d="M2.421 17.603c3.054 5.293 9.819 7.107 15.11 4.051A11.05 11.05 0 0 0 23 13.232m-1.421-6.697c-3.054-5.293-9.819-7.107-15.11-4.05A11.05 11.05 0 0 0 1 10.907" />
      <path d="M18.342 10.388a6.494 6.494 0 0 1-4.592 7.954m4.592-7.954a6.494 6.494 0 0 0-7.954-4.592m7.954 4.592 1.568-.42m-6.16 8.374a6.494 6.494 0 0 1-7.954-4.592m7.954 4.592.42 1.568m-8.374-6.16a6.494 6.494 0 0 1 4.592-7.954M5.796 13.75l-1.568.42m6.16-8.374-.42-1.568m4.453 7.21A2.435 2.435 0 1 1 9.717 12.7a2.435 2.435 0 0 1 4.704-1.26" />
    </g>
  </svg>
)
const ForwardRef = forwardRef(Component9Lg)
export default ForwardRef
