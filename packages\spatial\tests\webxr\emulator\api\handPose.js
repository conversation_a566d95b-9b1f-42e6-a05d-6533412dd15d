/*
CPAL-1.0 License

The contents of this file are subject to the Common Public Attribution License
Version 1.0. (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at
https://github.com/ir-engine/ir-engine/blob/dev/LICENSE.
The License is based on the Mozilla Public License Version 1.1, but Sections 14
and 15 have been added to cover use of software over a computer network and 
provide for limited attribution for the Original Developer. In addition, 
Exhibit A has been modified to be consistent with Exhibit B.

Software distributed under the License is distributed on an "AS IS" basis,
WITHOUT WARRANTY OF ANY KIND, either express or implied. See the License for the
specific language governing rights and limitations under the License.

The Original Code is Infinite Reality Engine.

The Original Developer is the Initial Developer. The Initial Developer of the
Original Code is the Infinite Reality Engine team.

All portions of the code written by the Infinite Reality Engine team are Copyright © 2021-2025
Infinite Reality Engine. All Rights Reserved.
*/


export const handPose = {
	wrist: {
		radius: 0.021460847929120064,
		transform: [0, 0, -1, 0, -1, 0, 0, 0, 0, 1, 0, 0, -0.037, 0.065, 0.002, 1],
	},
	'thumb-metacarpal': {
		radius: 0.019382517784833908,
		transform: [
			0.262, -0.498, 0.827, 0, 0.912, 0.408, -0.043, 0, -0.315, 0.765, 0.561, 0,
			-0.018, 0.03, -0.026, 1,
		],
	},
	'thumb-phalanx-proximal': {
		radius: 0.01228295173496008,
		transform: [
			0.306, -0.143, 0.941, 0, 0.9, 0.367, -0.237, 0, -0.311, 0.919, 0.241, 0,
			-0.008, 0.005, -0.045, 1,
		],
	},
	'thumb-phalanx-distal': {
		radius: 0.009768804535269737,
		transform: [
			0.083, -0.018, 0.996, 0, 0.892, 0.448, -0.066, 0, -0.445, 0.894, 0.054, 0,
			0.002, -0.026, -0.053, 1,
		],
	},
	'thumb-tip': {
		radius: 0.008768804371356964,
		transform: [
			0.083, -0.018, 0.996, 0, 0.892, 0.448, -0.066, 0, -0.445, 0.894, 0.054, 0,
			0.013, -0.049, -0.055, 1,
		],
	},
	'index-finger-metacarpal': {
		radius: 0.021228281781077385,
		transform: [0, 0, -1, 0, -1, 0, 0, 0, 0, 1, 0, 0, -0.026, 0.028, -0.017, 1],
	},
	'index-finger-phalanx-proximal': {
		radius: 0.010295259766280651,
		transform: [
			0.066, -0.039, -0.997, 0, -0.981, -0.184, -0.058, 0, -0.181, 0.982, -0.05,
			0, -0.029, -0.031, -0.021, 1,
		],
	},
	'index-finger-phalanx-intermediate': {
		radius: 0.00853810179978609,
		transform: [
			0.018, -0.062, -0.998, 0, -0.914, -0.406, 0.008, 0, -0.406, 0.912, -0.064,
			0, -0.022, -0.068, -0.019, 1,
		],
	},
	'index-finger-phalanx-distal': {
		radius: 0.007636196445673704,
		transform: [
			0.01, -0.122, -0.992, 0, -0.896, -0.442, 0.045, 0, -0.444, 0.888, -0.114,
			0, -0.013, -0.09, -0.018, 1,
		],
	},
	'index-finger-tip': {
		radius: 0.006636196281760931,
		transform: [
			0.01, -0.122, -0.992, 0, -0.896, -0.442, 0.045, 0, -0.444, 0.888, -0.114,
			0, -0.004, -0.11, -0.015, 1,
		],
	},
	'middle-finger-metacarpal': {
		radius: 0.021231964230537415,
		transform: [0, 0, -1, 0, -1, 0, 0, 0, 0, 1, 0, 0, -0.028, 0.03, -0.001, 1],
	},
	'middle-finger-phalanx-proximal': {
		radius: 0.01117393933236599,
		transform: [
			-0.009, -0.166, -0.986, 0, -0.945, -0.322, 0.063, 0, -0.328, 0.932,
			-0.154, 0, -0.034, -0.03, 0.001, 1,
		],
	},
	'middle-finger-phalanx-intermediate': {
		radius: 0.008030958473682404,
		transform: [
			-0.027, -0.182, -0.983, 0, -0.87, -0.48, 0.113, 0, -0.493, 0.858, -0.145,
			0, -0.02, -0.07, 0.007, 1,
		],
	},
	'middle-finger-phalanx-distal': {
		radius: 0.007629410829395056,
		transform: [
			-0.099, -0.222, -0.97, 0, -0.885, -0.426, 0.188, 0, -0.455, 0.877, -0.154,
			0, -0.006, -0.094, 0.011, 1,
		],
	},
	'middle-finger-tip': {
		radius: 0.006629410665482283,
		transform: [
			-0.099, -0.222, -0.97, 0, -0.885, -0.426, 0.188, 0, -0.455, 0.877, -0.154,
			0, 0.004, -0.116, 0.016, 1,
		],
	},
	'ring-finger-metacarpal': {
		radius: 0.019088275730609894,
		transform: [0, 0, -1, 0, -1, 0, 0, 0, 0, 1, 0, 0, -0.031, 0.03, 0.017, 1],
	},
	'ring-finger-phalanx-proximal': {
		radius: 0.00992213748395443,
		transform: [
			-0.088, -0.279, -0.956, 0, -0.919, -0.347, 0.186, 0, -0.384, 0.895,
			-0.226, 0, -0.03, -0.023, 0.02, 1,
		],
	},
	'ring-finger-phalanx-intermediate': {
		radius: 0.007611672393977642,
		transform: [
			-0.15, -0.308, -0.939, 0, -0.844, -0.454, 0.284, 0, -0.514, 0.836, -0.192,
			0, -0.015, -0.058, 0.029, 1,
		],
	},
	'ring-finger-phalanx-distal': {
		radius: 0.007231088820844889,
		transform: [
			-0.194, -0.266, -0.944, 0, -0.79, -0.528, 0.311, 0, -0.582, 0.806, -0.108,
			0, -0.001, -0.081, 0.034, 1,
		],
	},
	'ring-finger-tip': {
		radius: 0.0062310886569321156,
		transform: [
			-0.194, -0.266, -0.944, 0, -0.79, -0.528, 0.311, 0, -0.582, 0.806, -0.108,
			0, 0.012, -0.101, 0.037, 1,
		],
	},
	'pinky-finger-metacarpal': {
		radius: 0.01808827556669712,
		transform: [
			-0.396, -0.279, -0.875, 0, -0.914, 0.023, 0.406, 0, -0.094, 0.96, -0.264,
			0, -0.027, 0.031, 0.025, 1,
		],
	},
	'pinky-finger-phalanx-proximal': {
		radius: 0.008483353070914745,
		transform: [
			-0.225, -0.362, -0.905, 0, -0.881, -0.321, 0.348, 0, -0.417, 0.875,
			-0.246, 0, -0.023, -0.013, 0.037, 1,
		],
	},
	'pinky-finger-phalanx-intermediate': {
		radius: 0.0067641944624483585,
		transform: [
			-0.263, -0.461, -0.847, 0, -0.772, -0.426, 0.472, 0, -0.579, 0.778,
			-0.244, 0, -0.01, -0.04, 0.045, 1,
		],
	},
	'pinky-finger-phalanx-distal': {
		radius: 0.0064259846694767475,
		transform: [
			-0.318, -0.383, -0.867, 0, -0.762, -0.441, 0.474, 0, -0.564, 0.812,
			-0.151, 0, 0.002, -0.055, 0.05, 1,
		],
	},
	'pinky-finger-tip': {
		radius: 0.005425984505563974,
		transform: [
			-0.318, -0.383, -0.867, 0, -0.762, -0.441, 0.474, 0, -0.564, 0.812,
			-0.151, 0, 0.013, -0.074, 0.054, 1,
		],
	},
};

export const pinchHandPose = {
	wrist: {
		radius: 0.021460847929120064,
		transform: [0, 0, -1, 0, -1, 0, 0, 0, 0, 1, 0, 0, -0.037, 0.066, 0, 1],
	},
	'thumb-metacarpal': {
		radius: 0.019382517784833908,
		transform: [
			-0.095, -0.306, 0.947, 0, 0.764, 0.587, 0.266, 0, -0.638, 0.749, 0.178, 0,
			-0.013, 0.028, -0.021, 1,
		],
	},
	'thumb-phalanx-proximal': {
		radius: 0.01228295173496008,
		transform: [
			-0.141, 0.041, 0.989, 0, 0.85, 0.517, 0.1, 0, -0.508, 0.855, -0.108, 0,
			0.008, 0.003, -0.026, 1,
		],
	},
	'thumb-phalanx-distal': {
		radius: 0.009768804535269737,
		transform: [
			-0.338, 0.052, 0.94, 0, 0.751, 0.616, 0.237, 0, -0.567, 0.786, -0.247, 0,
			0.025, -0.026, -0.023, 1,
		],
	},
	'thumb-tip': {
		radius: 0.008768804371356964,
		transform: [
			-0.338, 0.052, 0.94, 0, 0.751, 0.616, 0.237, 0, -0.567, 0.786, -0.247, 0,
			0.039, -0.045, -0.018, 1,
		],
	},
	'index-finger-metacarpal': {
		radius: 0.021228281781077385,
		transform: [0, 0, -1, 0, -1, 0, 0, 0, 0, 1, 0, 0, -0.024, 0.028, -0.015, 1],
	},
	'index-finger-phalanx-proximal': {
		radius: 0.010295259766280651,
		transform: [
			0.074, 0.097, -0.993, 0, -0.737, -0.665, -0.12, 0, -0.672, 0.74, 0.022, 0,
			-0.03, -0.03, -0.023, 1,
		],
	},
	'index-finger-phalanx-intermediate': {
		radius: 0.00853810179978609,
		transform: [
			0.046, 0.054, -0.997, 0, 0.161, -0.986, -0.046, 0, -0.986, -0.158, -0.054,
			0, -0.005, -0.058, -0.024, 1,
		],
	},
	'index-finger-phalanx-distal': {
		radius: 0.007636196445673704,
		transform: [
			0.1, 0.037, -0.994, 0, 0.497, -0.867, 0.018, 0, -0.862, -0.496, -0.105, 0,
			0.019, -0.054, -0.023, 1,
		],
	},
	'index-finger-tip': {
		radius: 0.006636196281760931,
		transform: [
			0.1, 0.037, -0.994, 0, 0.497, -0.867, 0.018, 0, -0.862, -0.496, -0.105, 0,
			0.039, -0.044, -0.02, 1,
		],
	},
	'middle-finger-metacarpal': {
		radius: 0.021231964230537415,
		transform: [0, 0, -1, 0, -1, 0, 0, 0, 0, 1, 0, 0, -0.028, 0.03, 0.001, 1],
	},
	'middle-finger-phalanx-proximal': {
		radius: 0.01117393933236599,
		transform: [
			-0.014, -0.044, -0.999, 0, -0.96, -0.279, 0.026, 0, -0.28, 0.959, -0.038,
			0, -0.035, -0.03, -0.001, 1,
		],
	},
	'middle-finger-phalanx-intermediate': {
		radius: 0.008030958473682404,
		transform: [
			-0.034, -0.059, -0.998, 0, -0.773, -0.631, 0.064, 0, -0.634, 0.773,
			-0.024, 0, -0.023, -0.071, 0, 1,
		],
	},
	'middle-finger-phalanx-distal': {
		radius: 0.007629410829395056,
		transform: [
			-0.1, -0.114, -0.988, 0, -0.775, -0.614, 0.149, 0, -0.624, 0.781, -0.027,
			0, -0.005, -0.092, 0.001, 1,
		],
	},
	'middle-finger-tip': {
		radius: 0.006629410665482283,
		transform: [
			-0.1, -0.114, -0.988, 0, -0.775, -0.614, 0.149, 0, -0.624, 0.781, -0.027,
			0, 0.009, -0.112, 0.002, 1,
		],
	},
	'ring-finger-metacarpal': {
		radius: 0.019088275730609894,
		transform: [0, 0, -1, 0, -1, 0, 0, 0, 0, 1, 0, 0, -0.031, 0.031, 0.015, 1],
	},
	'ring-finger-phalanx-proximal': {
		radius: 0.00992213748395443,
		transform: [
			-0.095, -0.202, -0.975, 0, -0.972, -0.193, 0.135, 0, -0.215, 0.96, -0.178,
			0, -0.031, -0.023, 0.018, 1,
		],
	},
	'ring-finger-phalanx-intermediate': {
		radius: 0.007611672393977642,
		transform: [
			-0.166, -0.223, -0.961, 0, -0.825, -0.503, 0.259, 0, -0.541, 0.835, -0.1,
			0, -0.022, -0.06, 0.025, 1,
		],
	},
	'ring-finger-phalanx-distal': {
		radius: 0.007231088820844889,
		transform: [
			-0.213, -0.183, -0.96, 0, -0.748, -0.601, 0.28, 0, -0.628, 0.778, -0.009,
			0, -0.008, -0.082, 0.027, 1,
		],
	},
	'ring-finger-tip': {
		radius: 0.0062310886569321156,
		transform: [
			-0.213, -0.183, -0.96, 0, -0.748, -0.601, 0.28, 0, -0.628, 0.778, -0.009,
			0, 0.006, -0.102, 0.028, 1,
		],
	},
	'pinky-finger-metacarpal': {
		radius: 0.01808827556669712,
		transform: [
			-0.396, -0.279, -0.875, 0, -0.914, 0.023, 0.406, 0, -0.094, 0.96, -0.264,
			0, -0.028, 0.032, 0.023, 1,
		],
	},
	'pinky-finger-phalanx-proximal': {
		radius: 0.008483353070914745,
		transform: [
			-0.219, -0.358, -0.908, 0, -0.933, -0.194, 0.302, 0, -0.284, 0.913,
			-0.291, 0, -0.024, -0.012, 0.035, 1,
		],
	},
	'pinky-finger-phalanx-intermediate': {
		radius: 0.0067641944624483585,
		transform: [
			-0.277, -0.452, -0.848, 0, -0.784, -0.404, 0.472, 0, -0.556, 0.795,
			-0.242, 0, -0.015, -0.04, 0.044, 1,
		],
	},
	'pinky-finger-phalanx-distal': {
		radius: 0.0064259846694767475,
		transform: [
			-0.333, -0.373, -0.866, 0, -0.722, -0.49, 0.489, 0, -0.607, 0.788, -0.106,
			0, -0.004, -0.056, 0.049, 1,
		],
	},
	'pinky-finger-tip': {
		radius: 0.005425984505563974,
		transform: [
			-0.333, -0.373, -0.866, 0, -0.722, -0.49, 0.489, 0, -0.607, 0.788, -0.106,
			0, 0.009, -0.074, 0.052, 1,
		],
	},
};

export const pointHandPose = {
	wrist: {
		radius: 0.021460847929120064,
		transform: [0, 0, -1, 0, -1, 0, 0, 0, 0, 1, 0, 0, -0.037, 0.066, 0.002, 1],
	},
	'thumb-metacarpal': {
		radius: 0.019382517784833908,
		transform: [
			0.105, -0.416, 0.903, 0, 0.84, 0.524, 0.144, 0, -0.533, 0.743, 0.405, 0,
			-0.016, 0.029, -0.024, 1,
		],
	},
	'thumb-phalanx-proximal': {
		radius: 0.01228295173496008,
		transform: [
			-0.115, 0.252, 0.961, 0, 0.88, 0.475, -0.019, 0, -0.461, 0.843, -0.276, 0,
			0.002, 0.004, -0.037, 1,
		],
	},
	'thumb-phalanx-distal': {
		radius: 0.009768804535269737,
		transform: [
			-0.531, 0.705, 0.47, 0, 0.815, 0.577, 0.056, 0, -0.232, 0.413, -0.881, 0,
			0.017, -0.024, -0.028, 1,
		],
	},
	'thumb-tip': {
		radius: 0.008768804371356964,
		transform: [
			-0.531, 0.705, 0.47, 0, 0.815, 0.577, 0.056, 0, -0.232, 0.413, -0.881, 0,
			0.023, -0.035, -0.007, 1,
		],
	},
	'index-finger-metacarpal': {
		radius: 0.021228281781077385,
		transform: [0, 0, -1, 0, -1, 0, 0, 0, 0, 1, 0, 0, -0.025, 0.028, -0.016, 1],
	},
	'index-finger-phalanx-proximal': {
		radius: 0.010295259766280651,
		transform: [
			0.066, -0.02, -0.998, 0, -0.976, -0.211, -0.06, 0, -0.21, 0.977, -0.033,
			0, -0.03, -0.03, -0.022, 1,
		],
	},
	'index-finger-phalanx-intermediate': {
		radius: 0.00853810179978609,
		transform: [
			0.019, -0.045, -0.999, 0, -0.928, -0.374, -0.001, 0, -0.373, 0.926,
			-0.049, 0, -0.022, -0.067, -0.021, 1,
		],
	},
	'index-finger-phalanx-distal': {
		radius: 0.007636196445673704,
		transform: [
			0.006, -0.106, -0.994, 0, -0.956, -0.294, 0.025, 0, -0.295, 0.95, -0.103,
			0, -0.013, -0.09, -0.02, 1,
		],
	},
	'index-finger-tip': {
		radius: 0.006636196281760931,
		transform: [
			0.006, -0.106, -0.994, 0, -0.956, -0.294, 0.025, 0, -0.295, 0.95, -0.103,
			0, -0.007, -0.111, -0.017, 1,
		],
	},
	'middle-finger-metacarpal': {
		radius: 0.021231964230537415,
		transform: [0, 0, -1, 0, -1, 0, 0, 0, 0, 1, 0, 0, -0.028, 0.03, 0, 1],
	},
	'middle-finger-phalanx-proximal': {
		radius: 0.01117393933236599,
		transform: [
			-0.006, -0.157, -0.988, 0, -0.314, -0.937, 0.151, 0, -0.949, 0.311,
			-0.044, 0, -0.034, -0.03, 0, 1,
		],
	},
	'middle-finger-phalanx-intermediate': {
		radius: 0.008030958473682404,
		transform: [
			-0.003, -0.183, -0.983, 0, 0.99, -0.142, 0.024, 0, -0.144, -0.973, 0.182,
			0, 0.006, -0.043, 0.002, 1,
		],
	},
	'middle-finger-phalanx-distal': {
		radius: 0.007629410829395056,
		transform: [
			0.174, -0.155, -0.972, 0, 0.593, 0.805, -0.022, 0, 0.786, -0.573, 0.232,
			0, 0.01, -0.017, -0.003, 1,
		],
	},
	'middle-finger-tip': {
		radius: 0.006629410665482283,
		transform: [
			0.174, -0.155, -0.972, 0, 0.593, 0.805, -0.022, 0, 0.786, -0.573, 0.232,
			0, -0.009, -0.001, -0.009, 1,
		],
	},
	'ring-finger-metacarpal': {
		radius: 0.019088275730609894,
		transform: [0, 0, -1, 0, -1, 0, 0, 0, 0, 1, 0, 0, -0.031, 0.031, 0.017, 1],
	},
	'ring-finger-phalanx-proximal': {
		radius: 0.00992213748395443,
		transform: [
			-0.087, -0.215, -0.973, 0, -0.186, -0.956, 0.228, 0, -0.979, 0.201, 0.043,
			0, -0.03, -0.023, 0.019, 1,
		],
	},
	'ring-finger-phalanx-intermediate': {
		radius: 0.007611672393977642,
		transform: [
			-0.074, -0.305, -0.949, 0, 0.991, -0.126, -0.037, 0, -0.108, -0.944,
			0.312, 0, 0.008, -0.031, 0.017, 1,
		],
	},
	'ring-finger-phalanx-distal': {
		radius: 0.007231088820844889,
		transform: [
			-0.001, -0.333, -0.943, 0, 0.583, 0.766, -0.271, 0, 0.812, -0.55, 0.193,
			0, 0.011, -0.006, 0.009, 1,
		],
	},
	'ring-finger-tip': {
		radius: 0.0062310886569321156,
		transform: [
			-0.001, -0.333, -0.943, 0, 0.583, 0.766, -0.271, 0, 0.812, -0.55, 0.193,
			0, -0.008, 0.009, 0.004, 1,
		],
	},
	'pinky-finger-metacarpal': {
		radius: 0.01808827556669712,
		transform: [
			-0.396, -0.279, -0.875, 0, -0.914, 0.023, 0.406, 0, -0.094, 0.96, -0.264,
			0, -0.028, 0.032, 0.025, 1,
		],
	},
	'pinky-finger-phalanx-proximal': {
		radius: 0.008483353070914745,
		transform: [
			-0.266, -0.273, -0.925, 0, -0.203, -0.922, 0.33, 0, -0.942, 0.276, 0.19,
			0, -0.023, -0.012, 0.037, 1,
		],
	},
	'pinky-finger-phalanx-intermediate': {
		radius: 0.0067641944624483585,
		transform: [
			-0.167, -0.416, -0.894, 0, 0.961, -0.272, -0.053, 0, -0.221, -0.868,
			0.445, 0, 0.006, -0.021, 0.031, 1,
		],
	},
	'pinky-finger-phalanx-distal': {
		radius: 0.0064259846694767475,
		transform: [
			-0.147, -0.491, -0.859, 0, 0.681, 0.579, -0.448, 0, 0.717, -0.651, 0.249,
			0, 0.01, -0.003, 0.022, 1,
		],
	},
	'pinky-finger-tip': {
		radius: 0.005425984505563974,
		transform: [
			-0.147, -0.491, -0.859, 0, 0.681, 0.579, -0.448, 0, 0.717, -0.651, 0.249,
			0, -0.005, 0.012, 0.016, 1,
		],
	},
};

export const relaxedHandPose = {
	wrist: {
		radius: 0.021460847929120064,
		transform: [0, 0, -1, 0, -1, 0, 0, 0, 0, 1, 0, 0, -0.037, 0.066, 0.001, 1],
	},
	'thumb-metacarpal': {
		radius: 0.019382517784833908,
		transform: [
			0.067, -0.398, 0.915, 0, 0.805, 0.563, 0.186, 0, -0.589, 0.724, 0.358, 0,
			-0.015, 0.028, -0.024, 1,
		],
	},
	'thumb-phalanx-proximal': {
		radius: 0.01228295173496008,
		transform: [
			0.087, -0.137, 0.987, 0, 0.86, 0.511, -0.005, 0, -0.504, 0.849, 0.162, 0,
			0.004, 0.005, -0.035, 1,
		],
	},
	'thumb-phalanx-distal': {
		radius: 0.009768804535269737,
		transform: [
			-0.054, -0.22, 0.974, 0, 0.796, 0.58, 0.175, 0, -0.604, 0.784, 0.144, 0,
			0.021, -0.024, -0.041, 1,
		],
	},
	'thumb-tip': {
		radius: 0.008768804371356964,
		transform: [
			-0.054, -0.22, 0.974, 0, 0.796, 0.58, 0.175, 0, -0.604, 0.784, 0.144, 0,
			0.036, -0.043, -0.045, 1,
		],
	},
	'index-finger-metacarpal': {
		radius: 0.021228281781077385,
		transform: [0, 0, -1, 0, -1, 0, 0, 0, 0, 1, 0, 0, -0.025, 0.028, -0.016, 1],
	},
	'index-finger-phalanx-proximal': {
		radius: 0.010295259766280651,
		transform: [
			0.064, 0.038, -0.997, 0, -0.965, -0.253, -0.072, 0, -0.255, 0.967, 0.02,
			0, -0.03, -0.03, -0.022, 1,
		],
	},
	'index-finger-phalanx-intermediate': {
		radius: 0.00853810179978609,
		transform: [
			0.019, 0.012, -1, 0, -0.679, -0.734, -0.021, 0, -0.734, 0.68, -0.006, 0,
			-0.02, -0.067, -0.023, 1,
		],
	},
	'index-finger-phalanx-distal': {
		radius: 0.007636196445673704,
		transform: [
			0.037, -0.045, -0.998, 0, -0.546, -0.838, 0.018, 0, -0.837, 0.544, -0.055,
			0, -0.002, -0.084, -0.023, 1,
		],
	},
	'index-finger-tip': {
		radius: 0.006636196281760931,
		transform: [
			0.037, -0.045, -0.998, 0, -0.546, -0.838, 0.018, 0, -0.837, 0.544, -0.055,
			0, 0.016, -0.097, -0.021, 1,
		],
	},
	'middle-finger-metacarpal': {
		radius: 0.021231964230537415,
		transform: [0, 0, -1, 0, -1, 0, 0, 0, 0, 1, 0, 0, -0.028, 0.03, 0, 1],
	},
	'middle-finger-phalanx-proximal': {
		radius: 0.01117393933236599,
		transform: [
			-0.012, -0.101, -0.995, 0, -0.982, -0.188, 0.031, 0, -0.19, 0.977, -0.097,
			0, -0.035, -0.03, 0, 1,
		],
	},
	'middle-finger-phalanx-intermediate': {
		radius: 0.008030958473682404,
		transform: [
			-0.034, -0.114, -0.993, 0, -0.791, -0.604, 0.096, 0, -0.611, 0.788, -0.07,
			0, -0.026, -0.072, 0.004, 1,
		],
	},
	'middle-finger-phalanx-distal': {
		radius: 0.007629410829395056,
		transform: [
			-0.106, -0.17, -0.98, 0, -0.753, -0.63, 0.191, 0, -0.65, 0.758, -0.062, 0,
			-0.01, -0.094, 0.006, 1,
		],
	},
	'middle-finger-tip': {
		radius: 0.006629410665482283,
		transform: [
			-0.106, -0.17, -0.98, 0, -0.753, -0.63, 0.191, 0, -0.65, 0.758, -0.062, 0,
			0.006, -0.113, 0.008, 1,
		],
	},
	'ring-finger-metacarpal': {
		radius: 0.019088275730609894,
		transform: [0, 0, -1, 0, -1, 0, 0, 0, 0, 1, 0, 0, -0.031, 0.031, 0.016, 1],
	},
	'ring-finger-phalanx-proximal': {
		radius: 0.00992213748395443,
		transform: [
			-0.093, -0.232, -0.968, 0, -0.978, -0.163, 0.133, 0, -0.189, 0.959,
			-0.211, 0, -0.031, -0.023, 0.019, 1,
		],
	},
	'ring-finger-phalanx-intermediate': {
		radius: 0.007611672393977642,
		transform: [
			-0.165, -0.251, -0.954, 0, -0.82, -0.502, 0.274, 0, -0.548, 0.828, -0.123,
			0, -0.023, -0.06, 0.027, 1,
		],
	},
	'ring-finger-phalanx-distal': {
		radius: 0.007231088820844889,
		transform: [
			-0.215, -0.214, -0.953, 0, -0.708, -0.637, 0.303, 0, -0.672, 0.74, -0.015,
			0, -0.009, -0.082, 0.03, 1,
		],
	},
	'ring-finger-tip': {
		radius: 0.0062310886569321156,
		transform: [
			-0.215, -0.214, -0.953, 0, -0.708, -0.637, 0.303, 0, -0.672, 0.74, -0.015,
			0, 0.007, -0.101, 0.031, 1,
		],
	},
	'pinky-finger-metacarpal': {
		radius: 0.01808827556669712,
		transform: [
			-0.396, -0.279, -0.875, 0, -0.914, 0.023, 0.406, 0, -0.094, 0.96, -0.264,
			0, -0.028, 0.032, 0.024, 1,
		],
	},
	'pinky-finger-phalanx-proximal': {
		radius: 0.008483353070914745,
		transform: [
			-0.205, -0.431, -0.879, 0, -0.949, -0.131, 0.286, 0, -0.238, 0.893,
			-0.383, 0, -0.023, -0.012, 0.036, 1,
		],
	},
	'pinky-finger-phalanx-intermediate': {
		radius: 0.0067641944624483585,
		transform: [
			-0.269, -0.517, -0.813, 0, -0.812, -0.332, 0.48, 0, -0.518, 0.789, -0.331,
			0, -0.016, -0.04, 0.048, 1,
		],
	},
	'pinky-finger-phalanx-distal': {
		radius: 0.0064259846694767475,
		transform: [
			-0.321, -0.438, -0.84, 0, -0.759, -0.411, 0.505, 0, -0.566, 0.799, -0.201,
			0, -0.006, -0.056, 0.055, 1,
		],
	},
	'pinky-finger-tip': {
		radius: 0.005425984505563974,
		transform: [
			-0.321, -0.438, -0.84, 0, -0.759, -0.411, 0.505, 0, -0.566, 0.799, -0.201,
			0, 0.006, -0.074, 0.06, 1,
		],
	},
};

export const HAND_POSES = {
	pinch: pinchHandPose,
	relaxed: relaxedHandPose,
	point: pointHandPose,
};
