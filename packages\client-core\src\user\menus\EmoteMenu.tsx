/*
CPAL-1.0 License

The contents of this file are subject to the Common Public Attribution License
Version 1.0. (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at
https://github.com/ir-engine/ir-engine/blob/dev/LICENSE.
The License is based on the Mozilla Public License Version 1.1, but Sections 14
and 15 have been added to cover use of software over a computer network and 
provide for limited attribution for the Original Developer. In addition, 
Exhibit A has been modified to be consistent with Exhibit B.

Software distributed under the License is distributed on an "AS IS" basis,
WITHOUT WARRANTY OF ANY KIND, either express or implied. See the License for the
specific language governing rights and limitations under the License.

The Original Code is Infinite Reality Engine.

The Original Developer is the Initial Developer. The Initial Developer of the
Original Code is the Infinite Reality Engine team.

All portions of the code written by the Infinite Reality Engine team are Copyright © 2021-2025
Infinite Reality Engine. All Rights Reserved.
*/

import { UUIDComponent } from '@ir-engine/ecs'
import { emoteAnimations, preloadedAnimations } from '@ir-engine/engine/src/avatar/animation/Util'
import { AvatarComponent } from '@ir-engine/engine/src/avatar/components/AvatarComponent'
import { AvatarNetworkAction } from '@ir-engine/engine/src/avatar/state/AvatarNetworkActions'
import { dispatchAction, useHookstate } from '@ir-engine/hyperflux'
import { isMobile } from '@ir-engine/spatial/src/common/functions/isMobile'
import React, { HTMLProps, useLayoutEffect, useRef } from 'react'
import { ModalState } from '../../common/services/ModalState'

const iconItems = [
  {
    icon: (props: HTMLProps<SVGGElement>) => (
      <g {...props}>
        <path
          d="M14 196.5c0-17.949 14.55-32.5 32.5-32.5S79 178.551 79 196.5 64.45 229 46.5 229 14 214.449 14 196.5"
          fill="#DDE1E5"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M35.894 191.16a9.4 9.4 0 0 0-1.259 2.955c.208.153.394.339.547.558.328.471.482 1.073.361 1.686l-.328 1.882 4.027-8.941a2.3 2.3 0 0 1 1.259-1.182c.438-.164.93-.197 1.4-.066a1.9 1.9 0 0 0 0-.601 1.67 1.67 0 0 0-1.947-1.336c-.416.077-.81.318-1.073.701l-2.977 4.344zm23.047-8.962a.895.895 0 0 1 ************.88 0 0 1-.11 1.237l-2.494 2.101a.88.88 0 0 1-1.237-.11.87.87 0 0 1 .11-1.236zm-.788 7.441a.883.883 0 1 1 .142-1.762l3.25.274c.482.044.854.47.81.963a.88.88 0 0 1-.952.799zm-5.264-9.827a.9.9 0 0 1 .963-.81.88.88 0 0 1 .799.952l-.274 3.25a.88.88 0 0 1-.952.81.88.88 0 0 1-.81-.952zm-.372 18.276c.186-.416.186-.876.044-1.281a1.7 1.7 0 0 0-.876-.93 1.68 1.68 0 0 0-1.28-.033 1.67 1.67 0 0 0-.93.865l-2.277 5.045a.293.293 0 0 1-.383.142c-.153-.066-.219-.241-.153-.383l2.276-5.045 1.05-2.32c.187-.427.187-.875.034-1.28a1.6 1.6 0 0 0-.876-.931 1.7 1.7 0 0 0-1.27-.043 1.66 1.66 0 0 0-.93.875l-3.326 7.365c-.066.153-.241.219-.383.153a.296.296 0 0 1-.154-.394l3.327-7.365.318-.711a1.65 1.65 0 0 0 .032-1.269 1.62 1.62 0 0 0-.875-.931 1.6 1.6 0 0 0-1.27-.043 1.66 1.66 0 0 0-.93.875l-.328.711-3.316 7.365a.3.3 0 0 1-.394.143.293.293 0 0 1-.142-.383l3.316-7.376c.197-.416.197-.876.044-1.27a1.66 1.66 0 0 0-.876-.93 1.63 1.63 0 0 0-1.28-.044c-.394.153-.734.46-.93.876l-5.013 11.118a.297.297 0 0 1-.394.154.3.3 0 0 1-.164-.34l.766-4.224a1.7 1.7 0 0 0-.273-1.247 1.7 1.7 0 0 0-1.084-.69 1.7 1.7 0 0 0-1.247.274c-.35.241-.602.624-.69 1.072l-.93 5.187v.011a9.63 9.63 0 0 0 .941 5.801 9.53 9.53 0 0 0 4.553 4.333c2.385 1.084 5 1.084 7.288.219a9.5 9.5 0 0 0 5.319-4.979l1.587-3.535zm2.156-2.999-1.609 1.412c.022.033.033.066.044.098a2.2 2.2 0 0 1-.055 1.729l-1.97 4.389 2.003-1.784 3.797-3.338a1.7 1.7 0 0 0 .558-1.149 1.7 1.7 0 0 0-.415-1.215 1.72 1.72 0 0 0-1.15-.558 1.66 1.66 0 0 0-1.203.416m-.69-4.64-3.26 2.889c.021.35-.045.712-.198 1.051l-.427.941c.033-.011.066-.033.099-.044a2.3 2.3 0 0 1 1.729.055c.34.153.613.372.832.646l1.52-1.347 1.916-1.685a1.67 1.67 0 0 0 .142-2.353 1.6 1.6 0 0 0-1.149-.558 1.65 1.65 0 0 0-1.204.405m-6.522.525c.01.154 0 .318-.022.471l.23-.099a2.22 2.22 0 0 1 1.729.055c.558.252.974.711 1.182 1.259v.011l3.01-2.66.58-.514c.35-.306.536-.722.557-1.149a1.6 1.6 0 0 0-.404-1.204 1.63 1.63 0 0 0-1.15-.558 1.7 1.7 0 0 0-1.214.405zm-3.053-1.948a2.2 2.2 0 0 1 1.729.055 2.23 2.23 0 0 1 1.17 1.237l3.689-3.25c.339-.307.525-.723.558-1.15a1.66 1.66 0 0 0-.416-1.203 1.66 1.66 0 0 0-2.353-.154l-5.406 4.772.033.098c.***************.076.23.252-.284.57-.503.92-.635"
          fill="#000"
        />
        <path
          d="M33.266 207.453c.057.131.076.352.038.443l-1.15 2.708 8 3.396.88-2.077c.077-.18.211-.23.345-.28z"
          fill="#000"
        />
      </g>
    ),
    stateName: 'clap',
    description: 'Clap Hands'
  },
  {
    icon: (props: HTMLProps<SVGGElement>) => (
      <g {...props}>
        <path
          d="M33 330.5c0-17.949 14.55-32.5 32.5-32.5S98 312.551 98 330.5 83.45 363 65.5 363 33 348.449 33 330.5"
          fill="#DDE1E5"
        />
        <path
          d="M70.822 336.254a10 10 0 0 0 1.864-1.471 10 10 0 0 0 2.952-7.091v-9.424c0-.478-.198-.923-.52-1.245a1.78 1.78 0 0 0-1.245-.511 1.78 1.78 0 0 0-1.245.511 1.8 1.8 0 0 0-.51 1.245v5.83a.313.313 0 1 1-.627 0v-8.517a1.75 1.75 0 0 0-.52-1.245 1.73 1.73 0 0 0-1.237-.52 1.75 1.75 0 0 0-1.245.52c-.321.321-.52.758-.52 1.245v8.517c0 .173-.14.313-.312.313a.31.31 0 0 1-.305-.313v-9.334c0-.486-.198-.931-.52-1.245a1.753 1.753 0 0 0-2.49 0 1.77 1.77 0 0 0-.511 1.245v9.334a.313.313 0 0 1-.627 0v-8.517c0-.487-.197-.924-.52-1.245a1.73 1.73 0 0 0-1.236-.52 1.75 1.75 0 0 0-1.245.52 1.75 1.75 0 0 0-.52 1.245v12.862c0 .173-.14.313-.304.313a.33.33 0 0 1-.314-.256l-1.096-4.386a1.77 1.77 0 0 0-.8-1.08 1.75 1.75 0 0 0-2.416.61 1.73 1.73 0 0 0-.198 1.328l1.353 5.392a10.06 10.06 0 0 0 3.421 5.169c.63.506 1.323.937 2.065 1.28v2.159h9.326v-2.421c0-.132.043-.222.102-.297m.935 3.955H60.379q-.37 0-.371.371V348h12.12v-7.42c0-.124-.124-.371-.371-.371m-2.845 5.194c-.742 0-1.36-.618-1.36-1.36s.618-1.361 1.36-1.361 1.36.619 1.36 1.361c-.123.742-.618 1.36-1.36 1.36"
          fill="#000"
        />
      </g>
    ),
    stateName: 'wave',
    description: 'Wave hand'
  },

  {
    icon: (props: HTMLProps<SVGGElement>) => (
      <g {...props}>
        <path
          d="M377 330.5c0-17.949 14.551-32.5 32.5-32.5s32.5 14.551 32.5 32.5-14.551 32.5-32.5 32.5-32.5-14.551-32.5-32.5"
          fill="#DDE1E5"
        />
        <path
          d="M415.634 324.609v6.767c.938 2.816 2.371 8.843-.494 13.141-.445.691-1.482.79-2.125.197-.493-.444-.543-1.185-.197-1.729 1.926-2.964 1.087-7.509 0-10.868l-2.964 8.645c-.346 1.087-1.729 1.482-2.618.741l-4.644-4.05c-.593-.495-.642-1.334-.148-1.927a1.35 1.35 0 0 1 1.927-.148l3.211 2.717 1.58-5.928v-7.262c-1.432-.049-3.013-.099-4.495-.148-1.087-.05-1.927-.988-1.828-2.124l.642-7.559c.198-.543.791-.889 1.334-.889.889 0 1.63.889 1.383 1.779l-.494 4.001c-.098.692.494 1.334 1.186 1.334h8.151c.395 0 .741-.148.939-.445 1.136-1.482 1.877-3.853 2.272-5.582.296-.889.889-1.087 1.531-1.087.791.099 1.334.889 1.186 1.68-.544 2.47-1.976 7.311-5.335 8.744"
          fill="#000"
        />
        <path
          d="M409.558 318.385c-.791-1.186-.939-2.52-.297-3.656.544-1.087 1.68-1.729 2.865-1.729 1.779 0 3.261 1.482 3.261 3.26 0 1.186-.692 2.47-1.68 3.113a2.72 2.72 0 0 1-1.581.494c-.395 0-.741-.099-1.136-.247a2.9 2.9 0 0 1-1.432-1.235m14.412 26.594h.91v1.284h-.91V348h-1.593v-1.737h-3.291l-.072-1.003 3.346-5.287h1.61zm-3.368 0h1.775v-2.834l-.105.182zm-20.097-13.915c-.705 0-1.34-.358-1.834-.932-.282-.359-.494-.861-.494-1.291v-6.887l-2.045 1.722c-.282.287-.776.215-.988-.072s-.211-.789.141-1.004l3.175-2.654c.211-.215.493-.215.775-.072q.424.216.424.646v6.097c.07 0 .141-.072.282-.072a2.3 2.3 0 0 1 2.045.359 2.39 2.39 0 0 1 .917 1.865c-.07 1.22-1.128 2.295-2.398 2.295"
          fill="#000"
        />
      </g>
    ),
    stateName: 'dance4',
    description: 'Dances - Running Man'
  },

  {
    icon: (props: HTMLProps<SVGGElement>) => (
      <g {...props}>
        <path
          d="M112 71.5c0-17.95 14.551-32.5 32.5-32.5S177 53.55 177 71.5 162.449 104 144.5 104 112 89.45 112 71.5"
          fill="#DDE1E5"
        />
        <path
          d="M151.068 65.609v6.767c.939 2.816 2.372 8.843-.494 13.14-.444.692-1.482.791-2.124.198-.494-.444-.543-1.185-.198-1.729 1.927-2.964 1.087-7.508 0-10.868l-2.963 8.645c-.346 1.087-1.729 1.482-2.619.741l-4.643-4.05c-.593-.495-.642-1.334-.148-1.927s1.383-.642 1.926-.148l3.211 2.717 1.581-5.928v-7.262c-1.433-.05-3.013-.099-4.495-.148-1.087-.05-1.927-.988-1.828-2.124l.642-7.559c.198-.543.79-.889 1.334-.889.889 0 1.63.89 1.383 1.779l-.494 4.001c-.099.692.494 1.334 1.186 1.334h8.15c.396 0 .741-.148.939-.445 1.136-1.482 1.877-3.853 2.272-5.582.297-.89.89-1.087 1.532-********** 1.334.89 1.185 1.68-.543 2.47-1.976 7.311-5.335 8.744"
          fill="#000"
        />
        <path
          d="M144.992 59.385c-.79-1.186-.938-2.52-.296-3.656.543-1.087 1.679-1.729 2.865-1.729 1.778 0 3.26 1.482 3.26 3.26 0 1.186-.691 2.47-1.679 3.113a2.7 2.7 0 0 1-1.581.494c-.395 0-.741-.1-1.136-.247a2.9 2.9 0 0 1-1.433-1.235M158.563 89h-1.593v-6.141l-1.902.59v-1.296l3.325-1.19h.17zm-22.621-15.806c-.705 0-1.34-.36-1.834-.933-.282-.359-.494-.86-.494-1.291v-6.886l-2.045 1.721c-.282.287-.776.215-.988-.072s-.211-.789.141-1.004l3.174-2.654c.212-.215.494-.215.776-.072q.423.216.423.646v6.097c.071 0 .142-.072.283-.072a2.3 2.3 0 0 1 2.045.36c.564.43.917 1.147.917 1.864-.071 1.22-1.129 2.296-2.398 2.296"
          fill="#000"
        />
      </g>
    ),
    stateName: 'dance1',
    description: 'Dances - Cabbage Patch'
  },

  {
    icon: (props: HTMLProps<SVGGElement>) => (
      <g {...props}>
        <path
          d="M396 196.5c0-17.949 14.551-32.5 32.5-32.5s32.5 14.551 32.5 32.5-14.551 32.5-32.5 32.5-32.5-14.551-32.5-32.5"
          fill="#DDE1E5"
        />
        <path
          d="M434.569 190.572v6.747c.936 2.807 2.364 8.814-.493 13.099-.443.689-1.477.788-2.117.197-.492-.444-.542-1.182-.197-1.724 1.92-2.955 1.083-7.485 0-10.834l-2.955 8.618c-.344 1.083-1.723 1.477-2.61.739l-4.629-4.038c-.591-.493-.64-1.33-.147-1.921a1.344 1.344 0 0 1 1.92-.148l3.201 2.709 1.576-5.909v-7.239c-1.428-.05-3.004-.099-4.481-.148-1.084-.049-1.921-.985-1.822-2.118l.64-7.534c.197-.542.788-.886 1.329-.886.887 0 1.625.886 1.379 1.772l-.492 3.989c-.099.69.492 1.33 1.182 1.33h8.125c.394 0 .739-.148.936-.443 1.132-1.478 1.871-3.842 2.265-5.565.295-.886.886-1.083 1.526-1.083.788.098 1.33.886 1.182 1.674-.541 2.462-1.97 7.288-5.318 8.716"
          fill="#000"
        />
        <path
          d="M428.512 184.368c-.788-1.182-.936-2.512-.296-3.644.542-1.084 1.675-1.724 2.857-1.724 1.772 0 3.25 1.477 3.25 3.25 0 1.182-.69 2.462-1.675 3.103a2.7 2.7 0 0 1-1.575.492c-.394 0-.739-.098-1.133-.246a2.9 2.9 0 0 1-1.428-1.231m11.283 24.818h.846q.605 0 .896-.302.291-.303.291-.803 0-.483-.291-.753-.285-.269-.791-.269-.456 0-.764.253a.8.8 0 0 0-.308.648h-1.588q0-.626.335-1.121.34-.5.945-.78.61-.28 1.341-.28 1.27 0 1.99.61.72.604.719 1.67 0 .55-.335 1.011a2.16 2.16 0 0 1-.879.709q.676.242 1.006.726.335.483.335 1.143 0 1.066-.78 1.709-.775.643-2.056.643-1.197 0-1.962-.632-.758-.632-.758-1.671h1.588q0 .451.335.737.341.285.836.286.566 0 .884-.297.325-.303.325-.797 0-1.198-1.319-1.198h-.841zm-20.307-12.178c-.703 0-1.336-.358-1.828-.93-.282-.357-.493-.858-.493-1.287v-6.865l-2.039 1.716c-.281.286-.773.215-.984-.071s-.211-.787.141-1.001l3.164-2.646c.211-.214.492-.214.773-.071q.422.214.422.643v6.078c.07 0 .141-.071.281-.071a2.3 2.3 0 0 1 2.039.357c.563.429.914 1.144.914 1.86-.07 1.215-1.125 2.288-2.39 2.288"
          fill="#000"
        />
      </g>
    ),
    stateName: 'dance3',
    description: 'Dances - Twist and Shout Dance'
  },

  {
    icon: (props: HTMLProps<SVGGElement>) => (
      <g {...props}>
        <path
          d="M301 71.5c0-17.95 14.551-32.5 32.5-32.5S366 53.55 366 71.5 351.449 104 333.5 104 301 89.45 301 71.5"
          fill="#DDE1E5"
        />
        <path
          d="M339.632 65.609v6.767c.938 2.816 2.371 8.843-.495 13.14-.444.692-1.481.791-2.124.198-.494-.444-.543-1.185-.197-1.729 1.926-2.964 1.087-7.508 0-10.868l-2.964 8.645c-.346 1.087-1.729 1.482-2.618.741l-4.644-4.05c-.593-.495-.642-1.334-.148-1.927s1.383-.642 1.926-.148l3.211 2.717 1.581-5.928v-7.262c-1.432-.05-3.013-.099-4.495-.148-1.087-.05-1.927-.988-1.828-2.124l.642-7.559c.198-.543.791-.889 1.334-.889.889 0 1.63.89 1.383 1.779l-.494 4.001c-.099.692.494 1.334 1.186 1.334h8.151c.395 0 .741-.148.938-.445 1.137-1.482 1.877-3.853 2.273-5.582.296-.89.889-1.087 1.531-1.087.791.1 1.334.89 1.186 1.68-.544 2.47-1.976 7.311-5.335 8.744"
          fill="#000"
        />
        <path
          d="M333.555 59.385c-.79-1.186-.938-2.52-.296-3.656.543-1.087 1.68-1.729 2.865-1.729 1.779 0 3.261 1.482 3.261 3.26 0 1.186-.692 2.47-1.68 3.113a2.7 2.7 0 0 1-1.581.494c-.395 0-.741-.1-1.136-.247a2.9 2.9 0 0 1-1.433-1.235M348.759 89h-5.501v-1.091l2.596-2.768q.536-.585.789-1.02.258-.435.259-.827 0-.534-.27-.838-.271-.309-.772-.308-.54 0-.855.374-.309.37-.308.976h-1.599q0-.733.347-1.34.353-.606.992-.948.64-.347 1.45-.347 1.241 0 1.924.595.69.596.689 1.682 0 .595-.308 1.213-.309.616-1.059 1.439l-1.825 1.924h3.451zm-24.254-16.935c-.705 0-1.34-.36-1.834-.933-.282-.359-.494-.86-.494-1.291v-6.886l-2.045 1.721c-.282.287-.776.215-.988-.072s-.211-.789.141-1.004l3.175-2.654c.211-.215.493-.215.775-.072q.424.216.424.646v6.097c.07 0 .141-.072.282-.072a2.3 2.3 0 0 1 2.045.359 2.39 2.39 0 0 1 .917 1.865c-.07 1.22-1.128 2.295-2.398 2.295"
          fill="#000"
        />
      </g>
    ),
    stateName: 'dance2',
    description: 'Dances - Macarena'
  }

  /*{
    icon: (props: HTMLProps<SVGSVGElement>) => (
      <svg x="33" y="298" width="62" height="62" viewBox="0 0 62 62" fill="none" {...props}>
        <path
          d="M30.741 0.0763855C13.833 0.0763855 0.076416 13.833 0.076416 30.741C0.076416 47.6489 13.833 61.4055 30.741 61.4055C36.329 61.4055 41.5514 59.8794 46.0686 57.2587L42.6695 53.763C38.9869 55.6844 34.8947 56.6879 30.741 56.6879C16.4348 56.6879 4.79404 45.0472 4.79404 30.741C4.79404 16.4348 16.4348 4.79401 30.741 4.79401C45.0472 4.79401 56.6879 16.4348 56.6879 30.741C56.6879 30.8023 56.6785 30.8613 56.6785 30.9202C58.3408 31.179 59.9221 31.8139 61.3018 32.7766C61.3442 32.0996 61.4056 31.4297 61.4056 30.741C61.4056 13.833 47.649 0.0763855 30.741 0.0763855ZM20.1263 21.3057C19.1879 21.3057 18.288 21.6785 17.6244 22.342C16.9609 23.0056 16.5881 23.9055 16.5881 24.8439C16.5881 25.7823 16.9609 26.6823 17.6244 27.3458C18.288 28.0094 19.1879 28.3821 20.1263 28.3821C21.0647 28.3821 21.9647 28.0094 22.6282 27.3458C23.2918 26.6823 23.6645 25.7823 23.6645 24.8439C23.6645 23.9055 23.2918 23.0056 22.6282 22.342C21.9647 21.6785 21.0647 21.3057 20.1263 21.3057ZM40.1762 21.3057C35.2369 21.3057 31.6609 24.1528 31.6609 24.1528L34.5387 27.8939C34.5387 27.8939 37.0909 26.0233 40.1786 26.0233C43.2663 26.0233 45.8162 27.8939 45.8162 27.8939L48.6939 24.1528C48.6868 24.1528 45.1132 21.3057 40.1762 21.3057ZM28.3822 33.1186V36.6333C30.5098 36.6333 31.8661 37.6122 31.8661 38.1193C31.8661 38.6288 30.5169 39.5959 28.4058 39.6054H28.3822V43.1247H28.4058C30.5169 43.1318 31.8661 44.1013 31.8661 44.6084C31.8661 45.1179 30.5075 46.0945 28.3822 46.0945V49.6115C32.319 49.6115 35.4044 47.413 35.4044 44.6084C35.4044 43.3535 34.7628 42.2355 33.7272 41.365C34.7628 40.4946 35.402 39.3766 35.402 38.1217C35.402 35.3147 32.319 33.1186 28.3822 33.1186ZM46.663 35.4586C43.054 35.4586 40.1762 38.5014 40.1762 42.0633C40.1762 44.1367 41.3674 45.6604 42.2284 46.545L50.7909 55.3434L59.377 46.545C60.2379 45.6628 61.4056 44.4433 61.4056 42.0633C61.4056 38.5014 58.5278 35.4586 54.9188 35.4586C53.1969 35.4586 51.7816 36.0483 50.7909 36.638C49.8002 36.0483 48.3849 35.4586 46.663 35.4586ZM46.663 40.1762C47.2267 40.1762 47.8023 40.3508 48.3778 40.6928L50.7909 42.1317L53.204 40.6928C53.7181 40.3681 54.3109 40.1896 54.9188 40.1762C55.8623 40.1762 56.6879 41.0608 56.6879 42.0633C56.6879 42.5067 56.6691 42.5657 56.0015 43.2497L50.7956 48.583L45.6062 43.2521C45.342 42.9808 44.8939 42.4619 44.8939 42.0656C44.8939 41.0608 45.7194 40.1762 46.663 40.1762Z"
          fill="#000"
        />
      </svg>
    ),
    stateName: 'kiss'
  },

  {
    icon: (props: HTMLProps<SVGSVGElement>) => (
      <svg x="14" y="164" width="62" height="62" viewBox="0 0 66 66" fill="none" {...props}>
        <circle cx="33" cy="33" r="33" fill="transparent" />
        <path
          d="M33.3166 0.0554047C15.378 0.0554047 0.843811 14.8059 0.843811 33.0114C0.843811 44.9845 7.15505 55.4294 16.5564 61.1967V37.2638C16.5564 36.0944 17.4992 35.1376 18.6515 35.1376C19.8037 35.1376 20.7465 36.0944 20.7465 37.2638V63.4027C24.6092 65.0505 28.8647 65.9674 33.3166 65.9674C37.7685 65.9674 42.024 65.0505 45.8867 63.4027V37.2638C45.8867 36.0944 46.8294 35.1376 47.9817 35.1376C49.1339 35.1376 50.0767 36.0944 50.0767 37.2638V61.1967C59.4781 55.4294 65.7893 44.9712 65.7893 33.0114C65.7893 14.8059 51.2551 0.0554047 33.3166 0.0554047ZM24.7401 28.8254C22.8022 27.0713 18.6907 27.0713 16.7528 28.8254L15.5089 29.955C15.0114 30.3935 14.2912 30.4865 13.7151 30.1676C13.1389 29.8487 12.8116 29.1842 12.9163 28.5198C13.4401 25.1711 17.3944 22.9253 20.7596 22.9253C24.1247 22.9253 28.079 25.1711 28.6028 28.5198C28.7075 29.1842 28.3802 29.8487 27.8041 30.1676C27.0446 30.5796 26.3375 30.2606 26.0102 29.955L24.7401 28.8254ZM33.3166 54.2733C29.8467 54.2733 27.0315 50.4595 27.0315 45.7685C27.0315 41.0776 29.8467 37.2638 33.3166 37.2638C36.7864 37.2638 39.6016 41.0776 39.6016 45.7685C39.6016 50.4595 36.7864 54.2733 33.3166 54.2733ZM52.9312 30.1543C52.1717 30.5663 51.4647 30.2473 51.1373 29.9417L49.8934 28.8122C47.9555 27.0581 43.844 27.0581 41.9061 28.8122L40.6491 29.955C40.1516 30.3935 39.4314 30.4865 38.8553 30.1676C38.2791 29.8487 37.9518 29.1842 38.0565 28.5198C38.5803 25.1711 42.5346 22.9253 45.8998 22.9253C49.2649 22.9253 53.2192 25.1711 53.743 28.5198C53.8215 29.171 53.5073 29.8354 52.9312 30.1543Z"
          fill="#000"
        />
      </svg>
    ),

    stateName: 'cry'
  },

  {
    icon: (props: HTMLProps<SVGSVGElement>) => (
      <svg x="112" y="39" width="62" height="62" viewBox="0 0 44 44" fill="none" {...props}>
        <path
          d="M21.5 37C13.4892 37 7 30.5108 7 22.5C7 14.4892 13.4892 8 21.5 8C29.5108 8 36 14.4892 36 22.5C36 30.5108 29.5108 37 21.5 37ZM21.5 9.34259C14.25 9.34259 8.34259 15.25 8.34259 22.5C8.34259 29.75 14.25 35.6574 21.5 35.6574C28.75 35.6574 34.6574 29.75 34.6574 22.5C34.6574 15.25 28.75 9.34259 21.5 9.34259ZM21.5 33.375C16.3534 33.375 11.8781 29.7052 10.8488 24.6481C10.7145 24.0664 10.8935 23.4846 11.2515 23.037C11.6096 22.5895 12.1466 22.321 12.7284 22.321H30.3164C30.8981 22.321 31.4352 22.5895 31.7932 23.037C32.1512 23.4846 32.2855 24.0216 32.196 24.6034V24.6929C31.1667 29.7052 26.6466 33.375 21.5 33.375ZM12.7284 23.7083C12.5046 23.7083 12.3704 23.8426 12.2809 23.9321C12.1914 24.0664 12.1466 24.2454 12.1466 24.4244C13.0417 28.8549 16.9799 32.0324 21.5 32.0324C26.0201 32.0324 29.9583 28.8102 30.8534 24.3796V24.2901C30.8981 24.1559 30.8086 23.9769 30.7191 23.9321C30.6296 23.7978 30.4506 23.7083 30.2716 23.7083H12.7284ZM14.6975 19.8596C14.4738 19.5463 14.5633 19.1435 14.8765 18.9198L17.3827 17.2639L14.6975 15.3395C14.3843 15.1157 14.3395 14.713 14.5185 14.3997C14.7423 14.0864 15.1451 14.0417 15.4583 14.2207L18.6358 16.4583C18.9043 16.6373 19.0386 16.9506 19.0386 17.2639C19.0386 17.5772 18.8596 17.8904 18.591 18.0694L15.5926 20.0833C15.3241 20.2623 14.8765 20.1728 14.6975 19.8596ZM28.3025 19.8596C28.0787 20.1728 27.6759 20.2623 27.3627 20.0386L24.3642 18.0694C24.0957 17.8904 23.9614 17.6219 23.9167 17.2639C23.9167 16.9506 24.0509 16.6373 24.3194 16.4583L27.4969 14.2207C27.8102 13.9969 28.213 14.0864 28.4367 14.3997C28.6605 14.713 28.571 15.1157 28.2577 15.3395L25.5278 17.2639L28.0339 18.9198C28.4367 19.1435 28.5262 19.5463 28.3025 19.8596Z"
          fill="#000"
        />
        <circle opacity="0.3" cx="22" cy="22" r="22" fill="url(#paint0_radial)" />
      </svg>
    ),

    stateName: 'laugh'
  },

  {
    icon: (props: HTMLProps<SVGSVGElement>) => (
      <svg x="301" y="29" width="62" height="62" viewBox="0 0 44 44" fill="none" {...props}>
        <path
          d="M21.5 37C13.4892 37 7 30.5108 7 22.5C7 14.4892 13.4892 8 21.5 8C29.5108 8 36 14.4892 36 22.5C36 30.5108 29.5108 37 21.5 37ZM21.5 9.34259C14.25 9.34259 8.34259 15.25 8.34259 22.5C8.34259 29.75 14.25 35.6574 21.5 35.6574C28.75 35.6574 34.6574 29.75 34.6574 22.5C34.6574 15.25 28.75 9.34259 21.5 9.34259Z"
          fill="#000"
        />
        <path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M17.9001 20.4698C17.1319 21.1685 16.0298 21.5784 14.8421 21.5784C13.6543 21.5784 12.5523 21.1685 11.784 20.4698C11.5084 20.2192 11.0959 20.5526 11.2831 20.8747C11.998 22.1046 13.3501 22.8818 14.8421 22.8818C16.3341 22.8818 17.6861 22.1046 18.401 20.8747C18.5883 20.5526 18.1757 20.2192 17.9001 20.4698Z"
          fill="#000"
        />
        <path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M31.2596 20.4698C30.4914 21.1685 29.3893 21.5784 28.2016 21.5784C27.0138 21.5784 25.9118 21.1685 25.1435 20.4698C24.8679 20.2192 24.4554 20.5526 24.6426 20.8747C25.3575 22.1046 26.7096 22.8818 28.2016 22.8818C29.6936 22.8818 31.0456 22.1046 31.7605 20.8747C31.9478 20.5526 31.5352 20.2192 31.2596 20.4698Z"
          fill="#000"
        />
        <path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M25.4924 28.1522C24.5408 27.1194 23.1314 26.4661 21.5219 26.4661C19.9123 26.4661 18.5029 27.1194 17.5513 28.1522C16.6914 29.0856 16.3563 30.1978 16.8884 30.7509C17.1919 31.0664 17.5763 31.0969 18.0461 30.9234C18.2102 30.8628 18.3833 30.7804 18.6259 30.652C18.6375 30.6459 19.1116 30.3905 19.2598 30.3142C20.093 29.8851 20.7631 29.6728 21.5218 29.6728C22.2804 29.6728 22.9506 29.8851 23.7838 30.3142C23.932 30.3905 24.4061 30.6459 24.4177 30.652C24.6604 30.7804 24.8334 30.8628 24.9975 30.9234C25.4673 31.0969 25.8517 31.0664 26.1553 30.7509C26.6873 30.1978 26.3523 29.0856 25.4924 28.1522Z"
          fill="#000"
        />
        <circle opacity="0.3" cx="22" cy="22" r="22" fill="url(#paint0_radial)" />
      </svg>
    ),
    stateName: 'defeat'
  }*/
]

const EmoteMenu = (): JSX.Element => {
  const playAnimation = (stateName: string) => {
    const selfAvatarEntity = AvatarComponent.getSelfAvatarEntity()
    dispatchAction(
      AvatarNetworkAction.setAnimationState({
        animationAsset: preloadedAnimations.emotes,
        clipName: stateName,
        loop: false,
        layer: 0,
        entityUUID: UUIDComponent.get(selfAvatarEntity)
      })
    )
    ModalState.closeModal()
  }

  const dimensions = useHookstate({ width: 474, height: 440 })
  const tooltipContent = useHookstate('')

  const { TooltipInjection, onMouseEnter, onMouseLeave, onMouseMove } = useMovingTooltip()

  useLayoutEffect(() => {
    if (isMobile) {
      const viewportWidth = window.visualViewport?.width || window.innerWidth
      const viewportHeight = window.visualViewport?.height || window.innerHeight

      const aspectRatio = dimensions.width.value / dimensions.height.value
      const paddingFactor = 0.9 // 90% of the viewport

      let maxWidth = viewportWidth * paddingFactor
      let maxHeight = viewportWidth / aspectRatio

      if (maxHeight > viewportHeight) {
        maxHeight = viewportHeight * paddingFactor
        maxWidth = viewportHeight * aspectRatio
      }

      dimensions.set((prev) => ({
        width: maxWidth,
        height: maxHeight
      }))
    }
  }, [])

  return (
    <>
      <svg
        className="pointer-events-auto"
        width={dimensions.width.value}
        height={dimensions.height.value}
        viewBox="0 0 474 440"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M90.454 423.273C35.368 379.876 0 312.568 0 236.999c0-130.891 106.108-237 237-237 130.891 0 237 106.109 237 237 0 75.569-35.368 142.877-90.454 186.274-13.952 10.991-33.554 10.231-48.435.535-10.823-7.052-17.511-19.199-17.511-32.117 0-15.468 8.252-29.5 19.783-39.81 31.008-27.725 50.525-68.035 50.525-112.903 0-83.452-67.518-151.135-150.908-151.402-83.39.267-150.908 67.95-150.908 151.402 0 44.868 19.517 85.178 50.525 112.903 11.531 10.31 19.783 24.342 19.783 39.81 0 12.918-6.688 25.065-17.511 32.117-14.881 9.696-34.483 10.456-48.435-.535"
          fill="#F5F5F5"
        />

        {iconItems.map(({ icon: Icon, stateName, description }, index) => (
          <Icon
            className="cursor-pointer"
            style={{
              // @ts-ignore: This is not supported by tailwind. And this value is only supported by SVGs, hence incorrecly flagged by TS
              pointerEvents: 'bounding-box'
            }}
            onMouseEnter={onMouseEnter}
            onMouseLeave={onMouseLeave}
            onMouseMove={(event) => {
              onMouseMove(event)
              tooltipContent.set(description || stateName)
            }}
            onClick={() => {
              playAnimation(emoteAnimations[stateName])
            }}
            key={index}
          />
        ))}
      </svg>
      <TooltipInjection content={tooltipContent.value} />
    </>
  )
}

const useMovingTooltip = () => {
  const isTooltipVisible = useHookstate(false)
  const tooltipPosition = useHookstate({ x: 0, y: 0 })

  const tooltipRef = useRef<HTMLDivElement>(null)

  const onMouseMove = (event: React.MouseEvent) => {
    const tooltipX = event.clientX + 24,
      tooltipY = event.clientY - 24
    tooltipPosition.set({ x: tooltipX, y: tooltipY })
  }

  const onMouseEnter = () => {
    isTooltipVisible.set(true)
  }

  const onMouseLeave = () => {
    isTooltipVisible.set(false)
  }

  const TooltipInjection = ({ content }: { content: string }) => {
    return (
      <div className={isTooltipVisible.value ? 'block' : 'hidden'}>
        {isTooltipVisible.value && (
          <div
            ref={tooltipRef}
            className={`fixed z-50 bg-black p-4 shadow-[0px_4px_4px_0px_rgba(0,0,0,0.25),_0px_2px_8px_0px_rgba(0,0,0,0.04),_0px_8px_16px_0px_rgba(0,0,0,0.08)] ${
              content ? 'opacity-100' : 'opacity-0'
            }`}
            style={{
              top: tooltipPosition.y.value,
              left: tooltipPosition.x.value
            }}
          >
            {content}
          </div>
        )}
      </div>
    )
  }

  return { onMouseMove, onMouseEnter, onMouseLeave, TooltipInjection }
}

export default EmoteMenu
