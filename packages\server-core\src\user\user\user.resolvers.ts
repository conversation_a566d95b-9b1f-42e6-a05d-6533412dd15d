/*
CPAL-1.0 License

The contents of this file are subject to the Common Public Attribution License
Version 1.0. (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at
https://github.com/ir-engine/ir-engine/blob/dev/LICENSE.
The License is based on the Mozilla Public License Version 1.1, but Sections 14
and 15 have been added to cover use of software over a computer network and 
provide for limited attribution for the Original Developer. In addition, 
Exhibit A has been modified to be consistent with Exhibit B.

Software distributed under the License is distributed on an "AS IS" basis,
WITHOUT WARRANTY OF ANY KIND, either express or implied. See the License for the
specific language governing rights and limitations under the License.

The Original Code is Infinite Reality Engine.

The Original Developer is the Initial Developer. The Initial Developer of the
Original Code is the Infinite Reality Engine team.

All portions of the code written by the Infinite Reality Engine team are Copyright © 2021-2025
Infinite Reality Engine. All Rights Reserved.
*/

// For more information about this file see https://dove.feathersjs.com/guides/cli/service.schemas.html
import { resolve, virtual } from '@feathersjs/schema'
import { v4 as uuidv4 } from 'uuid'

import { InviteCode, UserID, UserName, UserQuery, UserType } from '@ir-engine/common/src/schemas/user/user.schema'
import { fromDateTimeSql, getDateTimeSql } from '@ir-engine/common/src/utils/datetime-sql'
import type { HookContext } from '@ir-engine/server-core/declarations'

import config from '../../appconfig'
import getFreeInviteCode from '../../util/get-free-invite-code'

export const userResolver = resolve<UserType, HookContext>({
  ageVerified: virtual(async (user, context) => {
    if (!config.server.requireAgeVerification) return true
    return !!user.ageVerified
  }),
  createdAt: virtual(async (user) => fromDateTimeSql(user.createdAt)),
  updatedAt: virtual(async (user) => fromDateTimeSql(user.updatedAt)),
  deactivatedAt: virtual(async (user) => (user.deactivatedAt ? fromDateTimeSql(user.deactivatedAt) : undefined))
})

export const userExternalResolver = resolve<UserType, HookContext>({
  // https://stackoverflow.com/a/56523892/2077741
  isGuest: async (value, user) => !!user.isGuest,
  isDeactivated: async (value, user) => !!user.isDeactivated
})

export const userDataResolver = resolve<UserType, HookContext>({
  id: async (id) => {
    return id || (uuidv4() as UserID)
  },
  name: async (name) => {
    return name || (('Guest #' + Math.floor(Math.random() * (999 - 100 + 1) + 100)) as UserName)
  },
  inviteCode: async (inviteCode, _, context) => {
    return inviteCode || ((await getFreeInviteCode(context.app)) as InviteCode)
  },
  createdAt: getDateTimeSql,
  updatedAt: getDateTimeSql
})

export const userPatchResolver = resolve<UserType, HookContext>({
  updatedAt: getDateTimeSql,
  deactivatedAt: async (_, userData) => {
    if (userData.isDeactivated) {
      return getDateTimeSql()
    }
    return undefined
  }
})

export const userQueryResolver = resolve<UserQuery, HookContext>({})
