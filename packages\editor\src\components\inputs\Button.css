.button {
  display: flex;
  border: none;
  border-radius: 4px;
  background: var(--buttonFilled);
  color: var(--white);
  white-space: nowrap;
  min-height: 24px;
  font-size: 15px;
  font-family: 'Lato', sans-serif;
  text-align: center;
  cursor: pointer;
  align-items: center;
  justify-content: center;
  text-decoration: none;
  padding: 1px 6px;
  width: 150px;
  margin-right: 15px;
  margin:0;
}

.button:hover {
  color: var(--textColor);
  opacity: 0.8;
}

.button:active {
  color: var(--textColor);
}

.button:disabled {
  background: var(--disabled);
  color: var(--disabledText);
}

.button:disabled:hover {
  background-color: var(--disabled);
}

.medium-button {
  line-height: 1em;
  height: 3em;
  padding: 1em;
}

.large-button {
  min-height: 24px;
  padding: 1em 2em;
  font-size: 1.5em;
}

.secondary-button {
  background-color: var(--buttonFilled);
  color: var(--textColor);
}

.secondary-button:hover {
  opacity: 0.8;
}

.secondary-button:active {
  opacity: 0.8;
}

.secondary-button:disabled {
  background-color: var(--disabled);
  color: var(--disabledText);
}

.secondary-button:disabled:hover {
  background-color: transparent;
}

.menu-button {
  background-color: buttonFilled;
  color: var(--textColor);
  padding: 1px 8px;
  width: 20px;
  margin-right: 0px;
}

.menu-button:hover {
  opacity: 0.8;
}

.menu-button:active {
  opacity: 0.8;
}

.menu-button:disabled {
  background-color: var(--disabled);
  color: var(--disabledText);
}

.menu-button:disabled:hover {
  background-color: transparent;
}

.properties-panel-button {
  align-self: center;
  justify-content: center;
  width: 200px;
  overflow-x: hidden;
}
