/*
CPAL-1.0 License

The contents of this file are subject to the Common Public Attribution License
Version 1.0. (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at
https://github.com/ir-engine/ir-engine/blob/dev/LICENSE.
The License is based on the Mozilla Public License Version 1.1, but Sections 14
and 15 have been added to cover use of software over a computer network and 
provide for limited attribution for the Original Developer. In addition, 
Exhibit A has been modified to be consistent with Exhibit B.

Software distributed under the License is distributed on an "AS IS" basis,
WITHOUT WARRANTY OF ANY KIND, either express or implied. See the License for the
specific language governing rights and limitations under the License.

The Original Code is Infinite Reality Engine.

The Original Developer is the Initial Developer. The Initial Developer of the
Original Code is the Infinite Reality Engine team.

All portions of the code written by the Infinite Reality Engine team are Copyright © 2021-2025
Infinite Reality Engine. All Rights Reserved.
*/

import { useEffect } from 'react'

import { defineSystem } from '@ir-engine/ecs/src/SystemFunctions'
import { PresentationSystemGroup } from '@ir-engine/ecs/src/SystemGroups'
import { UploadRequestState } from '@ir-engine/engine/src/assets/state/UploadRequestState'
import { getMutableState, getState, NO_PROXY, useState } from '@ir-engine/hyperflux'

import { uploadProjectFiles } from '../functions/assetFunctions'
import { EditorState } from '../services/EditorServices'
import { ImportSettingsState } from '../services/ImportSettingsState'

export const UploadRequestSystem = defineSystem({
  uuid: 'ee.editor.UploadRequestSystem',
  insert: { after: PresentationSystemGroup },
  reactor: () => {
    const uploadRequestState = useState(getMutableState(UploadRequestState))
    useEffect(() => {
      const uploadRequests = uploadRequestState.queue.get(NO_PROXY)
      if (uploadRequests.length === 0) return
      const publishSceneName = getState(EditorState).sceneName?.split('.').shift()
      const publishFolder = '/public/publish/' + publishSceneName + '/'
      const importSettings = getState(ImportSettingsState)

      const uploadPromises = uploadRequests.map((uploadRequest) => {
        const projectName = uploadRequest.projectName
        let uploadFolderPath = `projects/${projectName}${
          uploadRequest.path ? uploadRequest.path : importSettings.importFolder
        }`
        if (uploadRequestState.isOnPublishing.value === true) {
          uploadFolderPath = `projects/${projectName}${publishFolder}`
        }
        return Promise.all(uploadProjectFiles(projectName, [uploadRequest.file], [uploadFolderPath]).promises).then(
          uploadRequest.callback
        )
      })

      uploadRequestState.queue.set([])
    }, [uploadRequestState.queue.length])
    return null
  }
})
