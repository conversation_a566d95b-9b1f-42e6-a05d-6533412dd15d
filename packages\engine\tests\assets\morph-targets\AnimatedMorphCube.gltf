{"accessors": [{"bufferView": 0, "componentType": 5126, "count": 24, "type": "VEC3"}, {"bufferView": 1, "componentType": 5126, "count": 24, "type": "VEC4"}, {"bufferView": 2, "componentType": 5126, "count": 24, "type": "VEC3", "max": [0.0100000035, 0.0100000035, 0.01], "min": [-0.0100000044, -0.0100000054, -0.01]}, {"bufferView": 3, "componentType": 5126, "count": 24, "type": "VEC3", "name": "thin"}, {"bufferView": 4, "componentType": 5126, "count": 24, "type": "VEC3", "max": [0.0, 0.01893253, 0.0], "min": [0.0, 0.0, 0.0], "name": "thin"}, {"bufferView": 5, "componentType": 5126, "count": 24, "type": "VEC3", "name": "thin"}, {"bufferView": 6, "componentType": 5126, "count": 24, "type": "VEC3", "name": "angle"}, {"bufferView": 7, "componentType": 5126, "count": 24, "type": "VEC3", "max": [0.0, 0.0198908355, 0.0], "min": [0.0, 0.0, 0.0], "name": "angle"}, {"bufferView": 8, "componentType": 5126, "count": 24, "type": "VEC3", "name": "angle"}, {"bufferView": 9, "componentType": 5123, "count": 36, "type": "SCALAR"}, {"bufferView": 10, "componentType": 5126, "count": 127, "type": "SCALAR", "max": [4.19999743], "min": [0.0]}, {"bufferView": 11, "componentType": 5126, "count": 254, "type": "SCALAR"}], "animations": [{"channels": [{"sampler": 0, "target": {"node": 0, "path": "weights"}}], "samplers": [{"input": 10, "interpolation": "LINEAR", "output": 11}], "name": "Square"}], "asset": {"generator": "glTF Tools for Unity", "version": "2.0"}, "bufferViews": [{"buffer": 0, "byteLength": 288}, {"buffer": 0, "byteOffset": 288, "byteLength": 384}, {"buffer": 0, "byteOffset": 672, "byteLength": 288}, {"buffer": 0, "byteOffset": 960, "byteLength": 288}, {"buffer": 0, "byteOffset": 1248, "byteLength": 288}, {"buffer": 0, "byteOffset": 1536, "byteLength": 288}, {"buffer": 0, "byteOffset": 1824, "byteLength": 288}, {"buffer": 0, "byteOffset": 2112, "byteLength": 288}, {"buffer": 0, "byteOffset": 2400, "byteLength": 288}, {"buffer": 0, "byteOffset": 2688, "byteLength": 72}, {"buffer": 0, "byteOffset": 2760, "byteLength": 508}, {"buffer": 0, "byteOffset": 3268, "byteLength": 1016}], "buffers": [{"uri": "AnimatedMorphCube.bin", "byteLength": 4284}], "meshes": [{"primitives": [{"attributes": {"NORMAL": 0, "TANGENT": 1, "POSITION": 2}, "indices": 9, "material": 0, "targets": [{"NORMAL": 3, "POSITION": 4, "TANGENT": 5}, {"NORMAL": 6, "POSITION": 7, "TANGENT": 8}]}], "weights": [0.0, 0.0], "name": "C<PERSON>"}], "materials": [{"pbrMetallicRoughness": {"baseColorFactor": [0.6038274, 0.6038274, 0.6038274, 1.0], "metallicFactor": 0.0, "roughnessFactor": 0.5}, "name": "Material"}], "nodes": [{"mesh": 0, "rotation": [0.0, 0.7071067, -0.7071068, 0.0], "scale": [100.0, 100.0, 100.0], "name": "AnimatedMorphCube"}], "scene": 0, "scenes": [{"nodes": [0]}]}