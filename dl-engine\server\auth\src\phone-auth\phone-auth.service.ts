/**
 * DL-Engine 手机号认证服务
 * 
 * 核心功能：
 * - 手机号验证和注册
 * - 短信验证码管理
 * - 手机号登录逻辑
 * - 手机号绑定/解绑
 * - 防刷机制
 */

import { Injectable, BadRequestException, ConflictException, NotFoundException, UnauthorizedException } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import { Repository } from 'typeorm'
import { JwtService } from '@nestjs/jwt'
import { SmsService } from '../sms/sms.service'
import { RedisService } from '../redis/redis.service'
import { LoggerService } from '../common/logger.service'
import { ConfigService } from '../config/config.service'
import { User } from '../users/entities/user.entity'
import { PhoneVerification } from './entities/phone-verification.entity'
import { 
  SendSmsDto, 
  VerifyPhoneDto, 
  PhoneLoginDto, 
  PhoneRegisterDto,
  BindPhoneDto,
  UnbindPhoneDto
} from './dto/phone-auth.dto'

@Injectable()
export class PhoneAuthService {
  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(PhoneVerification)
    private readonly phoneVerificationRepository: Repository<PhoneVerification>,
    private readonly smsService: SmsService,
    private readonly redisService: RedisService,
    private readonly jwtService: JwtService,
    private readonly configService: ConfigService,
    private readonly logger: LoggerService
  ) {}

  /**
   * 发送短信验证码
   */
  async sendSms(sendSmsDto: SendSmsDto) {
    const { phone, countryCode = '+86', purpose = 'login' } = sendSmsDto
    const fullPhone = `${countryCode}${phone}`

    // 检查发送频率限制
    await this.checkSmsRateLimit(fullPhone)

    // 检查日发送限制
    await this.checkDailyLimit(fullPhone)

    // 生成验证码
    const code = this.generateVerificationCode()
    const expiresAt = new Date(Date.now() + this.configService.getSmsConfig().verification.expiresIn * 1000)

    try {
      // 发送短信
      await this.smsService.sendVerificationCode(fullPhone, code, purpose)

      // 保存验证记录
      await this.saveVerificationRecord(fullPhone, code, purpose, expiresAt)

      // 设置Redis缓存
      await this.cacheVerificationCode(fullPhone, code, purpose)

      // 记录发送统计
      await this.recordSmsStats(fullPhone)

      this.logger.log(`SMS sent to ${fullPhone} for ${purpose}`)

      return {
        success: true,
        message: '验证码发送成功',
        data: {
          phone: fullPhone,
          expiresIn: this.configService.getSmsConfig().verification.expiresIn,
          cooldown: this.configService.getSmsConfig().verification.cooldown
        }
      }
    } catch (error) {
      this.logger.error(`Failed to send SMS to ${fullPhone}: ${error.message}`)
      throw new BadRequestException('验证码发送失败，请稍后重试')
    }
  }

  /**
   * 验证手机号码
   */
  async verifyPhone(verifyPhoneDto: VerifyPhoneDto) {
    const { phone, countryCode = '+86', code, purpose = 'login' } = verifyPhoneDto
    const fullPhone = `${countryCode}${phone}`

    // 从Redis获取验证码
    const cachedCode = await this.getCachedVerificationCode(fullPhone, purpose)
    
    if (!cachedCode) {
      throw new BadRequestException('验证码已过期或不存在')
    }

    if (cachedCode !== code) {
      // 记录验证失败
      await this.recordVerificationAttempt(fullPhone, false)
      throw new UnauthorizedException('验证码错误')
    }

    // 验证成功，清除缓存
    await this.clearVerificationCode(fullPhone, purpose)
    
    // 记录验证成功
    await this.recordVerificationAttempt(fullPhone, true)

    this.logger.log(`Phone verification successful for ${fullPhone}`)

    return {
      success: true,
      message: '手机号验证成功',
      data: {
        phone: fullPhone,
        verified: true,
        timestamp: new Date().toISOString()
      }
    }
  }

  /**
   * 手机号注册
   */
  async register(phoneRegisterDto: PhoneRegisterDto) {
    const { phone, countryCode = '+86', code, nickname, password } = phoneRegisterDto
    const fullPhone = `${countryCode}${phone}`

    // 先验证手机号
    await this.verifyPhone({ phone, countryCode, code, purpose: 'register' })

    // 检查手机号是否已注册
    const existingUser = await this.userRepository.findOne({ 
      where: { phone: fullPhone } 
    })

    if (existingUser) {
      throw new ConflictException('该手机号已注册')
    }

    try {
      // 创建新用户
      const user = this.userRepository.create({
        phone: fullPhone,
        nickname: nickname || `用户${phone.slice(-4)}`,
        password: password ? await this.hashPassword(password) : null,
        phoneVerified: true,
        registeredAt: new Date(),
        lastLoginAt: new Date(),
        status: 'active'
      })

      const savedUser = await this.userRepository.save(user)

      // 生成JWT令牌
      const tokens = await this.generateTokens(savedUser)

      this.logger.log(`User registered with phone ${fullPhone}`)

      return {
        success: true,
        message: '注册成功',
        data: {
          user: this.sanitizeUser(savedUser),
          ...tokens
        }
      }
    } catch (error) {
      this.logger.error(`Registration failed for ${fullPhone}: ${error.message}`)
      throw new BadRequestException('注册失败，请稍后重试')
    }
  }

  /**
   * 手机号登录
   */
  async login(phoneLoginDto: PhoneLoginDto) {
    const { phone, countryCode = '+86', code } = phoneLoginDto
    const fullPhone = `${countryCode}${phone}`

    // 验证手机号和验证码
    await this.verifyPhone({ phone, countryCode, code, purpose: 'login' })

    // 查找用户
    const user = await this.userRepository.findOne({ 
      where: { phone: fullPhone } 
    })

    if (!user) {
      throw new NotFoundException('用户不存在，请先注册')
    }

    if (user.status !== 'active') {
      throw new UnauthorizedException('账户已被禁用')
    }

    try {
      // 更新最后登录时间
      user.lastLoginAt = new Date()
      await this.userRepository.save(user)

      // 生成JWT令牌
      const tokens = await this.generateTokens(user)

      this.logger.log(`User logged in with phone ${fullPhone}`)

      return {
        success: true,
        message: '登录成功',
        data: {
          user: this.sanitizeUser(user),
          ...tokens
        }
      }
    } catch (error) {
      this.logger.error(`Login failed for ${fullPhone}: ${error.message}`)
      throw new BadRequestException('登录失败，请稍后重试')
    }
  }

  /**
   * 绑定手机号
   */
  async bindPhone(userId: string, bindPhoneDto: BindPhoneDto) {
    const { phone, countryCode = '+86', code } = bindPhoneDto
    const fullPhone = `${countryCode}${phone}`

    // 验证手机号和验证码
    await this.verifyPhone({ phone, countryCode, code, purpose: 'bind' })

    // 检查手机号是否已被其他用户绑定
    const existingUser = await this.userRepository.findOne({ 
      where: { phone: fullPhone } 
    })

    if (existingUser && existingUser.id !== userId) {
      throw new ConflictException('该手机号已被其他用户绑定')
    }

    // 更新用户手机号
    const user = await this.userRepository.findOne({ where: { id: userId } })
    if (!user) {
      throw new NotFoundException('用户不存在')
    }

    user.phone = fullPhone
    user.phoneVerified = true
    await this.userRepository.save(user)

    this.logger.log(`Phone ${fullPhone} bound to user ${userId}`)

    return {
      success: true,
      message: '手机号绑定成功',
      data: {
        phone: fullPhone,
        verified: true
      }
    }
  }

  /**
   * 解绑手机号
   */
  async unbindPhone(userId: string, unbindPhoneDto: UnbindPhoneDto) {
    const { code } = unbindPhoneDto

    const user = await this.userRepository.findOne({ where: { id: userId } })
    if (!user || !user.phone) {
      throw new BadRequestException('用户未绑定手机号')
    }

    // 验证当前绑定的手机号
    await this.verifyPhone({ 
      phone: user.phone.replace(/^\+86/, ''), 
      countryCode: '+86', 
      code, 
      purpose: 'unbind' 
    })

    // 解绑手机号
    user.phone = null
    user.phoneVerified = false
    await this.userRepository.save(user)

    this.logger.log(`Phone unbound from user ${userId}`)

    return {
      success: true,
      message: '手机号解绑成功'
    }
  }

  /**
   * 检查手机号状态
   */
  async checkPhone(phone: string, countryCode = '+86') {
    const fullPhone = `${countryCode}${phone}`
    
    const user = await this.userRepository.findOne({ 
      where: { phone: fullPhone },
      select: ['id', 'phone', 'phoneVerified', 'registeredAt']
    })

    return {
      phone: fullPhone,
      exists: !!user,
      verified: user?.phoneVerified || false,
      registeredAt: user?.registeredAt || null
    }
  }

  /**
   * 获取支持的国家代码
   */
  async getSupportedCountries() {
    // 支持的国家代码列表
    const countries = [
      { code: '+86', name: '中国', flag: '🇨🇳' },
      { code: '+1', name: '美国', flag: '🇺🇸' },
      { code: '+44', name: '英国', flag: '🇬🇧' },
      { code: '+81', name: '日本', flag: '🇯🇵' },
      { code: '+82', name: '韩国', flag: '🇰🇷' },
      { code: '+65', name: '新加坡', flag: '🇸🇬' },
      { code: '+852', name: '香港', flag: '🇭🇰' },
      { code: '+853', name: '澳门', flag: '🇲🇴' },
      { code: '+886', name: '台湾', flag: '🇹🇼' }
    ]

    return {
      countries,
      default: '+86'
    }
  }

  /**
   * 获取短信统计
   */
  async getSmsStats(userId: string) {
    const user = await this.userRepository.findOne({ where: { id: userId } })
    if (!user || !user.phone) {
      return {
        phone: null,
        todaySent: 0,
        totalSent: 0,
        lastSent: null
      }
    }

    const todayKey = `sms:daily:${user.phone}:${new Date().toISOString().split('T')[0]}`
    const totalKey = `sms:total:${user.phone}`
    const lastSentKey = `sms:last:${user.phone}`

    const [todaySent, totalSent, lastSent] = await Promise.all([
      this.redisService.get(todayKey),
      this.redisService.get(totalKey),
      this.redisService.get(lastSentKey)
    ])

    return {
      phone: user.phone,
      todaySent: parseInt(todaySent || '0'),
      totalSent: parseInt(totalSent || '0'),
      lastSent: lastSent ? new Date(lastSent) : null
    }
  }

  // 私有方法

  private generateVerificationCode(): string {
    const length = this.configService.getSmsConfig().verification.length
    return Math.random().toString().slice(2, 2 + length)
  }

  private async checkSmsRateLimit(phone: string) {
    const cooldownKey = `sms:cooldown:${phone}`
    const lastSent = await this.redisService.get(cooldownKey)
    
    if (lastSent) {
      const cooldown = this.configService.getSmsConfig().verification.cooldown
      throw new BadRequestException(`请等待${cooldown}秒后再发送`)
    }
  }

  private async checkDailyLimit(phone: string) {
    const todayKey = `sms:daily:${phone}:${new Date().toISOString().split('T')[0]}`
    const todaySent = await this.redisService.get(todayKey)
    const dailyLimit = this.configService.getSmsConfig().verification.dailyLimit
    
    if (todaySent && parseInt(todaySent) >= dailyLimit) {
      throw new BadRequestException(`今日短信发送次数已达上限(${dailyLimit}次)`)
    }
  }

  private async saveVerificationRecord(phone: string, code: string, purpose: string, expiresAt: Date) {
    const verification = this.phoneVerificationRepository.create({
      phone,
      code,
      purpose,
      expiresAt,
      createdAt: new Date()
    })
    
    await this.phoneVerificationRepository.save(verification)
  }

  private async cacheVerificationCode(phone: string, code: string, purpose: string) {
    const key = `sms:code:${phone}:${purpose}`
    const expiresIn = this.configService.getSmsConfig().verification.expiresIn
    await this.redisService.setex(key, expiresIn, code)
  }

  private async getCachedVerificationCode(phone: string, purpose: string): Promise<string | null> {
    const key = `sms:code:${phone}:${purpose}`
    return this.redisService.get(key)
  }

  private async clearVerificationCode(phone: string, purpose: string) {
    const key = `sms:code:${phone}:${purpose}`
    await this.redisService.del(key)
  }

  private async recordSmsStats(phone: string) {
    const todayKey = `sms:daily:${phone}:${new Date().toISOString().split('T')[0]}`
    const totalKey = `sms:total:${phone}`
    const lastSentKey = `sms:last:${phone}`
    const cooldownKey = `sms:cooldown:${phone}`
    
    const cooldown = this.configService.getSmsConfig().verification.cooldown

    await Promise.all([
      this.redisService.incr(todayKey),
      this.redisService.incr(totalKey),
      this.redisService.set(lastSentKey, new Date().toISOString()),
      this.redisService.setex(cooldownKey, cooldown, '1'),
      this.redisService.expire(todayKey, 86400) // 24小时过期
    ])
  }

  private async recordVerificationAttempt(phone: string, success: boolean) {
    const key = `sms:attempts:${phone}`
    if (success) {
      await this.redisService.del(key)
    } else {
      await this.redisService.incr(key)
      await this.redisService.expire(key, 3600) // 1小时过期
    }
  }

  private async hashPassword(password: string): Promise<string> {
    const bcrypt = await import('bcryptjs')
    return bcrypt.hash(password, 12)
  }

  private async generateTokens(user: User) {
    const payload = { 
      sub: user.id, 
      phone: user.phone,
      nickname: user.nickname,
      type: 'access'
    }

    const refreshPayload = { 
      sub: user.id, 
      type: 'refresh'
    }

    const [accessToken, refreshToken] = await Promise.all([
      this.jwtService.signAsync(payload),
      this.jwtService.signAsync(refreshPayload, { 
        expiresIn: this.configService.getJwtRefreshExpiresIn() 
      })
    ])

    return {
      accessToken,
      refreshToken,
      expiresIn: this.configService.getJwtExpiresIn()
    }
  }

  private sanitizeUser(user: User) {
    const { password, ...sanitized } = user
    return sanitized
  }
}
