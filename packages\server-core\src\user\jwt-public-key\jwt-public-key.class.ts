/*
CPAL-1.0 License

The contents of this file are subject to the Common Public Attribution License
Version 1.0. (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at
https://github.com/ir-engine/ir-engine/blob/dev/LICENSE.
The License is based on the Mozilla Public License Version 1.1, but Sections 14
and 15 have been added to cover use of software over a computer network and 
provide for limited attribution for the Original Developer. In addition, 
Exhibit A has been modified to be consistent with Exhibit B.

Software distributed under the License is distributed on an "AS IS" basis,
WITHOUT WARRANTY OF ANY KIND, either express or implied. See the License for the
specific language governing rights and limitations under the License.

The Original Code is Infinite Reality Engine.

The Original Developer is the Initial Developer. The Initial Developer of the
Original Code is the Infinite Reality Engine team.

All portions of the code written by the Infinite Reality Engine team are Copyright © 2021-2025
Infinite Reality Engine. All Rights Reserved.
*/

import { Params, ServiceInterface } from '@feathersjs/feathers'
import { Application } from '../../../declarations'
import appconfig from '../../appconfig'

import { createHash } from 'crypto'
import * as jose from 'jose'

/**
 * A class for Login service
 */
export class JWTPublicKeyService implements ServiceInterface {
  app: Application

  constructor(app: Application) {
    this.app = app
  }

  /**
   * A function which find specific login details
   *
   * @param params
   * @returns {token}
   */
  async find(params?: Params) {
    if (appconfig.authentication.jwtAlgorithm !== 'RS256' || typeof appconfig.authentication.jwtPublicKey !== 'string')
      return {}
    const publicKey = await jose.importSPKI(appconfig.authentication.jwtPublicKey, 'RS256')
    let jwk = await jose.exportJWK(publicKey)
    jwk.kid = createHash('sha3-256').update(appconfig.authentication.jwtPublicKey).digest('hex')
    jwk.alg = 'RS256'
    jwk.use = 'sig'
    jwk.x5c = [
      appconfig.authentication.jwtPublicKey
        .replace('-----BEGIN PUBLIC KEY-----', '')
        .replace('-----END PUBLIC KEY-----', '')
        .replaceAll('\n', '')
    ]
    jwk['x5t#S256'] = await jose.calculateJwkThumbprint(jwk, 'sha256')
    return {
      keys: [jwk]
    }
  }
}
