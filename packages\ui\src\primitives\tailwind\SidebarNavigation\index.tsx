/*
CPAL-1.0 License

The contents of this file are subject to the Common Public Attribution License
Version 1.0. (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at
https://github.com/ir-engine/ir-engine/blob/dev/LICENSE.
The License is based on the Mozilla Public License Version 1.1, but Sections 14
and 15 have been added to cover use of software over a computer network and 
provide for limited attribution for the Original Developer. In addition, 
Exhibit A has been modified to be consistent with Exhibit B.

Software distributed under the License is distributed on an "AS IS" basis,
WITHOUT WARRANTY OF ANY KIND, either express or implied. See the License for the
specific language governing rights and limitations under the License.

The Original Code is Infinite Reality Engine.

The Original Developer is the Initial Developer. The Initial Developer of the
Original Code is the Infinite Reality Engine team.

All portions of the code written by the Infinite Reality Engine team are Copyright © 2021-2025
Infinite Reality Engine. All Rights Reserved.
*/

import React from 'react'
import { twMerge } from 'tailwind-merge'

export interface SidebarNavigationProps extends Omit<React.HTMLAttributes<HTMLDivElement>, 'onChange'> {
  labels: string[] | React.ReactNode[]
  displayMode?: 'justify-between' | 'justify-start'
  currentTabIndex: number
  onChange: (index: number) => void
}

const SidebarNavigation = ({
  labels,
  displayMode = 'justify-start',
  currentTabIndex,
  onChange,
  ...props
}: SidebarNavigationProps): JSX.Element => {
  return (
    <div
      className={twMerge(
        'flex items-center gap-x-9',
        displayMode === 'justify-between' ? 'justify-between' : 'justify-start'
      )}
      {...props}
    >
      {labels.map((label: string | React.ReactNode, index: number) => (
        <button
          key={index}
          className={twMerge(
            'flex items-center justify-start gap-x-1 border-text-secondary pb-4 font-medium text-text-secondary hover:border-b-2',
            index === currentTabIndex ? 'border-b-2 border-ui-select-primary text-ui-select-primary' : ''
          )}
          data-testid="sidebar-navigation-button"
          onClick={() => onChange(index)}
        >
          {label}
        </button>
      ))}
    </div>
  )
}

export default SidebarNavigation
