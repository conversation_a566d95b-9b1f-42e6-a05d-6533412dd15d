.profileButton {
  display: flex !important;
  align-items: center !important;
  color: var(--textColor) !important;
  background: transparent;

  span {
    margin-right: 5px;
    font-size: 14px;
  }

  &:hover,
  &:focus-visible {
    color: var(--green);
  }
}

.navbar {
  width: 100%;
  display: flex;
  justify-content: center;
  pointer-events: auto;
  background: var(--navbarBackground);
  height: 50px;

  .navContainer {
    width: 90vw;
    display: flex;
    justify-content: space-between;
    position: relative;

    .logoBlock {
      width: 100px;
      background-size: contain;
      background-repeat: no-repeat;
      background-position-y: 50%;
    }

    .backdrop {
      position: fixed;
      width: 100vw;
      height: calc(100vh - 50px);
      top: 50px;
      left: 0;
      background: #00000080;
      z-index: 100;
    }
  }
}

.projectPage {
  width: 100%;
  display: flex;
  flex-direction: column;
  flex: 1;
  font-family: var(--lato);
  font-size: 12px;
  background-color: var(--mainBackground);
  pointer-events: auto;
  max-height: 100%;
  color: var(--textColor);
}

.projectPageContainer {
  display: flex;
  flex-wrap: wrap;
  align-content: space-between;
  margin: 30px auto;
  width: 90vw;
  background-color: var(--panelBackground);
  padding: 30px;
  border-radius: 10px;
  min-height: calc(100vh - 110px);
  max-width: 1280px;
  overflow-y: auto;

  .projectGridContainer {
    display: flex;
    flex: 1 0 100%;
    width: 100%;
    flex-direction: column;
    border-radius: 3px;

    .projectGridHeader {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 15px;
      position: relative;

      h2 {
        font-size: 24px;
        font-weight: normal;
      }

      .buttonContainer {
        display: flex;
        gap: 10px;
      }

      .btn {
        margin: 0 !important;
        height: 40px !important;
        width: 90px !important;
        font-size: 16px !important;
        flex-shrink: 0 !important;
        background: linear-gradient(90deg, var(--buttonGradientStart), var(--buttonGradientEnd)) !important;
        color: var(--textColor) !important;
      }

      .searchInputRoot {
        background: var(--inputBackground);
        display: flex;
        align-items: center;
        width: 100%;
        max-width: 500px;
        height: 40px;
        margin: 0 30px;

        button {
          color: var(--textColor) !important;
          padding: 10px !important;

          &:hover,
          &:focus-visible {
            color: var(--textColor) !important;
          }
        }

        .inputRoot {
          flex: 1;
          color: var(--textColor);
        }
      }

      @media (max-width: 600px) {
        flex-wrap: wrap;

        .searchInputRoot {
          margin: 20px 0 0;
        }

        .buttonContainer {
          position: absolute;
          top: -2px;
          right: 0;

          .btn {
            height: 35px !important;
            width: 75px !important;
            font-size: 14px !important;
          }
        }
      }
    }

    .projectGrid {
      display: flex;
      flex: 1;
      flex-direction: column;

      .errorMsg {
        color: var(--red);
        text-align: center;
      }

      .expansionList {
        background: transparent;
        padding: 0;
        box-shadow: none;
        margin-top: 15px;

        &::before {
          display: none;
        }

        .expansionSummary {
          color: var(--textColor);
          font-size: 15px;
          padding: 0;
          min-height: 30px;
        }

        .expansionSummaryContent {
          margin: 0;
          height: 30px;

          button {
            padding: 0 !important;
            color: var(--textColor) !important;
            margin-left: -16px !important;

            svg {
              width: 40px;
              height: auto;
              transition: all .2s ease-out;
            }
          }

          h3 {
            margin:0;
            padding:0;
            line-height: 30px;
            margin-left: -5px;
            font-size:100%;
            font-weight:normal;
          }
        }

        .expansionSummaryExpanded {
          button > svg {
            transform: rotate(90deg);
          }
        }

        .expansionDetail {
          padding: 8px 0;

          ul{
            margin: 0;
            padding: 0;
          }

          h3{
            margin:0;
            padding:0;
          }
        }
      }

      .listContainer {
        display: grid;
        justify-content: center;
        align-items: center;
        grid-template-columns: repeat(auto-fill, minmax(232px, 1fr));
        gap: 15px;

        .itemContainer {
          padding: 10px;
          flex-shrink: 0;
          background: var(--dropdownMenuBackground);
          border-radius: 3px;
          position: relative;
          overflow: hidden;
          height: 205px;

          a {
            text-decoration: none;

            &:hover {
              color: var(--textColor);
            }
          }

          &:hover,
          &:focus-within {
            cursor: pointer;
            outline: solid 1px var(--panelCardHoverOutline);
          }

          .thumbnailContainer {
            width: 100%;
            height: 125px;
            background-size: cover;
            background-repeat: no-repeat;
            background-position: center center;
          }

          .headerContainer {
            display: flex;
            margin-top: 12px;

            .header {
              width: calc(100% - 24px);
              font-size: 14px;
              font-weight: bold;
              color: var(--textColor);
            }

            button {
              width: 24px !important;
              height: 24px !important;
              padding: 0 !important;

              svg {
                width: 22px;
              }

              &:hover,
              &:focus-within {
                opacity: 0.8;
              }
            }
          }

          .installedIcon {
            position: absolute;
            top: 2px;
            right: 2px;

            svg {
              position: relative;
              z-index: 1;
            }

            &::before {
              content: "";
              position: absolute;
              background: var(--iconButtonSelectedBackground);
              width: 60px;
              height: 60px;
              border-radius: 50%;
              top: -25px;
              right: -25px;
            }
          }

          .description {
            font-size: 12px;
            margin-top: 8px;
            color: var(--textColor);
            opacity: 0.9;
            overflow: hidden;
            position: relative;
            text-overflow: ellipsis;
            white-space: nowrap;

            &::after {
              content: "";
              position: absolute;
              bottom: 0;
              left: 0;
              width: 100%;
              height: 15px;
            }
          }
        }
      }
    }
  }

  .welcomeContainer {
    max-width: 460px;
    margin: 0 auto 30px;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;

    h1 {
      width: 100%;
      font-size: 30px;
      text-align: center;
    }

    h2 {
      width: 100%;
      font-size: 15px;
      text-align: center;
      margin-top: 10px;
      color: var(--textColor);
      font-weight: normal;
    }

    button {
      margin-right: 0 !important;
      margin-top: 30px !important;
      height: 35px !important;
      width: 135px !important;
    }
  }
}

.filterMenu {
  background: var(--dropdownMenuBackground);

  .filterMenuItem {
    position: relative;
    color: var(--textColor);
    font-size: 14px;
    padding-left: 35px;

    &:hover,
    &:focus-visible {
      background: var(--dropdownMenuHoverBackground);
    }

    & > svg,
    & > .progressbar {
      position: absolute;
      left: 10px;
      width: 15px;
      height: auto;
    }
  }
}

.width100 {
  width: 100%;
}

.githubRepoLinkDialog {
  background: var(--popupBackground);
  box-shadow: 16px 16px 32px 0 #11111159;
  backdrop-filter: blur(50px);
  color: var(--textColor);
  width: 400px;

  .inputContainer {
    color: var(--textColor);
    width: 100%;
    font-size: 14px;
    background-color: var(--inputBackground);
  }

  .input {
    border-color: var(--inputOutline);
    background-color: var(--inputBackground);
  }

  .input:hover + .outline {
    border-color: var(--inputOutline);
  }

  .input:focus + .outline,
  .outline {
    border-color: var(--inputOutline);
  }

  .btn {
    margin-top: 10px !important;
    height: 35px !important;
    width: 85px !important;
    font-size: 15px !important;
    background: linear-gradient(90deg, var(--buttonGradientStart), var(--buttonGradientEnd)) !important;

    &:disabled {
      cursor: not-allowed;
      background: var(--disabled);
    }
  }

  .errorMessage {
    color: var(--red);
    margin: 5px 2px;
    font-size: 100%;
    font-weight: normal;
  }

  .secondaryText {
    color: #808080;
  }

  .deleteButtons {
    display: flex;
    margin-top: 10px;
  }

  .outlinedButton {
    height: 40px;
    width: 100%;
    margin: 0 10px 0 0;

    &:hover {
      opacity: 0.8;
    }

    &:disabled {
      background: var(--disabled);
    }

    @media (max-width: 900px) {
      font-size: 0.7rem;
    }
  }

  .gradientButton {
    height: 40px;
    width: 100%;
    margin: 0 10px 0 0;
    background: linear-gradient(90deg, var(--buttonGradientStart), var(--buttonGradientEnd));
    color: var(--textColor) !important;

    &:hover {
      opacity: 0.8;
    }

    &:disabled {
      background: var(--disabled);
    }

    @media (max-width: 900px) {
      font-size: 0.7rem;
    }
  }

  .processing {
    display: flex;
    flex-direction: column;
    align-items: center;

    svg {
      color: var(--blue);
    }

    .text {
      margin-top: 10px;
      color: var(--textColor);
    }
  }
}

.createProjectDialog {
  background: var(--popupBackground);
  box-shadow: 16px 16px 32px 0 #11111159;
  backdrop-filter: blur(50px);
  color: var(--textColor);
  width: 300px;

  .inputContainer {
    color: var(--textColor);
    width: 100%;
    font-size: 14px;
    background-color: var(--inputBackground);
  }

  .input {
    border-color: var(--inputOutline);
    background-color: var(--inputBackground);
  }

  .input:hover + .outline {
    border-color: var(--inputOutline);
  }

  .input:focus + .outline,
  .outline {
    border-color: var(--inputOutline);
  }

  .btn {
    margin-top: 10px !important;
    height: 35px !important;
    width: 85px !important;
    font-size: 15px !important;
    background: linear-gradient(90deg, var(--buttonGradientStart), var(--buttonGradientEnd)) !important;

    &:disabled {
      cursor: not-allowed !important;
      background: var(--disabled) !important;
    }
  }

  .errorMessage {
    color: var(--red);
    margin: 5px 2px;
  }

  .secondaryText {
    color: #808080;
  }

  .deleteButtons {
    display: flex;
  }

  .processing {
    display: flex;
    flex-direction: column;
    align-items: center;

    svg {
      color: var(--blue);
    }

    .text {
      margin-top: 10px;
      color: var(--textColor);
    }
  }
}

.deleteDialog {
  background: var(--dockBackground);
  color: var(--textColor);
  width: 300px;

  .contentWrapper {
    display: flex;
    justify-content: center;

    .cancelBtn {
      margin: 0 10px !important;
      height: 35px !important;
      width: 85px !important;
      font-size: 14px !important;

      &:hover,
      &:focus-visible {
        background: var(--blue) !important;
        color: var(--textColor) !important;
      }
    }

    .confirmBtn {
      margin: 0 10px !important;
      height: 35px !important;
      width: 85px !important;
      font-size: 14px !important;
      background: var(--red) !important;

      &:hover,
      &:focus-visible {
        opacity: 0.8 !important;
      }
    }

    .confirmBtn[disabled] {
      background: var(--disabledText) !important;
      color: var(--textColor) !important;
      cursor: initial !important;
      opacity: 1 !important;
    }
  }
}

.iconButton {
  color: var(--iconButtonColor);
}

.profilePaper {
  background-color: transparent;
}

.refreshGHBtn {
  width: 200px;
  margin-top: 0;
  margin-bottom: 0;
  font-size: 14px;
  background-color: var(--popupBackground) !important;
  color: var(--textColor) !important;
}

