/**
 * DL-Engine API Gateway 路由数据传输对象
 */

import { IsString, IsOptional, IsBoolean, IsArray, IsObject, IsIn, IsNumber, <PERSON>, <PERSON> } from 'class-validator'
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { Transform } from 'class-transformer'

export class CreateRouteDto {
  @ApiProperty({ description: '路由路径', example: '/api/users/*' })
  @IsString()
  path: string

  @ApiProperty({ description: 'HTTP方法', example: 'GET', enum: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'ALL'] })
  @IsString()
  @IsIn(['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'ALL'])
  method: string

  @ApiProperty({ description: '目标服务名称', example: 'api' })
  @IsString()
  service: string

  @ApiPropertyOptional({ description: '中间件列表', example: ['cors', 'auth', 'rateLimit'] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  middleware?: string[]

  @ApiPropertyOptional({ description: '是否启用', example: true })
  @IsOptional()
  @IsBoolean()
  enabled?: boolean

  @ApiPropertyOptional({ description: '路由元数据', example: { auth: { required: true } } })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>
}

export class UpdateRouteDto {
  @ApiPropertyOptional({ description: '路由路径', example: '/api/users/*' })
  @IsOptional()
  @IsString()
  path?: string

  @ApiPropertyOptional({ description: 'HTTP方法', example: 'GET', enum: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'ALL'] })
  @IsOptional()
  @IsString()
  @IsIn(['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'ALL'])
  method?: string

  @ApiPropertyOptional({ description: '目标服务名称', example: 'api' })
  @IsOptional()
  @IsString()
  service?: string

  @ApiPropertyOptional({ description: '中间件列表', example: ['cors', 'auth', 'rateLimit'] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  middleware?: string[]

  @ApiPropertyOptional({ description: '是否启用', example: true })
  @IsOptional()
  @IsBoolean()
  enabled?: boolean

  @ApiPropertyOptional({ description: '路由元数据', example: { auth: { required: true } } })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>
}

export class RouteQueryDto {
  @ApiPropertyOptional({ description: '服务名称过滤', example: 'api' })
  @IsOptional()
  @IsString()
  service?: string

  @ApiPropertyOptional({ description: '启用状态过滤', example: true })
  @IsOptional()
  @Transform(({ value }) => value === 'true')
  @IsBoolean()
  enabled?: boolean

  @ApiPropertyOptional({ description: '路径过滤', example: '/api/users' })
  @IsOptional()
  @IsString()
  path?: string

  @ApiPropertyOptional({ description: '页码', example: 1, minimum: 1 })
  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  @IsNumber()
  @Min(1)
  page?: number

  @ApiPropertyOptional({ description: '每页数量', example: 10, minimum: 1, maximum: 100 })
  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  @IsNumber()
  @Min(1)
  @Max(100)
  limit?: number

  @ApiPropertyOptional({ description: '排序字段', example: 'createdAt' })
  @IsOptional()
  @IsString()
  sortBy?: string

  @ApiPropertyOptional({ description: '排序顺序', example: 'desc', enum: ['asc', 'desc'] })
  @IsOptional()
  @IsString()
  @IsIn(['asc', 'desc'])
  sortOrder?: 'asc' | 'desc'
}
