/**
 * DL-Engine API Gateway 代理模块
 */

import { Module } from '@nestjs/common'
import { ProxyController } from './proxy.controller'
import { ProxyService } from './proxy.service'
import { RoutingModule } from '../routing/routing.module'
import { LoadBalancerModule } from '../load-balancer/load-balancer.module'

@Module({
  imports: [RoutingModule, LoadBalancerModule],
  controllers: [ProxyController],
  providers: [ProxyService],
  exports: [ProxyService]
})
export class ProxyModule {}
