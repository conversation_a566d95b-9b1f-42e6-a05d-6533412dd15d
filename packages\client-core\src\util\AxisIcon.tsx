/*
CPAL-1.0 License

The contents of this file are subject to the Common Public Attribution License
Version 1.0. (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at
https://github.com/ir-engine/ir-engine/blob/dev/LICENSE.
The License is based on the Mozilla Public License Version 1.1, but Sections 14
and 15 have been added to cover use of software over a computer network and 
provide for limited attribution for the Original Developer. In addition, 
Exhibit A has been modified to be consistent with Exhibit B.

Software distributed under the License is distributed on an "AS IS" basis,
WITHOUT WARRANTY OF ANY KIND, either express or implied. See the License for the
specific language governing rights and limitations under the License.

The Original Code is Infinite Reality Engine.

The Original Developer is the Initial Developer. The Initial Developer of the
Original Code is the Infinite Reality Engine team.

All portions of the code written by the Infinite Reality Engine team are Copyright © 2021-2025
Infinite Reality Engine. All Rights Reserved.
*/

import React from 'react'

export const AxisIcon = () => {
  return (
    <svg role="img" focusable="false" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 14 14">
      <path d="M 6.99514,1.80245 6.2576,3.98086 c 0.15422,0.0866 0.32769,0.14517 0.50913,0.1732 l 0,3.36119 -0.97869,0.56507 0,1.08164 -2.86891,1.65638 C 2.80412,10.67521 2.66662,10.55428 2.51454,10.464 L 1,12.19755 3.25533,11.74707 c -0.002,-0.17683 -0.0381,-0.35636 -0.10457,-0.52751 l 2.82694,-1.63214 1.02069,0.58932 1.02069,-0.58932 2.82694,1.63214 c -0.0664,0.17116 -0.10244,0.35069 -0.10459,0.52755 L 13,12.19196 11.48222,10.46402 c -0.15208,0.0903 -0.28956,0.2112 -0.40457,0.35433 l -2.86888,-1.65636 0,-1.08167 -0.97875,-0.5651 0,-3.36116 c 0.18145,-0.028 0.35493,-0.0866 0.50916,-0.1732 L 6.99514,1.80245 Z" />
    </svg>
  )
}
