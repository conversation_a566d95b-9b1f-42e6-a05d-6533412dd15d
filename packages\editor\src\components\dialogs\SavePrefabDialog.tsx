/*
CPAL-1.0 License

The contents of this file are subject to the Common Public Attribution License
Version 1.0. (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at
https://github.com/ir-engine/ir-engine/blob/dev/LICENSE.
The License is based on the Mozilla Public License Version 1.1, but Sections 14
and 15 have been added to cover use of software over a computer network and 
provide for limited attribution for the Original Developer. In addition, 
Exhibit A has been modified to be consistent with Exhibit B.

Software distributed under the License is distributed on an "AS IS" basis,
WITHOUT WARRANTY OF ANY KIND, either express or implied. See the License for the
specific language governing rights and limitations under the License.

The Original Code is Infinite Reality Engine.

The Original Developer is the Initial Developer. The Initial Developer of the
Original Code is the Infinite Reality Engine team.

All portions of the code written by the Infinite Reality Engine team are Copyright © 2021-2025
Infinite Reality Engine. All Rights Reserved.
*/

import { ModalState } from '@ir-engine/client-core/src/common/services/ModalState'
import { isValidFileName } from '@ir-engine/common/src/utils/validateFileName'
import { getComponent, hasComponent } from '@ir-engine/ecs'
import { STATIC_ASSET_REGEX } from '@ir-engine/engine/src/assets/functions/pathResolver'
import { GLTFComponent } from '@ir-engine/engine/src/gltf/GLTFComponent'
import { getState, useHookstate } from '@ir-engine/hyperflux'
import { Input } from '@ir-engine/ui'
import Modal from '@ir-engine/ui/src/primitives/tailwind/Modal'
import React from 'react'
import { useTranslation } from 'react-i18next'
import { exportRelativeGLTF } from '../../functions/exportGLTF'
import { AssetsRefreshState } from '../../panels/assets/hooks'
import { FileRefreshState } from '../../panels/files/helpers'
import { EditorState } from '../../services/EditorServices'

export default function SavePrefabPanel({ entity }) {
  const { t } = useTranslation()
  if (!hasComponent(entity, GLTFComponent))
    throw new Error('Cannot save a prefab that has no GLTF Component on root entity')
  const gltfComponent = getComponent(entity, GLTFComponent)
  const srcPath = useHookstate(STATIC_ASSET_REGEX.exec(gltfComponent.src)?.[3].replace(/\.[^.]*$/, ''))
  const fileName = (srcPath.value ?? '').split('/').pop() ?? ''
  const resultFileName = useHookstate(isValidFileName(fileName))

  const onSavePrefab = async () => {
    const saveName = srcPath.value + '.gltf'
    await exportRelativeGLTF(entity, getState(EditorState).projectName!, saveName, false)
    AssetsRefreshState.triggerRefresh()
    FileRefreshState.triggerRefresh()

    ModalState.closeModal()
  }

  return (
    <Modal
      title={t('editor:dialog.savePrefab.title')}
      onSubmit={onSavePrefab}
      submitButtonDisabled={!resultFileName.isValid.value}
      className="w-[50vw] max-w-2xl"
      onClose={ModalState.closeModal}
    >
      <Input
        value={srcPath.value}
        onChange={(event) => {
          const fileName = (event.target.value ?? '').split('/').pop() ?? ''
          resultFileName.set(isValidFileName(fileName))
          srcPath.set(event.target.value)
        }}
        labelProps={{
          text: t('editor:dialog.savePrefab.lbl-save-path'),
          position: 'top'
        }}
        state={!resultFileName.value.isValid ? 'error' : undefined}
        helperText={!resultFileName.value.isValid ? resultFileName.value.error : undefined}
      />
    </Modal>
  )
}
