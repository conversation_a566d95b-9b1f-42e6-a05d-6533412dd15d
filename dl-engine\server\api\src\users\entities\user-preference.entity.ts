import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToOne,
  JoinColumn
} from 'typeorm'
import { User } from './user.entity'

/**
 * 主题模式枚举
 */
export enum ThemeMode {
  LIGHT = 'light',
  DARK = 'dark',
  AUTO = 'auto'
}

/**
 * 通知频率枚举
 */
export enum NotificationFrequency {
  IMMEDIATE = 'immediate',
  HOURLY = 'hourly',
  DAILY = 'daily',
  WEEKLY = 'weekly',
  NEVER = 'never'
}

/**
 * 学习提醒设置
 */
export interface LearningReminderSettings {
  enabled: boolean
  time: string // HH:mm 格式
  days: number[] // 0-6，0为周日
  timezone: string
}

/**
 * 用户偏好设置实体
 * 
 * 存储用户的个性化设置和偏好
 */
@Entity('user_preferences')
export class UserPreference {
  @PrimaryGeneratedColumn('uuid')
  id: string

  /**
   * 关联的用户ID
   */
  @Column({ type: 'uuid' })
  userId: string

  /**
   * 界面语言
   */
  @Column({ type: 'varchar', length: 10, default: 'zh-CN' })
  language: string

  /**
   * 主题模式
   */
  @Column({
    type: 'enum',
    enum: ThemeMode,
    default: ThemeMode.AUTO
  })
  theme: ThemeMode

  /**
   * 时区设置
   */
  @Column({ type: 'varchar', length: 50, default: 'Asia/Shanghai' })
  timezone: string

  /**
   * 日期格式
   */
  @Column({ type: 'varchar', length: 20, default: 'YYYY-MM-DD' })
  dateFormat: string

  /**
   * 时间格式
   */
  @Column({ type: 'varchar', length: 20, default: 'HH:mm' })
  timeFormat: string

  /**
   * 是否启用声音
   */
  @Column({ type: 'boolean', default: true })
  soundEnabled: boolean

  /**
   * 音量设置（0-100）
   */
  @Column({ type: 'int', default: 80 })
  volume: number

  /**
   * 是否启用动画效果
   */
  @Column({ type: 'boolean', default: true })
  animationsEnabled: boolean

  /**
   * 是否启用高对比度模式
   */
  @Column({ type: 'boolean', default: false })
  highContrastMode: boolean

  /**
   * 字体大小（相对大小）
   */
  @Column({ type: 'varchar', length: 20, default: 'medium' })
  fontSize: string // small, medium, large, extra-large

  /**
   * 是否启用邮件通知
   */
  @Column({ type: 'boolean', default: true })
  emailNotifications: boolean

  /**
   * 是否启用短信通知
   */
  @Column({ type: 'boolean', default: true })
  smsNotifications: boolean

  /**
   * 是否启用推送通知
   */
  @Column({ type: 'boolean', default: true })
  pushNotifications: boolean

  /**
   * 通知频率
   */
  @Column({
    type: 'enum',
    enum: NotificationFrequency,
    default: NotificationFrequency.IMMEDIATE
  })
  notificationFrequency: NotificationFrequency

  /**
   * 学习提醒设置
   */
  @Column({ type: 'json', nullable: true })
  learningReminder?: LearningReminderSettings

  /**
   * 是否启用学习统计
   */
  @Column({ type: 'boolean', default: true })
  learningAnalytics: boolean

  /**
   * 是否启用自动保存
   */
  @Column({ type: 'boolean', default: true })
  autoSave: boolean

  /**
   * 自动保存间隔（秒）
   */
  @Column({ type: 'int', default: 30 })
  autoSaveInterval: number

  /**
   * 默认项目可见性
   */
  @Column({ type: 'varchar', length: 20, default: 'private' })
  defaultProjectVisibility: string // public, private, unlisted

  /**
   * 是否启用协作邀请
   */
  @Column({ type: 'boolean', default: true })
  allowCollaborationInvites: boolean

  /**
   * 是否启用好友请求
   */
  @Column({ type: 'boolean', default: true })
  allowFriendRequests: boolean

  /**
   * 是否显示在线状态
   */
  @Column({ type: 'boolean', default: true })
  showOnlineStatus: boolean

  /**
   * 键盘快捷键设置
   */
  @Column({ type: 'json', nullable: true })
  keyboardShortcuts?: Record<string, string>

  /**
   * 编辑器设置
   */
  @Column({ type: 'json', nullable: true })
  editorSettings?: {
    gridSnap: boolean
    gridSize: number
    showGrid: boolean
    showGizmos: boolean
    cameraSpeed: number
    [key: string]: any
  }

  /**
   * 创建时间
   */
  @CreateDateColumn()
  createdAt: Date

  /**
   * 更新时间
   */
  @UpdateDateColumn()
  updatedAt: Date

  // 关联关系
  @OneToOne(() => User, user => user.preference)
  @JoinColumn({ name: 'userId' })
  user: User
}
