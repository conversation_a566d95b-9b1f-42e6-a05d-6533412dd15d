/*
CPAL-1.0 License

The contents of this file are subject to the Common Public Attribution License
Version 1.0. (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at
https://github.com/ir-engine/ir-engine/blob/dev/LICENSE.
The License is based on the Mozilla Public License Version 1.1, but Sections 14
and 15 have been added to cover use of software over a computer network and 
provide for limited attribution for the Original Developer. In addition, 
Exhibit A has been modified to be consistent with Exhibit B.

Software distributed under the License is distributed on an "AS IS" basis,
WITHOUT WARRANTY OF ANY KIND, either express or implied. See the License for the
specific language governing rights and limitations under the License.

The Original Code is Infinite Reality Engine.

The Original Developer is the Initial Developer. The Initial Developer of the
Original Code is the Infinite Reality Engine team.

All portions of the code written by the Infinite Reality Engine team are Copyright © 2021-2025
Infinite Reality Engine. All Rights Reserved.
*/

import type { SVGProps } from 'react'
import * as React from 'react'
import { Ref, forwardRef } from 'react'
const GoogleRoundedSquareFalse = (props: SVGProps<SVGSVGElement>, ref: Ref<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="1em"
    height="1em"
    fill="none"
    viewBox="0 0 48 48"
    role="img"
    stroke="currentColor"
    ref={ref}
    {...props}
  >
    <mask id="prefix__a" fill="#fff">
      <path
        fillRule="evenodd"
        d="M1.126 5.39C0 7.558 0 10.405 0 16.1v15.8c0 5.695 0 8.542 1.126 10.71a10 10 0 0 0 4.264 4.264C7.558 48 10.405 48 16.1 48h15.8c5.695 0 8.542 0 10.71-1.126a10 10 0 0 0 4.264-4.264C48 40.442 48 37.595 48 31.9V16.1c0-5.695 0-8.542-1.126-10.71a10 10 0 0 0-4.264-4.264C40.442 0 37.595 0 31.9 0H16.1C10.405 0 7.558 0 5.39 1.126A10 10 0 0 0 1.126 5.39M24 14.364c2.35 0 4.458.807 6.116 2.392l4.59-4.589C31.935 9.585 28.313 8 24 8c-6.254 0-11.665 3.585-14.298 8.815A16 16 0 0 0 8 24c0 2.582.618 5.026 1.702 7.186C12.335 36.415 17.746 40 24 40c4.32 0 7.942-1.433 10.59-3.876 3.025-2.786 4.77-6.888 4.77-11.76 0-1.135-.102-2.226-.29-3.273H24v6.19h8.61c-.37 1.999-1.497 3.694-3.192 4.828-1.433.96-3.265 1.527-5.418 1.527-4.167 0-7.695-2.814-8.953-6.596a9.6 9.6 0 0 1-.502-3.04c0-1.055.182-2.08.502-3.04 1.258-3.782 4.786-6.596 8.953-6.596"
        clipRule="evenodd"
      />
    </mask>
    <path
      fill="#080808"
      fillRule="evenodd"
      d="M1.126 5.39C0 7.558 0 10.405 0 16.1v15.8c0 5.695 0 8.542 1.126 10.71a10 10 0 0 0 4.264 4.264C7.558 48 10.405 48 16.1 48h15.8c5.695 0 8.542 0 10.71-1.126a10 10 0 0 0 4.264-4.264C48 40.442 48 37.595 48 31.9V16.1c0-5.695 0-8.542-1.126-10.71a10 10 0 0 0-4.264-4.264C40.442 0 37.595 0 31.9 0H16.1C10.405 0 7.558 0 5.39 1.126A10 10 0 0 0 1.126 5.39M24 14.364c2.35 0 4.458.807 6.116 2.392l4.59-4.589C31.935 9.585 28.313 8 24 8c-6.254 0-11.665 3.585-14.298 8.815A16 16 0 0 0 8 24c0 2.582.618 5.026 1.702 7.186C12.335 36.415 17.746 40 24 40c4.32 0 7.942-1.433 10.59-3.876 3.025-2.786 4.77-6.888 4.77-11.76 0-1.135-.102-2.226-.29-3.273H24v6.19h8.61c-.37 1.999-1.497 3.694-3.192 4.828-1.433.96-3.265 1.527-5.418 1.527-4.167 0-7.695-2.814-8.953-6.596a9.6 9.6 0 0 1-.502-3.04c0-1.055.182-2.08.502-3.04 1.258-3.782 4.786-6.596 8.953-6.596"
      clipRule="evenodd"
    />
    <path
      fill="#000"
      fillOpacity={0.15}
      d="m1.126 5.39.444.23zm0 37.22-.444.23zm4.264 4.264-.23.444zm37.22 0 .23.444zm4.264-4.264.444.23zm0-37.22.444-.23zM42.61 1.126l.23-.444zm-37.22 0 .23.444zm24.726 15.63-.345.362.353.338.346-.346zm4.59-4.589.353.354.366-.366-.379-.353zM9.702 16.815l-.447-.225zm0 14.37-.447.225zm24.887 4.939-.338-.368h-.001zm4.48-15.033.492-.089-.074-.411h-.418zm-15.069 0v-.5h-.5v.5zm0 6.19h-.5v.5h.5zm8.61 0 .493.09.11-.59h-.602zm-3.192 4.828-.278-.415zm-14.37-5.069.474-.158zm0-6.08.474.158zM.5 16.1c0-2.856 0-4.967.139-6.636.138-1.663.41-2.843.93-3.843L.683 5.16C.076 6.327-.214 7.655-.358 9.38-.5 11.102-.5 13.26-.5 16.1zm0 15.8V16.1h-1v15.8zm1.07 10.48c-.52-1.001-.793-2.18-.931-3.844C.5 36.867.5 34.756.5 31.9h-1c0 2.84 0 4.999.142 6.72.144 1.725.434 3.053 1.04 4.22zm4.05 4.05a9.5 9.5 0 0 1-4.05-4.05l-.888.46a10.5 10.5 0 0 0 4.478 4.478zM16.1 47.5c-2.856 0-4.967 0-6.636-.139-1.663-.138-2.843-.41-3.843-.93l-.461.887c1.167.606 2.495.896 4.22 1.04 1.721.142 3.88.142 6.72.142zm15.8 0H16.1v1h15.8zm10.48-1.07c-1.001.52-2.18.793-3.844.931-1.669.139-3.78.139-6.636.139v1c2.84 0 4.999 0 6.72-.142 1.725-.144 3.053-.434 4.22-1.04zm4.05-4.05a9.5 9.5 0 0 1-4.05 4.05l.46.888a10.5 10.5 0 0 0 4.478-4.478zM47.5 31.9c0 2.856 0 4.967-.139 6.636-.138 1.663-.41 2.843-.93 3.843l.887.461c.606-1.167.896-2.495 1.04-4.22.142-1.721.142-3.88.142-6.72zm0-15.8v15.8h1V16.1zM46.43 5.62c.52 1.001.793 2.18.931 3.844.139 1.669.139 3.78.139 6.636h1c0-2.84 0-4.999-.142-6.72-.144-1.725-.434-3.053-1.04-4.22zm-4.05-4.05a9.5 9.5 0 0 1 4.05 4.05l.888-.46A10.5 10.5 0 0 0 42.84.682zM31.9.5c2.856 0 4.967 0 6.636.139 1.663.138 2.843.41 3.843.93l.461-.887c-1.167-.606-2.495-.896-4.22-1.04C36.898-.5 34.74-.5 31.9-.5zM16.1.5h15.8v-1H16.1zM5.62 1.57C6.622 1.05 7.8.777 9.465.639 11.133.5 13.244.5 16.1.5v-1c-2.84 0-4.999 0-6.72.142-1.725.144-3.053.434-4.22 1.04zM1.57 5.62a9.5 9.5 0 0 1 4.05-4.05L5.16.682A10.5 10.5 0 0 0 .682 5.16zm28.892 10.775c-1.757-1.68-3.992-2.531-6.462-2.531v1c2.228 0 4.211.762 5.77 2.254zm3.89-4.581-4.59 4.589.708.707 4.59-4.59zM24 8.5c4.182 0 7.683 1.534 10.365 4.033l.681-.731C32.186 9.137 28.444 7.5 24 7.5zm-13.852 8.54C12.7 11.971 17.942 8.5 24 8.5v-1c-6.45 0-12.03 3.698-14.745 9.09zM8.5 24c0-2.501.599-4.868 1.649-6.961l-.894-.449A16.5 16.5 0 0 0 7.5 24zm1.649 6.961a15.5 15.5 0 0 1-1.649-6.96h-1c0 2.661.638 5.182 1.755 7.409zM24 39.5c-6.058 0-11.3-3.472-13.852-8.54l-.893.45C11.97 36.801 17.55 40.5 24 40.5zm10.25-3.744C31.703 38.107 28.205 39.5 24 39.5v1c4.435 0 8.18-1.473 10.928-4.009zm4.61-11.392c0 4.75-1.7 8.712-4.61 11.392l.678.736c3.14-2.892 4.932-7.133 4.932-12.128zm-.283-3.184c.184 1.018.283 2.08.283 3.184h1c0-1.165-.105-2.286-.299-3.362zM24 21.59h15.07v-1H24zm.5 5.69v-6.19h-1v6.19zm8.11-.5H24v1h8.61zm-2.914 5.744c1.805-1.209 3.01-3.02 3.407-5.154l-.984-.182c-.346 1.865-1.395 3.444-2.979 4.505zM24 34.136c2.242 0 4.174-.591 5.697-1.611l-.557-.831c-1.343.9-3.076 1.442-5.14 1.442zm-9.427-6.938c1.32 3.97 5.027 6.938 9.427 6.938v-1c-3.935 0-7.282-2.66-8.478-6.254zM14.046 24c0 1.112.191 2.19.527 3.198l.949-.316A9.1 9.1 0 0 1 15.046 24zm.527-3.198A10.1 10.1 0 0 0 14.046 24h1c0-.997.171-1.97.476-2.882zM24 13.864c-4.4 0-8.107 2.969-9.427 6.938l.949.316c1.196-3.594 4.543-6.254 8.478-6.254z"
      mask="url(#prefix__a)"
    />
  </svg>
)
const ForwardRef = forwardRef(GoogleRoundedSquareFalse)
export default ForwardRef
