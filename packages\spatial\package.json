{"name": "@ir-engine/spatial", "version": "1.0.3", "main": "index.ts", "publishConfig": {"access": "public"}, "description": "Core spatial system for the Infinite Reality Engine", "scripts": {"check-errors": "tsc --noemit && npx cycle-import-check src || true", "test": "cross-env TEST=true vitest run --config=../../vitest.client.config.ts", "test-coverage": "npm run test-coverage-generate ; npm run test-coverage-launch", "test-coverage-generate": "npm run test -- --coverage --silent", "test-coverage-launch": "vite preview --open --outDir coverage", "test-spot": "cross-env TEST=true vitest ./src/renderer/components/lights/DirectionalLightComponent.test.ts --config=../../vitest.client.config.ts"}, "repository": "http://github.com/ir-engine/ir-engine", "author": "Infinite Reality Engine", "license": "CPAL", "dependencies": {"detect-gpu": "^5.0.38", "@dimforge/rapier3d-compat": "0.11.2", "@ir-engine/ecs": "^1.0.3", "@ir-engine/hyperflux": "^1.0.3", "@tweenjs/tween.js": "^23.1.2", "lodash": "4.17.21", "noisejs": "2.1.0", "postprocessing": "6.37.3", "react": "18.2.0", "react-dom": "18.2.0", "three": "0.176.0", "three-mesh-bvh": "0.9.0", "ts-matches": "5.3.0", "web-worker": "1.2.0"}, "devDependencies": {"@types/node": "18.15.5", "fake-indexeddb": "6.0.0", "gl-matrix": "3.4.3", "rimraf": "4.4.0", "localforage": "^1.10.0", "webxr-polyfill": "github:felixtrz/webxr-polyfill#81d2db4f01b518f2c42b74a90973dac095515e9f"}, "gitHead": "2313453697ca7c6b8d36b3b166b5a6445fe1c851"}