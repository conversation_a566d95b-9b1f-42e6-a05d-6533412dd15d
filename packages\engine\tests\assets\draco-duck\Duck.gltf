{"asset": {"generator": "COLLADA2GLTF", "version": "2.0"}, "scene": 0, "scenes": [{"nodes": [0]}], "nodes": [{"children": [2, 1], "matrix": [0.009999999776482582, 0, 0, 0, 0, 0.009999999776482582, 0, 0, 0, 0, 0.009999999776482582, 0, 0, 0, 0, 1]}, {"matrix": [-0.7289686799049377, 0, -0.6845470666885376, 0, -0.4252049028873444, 0.7836934328079224, 0.4527972936630249, 0, 0.5364750623703003, 0.6211478114128113, -0.571287989616394, 0, 400.1130065917969, 463.2640075683594, -431.0780334472656, 1], "camera": 0}, {"mesh": 0}], "cameras": [{"perspective": {"aspectRatio": 1.5, "yfov": 0.6605925559997559, "zfar": 10000, "znear": 1}, "type": "perspective"}], "meshes": [{"primitives": [{"attributes": {"NORMAL": 1, "POSITION": 2, "TEXCOORD_0": 3}, "indices": 0, "mode": 4, "material": 0, "extensions": {"KHR_draco_mesh_compression": {"bufferView": 0, "attributes": {"NORMAL": 0, "POSITION": 1, "TEXCOORD_0": 2}}}}], "name": "LOD3spShape"}], "accessors": [{"componentType": 5123, "count": 12636, "max": [2398], "min": [0], "type": "SCALAR"}, {"componentType": 5126, "count": 2399, "max": [1.0083384775647932, 1.007422473383885, 1.0075904988775068], "min": [-1.0069254898557476, -1.0078414940366558, -1.007673468543034], "type": "VEC3"}, {"componentType": 5126, "count": 2399, "max": [96.26074059602396, 164.09024489374352, 54.029730459044615], "min": [-69.37933953401223, 9.848530453475558, -61.40903695222513], "type": "VEC3"}, {"componentType": 5126, "count": 2399, "max": [0.9846059706495423, 0.9809754626608782], "min": [0.025470511450678954, 0.019024537339121947], "type": "VEC2"}], "materials": [{"pbrMetallicRoughness": {"baseColorTexture": {"index": 0, "texCoord": 0}, "metallicFactor": 0, "baseColorFactor": [1, 1, 1, 1], "roughnessFactor": 1}, "emissiveFactor": [0, 0, 0], "name": "blinn3-fx", "alphaMode": "OPAQUE", "doubleSided": false}], "textures": [{"sampler": 0, "source": 0}], "images": [{"name": "DuckCM", "uri": "DuckCM.png"}], "samplers": [{"magFilter": 9729, "minFilter": 9986, "wrapS": 10497, "wrapT": 10497}], "bufferViews": [{"buffer": 0, "byteOffset": 0, "byteLength": 10366}], "buffers": [{"name": "<PERSON>", "byteLength": 10368, "uri": "Duck.bin"}], "extensionsRequired": ["KHR_draco_mesh_compression"], "extensionsUsed": ["KHR_draco_mesh_compression"]}