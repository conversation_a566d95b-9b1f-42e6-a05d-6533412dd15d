/*
CPAL-1.0 License

The contents of this file are subject to the Common Public Attribution License
Version 1.0. (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at
https://github.com/EtherealEngine/etherealengine/blob/dev/LICENSE.
The License is based on the Mozilla Public License Version 1.1, but Sections 14
and 15 have been added to cover use of software over a computer network and 
provide for limited attribution for the Original Developer. In addition, 
Exhibit A has been modified to be consistent with Exhibit B.

Software distributed under the License is distributed on an "AS IS" basis,
WITHOUT WARRANTY OF ANY KIND, either express or implied. See the License for the
specific language governing rights and limitations under the License.

The Original Code is Ethereal Engine.

The Original Developer is the Initial Developer. The Initial Developer of the
Original Code is the Ethereal Engine team.

All portions of the code written by the Ethereal Engine team are Copyright © 2021-2025 
Ethereal Engine. All Rights Reserved.
*/

class MeshoptDecoderConstructor {
  supported: boolean;
  ready: Promise<void>;
  useWorkers: (numWorkers: number) => void;
  decodeVertexBuffer: (buffer: ArrayBuffer, count: number, stride: number, filter: number) => ArrayBuffer;
  decodeIndexBuffer: (buffer: ArrayBuffer, count: number, filter: number) => ArrayBuffer;
  decodeIndexSequence: (buffer: ArrayBuffer, count: number, filter: number) => ArrayBuffer;
  decodeGltfBuffer: (buffer: Uint8Array, count: number, stride: number, source: Uint8Array, mode: number, filter: number) => void;
  decodeGltfBufferAsync?: (count: number, stride: number, source: Uint8Array, mode: number, filter: number) => Promise<{ buffer: ArrayBuffer }>;
}

declare const MeshoptDecoder: MeshoptDecoderConstructor;
export { MeshoptDecoder };