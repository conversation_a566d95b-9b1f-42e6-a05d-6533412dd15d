{"name": "@ir-engine/xrui", "version": "1.0.3", "main": "index.ts", "description": "A set of utilities for using DOM elements to create immerive user interfaces", "homepage": "https://github.com/ir-engine/ir-engine#readme", "keywords": ["3D", "AR", "DOM", "VR", "XR", "three", "web"], "repository": {"type": "git", "url": "git://github.com/ir-engine/ir-engine.git"}, "author": {"name": "Infinite Reality Engine Collective", "email": "<EMAIL>", "url": "http://github.com/ir-engine"}, "bugs": {"url": "https://github.com/ir-engine/ir-engine/issues"}, "engines": {"node": ">= 22.11.0"}, "publishConfig": {"access": "public"}, "npmClient": "npm", "scripts": {"check-errors": "tsc --noemit && npx cycle-import-check src", "build": "npm run build:worker", "build:worker": "esbuild core/textures/KTX2Worker.ts --bundle --format=esm --outfile=core/textures/KTX2Worker.bundle.js --loader:.wasm=binary", "test": "cross-env TEST=true vitest run --config=../../vitest.client.config.ts"}, "dependencies": {"@juggle/resize-observer": "^3.4.0", "fflate": "^0.7.4", "msgpackr": "^1.9.2"}, "devDependencies": {"@types/node": "18.15.5", "cross-env": "^7.0.3", "esbuild": "0.17.12"}, "license": "CPAL"}