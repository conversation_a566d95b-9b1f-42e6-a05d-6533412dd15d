{"name": "@ir-engine/hyperflux", "version": "1.0.3", "main": "index.ts", "description": "Agent Centric Reactive Data Management for Infinite Reality Engine", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git://github.com/ir-engine/ir-engine.git"}, "author": "", "license": "CPAL", "bugs": {"url": "https://github.com/ir-engine/ir-engine/issues"}, "dependencies": {"@hookstate/identifiable": "^4.0.0", "@hookstate/core": "4.0.1", "react": "18.2.0", "react-reconciler": "0.29.0", "ts-matches": "5.3.0", "ts-toolbelt": "^9.6.0", "uuid": "9.0.0"}, "devDependencies": {"@types/node": "^22.5.2", "@types/react-reconciler": "^0.28.2", "cross-env": "^7.0.3", "ts-matches": "5.3.0", "typescript": "5.6.3", "vite": "5.4.8", "vite-plugin-dts": "^3.9.1", "vite-plugin-externalize-deps": "^0.8.0", "vite-plugin-static-copy": "^1.0.6"}, "scripts": {"check-errors": "tsc --noemit && npx cycle-import-check src || true", "test": "cross-env TEST=true vitest run --config=../../vitest.client.config.ts", "test-coverage": "npm run test-coverage-generate ; npm run test-coverage-launch", "test-coverage-generate": "npm run test -- --coverage --silent", "test-coverage-launch": "vite preview --open --outDir coverage", "build": "ts-node --swc build.ts", "build-publish": "npm run build && cd dist && npm publish --access public --dry-run"}, "overrides": {"@hookstate/identifiable": {"@hookstate/core": "npm:@hookstate/core@4.0.1"}}}