/*
CPAL-1.0 License

The contents of this file are subject to the Common Public Attribution License
Version 1.0. (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at
https://github.com/ir-engine/ir-engine/blob/dev/LICENSE.
The License is based on the Mozilla Public License Version 1.1, but Sections 14
and 15 have been added to cover use of software over a computer network and 
provide for limited attribution for the Original Developer. In addition, 
Exhibit A has been modified to be consistent with Exhibit B.

Software distributed under the License is distributed on an "AS IS" basis,
WITHOUT WARRANTY OF ANY KIND, either express or implied. See the License for the
specific language governing rights and limitations under the License.

The Original Code is Infinite Reality Engine.

The Original Developer is the Initial Developer. The Initial Developer of the
Original Code is the Infinite Reality Engine team.

All portions of the code written by the Infinite Reality Engine team are Copyright © 2021-2025
Infinite Reality Engine. All Rights Reserved.
*/

import React from 'react'
import { useTranslation } from 'react-i18next'

import { useFind, useMutation } from '@ir-engine/common'
import { ChannelID, messagePath } from '@ir-engine/common/src/schema.type.module'
import { Engine } from '@ir-engine/ecs/src/Engine'
import { useHookstate, useMutableState } from '@ir-engine/hyperflux'

import { Input } from '@ir-engine/ui'
import { ArrowLeftLg, Send01Lg } from '@ir-engine/ui/src/icons'
import Text from '@ir-engine/ui/src/primitives/tailwind/Text'
import { MdCall, MdCallEnd } from 'react-icons/md'
import { ModalState } from '../../../common/services/ModalState'
import { useUserAvatarThumbnail } from '../../../hooks/useUserAvatarThumbnail'
import { ChannelService, ChannelState } from '../../../social/services/ChannelService'
import XRIconButton from '../../../systems/components/XRIconButton'
import FriendsMenu from './FriendsMenu'

const MessagesMenu = (props: { channelID: ChannelID; name: string }): JSX.Element => {
  const { t } = useTranslation()

  const userThumbnail = useUserAvatarThumbnail(Engine.instance.userID)

  const { data: messages } = useFind(messagePath, {
    query: {
      channelId: props.channelID,
      $sort: {
        createdAt: 1
      }
    }
  })

  const channelState = useMutableState(ChannelState)
  const inChannelCall = channelState.targetChannelId.value === props.channelID

  const startMediaCall = () => {
    ChannelService.joinChannelInstance(inChannelCall ? ('' as ChannelID) : props.channelID)
  }

  const SelfMessage = (props: { message: (typeof messages)[0] }) => {
    return (
      <div style={{ display: 'flex', flexWrap: 'wrap', marginTop: '24px', marginLeft: 'auto' }}>
        <div style={{ height: '20px', marginLeft: '147px', marginRight: '20px' }}>
          <p
            style={{
              borderRadius: '20px',
              border: '2px solid #E1E1E1',
              color: 'black',
              backgroundColor: '#E1E1E1',
              padding: '3px',
              fontFamily: 'var(--lato)'
            }}
          >
            {props.message.text}
          </p>
        </div>
        <img
          style={{ maxWidth: '100%', borderRadius: '38px', width: '36px', height: '36px', objectFit: 'cover' }}
          alt=""
          src={userThumbnail}
        />
      </div>
    )
  }

  const OtherMessage = (props: { message: (typeof messages)[0] }) => {
    const systemMessage = !props.message.sender

    const userThumbnail = useUserAvatarThumbnail(props.message.sender?.id)
    return (
      <div style={{ display: 'flex', flexWrap: 'wrap', marginLeft: systemMessage ? 'auto' : '', marginRight: 'auto' }}>
        {!systemMessage && (
          <img
            style={{ maxWidth: '100%', borderRadius: '38px', width: '36px', height: '36px', objectFit: 'cover' }}
            alt=""
            src={userThumbnail}
          />
        )}
        <div style={{ height: '20px', marginLeft: '20px' }}>
          <p
            style={{
              borderRadius: '20px',
              border: systemMessage ? '' : '2px solid #F8F8F8',
              color: 'black',
              backgroundColor: systemMessage ? '' : '#F8F8F8',
              padding: '3px',
              fontFamily: 'var(--lato)'
            }}
          >
            {props.message.text}
          </p>
        </div>
      </div>
    )
  }

  const MessageField = () => {
    const composingMessage = useHookstate('')

    const mutateMessage = useMutation(messagePath)

    const sendMessage = () => {
      mutateMessage.create({
        text: composingMessage.value,
        channelId: props.channelID
      })
      composingMessage.set('')
    }

    const handleMessageKeyDown = (event) => {
      if (event.key === 'Enter' && event.shiftKey) {
        event.preventDefault()
        const selectionStart = (event.target as HTMLInputElement).selectionStart

        composingMessage.set(
          composingMessage.value.substring(0, selectionStart || 0) +
            '\n' +
            composingMessage.value.substring(selectionStart || 0)
        )
        return
      } else if (event.key === 'Enter' && !event.shiftKey) {
        event.preventDefault()
        sendMessage()
        return
      }
    }

    return (
      <div style={{ position: 'absolute', bottom: '0px', display: 'flex' }}>
        <Input
          placeholder={t('user:messages.enterMessage')}
          value={composingMessage.value}
          onChange={(e) => composingMessage.set(e.target.value)}
          onKeyDown={(e) => handleMessageKeyDown(e)}
          endComponent={
            <button className="h-4 w-4" onMouseDown={sendMessage}>
              <Send01Lg />
            </button>
          }
          startComponent={
            <img
              style={{ maxWidth: '100%', borderRadius: '38px', width: '16px', height: '16px', objectFit: 'cover' }}
              src={userThumbnail}
            />
          }
        />
        <XRIconButton
          size="large"
          xr-layer="true"
          title={t('user:friends.call')}
          style={{ position: 'absolute', right: '0px' }}
          variant="iconOnly"
          onClick={() => startMediaCall()}
          content={inChannelCall ? <MdCallEnd /> : <MdCall />}
        />
      </div>
    )
  }

  return (
    <div className="absolute z-50 h-fit max-h-[60vh] w-[50vw] min-w-[500px] max-w-2xl overflow-y-auto rounded-2xl bg-surface-1 px-10 py-6">
      <Text fontWeight="semibold" fontSize="lg" component="h2">
        {props.name}
      </Text>
      <XRIconButton
        size="large"
        xr-layer="true"
        className="iconBlock"
        variant="iconOnly"
        onClick={() => ModalState.openModal(<FriendsMenu />)}
        content={<ArrowLeftLg fontSize="larger" />}
      />
      <div style={{ height: '600px', maxWidth: '100%', overflowX: 'hidden' }}>
        <div
          style={{
            height: 'auto',
            marginLeft: '6px',
            marginBottom: '100px',
            marginTop: '4px',
            marginRight: '8px',
            display: 'flex',
            flexDirection: 'column',
            flexWrap: 'wrap'
          }}
        >
          {messages.map((message, index) => {
            if (message.sender?.id === Engine.instance.userID) return <SelfMessage key={index} message={message} />
            else return <OtherMessage key={index} message={message} />
          })}
        </div>
        <MessageField />
      </div>
    </div>
  )
}

export default MessagesMenu
