/**
 * DL-Engine 认证服务配置
 * 
 * 管理所有认证相关配置：
 * - 服务器配置
 * - 数据库配置
 * - JWT配置
 * - 短信服务配置
 * - OAuth配置
 * - 安全配置
 */

import { Injectable } from '@nestjs/common'

@Injectable()
export class ConfigService {
  private readonly config = {
    server: {
      port: parseInt(process.env.AUTH_PORT || '3031'),
      host: process.env.AUTH_HOST || '0.0.0.0',
      environment: process.env.NODE_ENV || 'development'
    },

    cors: {
      allowedOrigins: (process.env.ALLOWED_ORIGINS || 'http://localhost:3000,https://localhost:3001').split(','),
      credentials: true
    },

    database: {
      type: 'mysql' as const,
      host: process.env.MYSQL_HOST || 'localhost',
      port: parseInt(process.env.MYSQL_PORT || '3306'),
      username: process.env.MYSQL_USER || 'root',
      password: process.env.MYSQL_PASSWORD || 'password',
      database: process.env.MYSQL_DATABASE || 'dl_engine_auth',
      synchronize: process.env.NODE_ENV === 'development',
      logging: process.env.NODE_ENV === 'development',
      charset: 'utf8mb4',
      timezone: '+08:00'
    },

    redis: {
      host: process.env.REDIS_HOST || 'localhost',
      port: parseInt(process.env.REDIS_PORT || '6379'),
      password: process.env.REDIS_PASSWORD,
      db: parseInt(process.env.REDIS_AUTH_DB || '1'),
      keyPrefix: 'dl-engine:auth:'
    },

    jwt: {
      secret: process.env.JWT_SECRET || 'dl-engine-auth-secret-key-2024',
      expiresIn: process.env.JWT_EXPIRES_IN || '24h',
      refreshExpiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '7d',
      algorithm: 'HS256' as const
    },

    sms: {
      // 阿里云短信服务配置
      aliyun: {
        accessKeyId: process.env.ALIYUN_ACCESS_KEY_ID || '',
        accessKeySecret: process.env.ALIYUN_ACCESS_KEY_SECRET || '',
        signName: process.env.ALIYUN_SMS_SIGN_NAME || 'DL学习引擎',
        templateCode: process.env.ALIYUN_SMS_TEMPLATE_CODE || 'SMS_123456789',
        endpoint: 'https://dysmsapi.aliyuncs.com'
      },
      // 腾讯云短信服务配置
      tencent: {
        secretId: process.env.TENCENT_SECRET_ID || '',
        secretKey: process.env.TENCENT_SECRET_KEY || '',
        sdkAppId: process.env.TENCENT_SMS_SDK_APP_ID || '',
        signName: process.env.TENCENT_SMS_SIGN_NAME || 'DL学习引擎',
        templateId: process.env.TENCENT_SMS_TEMPLATE_ID || '123456'
      },
      // 验证码配置
      verification: {
        length: 6,
        expiresIn: 300, // 5分钟
        maxAttempts: 3,
        cooldown: 60,   // 1分钟冷却
        dailyLimit: 10  // 每日限制
      }
    },

    oauth: {
      // 微信登录配置
      wechat: {
        appId: process.env.WECHAT_APP_ID || '',
        appSecret: process.env.WECHAT_APP_SECRET || '',
        redirectUri: process.env.WECHAT_REDIRECT_URI || 'http://localhost:3000/auth/wechat/callback'
      },
      // QQ登录配置
      qq: {
        appId: process.env.QQ_APP_ID || '',
        appKey: process.env.QQ_APP_KEY || '',
        redirectUri: process.env.QQ_REDIRECT_URI || 'http://localhost:3000/auth/qq/callback'
      },
      // 支付宝登录配置
      alipay: {
        appId: process.env.ALIPAY_APP_ID || '',
        privateKey: process.env.ALIPAY_PRIVATE_KEY || '',
        publicKey: process.env.ALIPAY_PUBLIC_KEY || '',
        redirectUri: process.env.ALIPAY_REDIRECT_URI || 'http://localhost:3000/auth/alipay/callback'
      }
    },

    security: {
      // 密码策略
      password: {
        minLength: 8,
        requireUppercase: true,
        requireLowercase: true,
        requireNumbers: true,
        requireSpecialChars: false
      },
      // 账户锁定策略
      lockout: {
        maxAttempts: 5,
        lockoutDuration: 1800, // 30分钟
        resetTime: 3600        // 1小时后重置计数
      },
      // 会话配置
      session: {
        maxConcurrentSessions: 3,
        sessionTimeout: 86400,  // 24小时
        refreshThreshold: 3600  // 1小时内刷新
      }
    },

    monitoring: {
      enabled: process.env.MONITORING_ENABLED === 'true',
      metricsPath: '/metrics',
      healthPath: '/health'
    }
  }

  getPort(): number {
    return this.config.server.port
  }

  getHost(): string {
    return this.config.server.host
  }

  getEnvironment(): string {
    return this.config.server.environment
  }

  getAllowedOrigins(): string[] {
    return this.config.cors.allowedOrigins
  }

  getDatabaseConfig() {
    return this.config.database
  }

  getRedisConfig() {
    return this.config.redis
  }

  getJwtSecret(): string {
    return this.config.jwt.secret
  }

  getJwtExpiresIn(): string {
    return this.config.jwt.expiresIn
  }

  getJwtRefreshExpiresIn(): string {
    return this.config.jwt.refreshExpiresIn
  }

  getJwtConfig() {
    return this.config.jwt
  }

  getSmsConfig() {
    return this.config.sms
  }

  getOAuthConfig() {
    return this.config.oauth
  }

  getSecurityConfig() {
    return this.config.security
  }

  getMonitoringConfig() {
    return this.config.monitoring
  }

  isDevelopment(): boolean {
    return this.config.server.environment === 'development'
  }

  isProduction(): boolean {
    return this.config.server.environment === 'production'
  }

  // 获取短信服务提供商配置
  getSmsProvider(): 'aliyun' | 'tencent' {
    return process.env.SMS_PROVIDER as 'aliyun' | 'tencent' || 'aliyun'
  }

  // 获取当前使用的短信服务配置
  getCurrentSmsConfig() {
    const provider = this.getSmsProvider()
    return {
      provider,
      config: this.config.sms[provider],
      verification: this.config.sms.verification
    }
  }
}
