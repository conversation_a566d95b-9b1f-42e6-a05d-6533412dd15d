/*
CPAL-1.0 License

The contents of this file are subject to the Common Public Attribution License
Version 1.0. (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at
https://github.com/ir-engine/ir-engine/blob/dev/LICENSE.
The License is based on the Mozilla Public License Version 1.1, but Sections 14
and 15 have been added to cover use of software over a computer network and 
provide for limited attribution for the Original Developer. In addition, 
Exhibit A has been modified to be consistent with Exhibit B.

Software distributed under the License is distributed on an "AS IS" basis,
WITHOUT WARRANTY OF ANY KIND, either express or implied. See the License for the
specific language governing rights and limitations under the License.

The Original Code is Infinite Reality Engine.

The Original Developer is the Initial Developer. The Initial Developer of the
Original Code is the Infinite Reality Engine team.

All portions of the code written by the Infinite Reality Engine team are Copyright © 2021-2025
Infinite Reality Engine. All Rights Reserved.
*/

import { Entity } from '@ir-engine/ecs/src/Entity'
import { createXRUI } from '@ir-engine/engine/src/xrui/createXRUI'
import { dispatchAction } from '@ir-engine/hyperflux'
import { SVGIconType } from '@ir-engine/ui/src/icons/types'
import { IconType } from 'react-icons'
import { RegisteredWidgets, WidgetAppActions } from './WidgetAppService'

/**
 * The widget interface.
 *
 * @param {XRUI} ui stores a reference to the XRUI container, entity and state
 * @param {string} label is the display label of the widget
 * @param {any} icon is the icon to display on the widget menu
 * @param {Function} system is a system that will run while a widget is enabled and visible
 */

export const WidgetName = {
  PROFILE: 'ProfileMenu',
  SETTINGS: 'SettingsMenu',
  SOCIALS: 'SocialsMenu',
  LOCATION: 'LocationMenu',
  ADMIN_CONTROLS: 'AdminControlsMenu',
  MEDIA_SESSION: 'MediaSessionMenu',
  CHAT: 'Chat',
  EMOTE: 'Emote',
  READY_PLAYER: 'ReadyPlayer',
  SELECT_AVATAR: 'SelectAvatar',
  SHARE_LOCATION: 'ShareLocation',
  UPLOAD_AVATAR: 'UploadAvatar'
}

export type Widget = {
  ui: ReturnType<typeof createXRUI>
  label: string
  icon?: SVGIconType | IconType
  onOpen?: () => void
  onClose?: () => void
  system?: () => void
  cleanup?: () => Promise<void>
}

export const registerWidget = (xruiEntity: Entity, widget: Widget) => {
  const id = `${widget.label}-${xruiEntity}`
  dispatchAction(WidgetAppActions.registerWidget({ id }))
  RegisteredWidgets.set(id, widget)
  return id
}

export const Widgets = {
  registerWidget
}
