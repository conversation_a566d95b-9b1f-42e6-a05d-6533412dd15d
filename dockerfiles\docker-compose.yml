services:
  taskserver:
    image: ${LABEL}-taskserver
    build:
      context: ../
      dockerfile: ./dockerfiles/taskserver/Dockerfile-taskserver
  api:
    image: ${LABEL}-api
    build:
      context: ../
      dockerfile: ./dockerfiles/api/Dockerfile-api
  client:
    image: ${LABEL}-client
    build:
      context: ../
      dockerfile: ./dockerfiles/client/Dockerfile-client
      args:
        MYSQL_HOST: $MYSQL_HOST
        MYSQL_PORT: $MYSQL_PORT
        MYSQL_PASSWORD: $MYSQL_PASSWORD
        MYSQL_DATABASE: $MYSQL_DATABASE
        MYSQL_USER: $MYSQL_USER
  instanceserver:
    image: ${LABEL}-instanceserver
    build:
      context: ..
      dockerfile: ./dockerfiles/instanceserver/Dockerfile-instanceserver
  testbot:
    image: ${LABEL}-testbot
    build:
      context: ../
      dockerfile: ./dockerfiles/testbot/Dockerfile-testbot
