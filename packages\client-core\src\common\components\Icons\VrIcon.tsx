/*
CPAL-1.0 License

The contents of this file are subject to the Common Public Attribution License
Version 1.0. (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at
https://github.com/ir-engine/ir-engine/blob/dev/LICENSE.
The License is based on the Mozilla Public License Version 1.1, but Sections 14
and 15 have been added to cover use of software over a computer network and 
provide for limited attribution for the Original Developer. In addition, 
Exhibit A has been modified to be consistent with Exhibit B.

Software distributed under the License is distributed on an "AS IS" basis,
WITHOUT WARRANTY OF ANY KIND, either express or implied. See the License for the
specific language governing rights and limitations under the License.

The Original Code is Infinite Reality Engine.

The Original Developer is the Initial Developer. The Initial Developer of the
Original Code is the Infinite Reality Engine team.

All portions of the code written by the Infinite Reality Engine team are Copyright © 2021-2025
Infinite Reality Engine. All Rights Reserved.
*/

import React from 'react'

export const VrIcon = (props) => {
  return (
    <svg {...props} width="26" height="26" viewBox="0 0 26 17" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
      <path d="M23.5837 0H1.82935C0.791068 0 0 0.90883 0 1.97804V14.0601C0 15.1828 0.84051 16.0382 1.82935 16.0382H8.45454C8.9984 16.0382 9.49282 15.7174 9.69059 15.1828L11.1738 11.1733C11.7671 9.78329 13.5965 9.83675 14.1403 11.2267L15.4258 15.1294C15.6236 15.664 16.118 16.0382 16.6619 16.0382H23.5343C24.5726 16.0382 25.3636 15.1294 25.3636 14.0601V2.0315C25.4625 0.908831 24.622 0 23.5837 0ZM7.26794 11.494C5.68581 11.494 4.44976 10.1041 4.44976 8.44678C4.44976 6.7895 5.73525 5.39952 7.26794 5.39952C8.80064 5.39952 10.0861 6.7895 10.0861 8.44678C10.0861 10.1041 8.85008 11.494 7.26794 11.494ZM18.1451 11.494C16.563 11.494 15.327 10.1041 15.327 8.44678C15.327 6.7895 16.6124 5.39952 18.1451 5.39952C19.6778 5.39952 20.9633 6.7895 20.9633 8.44678C20.9633 10.1041 19.7273 11.494 18.1451 11.494Z" />
    </svg>
  )
}
