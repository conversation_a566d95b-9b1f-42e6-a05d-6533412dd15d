# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.



**Note:** Version bump only for package @etherealengine/engine







**Note:** Version bump only for package @etherealengine/engine







**Note:** Version bump only for package @etherealengine/engine







**Note:** Version bump only for package @etherealengine/engine







**Note:** Version bump only for package @etherealengine/engine







**Note:** Version bump only for package @etherealengine/engine







**Note:** Version bump only for package @etherealengine/engine







**Note:** Version bump only for package @xrengine/engine







**Note:** Version bump only for package @xrengine/engine







**Note:** Version bump only for package @xrengine/engine







**Note:** Version bump only for package @xrengine/engine







**Note:** Version bump only for package @xrengine/engine







**Note:** Version bump only for package @xrengine/engine







**Note:** Version bump only for package @xrengine/engine







**Note:** Version bump only for package @xrengine/engine







**Note:** Version bump only for package @xrengine/engine







**Note:** Version bump only for package @xrengine/engine







**Note:** Version bump only for package @xrengine/engine







**Note:** Version bump only for package @xrengine/engine







**Note:** Version bump only for package @xrengine/engine







**Note:** Version bump only for package @xrengine/engine







**Note:** Version bump only for package @xrengine/engine







**Note:** Version bump only for package @xrengine/engine







**Note:** Version bump only for package @xrengine/engine







**Note:** Version bump only for package @xrengine/engine







**Note:** Version bump only for package @xrengine/engine





## [0.5.2](https://github.com/XRFoundation/XREngine/compare/v0.5.1...v0.5.2) (2022-04-07)


### Reverts

* Revert "Fix some interactive system errors while scene is loading" ([126fef5](https://github.com/XRFoundation/XREngine/commit/126fef5e884e4c4cf7087ad0c49d7ba0b90ddca0))







**Note:** Version bump only for package @xrengine/engine







**Note:** Version bump only for package @xrengine/engine







**Note:** Version bump only for package @xrengine/engine







**Note:** Version bump only for package @xrengine/engine







**Note:** Version bump only for package @xrengine/engine







**Note:** Version bump only for package @xrengine/engine







**Note:** Version bump only for package @xrengine/engine







**Note:** Version bump only for package @xrengine/engine







**Note:** Version bump only for package @xrengine/engine







**Note:** Version bump only for package @xrengine/engine







**Note:** Version bump only for package @xrengine/engine







**Note:** Version bump only for package @xrengine/engine







**Note:** Version bump only for package @xrengine/engine







**Note:** Version bump only for package @xrengine/engine







**Note:** Version bump only for package @xrengine/engine







**Note:** Version bump only for package @xrengine/engine







**Note:** Version bump only for package @xrengine/engine







**Note:** Version bump only for package @xrengine/engine







**Note:** Version bump only for package @xrengine/engine







**Note:** Version bump only for package @xrengine/engine







**Note:** Version bump only for package @xrengine/engine







**Note:** Version bump only for package @xrengine/engine







**Note:** Version bump only for package @xrengine/engine







**Note:** Version bump only for package @xrengine/engine







**Note:** Version bump only for package @xrengine/engine







**Note:** Version bump only for package @xrengine/engine







**Note:** Version bump only for package @xrengine/engine







**Note:** Version bump only for package @xrengine/engine







**Note:** Version bump only for package @xrengine/engine







**Note:** Version bump only for package @xrengine/engine





## 0.2.36 (2021-06-25)

**Note:** Version bump only for package @xrengine/engine





## 0.2.35 (2021-06-25)

**Note:** Version bump only for package @xrengine/engine





## 0.2.34 (2021-06-25)

**Note:** Version bump only for package @xrengine/engine





## 0.2.33 (2021-06-25)

**Note:** Version bump only for package @xrengine/engine





## 0.2.32 (2021-06-25)

**Note:** Version bump only for package @xrengine/engine





## 0.2.31 (2021-06-25)

**Note:** Version bump only for package @xrengine/engine





## 0.2.30 (2021-06-25)

**Note:** Version bump only for package @xrengine/engine





## [0.2.29](https://github.com/XRFoundation/XREngine/compare/v0.2.28...v0.2.29) (2021-06-24)

**Note:** Version bump only for package @xrengine/engine





## [0.2.28](https://github.com/XRFoundation/XREngine/compare/v0.2.27...v0.2.28) (2021-06-22)

**Note:** Version bump only for package @xrengine/engine





## [0.2.27](https://github.com/XRFoundation/XREngine/compare/v0.2.26...v0.2.27) (2021-05-13)

**Note:** Version bump only for package @xrengine/engine





## [0.2.26](https://github.com/XRFoundation/XREngine/compare/v0.2.24...v0.2.26) (2021-05-12)

**Note:** Version bump only for package @xrengine/engine





## [0.2.25](https://github.com/XRFoundation/XREngine/compare/v0.2.24...v0.2.25) (2021-05-12)

**Note:** Version bump only for package @xrengine/engine





## [0.2.24](https://github.com/XRFoundation/XREngine/compare/v0.2.23...v0.2.24) (2021-05-12)

**Note:** Version bump only for package @xrengine/engine





## [0.2.23](https://github.com/XRFoundation/XREngine/compare/v0.2.22...v0.2.23) (2021-05-12)

**Note:** Version bump only for package @xrengine/engine





## [0.2.22](https://github.com/XRFoundation/XREngine/compare/v0.2.21...v0.2.22) (2021-05-05)

**Note:** Version bump only for package @xrengine/engine





## [0.2.21](https://github.com/xrengine/xrengine/compare/v0.2.20...v0.2.21) (2021-05-05)

**Note:** Version bump only for package @xrengine/engine





## [0.2.20](https://github.com/xrengine/xrengine/compare/v0.2.18...v0.2.20) (2021-05-04)


### Bug Fixes

* clearEventQueues assignes new array ref, and breaks some event listeners that are using that references ([6195fa3](https://github.com/xrengine/xrengine/commit/6195fa3b9d3e8d93db362730f3dcaf7703f9c09b))
* removed "input/touchEvents" in "test" call ([cb2a2f2](https://github.com/xrengine/xrengine/commit/cb2a2f28f67b12ab0dca701a78351c832ee7fcbf))





## 0.2.18 (2021-04-22)


### Bug Fixes

* clearEventQueues assignes new array ref, and breaks some event listeners that are using that references ([f19058a](https://github.com/XRFoundation/XREngine/commit/f19058aa08e42d0836b1be1e5584d9ba72c053d3))
* removed "input/touchEvents" in "test" call ([1e80b16](https://github.com/XRFoundation/XREngine/commit/1e80b16602beeacf6a2ed2d5768a05a971e07d6e))


### Reverts

* Revert "-temporary set avatar (change avatar is BROKEN); - animation load - NOT READY;" ([f62a4ad](https://github.com/XRFoundation/XREngine/commit/f62a4ad131bbf1b27e96858fc4829cea6ec32044))





## [0.2.11](https://github.com/XRFoundation/XREngine/compare/v0.2.10...v0.2.11) (2021-04-08)

**Note:** Version bump only for package @xrengine/engine





## [0.2.10](https://github.com/XRFoundation/XREngine/compare/v0.2.9...v0.2.10) (2021-03-31)

**Note:** Version bump only for package @xrengine/engine





## [0.2.9](https://github.com/XRFoundation/XREngine/compare/v0.2.8...v0.2.9) (2021-03-31)

**Note:** Version bump only for package @xrengine/engine





## [0.2.7](https://github.com/XRFoundation/XREngine/compare/v0.2.6...v0.2.7) (2021-03-31)

**Note:** Version bump only for package @xrengine/engine





## [0.2.6](https://github.com/XRFoundation/XREngine/compare/v0.2.5...v0.2.6) (2021-03-31)

**Note:** Version bump only for package @xrengine/engine





## 0.2.4 (2021-03-31)


### Bug Fixes

* clearEventQueues assignes new array ref, and breaks some event listeners that are using that references ([f19058a](https://github.com/XRFoundation/XREngine/commit/f19058aa08e42d0836b1be1e5584d9ba72c053d3))
* removed "input/touchEvents" in "test" call ([1e80b16](https://github.com/XRFoundation/XREngine/commit/1e80b16602beeacf6a2ed2d5768a05a971e07d6e))


### Reverts

* Revert "-temporary set avatar (change avatar is BROKEN); - animation load - NOT READY;" ([f62a4ad](https://github.com/XRFoundation/XREngine/commit/f62a4ad131bbf1b27e96858fc4829cea6ec32044))





## 0.2.3 (2021-03-31)


### Bug Fixes

* clearEventQueues assignes new array ref, and breaks some event listeners that are using that references ([f19058a](https://github.com/XRFoundation/XREngine/commit/f19058aa08e42d0836b1be1e5584d9ba72c053d3))
* removed "input/touchEvents" in "test" call ([1e80b16](https://github.com/XRFoundation/XREngine/commit/1e80b16602beeacf6a2ed2d5768a05a971e07d6e))


### Reverts

* Revert "-temporary set avatar (change avatar is BROKEN); - animation load - NOT READY;" ([f62a4ad](https://github.com/XRFoundation/XREngine/commit/f62a4ad131bbf1b27e96858fc4829cea6ec32044))
