/**
 * DL-Engine API Gateway 路由服务
 * 
 * 核心功能：
 * - 动态路由管理
 * - 服务发现和注册
 * - 负载均衡策略
 * - 健康检查
 * - 性能监控
 */

import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common'
import { ConfigService } from '../config/config.service'
import { LoggerService } from '../common/logger.service'
import { CreateRouteDto, UpdateRouteDto, RouteQueryDto } from './dto/route.dto'

export interface Route {
  id: string
  path: string
  method: string
  service: string
  target: string
  middleware: string[]
  enabled: boolean
  createdAt: Date
  updatedAt: Date
  metadata?: Record<string, any>
}

export interface ServiceInstance {
  id: string
  name: string
  url: string
  health: 'healthy' | 'unhealthy' | 'unknown'
  lastCheck: Date
  metadata?: Record<string, any>
}

@Injectable()
export class RoutingService {
  private routes: Map<string, Route> = new Map()
  private services: Map<string, ServiceInstance[]> = new Map()
  private routeMetrics: Map<string, any> = new Map()

  constructor(
    private readonly configService: ConfigService,
    private readonly logger: LoggerService
  ) {
    this.initializeRoutes()
    this.startHealthChecks()
  }

  /**
   * 初始化路由配置
   */
  private initializeRoutes() {
    const routeConfigs = this.configService.getRoutes()
    const services = this.configService.getServices()

    routeConfigs.forEach((config, index) => {
      const route: Route = {
        id: `route-${index + 1}`,
        path: config.path,
        method: config.method,
        service: config.service,
        target: services[config.service]?.url || '',
        middleware: config.middleware || [],
        enabled: true,
        createdAt: new Date(),
        updatedAt: new Date(),
        metadata: {
          auth: config.auth,
          rateLimit: config.rateLimit
        }
      }
      this.routes.set(route.id, route)
    })

    // 初始化服务实例
    Object.entries(services).forEach(([name, config]) => {
      if (config.enabled) {
        const instance: ServiceInstance = {
          id: `${name}-1`,
          name,
          url: config.url,
          health: 'unknown',
          lastCheck: new Date(),
          metadata: {
            timeout: config.timeout,
            retries: config.retries,
            weight: config.weight
          }
        }
        this.services.set(name, [instance])
      }
    })

    this.logger.log(`Initialized ${this.routes.size} routes and ${this.services.size} services`)
  }

  /**
   * 启动健康检查
   */
  private startHealthChecks() {
    setInterval(() => {
      this.performHealthChecks()
    }, 30000) // 每30秒检查一次
  }

  /**
   * 执行健康检查
   */
  private async performHealthChecks() {
    for (const [serviceName, instances] of this.services.entries()) {
      for (const instance of instances) {
        try {
          const serviceConfig = this.configService.getService(serviceName)
          if (!serviceConfig) continue

          const response = await fetch(`${instance.url}${serviceConfig.healthCheck}`, {
            method: 'GET',
            timeout: 5000
          })

          instance.health = response.ok ? 'healthy' : 'unhealthy'
          instance.lastCheck = new Date()
        } catch (error) {
          instance.health = 'unhealthy'
          instance.lastCheck = new Date()
          this.logger.warn(`Health check failed for ${serviceName}: ${error.message}`)
        }
      }
    }
  }

  /**
   * 获取所有路由
   */
  async getAllRoutes(query: RouteQueryDto) {
    let routes = Array.from(this.routes.values())

    // 过滤
    if (query.service) {
      routes = routes.filter(route => route.service === query.service)
    }
    if (query.enabled !== undefined) {
      routes = routes.filter(route => route.enabled === query.enabled)
    }
    if (query.path) {
      routes = routes.filter(route => route.path.includes(query.path))
    }

    // 排序
    if (query.sortBy) {
      routes.sort((a, b) => {
        const aValue = a[query.sortBy as keyof Route]
        const bValue = b[query.sortBy as keyof Route]
        return query.sortOrder === 'desc' 
          ? String(bValue).localeCompare(String(aValue))
          : String(aValue).localeCompare(String(bValue))
      })
    }

    // 分页
    const page = query.page || 1
    const limit = query.limit || 10
    const startIndex = (page - 1) * limit
    const endIndex = startIndex + limit

    return {
      data: routes.slice(startIndex, endIndex),
      total: routes.length,
      page,
      limit,
      totalPages: Math.ceil(routes.length / limit)
    }
  }

  /**
   * 获取指定路由
   */
  async getRoute(id: string): Promise<Route> {
    const route = this.routes.get(id)
    if (!route) {
      throw new NotFoundException(`Route with id ${id} not found`)
    }
    return route
  }

  /**
   * 创建路由
   */
  async createRoute(createRouteDto: CreateRouteDto): Promise<Route> {
    // 验证服务是否存在
    if (!this.services.has(createRouteDto.service)) {
      throw new BadRequestException(`Service ${createRouteDto.service} not found`)
    }

    const id = `route-${Date.now()}`
    const serviceInstances = this.services.get(createRouteDto.service)
    const target = serviceInstances?.[0]?.url || ''

    const route: Route = {
      id,
      path: createRouteDto.path,
      method: createRouteDto.method,
      service: createRouteDto.service,
      target,
      middleware: createRouteDto.middleware || [],
      enabled: createRouteDto.enabled ?? true,
      createdAt: new Date(),
      updatedAt: new Date(),
      metadata: createRouteDto.metadata
    }

    this.routes.set(id, route)
    this.logger.log(`Created route ${id}: ${route.method} ${route.path} -> ${route.service}`)

    return route
  }

  /**
   * 更新路由
   */
  async updateRoute(id: string, updateRouteDto: UpdateRouteDto): Promise<Route> {
    const route = await this.getRoute(id)

    // 更新字段
    if (updateRouteDto.path !== undefined) route.path = updateRouteDto.path
    if (updateRouteDto.method !== undefined) route.method = updateRouteDto.method
    if (updateRouteDto.service !== undefined) {
      if (!this.services.has(updateRouteDto.service)) {
        throw new BadRequestException(`Service ${updateRouteDto.service} not found`)
      }
      route.service = updateRouteDto.service
      const serviceInstances = this.services.get(updateRouteDto.service)
      route.target = serviceInstances?.[0]?.url || ''
    }
    if (updateRouteDto.middleware !== undefined) route.middleware = updateRouteDto.middleware
    if (updateRouteDto.enabled !== undefined) route.enabled = updateRouteDto.enabled
    if (updateRouteDto.metadata !== undefined) route.metadata = updateRouteDto.metadata

    route.updatedAt = new Date()
    this.routes.set(id, route)

    this.logger.log(`Updated route ${id}`)
    return route
  }

  /**
   * 删除路由
   */
  async deleteRoute(id: string): Promise<void> {
    const route = await this.getRoute(id)
    this.routes.delete(id)
    this.routeMetrics.delete(id)
    this.logger.log(`Deleted route ${id}`)
  }

  /**
   * 检查路由健康状态
   */
  async checkRouteHealth(id: string) {
    const route = await this.getRoute(id)
    const serviceInstances = this.services.get(route.service) || []
    
    const healthyInstances = serviceInstances.filter(instance => instance.health === 'healthy')
    const totalInstances = serviceInstances.length

    return {
      routeId: id,
      service: route.service,
      enabled: route.enabled,
      healthyInstances: healthyInstances.length,
      totalInstances,
      healthRatio: totalInstances > 0 ? healthyInstances.length / totalInstances : 0,
      instances: serviceInstances.map(instance => ({
        id: instance.id,
        url: instance.url,
        health: instance.health,
        lastCheck: instance.lastCheck
      }))
    }
  }

  /**
   * 获取路由性能指标
   */
  async getRouteMetrics(id: string, timeRange?: string) {
    const route = await this.getRoute(id)
    const metrics = this.routeMetrics.get(id) || {
      requestCount: 0,
      errorCount: 0,
      averageResponseTime: 0,
      lastRequest: null
    }

    return {
      routeId: id,
      path: route.path,
      service: route.service,
      timeRange: timeRange || '1h',
      metrics
    }
  }

  /**
   * 启用路由
   */
  async enableRoute(id: string): Promise<Route> {
    const route = await this.getRoute(id)
    route.enabled = true
    route.updatedAt = new Date()
    this.routes.set(id, route)
    this.logger.log(`Enabled route ${id}`)
    return route
  }

  /**
   * 禁用路由
   */
  async disableRoute(id: string): Promise<Route> {
    const route = await this.getRoute(id)
    route.enabled = false
    route.updatedAt = new Date()
    this.routes.set(id, route)
    this.logger.log(`Disabled route ${id}`)
    return route
  }

  /**
   * 服务发现
   */
  async discoverServices() {
    const services = Array.from(this.services.entries()).map(([name, instances]) => ({
      name,
      instances: instances.map(instance => ({
        id: instance.id,
        url: instance.url,
        health: instance.health,
        lastCheck: instance.lastCheck
      })),
      healthyCount: instances.filter(i => i.health === 'healthy').length,
      totalCount: instances.length
    }))

    return {
      services,
      totalServices: services.length,
      healthyServices: services.filter(s => s.healthyCount > 0).length
    }
  }

  /**
   * 重新加载路由配置
   */
  async reloadRoutes() {
    this.routes.clear()
    this.services.clear()
    this.routeMetrics.clear()
    
    this.initializeRoutes()
    
    this.logger.log('Routes configuration reloaded')
    return {
      message: 'Routes configuration reloaded successfully',
      routeCount: this.routes.size,
      serviceCount: this.services.size
    }
  }

  /**
   * 获取路由匹配
   */
  findMatchingRoute(path: string, method: string): Route | null {
    for (const route of this.routes.values()) {
      if (!route.enabled) continue
      
      // 简单的路径匹配逻辑
      const routePath = route.path.replace(/\*/g, '.*')
      const pathRegex = new RegExp(`^${routePath}$`)
      
      if (pathRegex.test(path) && (route.method === 'ALL' || route.method === method)) {
        return route
      }
    }
    return null
  }

  /**
   * 记录路由指标
   */
  recordRouteMetrics(routeId: string, responseTime: number, success: boolean) {
    const metrics = this.routeMetrics.get(routeId) || {
      requestCount: 0,
      errorCount: 0,
      averageResponseTime: 0,
      lastRequest: null
    }

    metrics.requestCount++
    if (!success) metrics.errorCount++
    metrics.averageResponseTime = (metrics.averageResponseTime + responseTime) / 2
    metrics.lastRequest = new Date()

    this.routeMetrics.set(routeId, metrics)
  }
}
