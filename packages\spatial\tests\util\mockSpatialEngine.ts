/*
CPAL-1.0 License

The contents of this file are subject to the Common Public Attribution License
Version 1.0. (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at
https://github.com/ir-engine/ir-engine/blob/dev/LICENSE.
The License is based on the Mozilla Public License Version 1.1, but Sections 14
and 15 have been added to cover use of software over a computer network and
provide for limited attribution for the Original Developer. In addition,
Exhibit A has been modified to be consistent with Exhibit B.

Software distributed under the License is distributed on an "AS IS" basis,
WITHOUT WARRANTY OF ANY KIND, either express or implied. See the License for the
specific language governing rights and limitations under the License.

The Original Code is Infinite Reality Engine.

The Original Developer is the Initial Developer. The Initial Developer of the
Original Code is the Infinite Reality Engine team.

All portions of the code written by the Infinite Reality Engine team are Copyright © 2021-2025
Infinite Reality Engine. All Rights Reserved.
*/

import { initializeSpatialEngine, initializeSpatialViewer } from '../../src/initializeEngine'
import { mockEngineRenderer } from './MockEngineRenderer'

import { ECSState, Timer, setComponent } from '@ir-engine/ecs'
import { getMutableState, getState } from '@ir-engine/hyperflux'
import { Matrix4 } from 'three'
import { ReferenceSpaceState } from '../../src/ReferenceSpaceState'
import { RendererComponent } from '../../src/renderer/components/RendererComponent'
import { ReferenceSpace, XRState } from '../../src/xr/XRState'
import { MockXRFrame, MockXRReferenceSpace, MockXRSpace } from './MockXR'

export const mockSpatialEngine = () => {
  initializeSpatialEngine()
  initializeSpatialViewer()

  const timer = Timer((time, xrFrame) => {
    getMutableState(XRState).xrFrame.set(xrFrame)
    // executeSystems(time)
    getMutableState(XRState).xrFrame.set(null)
  })
  getMutableState(ECSState).timer.set(timer)

  const { originEntity, localFloorEntity, viewerEntity } = getState(ReferenceSpaceState)
  mockEngineRenderer(viewerEntity)
  setComponent(viewerEntity, RendererComponent, { scenes: [originEntity, localFloorEntity, viewerEntity] })

  const xrFrame = new MockXRFrame()
  // @ts-expect-error Allow coercing the MockXRFrame type into the xrFrame property of XRState
  getMutableState(XRState).xrFrame.set(xrFrame)

  ReferenceSpace.origin = new MockXRReferenceSpace(new Matrix4()) as any as XRReferenceSpace
  ReferenceSpace.localFloor = new MockXRReferenceSpace(new Matrix4()) as any as XRReferenceSpace
  ReferenceSpace.viewer = new MockXRSpace(new Matrix4()) as any as XRReferenceSpace
}
