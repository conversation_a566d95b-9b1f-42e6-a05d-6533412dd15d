import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToOne,
  OneToMany,
  Index
} from 'typeorm'
import { UserProfile } from './user-profile.entity'
import { UserPreference } from './user-preference.entity'
import { UserPrivacy } from './user-privacy.entity'
import { UserEducation } from './user-education.entity'

/**
 * 用户状态枚举
 */
export enum UserStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  SUSPENDED = 'suspended',
  PENDING = 'pending'
}

/**
 * 用户类型枚举
 */
export enum UserType {
  STUDENT = 'student',
  TEACHER = 'teacher',
  ADMIN = 'admin',
  GUEST = 'guest'
}

/**
 * 用户实体
 * 
 * 核心用户信息，包括认证相关字段和基本信息
 */
@Entity('users')
@Index(['phone'], { unique: true, where: 'phone IS NOT NULL' })
@Index(['email'], { unique: true, where: 'email IS NOT NULL' })
@Index(['username'], { unique: true, where: 'username IS NOT NULL' })
export class User {
  @PrimaryGeneratedColumn('uuid')
  id: string

  /**
   * 用户名（可选，用于显示）
   */
  @Column({ type: 'varchar', length: 50, nullable: true, unique: true })
  username?: string

  /**
   * 手机号码（主要登录方式）
   */
  @Column({ type: 'varchar', length: 20, nullable: true, unique: true })
  phone?: string

  /**
   * 国家代码（默认+86）
   */
  @Column({ type: 'varchar', length: 5, default: '+86' })
  countryCode: string

  /**
   * 邮箱地址（备用登录方式）
   */
  @Column({ type: 'varchar', length: 255, nullable: true, unique: true })
  email?: string

  /**
   * 显示名称
   */
  @Column({ type: 'varchar', length: 100 })
  displayName: string

  /**
   * 头像URL
   */
  @Column({ type: 'text', nullable: true })
  avatarUrl?: string

  /**
   * 用户状态
   */
  @Column({
    type: 'enum',
    enum: UserStatus,
    default: UserStatus.PENDING
  })
  status: UserStatus

  /**
   * 用户类型
   */
  @Column({
    type: 'enum',
    enum: UserType,
    default: UserType.STUDENT
  })
  userType: UserType

  /**
   * 是否已验证手机号
   */
  @Column({ type: 'boolean', default: false })
  phoneVerified: boolean

  /**
   * 是否已验证邮箱
   */
  @Column({ type: 'boolean', default: false })
  emailVerified: boolean

  /**
   * 最后登录时间
   */
  @Column({ type: 'timestamp', nullable: true })
  lastLoginAt?: Date

  /**
   * 最后活跃时间
   */
  @Column({ type: 'timestamp', nullable: true })
  lastActiveAt?: Date

  /**
   * 登录次数
   */
  @Column({ type: 'int', default: 0 })
  loginCount: number

  /**
   * 是否接受服务条款
   */
  @Column({ type: 'boolean', default: false })
  termsAccepted: boolean

  /**
   * 接受服务条款的时间
   */
  @Column({ type: 'timestamp', nullable: true })
  termsAcceptedAt?: Date

  /**
   * 创建时间
   */
  @CreateDateColumn()
  createdAt: Date

  /**
   * 更新时间
   */
  @UpdateDateColumn()
  updatedAt: Date

  // 关联关系
  @OneToOne(() => UserProfile, profile => profile.user, { cascade: true })
  profile: UserProfile

  @OneToOne(() => UserPreference, preference => preference.user, { cascade: true })
  preference: UserPreference

  @OneToOne(() => UserPrivacy, privacy => privacy.user, { cascade: true })
  privacy: UserPrivacy

  @OneToMany(() => UserEducation, education => education.user, { cascade: true })
  educations: UserEducation[]
}
