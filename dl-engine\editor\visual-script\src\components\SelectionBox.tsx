/**
 * 选择框组件
 * 
 * 用于框选多个节点
 */

import React from 'react'
import classNames from 'classnames'

/**
 * 选择框属性
 */
export interface SelectionBoxProps {
  /** 起始X坐标 */
  x: number
  /** 起始Y坐标 */
  y: number
  /** 宽度 */
  width: number
  /** 高度 */
  height: number
  /** 是否可见 */
  visible?: boolean
  /** 自定义类名 */
  className?: string
}

/**
 * 选择框组件
 */
const SelectionBox: React.FC<SelectionBoxProps> = ({
  x,
  y,
  width,
  height,
  visible = true,
  className
}) => {
  if (!visible || (width === 0 && height === 0)) {
    return null
  }

  const selectionBoxClassName = classNames(
    'selection-box',
    className
  )

  return (
    <div
      className={selectionBoxClassName}
      style={{
        position: 'absolute',
        left: x,
        top: y,
        width: Math.abs(width),
        height: Math.abs(height),
        border: '1px dashed #1890ff',
        backgroundColor: 'rgba(24, 144, 255, 0.1)',
        pointerEvents: 'none',
        zIndex: 1000
      }}
    />
  )
}

export default SelectionBox
