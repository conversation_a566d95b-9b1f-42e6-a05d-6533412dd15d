{"name": "@ir-engine/server-core", "description": "Shared components for Infinite Reality Engine server", "version": "1.0.3", "repository": {"type": "git", "url": "git://github.com/ir-engine/ir-engine.git"}, "homepage": "", "publishConfig": {"access": "public"}, "main": "lib/server-core.umd.js", "module": "lib/server-core.es.js", "types": "lib/index.d.ts", "keywords": ["three", "three.js", "ecs", "webgl", "mmo", "game engine", "webrtc", "productivity", "xr", "vr"], "author": {"name": "Infinite Reality Engine Collective", "email": "<EMAIL>"}, "contributors": [], "bugs": {}, "engines": {"node": ">= 22.11.0"}, "scripts": {"check-errors": "tsc --noemit && npx cycle-import-check src || true", "test": "cross-env TEST=true vitest run --config=../../vitest.server.config.ts", "validate": "npm run build && npm run test", "migrate": "knex migrate:latest", "migrate:rollback": "knex migrate:rollback --all", "migrate:unlock": "knex migrate:unlock", "migrate:make": "knex --migrations-directory ./ migrate:make"}, "dependencies": {"@aws-sdk/client-cloudfront": "3.726.1", "@aws-sdk/client-ecr-public": "3.726.1", "@aws-sdk/client-s3": "3.726.1", "@aws-sdk/client-sns": "3.726.1", "@aws-sdk/credential-providers": "3.726.1", "@aws-sdk/lib-storage": "3.726.1", "@aws-sdk/s3-presigned-post": "3.726.1", "@feathersjs/adapter-commons": "5.0.5", "@feathersjs/authentication": "5.0.5", "@feathersjs/authentication-oauth": "5.0.5", "@feathersjs/feathers": "5.0.5", "@feathersjs/knex": "5.0.5", "@feathersjs/koa": "5.0.5", "@feathersjs/transport-commons": "5.0.5", "@ffprobe-installer/ffprobe": "^2.0.0", "@google-cloud/artifact-registry": "^3.5.0", "@google-cloud/bigquery": "^7.9.1", "@google-cloud/compute": "^4.11.0", "@google-cloud/networkservices": "^0.7.0", "@google-cloud/storage": "^7.15.0", "@ir-engine/common": "^1.0.3", "@ir-engine/engine": "^1.0.3", "@ir-engine/matchmaking": "^1.0.3", "@koa/multer": "^3.0.2", "@kubernetes/client-node": "1.0.0", "@octokit/rest": "^19.0.11", "app-root-path": "3.1.0", "bent": "^7.3.12", "chargebee": "2.19.0", "compare-versions": "^6.0.0-rc.1", "cors-anywhere": "^0.4.4", "cross-env": "7.0.3", "dotenv-flow": "^3.2.0", "execa": "^5.0.0", "extract-zip": "^2.0.1", "feathers-authentication-management": "^3.0.0", "feathers-hooks-common": "^7.0.1", "feathers-mailer": "^3.1.0", "feathers-swagger": "3.0.0", "feathers-sync": "3.0.2", "flatted": "3.1.0", "fs-blob-store": "6.0.0", "helmet": "^6.0.0", "ini": "^5.0.0", "internal-ip": "^6.1.0", "jose": "^5.9.2", "jsonwebtoken": "^9.0.0", "jszip": "^3.10.1", "kill-port": "^2.0.1", "knex": "2.4.2", "koa-compress": "^5.1.1", "koa-cors": "^0.0.16", "koa-helmet": "^7.0.2", "koa-mount": "^4.0.0", "koa-simple-healthcheck": "^0.0.1", "lodash": "4.17.21", "meshoptimizer": "^0.18.1", "mime-types": "^2.1.35", "minio": "7.1.3", "moment": "2.29.4", "mp3-duration": "^1.1.0", "multer": "1.4.5-lts.1", "mysql": "2.18.1", "mysql2": "3.2.0", "node-fetch": "2.6.9", "nodemailer-smtp-transport": "^2.7.4", "octokit": "2.0.14", "pg": "^8.16.0", "pgvector": "^0.2.1", "pino": "^8.11.0", "pino-elasticsearch": "6.6.0", "pino-opensearch": "^1.1.0", "pino-pretty": "^10.0.0", "pino-socket": "^7.3.0", "primus": "^8.0.6", "prom-client": "^15.1.0", "primus-emitter": "^3.1.1", "probe-image-size": "^7.2.3", "pug": "^3.0.2", "s3-blob-store": "^4.1.1", "semver": "^7.3.8", "sharp": "0.33.5", "simple-git": "3.17.0", "slugify": "1.6.5", "swagger-ui-dist": "^4.12.0", "trace-unhandled": "2.0.1", "typescript": "5.6.3", "util": "^0.12.5", "uuid": "9.0.0", "ffmpeg-static": "^5.2.0", "fluent-ffmpeg": "^2.1.3"}, "devDependencies": {"@types/dotenv-flow": "3.2.0", "@types/koa-bodyparser": "^4.3.10", "@types/koa-compress": "^4.0.3", "@types/koa-helmet": "^6.0.4", "@types/koa-multer": "^1.0.1", "@types/mime-types": "2.1.1", "@types/node": "18.15.5", "@types/node-fetch": "2.6.2", "@types/nodemailer-smtp-transport": "2.7.5", "@types/pg": "^8.15.4", "@types/pug": "2.0.6", "nock": "^13.4.0"}, "gitHead": "2313453697ca7c6b8d36b3b166b5a6445fe1c851"}