{"name": "@dl-engine/server-auth", "version": "1.0.0", "description": "DL-Engine Authentication Service - 手机号认证和权限管理服务", "main": "dist/index.js", "type": "module", "scripts": {"build": "tsc", "dev": "tsx watch src/index.ts", "start": "node dist/index.js", "test": "vitest", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix"}, "dependencies": {"@nestjs/common": "^10.0.0", "@nestjs/core": "^10.0.0", "@nestjs/platform-express": "^10.0.0", "@nestjs/swagger": "^7.0.0", "@nestjs/jwt": "^10.0.0", "@nestjs/passport": "^10.0.0", "@nestjs/typeorm": "^10.0.0", "typeorm": "^0.3.0", "mysql2": "^3.6.0", "redis": "^4.6.0", "passport": "^0.6.0", "passport-jwt": "^4.0.0", "passport-local": "^1.0.0", "bcryptjs": "^2.4.3", "class-validator": "^0.14.0", "class-transformer": "^0.5.1", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.0", "winston": "^3.10.0", "helmet": "^7.0.0", "cors": "^2.8.5", "compression": "^1.7.4", "express-rate-limit": "^7.0.0", "node-cron": "^3.0.0", "qrcode": "^1.5.3", "speakeasy": "^2.0.0"}, "devDependencies": {"@types/node": "^20.0.0", "@types/express": "^4.17.0", "@types/bcryptjs": "^2.4.0", "@types/passport-jwt": "^3.0.0", "@types/passport-local": "^1.0.0", "@types/qrcode": "^1.5.0", "@types/speakeasy": "^2.0.0", "typescript": "^5.6.3", "tsx": "^4.0.0", "vitest": "^1.0.0", "eslint": "^8.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0"}, "keywords": ["dl-engine", "authentication", "phone-login", "sms-verification", "jwt", "o<PERSON>h", "education", "chinese"], "author": "DL-Engine Team", "license": "MIT"}