.buttonContainer {
  width: 100%;
  padding: 10px;
  display: flex;
  cursor: pointer;
  font-size: 1rem;
  font-weight: bold;
  flex-direction: row;
  align-items: center;
  color: var(--textColor);
  justify-content: center;
  border: none;
  border-radius: 4px;
  text-transform: uppercase;

  &.filled {
    background-color: var(--buttonFilled);
  }

  &.outlined {
    color: var(--buttonOutlined);
    border: solid 1px var(--buttonOutlined);
    background-color: transparent;
  }

  &.gradient {
    background: linear-gradient(90deg, var(--buttonGradientStart), var(--buttonGradientEnd));
  }

  svg {
    width: 25px;
    height: 25px;
    margin: 0 5px;
    color: var(--iconButtonColor);

    path {
      fill: var(--iconButtonColor);
    }
  }
}

button:hover,
button:focus {
  outline: none;
  opacity: 0.7;
}

button[disabled] {
  background: var(--disabled);
  opacity: 1 !important;
}
