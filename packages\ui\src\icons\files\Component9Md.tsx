/*
CPAL-1.0 License

The contents of this file are subject to the Common Public Attribution License
Version 1.0. (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at
https://github.com/ir-engine/ir-engine/blob/dev/LICENSE.
The License is based on the Mozilla Public License Version 1.1, but Sections 14
and 15 have been added to cover use of software over a computer network and 
provide for limited attribution for the Original Developer. In addition, 
Exhibit A has been modified to be consistent with Exhibit B.

Software distributed under the License is distributed on an "AS IS" basis,
WITHOUT WARRANTY OF ANY KIND, either express or implied. See the License for the
specific language governing rights and limitations under the License.

The Original Code is Infinite Reality Engine.

The Original Developer is the Initial Developer. The Initial Developer of the
Original Code is the Infinite Reality Engine team.

All portions of the code written by the Infinite Reality Engine team are Copyright © 2021-2025
Infinite Reality Engine. All Rights Reserved.
*/

import type { SVGProps } from 'react'
import * as React from 'react'
import { Ref, forwardRef } from 'react'
const Component9Md = (props: SVGProps<SVGSVGElement>, ref: Ref<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="1em"
    height="1em"
    fill="none"
    viewBox="0 0 22 22"
    role="img"
    stroke="currentColor"
    ref={ref}
    {...props}
  >
    <g stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.75}>
      <path d="M2.284 16c2.759 4.781 8.87 6.42 13.65 3.659a9.98 9.98 0 0 0 4.94-7.609M19.592 6c-2.76-4.781-8.87-6.42-13.65-3.659A9.99 9.99 0 0 0 1 9.95" />
      <path d="M16.668 9.482a5.867 5.867 0 0 1-4.149 7.185m4.149-7.186a5.867 5.867 0 0 0-7.185-4.148m7.185 4.149 1.416-.38m-5.565 7.565a5.867 5.867 0 0 1-7.185-4.149m7.185 4.149.38 1.416m-7.565-5.565a5.867 5.867 0 0 1 4.149-7.185m-4.149 7.185-1.416.38m5.565-7.565-.38-1.417m4.023 6.515a2.2 2.2 0 1 1-4.25 1.138 2.2 2.2 0 0 1 4.25-1.139" />
    </g>
  </svg>
)
const ForwardRef = forwardRef(Component9Md)
export default ForwardRef
