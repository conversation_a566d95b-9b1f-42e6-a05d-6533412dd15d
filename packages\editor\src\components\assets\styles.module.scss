.assetsPanel {
  height: 100%;
}

.panelContainer {
  height: 100%;
  position: relative;
  flex: 1;
  background-color: transparent;
  overflow-y: auto;
}

.searchContainer {
  display: flex;
  flex-flow: row nowrap;
  align-items: center;
}

.searchContainer > button,
.searchContainer > input {
  margin: 0 !important; /* To remove any default margins */
}

.searchContainer > input {
  width: 100% !important;
  height: 30px !important;
  padding: 5px !important;
  border-radius: 4px !important;
  border: 1px solid var(--borderColor) !important;
  background: var(--dockBackground) !important;
  color: var(--textColor) !important;
}

.searchContainer > button {
  width: 16px !important;
  height: 16px !important;
  padding: 5px !important;
  border-radius: 4px !important;
  border: 1px solid var(--borderColor) !important;
  background: var(--dockBackground) !important;
  color: var(--textColor) !important;
}

/* Optional, if you want the button and input to have some spacing between them */
.searchContainer > button + input {
  margin-left: 5px !important;
}

.contentContainer {
  display: flex;
  width: 100%;
  flex-wrap: wrap;
  overflow: auto;
}

.viewModeSettings {
  padding: 10px;
  background-color: var(--inputBackground);
  font-family: var(--lato);
  font-size: 12px;
  color: var(--textColor);

  .viewModeSettingsLabel {
    font-size: 12px;
    font-family: var(--lato);
  }
}

.fileListItemContainer {
  display: block;
  color: var(--textColor);
  cursor: pointer;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  text-align: center;
  padding-bottom: 5px;

  &:hover {
    background: var(--dropdownMenuHoverBackground);
  }

  .fileNameContainer {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-left: auto;
    margin-right: auto;
    padding-top: 5px;
  }

  .extensionRibbon {
    --stick-out: 0.5em;

    font-size: 0.135em;
    font-weight: bold;
    text-align: right;
    text-transform: uppercase;
    color: currentcolor;
    padding: 0.2em 0.3em;
    border-radius: 0.3em;
    border: currentcolor 0.2em solid;
    background: var(--dockBackground);
    max-width: 90%;
    overflow-x: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    position: absolute;
    inset: calc(100% - 2.75em) var(--stick-out) auto auto;
  }
}

.table {
  .tableHeaderRow {
    background-color: var(--tableHeaderBackground);
    border-bottom: 2px solid var(--dropdownMenuHoverBackground);
  }

  .tableCell {
    color: var(--textColor);
    border: none;
    font-weight: 200;
    cursor: pointer;

    .cellName {
      display: flex;
      align-items: center;
      gap: 0.25rem;
    }
  }
}

.paperDialog {
  background: var(--popupBackground) !important;
  color: var(--textColor);
  width: 100%;
  padding: 20px;
}

.primaryText {
  color: var(--textColor);
}

.secondaryText {
  color: var(--textColor);
}

.inputContainer {
  padding: 2px 4px;
  display: flex;
  align-items: center;
  margin-left: 5px;
  background: #343b41;
  border: 1px solid #23282c;
  color: var(--textColor) !important;
}

.input {
  flex: 1 !important;
  color: var(--textColor) !important;
}

.btnContainer {
  display: flex;
  width: 100%;
  justify-content: flex-end;
  padding: 10px 10px 0;

  .newBtn {
    margin: 0 !important;
    height: 35px !important;
    width: 100px !important;
    font-size: 14px !important;
    flex-shrink: 0 !important;
    color: var(--textColor) !important;
    background-color: var(--buttonFilled) !important;
  }
}

%container {
  height: 100%;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
}

.container {
  @extend %container;
}

.error {
  color: #c53a7d;
  font-weight: bold;
}

.breadcrumbList {
  justify-content: center;
}

.separator {
  color: var(--textColor);
}

.breadcrumb {
  color: var(--textColor);
}

.pagination {
  color: var(--textColor);

  p {
    font-size: 12px;
  }
}

.filesLoading {
  position: absolute;
  background: #00000080;
  z-index: 1000;
}

.horizontalCenter {
  margin: 0 auto;
  display: block;
}

.modelMenu {
  position: relative;
  display: flex;
  flex-direction: row;
}

.headerContainer {
  font-size: 16px;
}

.presetBox {
  background-color: rgb(54 54 54);
  border-radius: 10px;
  width: 250px;
  height: 100%;
}

.presetHeader {
  justify-content: center;
  color: white;
}

.presetButton {
  justify-content: center;
  margin-top: 5px;
  margin-bottom: 5px;
  background-color: rgb(85 85 85);
  color: white;
  width: 100%;
  border-radius: 10px;
}

.confirmModal {
  position: absolute;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  top: 50%;
  left: 50%;
  transform: 'translate(-50%, -50%)';
  background-color: rgb(54 54 54);
  color: white;
  padding: 30px 30px 15px 15px;
  border-radius: 10px;
}

.confirmModalButtons {
  margin-top: 15px;
}

.resourceItemContainer {
  display: flex;
  flex-direction: column;
  outline: none;
  overflow: hidden;
  background: transparent;
  margin: 0.25rem 0.5rem;
  padding: 0.25rem;
  cursor: pointer;

  &:hover,
  &:focus {
    background-color: var(--dropdownMenuHoverBackground);
    color: var(--textColor);
  }

  &.selected {
    background: var(--dropdownMenuSelectedBackground);
    color: var(--textColor);

    &:hover,
    &:focus {
      background-color: var(--dropdownMenuSelectedBackground);
    }
  }

  &:active,
  &.active {
    background: var(--dropdownMenuSelectedBackground);
  }
}

.hideScrollbar {
  scrollbar-width: none; /* firefox */
  -ms-overflow-style: none; /* edge */
  ::-webkit-scrollbar {
    /* Hide scrollbar for Chrome, Safari and Opera */
    display: none;
  }
}
