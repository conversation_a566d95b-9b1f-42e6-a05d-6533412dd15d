/*
CPAL-1.0 License

The contents of this file are subject to the Common Public Attribution License
Version 1.0. (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at
https://github.com/ir-engine/ir-engine/blob/dev/LICENSE.
The License is based on the Mozilla Public License Version 1.1, but Sections 14
and 15 have been added to cover use of software over a computer network and 
provide for limited attribution for the Original Developer. In addition, 
Exhibit A has been modified to be consistent with Exhibit B.

Software distributed under the License is distributed on an "AS IS" basis,
WITHOUT WARRANTY OF ANY KIND, either express or implied. See the License for the
specific language governing rights and limitations under the License.

The Original Code is Infinite Reality Engine.

The Original Developer is the Initial Developer. The Initial Developer of the
Original Code is the Infinite Reality Engine team.

All portions of the code written by the Infinite Reality Engine team are Copyright © 2021-2025
Infinite Reality Engine. All Rights Reserved.
*/

import React, { forwardRef, useEffect, useImperativeHandle } from 'react'
import { useTranslation } from 'react-i18next'

import config from '@ir-engine/common/src/config'
import { THUMBNAIL_HEIGHT, THUMBNAIL_WIDTH } from '@ir-engine/common/src/constants/AvatarConstants'

import { getCanvasBlob } from '@ir-engine/client-core/src/common/utils'
import multiLogger from '@ir-engine/common/src/logger'
import { useHookstate } from '@ir-engine/hyperflux'
import { Button, Input } from '@ir-engine/ui'
import { ArrowLeftLg, XCloseLg } from '@ir-engine/ui/src/icons'
import LoadingView from '@ir-engine/ui/src/primitives/tailwind/LoadingView'
import Text from '@ir-engine/ui/src/primitives/tailwind/Text'
import { twMerge } from 'tailwind-merge'
import AvatarPreview from '../../../common/components/AvatarPreview'
import { ModalState } from '../../../common/services/ModalState'
import { AVATAR_ID_REGEX, generateAvatarId } from '../../../util/avatarIdFunctions'
import { AvatarService } from '../../services/AvatarService'
import { DiscardAvatarChangesMenu } from './DiscardAvatarChangesMenu'

export const SupportedSdks = {
  Avaturn: 'Avaturn',
  ReadyPlayerMe: 'ReadyPlayerMe'
}

const isAvaturn = (url: string) => {
  const fileExtensionRegex = /\.[0-9a-z]+$/i
  const avaturnUrl = config.client.avaturnUrl
  if (avaturnUrl && !fileExtensionRegex.test(url)) return url.startsWith(avaturnUrl)
  return false
}

enum LoadingState {
  None,
  LoadingCreator,
  Downloading,
  LoadingPreview,
  Uploading
}
interface AvatarCreatorMenuProps {
  showBackButton: boolean
  previewEnabled: boolean
  previewDisabledMessage?: boolean
}

const AvatarCreatorMenu = (selectedSdk: string) =>
  forwardRef((props: AvatarCreatorMenuProps, ref) => {
    const { previewEnabled = true, previewDisabledMessage } = props
    const { t } = useTranslation()
    const selectedBlob = useHookstate<Blob | null>(null)
    const thumbnail = useHookstate<Blob | null>(null)
    const avatarName = useHookstate('')
    const avatarUrl = useHookstate('')
    const loading = useHookstate(LoadingState.LoadingCreator)
    const error = useHookstate('')

    const logger = multiLogger.child({ component: 'client-core:AvatarCreatorMenu' })

    const getSdkUrl = () => {
      switch (selectedSdk) {
        case SupportedSdks.Avaturn:
          return config.client.avaturnUrl
        case SupportedSdks.ReadyPlayerMe:
        default:
          return config.client.readyPlayerMeUrl
      }
    }

    useEffect(() => {
      window.addEventListener('message', handleMessageEvent)
      return () => {
        window.removeEventListener('message', handleMessageEvent)
      }
    }, [])

    useEffect(() => {
      const rpmIframe = document.getElementById('rpm-iframe') as HTMLIFrameElement
      rpmIframe.src = getSdkUrl() as string
    }, [])

    const export2DReadyPlayerMeAvatar = async (avatarId: string): Promise<Blob> => {
      const res = await fetch(
        `https://models.readyplayer.me/${avatarId}.png?size=${THUMBNAIL_HEIGHT}&camera=portrait&pose=relaxed`
      )
      return await res.blob()
    }

    const generateAvatarThumbnail = async () => {
      const canvas = document.createElement('canvas')
      canvas.width = THUMBNAIL_WIDTH
      canvas.height = THUMBNAIL_HEIGHT

      const avatarCanvas = document.getElementById('stage')?.firstChild as CanvasImageSource

      const newContext = canvas.getContext('2d')
      newContext?.drawImage(avatarCanvas, 0, 0)
      return await getCanvasBlob(canvas)
    }

    const parseMessage = (event: MessageEvent) => {
      try {
        return JSON.parse(event.data)
      } catch (error) {
        return event.data
      }
    }

    const handleReadyPlayerMeMessageEvent = async (event: MessageEvent) => {
      const message = typeof event.data === 'string' ? parseMessage(event) : event.data

      if (message.source !== 'readyplayerme') {
        return
      }

      if (message.eventName === 'v1.frame.ready') {
        const rpmIframe = document.getElementById('rpm-iframe') as HTMLIFrameElement
        rpmIframe?.contentWindow?.postMessage(
          JSON.stringify({
            target: 'readyplayerme',
            type: 'subscribe',
            eventName: 'v1.**'
          }),
          '*'
        )
      }

      if (message.eventName === 'v1.avatar.exported') {
        loading.set(LoadingState.Downloading)
        error.set('')
        avatarName.set(message.data.avatarId)
        try {
          const res = await fetch(message.data.url)
          const data = await res.blob()

          loading.set(LoadingState.LoadingPreview)
          avatarUrl.set(message.data.url)
          selectedBlob.set(data)
          thumbnail.set(await export2DReadyPlayerMeAvatar(message.data.avatarId))
          if (!previewEnabled) {
            loading.set(LoadingState.None)
          }
        } catch (error) {
          logger.error(error)
          error.set(t('user:usermenu.avatar.selectValidFile'))
          loading.set(LoadingState.None)
        }
      }
    }

    const handleAvaturnMessageEvent = async (event: MessageEvent) => {
      const message = typeof event.data === 'string' ? parseMessage(event) : event.data

      if (message.source !== 'avaturn') return // always check the source its always 'avaturn'

      // Get avatar GLB URL
      if (message.eventName === 'v2.avatar.exported') {
        const url = message.data.url
        const avatarIdRegexExec = AVATAR_ID_REGEX.exec(url)
        if (url && url.toString().toLowerCase().startsWith('http')) {
          loading.set(LoadingState.Downloading)
          error.set('')
          avatarName.set(avatarIdRegexExec ? avatarIdRegexExec[1] : generateAvatarId())

          try {
            const res = await fetch(url)
            const data = await res.blob()
            loading.set(LoadingState.LoadingPreview)
            avatarUrl.set(url)
            selectedBlob.set(data)
          } catch (error) {
            logger.error(error)
            error.set(t('user:usermenu.avatar.selectValidFile'))
            loading.set(LoadingState.None)
          }
        }
      }
    }

    const handleMessageEvent = async (event: MessageEvent): Promise<void> => {
      switch (selectedSdk) {
        case SupportedSdks.Avaturn:
          handleAvaturnMessageEvent(event)
          break
        case SupportedSdks.ReadyPlayerMe:
        default:
          await handleReadyPlayerMeMessageEvent(event)
      }
    }

    const uploadAvatar = async (): Promise<void> => {
      if (error.value || selectedBlob.value === null) {
        return
      }
      loading.set(LoadingState.Uploading)

      if (!thumbnail.value) {
        thumbnail.set(await generateAvatarThumbnail())
      }

      const thumbnailName = avatarUrl.value.substring(0, avatarUrl.value.lastIndexOf('.')) + '.png'
      const modelName = !isAvaturn(avatarUrl.value)
        ? avatarUrl.value.substring(0, avatarUrl.value.lastIndexOf('.')) + '.glb'
        : avatarUrl.value.split('/').pop() + '.glb'

      await AvatarService.createAvatar(
        new File([selectedBlob.value!], modelName),
        new File([thumbnail.value!], thumbnailName),
        avatarName.value,
        false
      )

      loading.set(LoadingState.None)
      ModalState.closeModal()
    }

    const handleClose = () => {
      ModalState.openModal(
        <DiscardAvatarChangesMenu
          handleConfirm={() => {
            ModalState.closeModal()
            ModalState.closeModal()
          }}
        />
      )
    }

    // expose handleClose since only the parent component
    // can set the onClickOutside handler
    useImperativeHandle(ref, () => {
      return {
        handleClose
      }
    })

    const loadingMessages = {
      [LoadingState.Downloading]: t('user:avatar.downloading'),
      [LoadingState.LoadingPreview]: t('user:avatar.loadingPreview'),
      [LoadingState.Uploading]: t('user:avatar.packagingAvatar'),
      default: t(`user:avatar.loading${selectedSdk}`)
    }

    const loadingTitle = loadingMessages[loading.value] || loadingMessages.default

    const avatarPreviewLoaded = loading.value === LoadingState.None && selectedBlob.value

    return (
      <div
        id="create-avatar-modal"
        className={twMerge(
          'min-w-34 pointer-events-auto absolute z-50 m-auto flex h-[90dvh]',
          'w-full max-w-[90vw] flex-1 flex-col overflow-y-auto rounded-xl',
          'border border-surface-1 bg-white dark:bg-surface-1',
          'lg:h-[95dvh] lg:w-full lg:max-w-6xl'
        )}
      >
        <div className="grid h-14 w-full grid-cols-[1.5rem,1fr,1.5rem] border-b px-5">
          <button
            data-testid="back-create-avatar-modal-button"
            className=" h-6 w-6 cursor-pointer self-center bg-transparent text-text-primary hover:bg-transparent focus:bg-transparent"
            onClick={handleClose}
          >
            <ArrowLeftLg />
          </button>
          <Text className="col-start-2  place-self-center self-center text-text-primary">
            {loading.value !== LoadingState.Uploading
              ? t('user:avatar.titleCustomizeAvatar')
              : t('user:avatar.savingAvatar', { avatar: avatarName.value })}
          </Text>
          <button
            data-testid="close-create-avatar-modal-button"
            className=" h-6 w-6 cursor-pointer self-center bg-transparent text-text-primary hover:bg-transparent focus:bg-transparent"
            onClick={handleClose}
          >
            <XCloseLg />
          </button>
        </div>
        <div className="grid h-full w-full flex-1 grid-cols-[1fr,50%,1fr] gap-6 px-5 pb-2">
          {loading.value === LoadingState.LoadingCreator && (
            <iframe
              id="rpm-iframe"
              style={{
                width: '100%',
                height: '100%',
                zIndex: 2,
                maxWidth: '100%',
                border: 0
              }}
              className="col-span-3"
            />
          )}
          {loading.value !== LoadingState.LoadingCreator && avatarUrl.value && previewEnabled && (
            <div className="relative col-start-2 rounded-lg bg-gradient-to-b from-[#162941] to-[#114352]">
              <div className="stars absolute left-0 top-0 h-[2px] w-[2px] animate-twinkling bg-transparent"></div>
              <AvatarPreview
                avatarUrl={avatarUrl.value}
                fill
                onAvatarError={(e) => error.set(e)}
                onAvatarLoaded={() => loading.set(LoadingState.None)}
              />
            </div>
          )}
          {avatarPreviewLoaded && !previewEnabled && (
            <div className="relative col-span-3 flex">
              <Text className="m-auto" fontSize="lg">
                {previewDisabledMessage ? previewDisabledMessage : t('user:avatar.avatarPreviewDisabledMessage')}
              </Text>
            </div>
          )}
        </div>
        {avatarPreviewLoaded && (
          <div className="mx-auto mb-2 flex items-center gap-2 py-2">
            <Text className="text-text-secondary" fontSize="sm">
              {t('user:avatar.InputAvatarName')}
            </Text>
            <Input value={avatarName.value || ''} onChange={(e) => avatarName.set(e.target.value)} />
            <Button
              size="xs"
              disabled={loading.value !== LoadingState.None}
              data-testid="upload-avatar-button"
              onClick={uploadAvatar}
              className="w-fit place-self-center rounded-md"
            >
              {t('user:avatar.saveAvatar')}
            </Button>
          </div>
        )}
        {loading.value !== LoadingState.None && loading.value !== LoadingState.LoadingCreator && (
          <div className="mx-auto flex justify-between py-2">
            <LoadingView
              className="mr-2 h-6 max-h-6 w-6 justify-between text-text-primary"
              titleClassname="text-text-primary"
              containerClassName="flex-row"
              title={loadingTitle}
            />
          </div>
        )}
      </div>
    )
  })
export default AvatarCreatorMenu
