import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToOne,
  JoinColumn
} from 'typeorm'
import { User } from './user.entity'

/**
 * 可见性级别枚举
 */
export enum VisibilityLevel {
  PUBLIC = 'public',        // 所有人可见
  FRIENDS = 'friends',      // 仅好友可见
  PRIVATE = 'private'       // 仅自己可见
}

/**
 * 联系方式可见性枚举
 */
export enum ContactVisibility {
  PUBLIC = 'public',        // 所有人可见
  FRIENDS = 'friends',      // 仅好友可见
  TEACHERS = 'teachers',    // 仅教师可见
  PRIVATE = 'private'       // 仅自己可见
}

/**
 * 用户隐私设置实体
 * 
 * 管理用户的隐私控制和数据可见性设置
 */
@Entity('user_privacy')
export class UserPrivacy {
  @PrimaryGeneratedColumn('uuid')
  id: string

  /**
   * 关联的用户ID
   */
  @Column({ type: 'uuid' })
  userId: string

  /**
   * 资料可见性
   */
  @Column({
    type: 'enum',
    enum: VisibilityLevel,
    default: VisibilityLevel.FRIENDS
  })
  profileVisibility: VisibilityLevel

  /**
   * 头像可见性
   */
  @Column({
    type: 'enum',
    enum: VisibilityLevel,
    default: VisibilityLevel.PUBLIC
  })
  avatarVisibility: VisibilityLevel

  /**
   * 真实姓名可见性
   */
  @Column({
    type: 'enum',
    enum: ContactVisibility,
    default: ContactVisibility.FRIENDS
  })
  realNameVisibility: ContactVisibility

  /**
   * 手机号码可见性
   */
  @Column({
    type: 'enum',
    enum: ContactVisibility,
    default: ContactVisibility.PRIVATE
  })
  phoneVisibility: ContactVisibility

  /**
   * 邮箱地址可见性
   */
  @Column({
    type: 'enum',
    enum: ContactVisibility,
    default: ContactVisibility.FRIENDS
  })
  emailVisibility: ContactVisibility

  /**
   * 生日可见性
   */
  @Column({
    type: 'enum',
    enum: VisibilityLevel,
    default: VisibilityLevel.FRIENDS
  })
  birthdayVisibility: VisibilityLevel

  /**
   * 位置信息可见性
   */
  @Column({
    type: 'enum',
    enum: VisibilityLevel,
    default: VisibilityLevel.FRIENDS
  })
  locationVisibility: VisibilityLevel

  /**
   * 在线状态可见性
   */
  @Column({
    type: 'enum',
    enum: VisibilityLevel,
    default: VisibilityLevel.FRIENDS
  })
  onlineStatusVisibility: VisibilityLevel

  /**
   * 学习进度可见性
   */
  @Column({
    type: 'enum',
    enum: VisibilityLevel,
    default: VisibilityLevel.FRIENDS
  })
  learningProgressVisibility: VisibilityLevel

  /**
   * 项目列表可见性
   */
  @Column({
    type: 'enum',
    enum: VisibilityLevel,
    default: VisibilityLevel.PUBLIC
  })
  projectsVisibility: VisibilityLevel

  /**
   * 成就徽章可见性
   */
  @Column({
    type: 'enum',
    enum: VisibilityLevel,
    default: VisibilityLevel.PUBLIC
  })
  achievementsVisibility: VisibilityLevel

  /**
   * 是否允许搜索到我
   */
  @Column({ type: 'boolean', default: true })
  allowSearch: boolean

  /**
   * 是否允许陌生人发送消息
   */
  @Column({ type: 'boolean', default: false })
  allowMessagesFromStrangers: boolean

  /**
   * 是否允许好友请求
   */
  @Column({ type: 'boolean', default: true })
  allowFriendRequests: boolean

  /**
   * 是否允许协作邀请
   */
  @Column({ type: 'boolean', default: true })
  allowCollaborationInvites: boolean

  /**
   * 是否允许课程邀请
   */
  @Column({ type: 'boolean', default: true })
  allowCourseInvites: boolean

  /**
   * 是否允许群组邀请
   */
  @Column({ type: 'boolean', default: true })
  allowGroupInvites: boolean

  /**
   * 黑名单用户ID列表
   */
  @Column({ type: 'json', nullable: true })
  blockedUsers?: string[]

  /**
   * 是否启用数据分析
   */
  @Column({ type: 'boolean', default: true })
  allowAnalytics: boolean

  /**
   * 是否允许个性化推荐
   */
  @Column({ type: 'boolean', default: true })
  allowPersonalization: boolean

  /**
   * 是否允许第三方数据共享
   */
  @Column({ type: 'boolean', default: false })
  allowThirdPartySharing: boolean

  /**
   * 数据保留期限（天数，null表示永久保留）
   */
  @Column({ type: 'int', nullable: true })
  dataRetentionDays?: number

  /**
   * 是否启用两步验证
   */
  @Column({ type: 'boolean', default: false })
  twoFactorEnabled: boolean

  /**
   * 登录通知设置
   */
  @Column({ type: 'boolean', default: true })
  loginNotifications: boolean

  /**
   * 可信设备列表
   */
  @Column({ type: 'json', nullable: true })
  trustedDevices?: Array<{
    deviceId: string
    deviceName: string
    lastUsed: Date
    trusted: boolean
  }>

  /**
   * 隐私协议版本
   */
  @Column({ type: 'varchar', length: 20, nullable: true })
  privacyPolicyVersion?: string

  /**
   * 隐私协议接受时间
   */
  @Column({ type: 'timestamp', nullable: true })
  privacyPolicyAcceptedAt?: Date

  /**
   * 创建时间
   */
  @CreateDateColumn()
  createdAt: Date

  /**
   * 更新时间
   */
  @UpdateDateColumn()
  updatedAt: Date

  // 关联关系
  @OneToOne(() => User, user => user.privacy)
  @JoinColumn({ name: 'userId' })
  user: User

  /**
   * 检查用户是否被屏蔽
   */
  isUserBlocked(userId: string): boolean {
    return this.blockedUsers?.includes(userId) || false
  }

  /**
   * 屏蔽用户
   */
  blockUser(userId: string): void {
    if (!this.blockedUsers) {
      this.blockedUsers = []
    }
    if (!this.blockedUsers.includes(userId)) {
      this.blockedUsers.push(userId)
    }
  }

  /**
   * 取消屏蔽用户
   */
  unblockUser(userId: string): void {
    if (this.blockedUsers) {
      this.blockedUsers = this.blockedUsers.filter(id => id !== userId)
    }
  }
}
