services:
  db:
    image: mariadb:10.7
    container_name: ir-engine_db
    environment:
      MYSQL_ROOT_PASSWORD: ir-engine-root
      MYSQL_DATABASE: ir-engine
      MYSQL_USER: server
      MYSQL_PASSWORD: password
    ports:
      - '3306:3306'
  testdb:
    image: mariadb:10.7
    container_name: ir-engine_test_db
    environment:
      MYSQL_ROOT_PASSWORD: ir-engine-root
      MYSQL_DATABASE: ir-engine
      MYSQL_USER: server
      MYSQL_PASSWORD: password
    ports:
      - '3305:3306'
  minikubedb:
    image: mariadb:10.7
    container_name: ir-engine_minikube_db
    environment:
      MYSQL_ROOT_PASSWORD: ir-engine-root
      MYSQL_DATABASE: ir-engine
      MYSQL_USER: server
      MYSQL_PASSWORD: password
    ports:
      - '3304:3306'
  redis:
    image: redis
    container_name: ir-engine_redis
    command: redis-server
    ports:
      - '6379:6379'
  postgres:
    image: pgvector/pgvector:pg16
    profiles: ["vectordb"] # Only starts in vectordb profile
    container_name: ir-engine_vector_db
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DATABASE: vector-db
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
  testdb-vector:
    image: pgvector/pgvector:pg16
    profiles: ["vectordb"] # Only starts in vectordb profile
    container_name: ir-engine_vector_test_db
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DATABASE: vector-db
    ports:
      - "5433:5432"
    volumes:
      - postgres_test_data:/var/lib/postgresql/data

  ollama:
    image: ollama/ollama:latest
    profiles: ["vectordb"] # Only starts in vectordb profile
    entrypoint:
      [
        "/bin/bash",
        "-c",
        "ollama serve & sleep 5 && ollama pull mxbai-embed-large && wait",
      ]
    environment:
      - OLLAMA_KEEP_ALIVE="24h"
    volumes:
      - ollama_storage:/root/.ollama
    ports:
      - "11434:11434"
    healthcheck:
      test: ["CMD-SHELL", "ollama list | grep -q mxbai-embed-large"] # Check if model is pulled
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 60s # Give Ollama time to start and pull models    
    restart: unless-stopped

#   elasticsearch:
#     image: docker.elastic.co/elasticsearch/elasticsearch:7.4.0
#     container_name: elasticsearch
#     environment:
#       - xpack.security.enabled=false
#       - discovery.type=single-node
#     ulimits:
#       memlock:
#         soft: -1
#         hard: -1
#       nofile:
#         soft: 65536
#         hard: 65536
#     cap_add:
#       - IPC_LOCK
#     volumes:
#       - elasticsearch-data:/usr/share/elasticsearch/data
#     ports:
#       - 9200:9200
#       - 9300:9300
#   kibana:
#     container_name: kibana
#     image: docker.elastic.co/kibana/kibana:7.4.0
#     environment:
#       - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
#       - telemetry.enabled=false
#     ports:
#       - 5601:5601
#     depends_on:
#       - elasticsearch

volumes:
  postgres_data:
    driver: local

  postgres_test_data:
    driver: local

  ollama_storage:
    driver: local

# volumes:
#   elasticsearch-data:
#     driver: local
