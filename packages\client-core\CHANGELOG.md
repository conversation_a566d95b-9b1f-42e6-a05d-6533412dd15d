# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.



**Note:** Version bump only for package @etherealengine/client-core







**Note:** Version bump only for package @etherealengine/client-core







**Note:** Version bump only for package @etherealengine/client-core







**Note:** Version bump only for package @etherealengine/client-core







**Note:** Version bump only for package @etherealengine/client-core







**Note:** Version bump only for package @etherealengine/client-core







**Note:** Version bump only for package @etherealengine/client-core







**Note:** Version bump only for package @xrengine/client-core







**Note:** Version bump only for package @xrengine/client-core







**Note:** Version bump only for package @xrengine/client-core







**Note:** Version bump only for package @xrengine/client-core







**Note:** Version bump only for package @xrengine/client-core







**Note:** Version bump only for package @xrengine/client-core







**Note:** Version bump only for package @xrengine/client-core







**Note:** Version bump only for package @xrengine/client-core







**Note:** Version bump only for package @xrengine/client-core







**Note:** Version bump only for package @xrengine/client-core







**Note:** Version bump only for package @xrengine/client-core







**Note:** Version bump only for package @xrengine/client-core







**Note:** Version bump only for package @xrengine/client-core







**Note:** Version bump only for package @xrengine/client-core







**Note:** Version bump only for package @xrengine/client-core







**Note:** Version bump only for package @xrengine/client-core







**Note:** Version bump only for package @xrengine/client-core







**Note:** Version bump only for package @xrengine/client-core







**Note:** Version bump only for package @xrengine/client-core





## [0.5.2](https://github.com/XRFoundation/XREngine/compare/v0.5.1...v0.5.2) (2022-04-07)

**Note:** Version bump only for package @xrengine/client-core







**Note:** Version bump only for package @xrengine/client-core







**Note:** Version bump only for package @xrengine/client-core







**Note:** Version bump only for package @xrengine/client-core







**Note:** Version bump only for package @xrengine/client-core







**Note:** Version bump only for package @xrengine/client-core







**Note:** Version bump only for package @xrengine/client-core







**Note:** Version bump only for package @xrengine/client-core







**Note:** Version bump only for package @xrengine/client-core







**Note:** Version bump only for package @xrengine/client-core







**Note:** Version bump only for package @xrengine/client-core







**Note:** Version bump only for package @xrengine/client-core







**Note:** Version bump only for package @xrengine/client-core







**Note:** Version bump only for package @xrengine/client-core







**Note:** Version bump only for package @xrengine/client-core







**Note:** Version bump only for package @xrengine/client-core







**Note:** Version bump only for package @xrengine/client-core







**Note:** Version bump only for package @xrengine/client-core







**Note:** Version bump only for package @xrengine/client-core







**Note:** Version bump only for package @xrengine/client-core







**Note:** Version bump only for package @xrengine/client-core







**Note:** Version bump only for package @xrengine/client-core







**Note:** Version bump only for package @xrengine/client-core







**Note:** Version bump only for package @xrengine/client-core







**Note:** Version bump only for package @xrengine/client-core







**Note:** Version bump only for package @xrengine/client-core







**Note:** Version bump only for package @xrengine/client-core







**Note:** Version bump only for package @xrengine/client-core







**Note:** Version bump only for package @xrengine/client-core







**Note:** Version bump only for package @xrengine/client-core







**Note:** Version bump only for package @xrengine/client-core





## 0.2.36 (2021-06-25)

**Note:** Version bump only for package @xrengine/client-core





## 0.2.35 (2021-06-25)

**Note:** Version bump only for package @xrengine/client-core





## 0.2.34 (2021-06-25)

**Note:** Version bump only for package @xrengine/client-core





## 0.2.33 (2021-06-25)

**Note:** Version bump only for package @xrengine/client-core





## 0.2.32 (2021-06-25)

**Note:** Version bump only for package @xrengine/client-core





## 0.2.31 (2021-06-25)

**Note:** Version bump only for package @xrengine/client-core





## 0.2.30 (2021-06-25)

**Note:** Version bump only for package @xrengine/client-core





## [0.2.29](https://github.com/XRFoundation/XREngine/compare/v0.2.28...v0.2.29) (2021-06-24)

**Note:** Version bump only for package @xrengine/client-core





## [0.2.28](https://github.com/XRFoundation/XREngine/compare/v0.2.27...v0.2.28) (2021-06-22)

**Note:** Version bump only for package @xrengine/client-core





## [0.2.27](https://github.com/XRFoundation/XREngine/compare/v0.2.26...v0.2.27) (2021-05-13)

**Note:** Version bump only for package @xrengine/client-core





## [0.2.26](https://github.com/XRFoundation/XREngine/compare/v0.2.24...v0.2.26) (2021-05-12)

**Note:** Version bump only for package @xrengine/client-core





## [0.2.25](https://github.com/XRFoundation/XREngine/compare/v0.2.24...v0.2.25) (2021-05-12)

**Note:** Version bump only for package @xrengine/client-core





## [0.2.24](https://github.com/XRFoundation/XREngine/compare/v0.2.23...v0.2.24) (2021-05-12)

**Note:** Version bump only for package @xrengine/client-core





## [0.2.23](https://github.com/XRFoundation/XREngine/compare/v0.2.22...v0.2.23) (2021-05-12)

**Note:** Version bump only for package @xrengine/client-core





## [0.2.22](https://github.com/XRFoundation/XREngine/compare/v0.2.21...v0.2.22) (2021-05-05)

**Note:** Version bump only for package @xrengine/client-core





## [0.2.21](https://github.com/xrengine/xrengine/compare/v0.2.20...v0.2.21) (2021-05-05)

**Note:** Version bump only for package @xrengine/client-core





## [0.2.20](https://github.com/xrengine/xrengine/compare/v0.2.18...v0.2.20) (2021-05-04)


### Bug Fixes

* **deps:** pin dependencies ([ac0be70](https://github.com/xrengine/xrengine/commit/ac0be70b9194c3809e74ba8875529c091d084014))
* **deps:** update dependency react-i18next to v11.8.13 ([8fd41b7](https://github.com/xrengine/xrengine/commit/8fd41b7bb47a07fadc9558cab7dea60d1d2031f9))
* **deps:** update dependency react-modal to v3.13.1 ([293901c](https://github.com/xrengine/xrengine/commit/293901c94afa9dc883d17ffd22e9b3577dab88d6))
* **deps:** update dependency react-redux to v7.2.4 ([8bc2601](https://github.com/xrengine/xrengine/commit/8bc26013abb25ce0c07a96006d7d03d9e4d84665))
* **deps:** update dependency redux to v4.1.0 ([fa7ccd9](https://github.com/xrengine/xrengine/commit/fa7ccd9e2fce1df39a8537c2ba93f5e0d77834b1))
* **deps:** update dependency url-toolkit to v2.2.2 ([9d5aa72](https://github.com/xrengine/xrengine/commit/9d5aa72ab4ad254eb60d08822a90c7dd22a9a5fd))
* **deps:** update nivo monorepo to v0.68.0 ([e4e44b8](https://github.com/xrengine/xrengine/commit/e4e44b860e2c34e368f47c071f72c3f140abfb7b))





## 0.2.18 (2021-04-22)


### Bug Fixes

* **deps:** update dependency react-i18next to v11.8.13 ([309d94d](https://github.com/XRFoundation/XREngine/commit/309d94dc136028d50ac5f01efba62c61e5735e41))
* **deps:** update dependency react-modal to v3.13.1 ([279a21f](https://github.com/XRFoundation/XREngine/commit/279a21ff38bb067b634ff811ef0faf2197850800))





## [0.2.11](https://github.com/XRFoundation/XREngine/compare/v0.2.10...v0.2.11) (2021-04-08)

**Note:** Version bump only for package @xrengine/client-core





## [0.2.10](https://github.com/XRFoundation/XREngine/compare/v0.2.9...v0.2.10) (2021-03-31)

**Note:** Version bump only for package @xrengine/client-core





## [0.2.9](https://github.com/XRFoundation/XREngine/compare/v0.2.8...v0.2.9) (2021-03-31)

**Note:** Version bump only for package @xrengine/client-core





## [0.2.7](https://github.com/XRFoundation/XREngine/compare/v0.2.6...v0.2.7) (2021-03-31)

**Note:** Version bump only for package @xrengine/client-core





## [0.2.6](https://github.com/XRFoundation/XREngine/compare/v0.2.5...v0.2.6) (2021-03-31)

**Note:** Version bump only for package @xrengine/client-core





## 0.2.4 (2021-03-31)

**Note:** Version bump only for package @xrengine/client-core





## 0.2.3 (2021-03-31)

**Note:** Version bump only for package @xrengine/client-core
