ARG REPO_URL
ARG REPO_NAME
ARG STAGE
ARG TAG
FROM ${REPO_URL}/${REPO_NAME}:${TAG} AS builder

# Create app directory
WORKDIR /app
COPY project-package-jsons ./
# to make use of caching, copy only package files and install dependencies
COPY packages/taskserver/package.json ./packages/taskserver/

ARG NODE_ENV
RUN --mount=type=cache,target=/root/.npm npm install --loglevel notice --legacy-peer-deps --production

COPY . .

# copy then compile the code

ENV APP_ENV=production

FROM node:22-slim AS runner
WORKDIR /app

COPY --from=builder /app ./

CMD ["scripts/start-server.sh"]
