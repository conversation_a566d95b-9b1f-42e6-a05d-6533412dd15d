/*
CPAL-1.0 License

The contents of this file are subject to the Common Public Attribution License
Version 1.0. (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at
https://github.com/ir-engine/ir-engine/blob/dev/LICENSE.
The License is based on the Mozilla Public License Version 1.1, but Sections 14
and 15 have been added to cover use of software over a computer network and 
provide for limited attribution for the Original Developer. In addition, 
Exhibit A has been modified to be consistent with Exhibit B.

Software distributed under the License is distributed on an "AS IS" basis,
WITHOUT WARRANTY OF ANY KIND, either express or implied. See the License for the
specific language governing rights and limitations under the License.

The Original Code is Infinite Reality Engine.

The Original Developer is the Initial Developer. The Initial Developer of the
Original Code is the Infinite Reality Engine team.

All portions of the code written by the Infinite Reality Engine team are Copyright © 2021-2025
Infinite Reality Engine. All Rights Reserved.
*/

import { useOptionalComponent } from '@ir-engine/ecs'
import { useTexture } from '@ir-engine/engine/src/assets/functions/resourceLoaderHooks'
import { MediaComponent } from '@ir-engine/engine/src/scene/components/MediaComponent'
import { useHelperEntity } from '@ir-engine/spatial/src/helper/functions/useHelperEntity'
import { DoubleSide, Mesh, MeshBasicMaterial, PlaneGeometry } from 'three'

const AUDIO_TEXTURE_PATH = '/static/editor/audio-icon.png'

export const MediaHelperReactor: React.FC = (props: { parentEntity; iconEntity; selected; hovered }) => {
  const { parentEntity, iconEntity, selected, hovered } = props
  const mediaComponent = useOptionalComponent(parentEntity, MediaComponent)
  const debugEnabled =
    (selected || hovered) && mediaComponent?.resources.value && mediaComponent.resources.value.length === 0
  const [audioHelperTexture] = useTexture(debugEnabled ? AUDIO_TEXTURE_PATH : '', parentEntity)

  useHelperEntity(
    parentEntity,
    () => {
      const material = new MeshBasicMaterial({
        transparent: true,
        opacity: 0.3,
        side: DoubleSide,
        depthTest: false,
        depthWrite: false
      })

      const plane = new PlaneGeometry()

      return new Mesh(plane, material)
    },
    debugEnabled && !!audioHelperTexture
  )
  return null
}
