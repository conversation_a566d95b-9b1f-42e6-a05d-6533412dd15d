ARG REPO_URL
ARG REPO_NAME
ARG STAGE
ARG TAG
FROM ${REPO_URL}/${REPO_NAME}:${TAG} AS builder

# Create app directory
WORKDIR /app
COPY project-package-jsons ./
RUN --mount=type=cache,target=/root/.npm npm install --loglevel notice --legacy-peer-deps --production
# copy then compile the code
COPY . .

ENV APP_ENV=production

FROM node:22-slim AS runner

WORKDIR /app

RUN apt-get update \
    && apt-get install -y wget gnupg \
    && wget -q -O - https://dl-ssl.google.com/linux/linux_signing_key.pub | apt-key add - \
    && sh -c 'echo "deb [arch=amd64] http://dl.google.com/linux/chrome/deb/ stable main" >> /etc/apt/sources.list.d/google.list' \
    && apt-get update \
    && apt-get install -y google-chrome-stable fonts-ipafont-gothic fonts-wqy-zenhei fonts-thai-tlwg fonts-kacst fonts-freefont-ttf libxss1 \
      --no-install-recommends \
    && rm -rf /var/lib/apt/lists/*

COPY --from=builder /app ./

CMD ["scripts/start-testbot.sh"]