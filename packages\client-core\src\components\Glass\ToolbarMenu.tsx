/*
CPAL-1.0 License

The contents of this file are subject to the Common Public Attribution License
Version 1.0. (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at
https://github.com/ir-engine/ir-engine/blob/dev/LICENSE.
The License is based on the Mozilla Public License Version 1.1, but Sections 14
and 15 have been added to cover use of software over a computer network and 
provide for limited attribution for the Original Developer. In addition, 
Exhibit A has been modified to be consistent with Exhibit B.

Software distributed under the License is distributed on an "AS IS" basis,
WITHOUT WARRANTY OF ANY KIND, either express or implied. See the License for the
specific language governing rights and limitations under the License.

The Original Code is Infinite Reality Engine.

The Original Developer is the Initial Developer. The Initial Developer of the
Original Code is the Infinite Reality Engine team.

All portions of the code written by the Infinite Reality Engine team are Copyright © 2021-2025
Infinite Reality Engine. All Rights Reserved.
*/

import React, { useState } from 'react'

import {
  ChevronDownMd as ArrowDownIcon,
  ChevronLeftMd as ArrowLeftIcon,
  EmoteM as EmoteIcon
} from '@ir-engine/ui/src/icons'
import { useTranslation } from 'react-i18next'
import { BsChatLeftTextFill as MessageIcon } from 'react-icons/bs'
import { IoSettingsSharp as SettingsIcon } from 'react-icons/io5'
import { RiShareForwardFill as ShareIcon } from 'react-icons/ri'
import { twMerge } from 'tailwind-merge'
import { Badge, BaseBadgeProps } from './Badge'
import { MenuIconButton } from './buttons/MenuIconButton'
import { useChatProvider } from './ChatProvider'
import { EmoteMenu } from './EmoteMenu'
import { useMultimediaStateProvider } from './MultimediaStateProvider'

export const ChatButton = ({ badge, onClick, active }) => {
  return (
    <MenuIconButton badge={badge} onClick={onClick} active={active}>
      <MessageIcon className={'relative top-[0.04em]'} />
    </MenuIconButton>
  )
}

export const MicButton = () => {
  const { onMicClick, _MicIcon, isMicReady } = useMultimediaStateProvider()

  return isMicReady ? (
    <MenuIconButton onClick={onMicClick}>
      <_MicIcon />
    </MenuIconButton>
  ) : (
    <></>
  )
}

export const CamButton = () => {
  const { onCamClick, _CamIcon, isCamReady, isCamLoading } = useMultimediaStateProvider()

  return isCamReady ? (
    <MenuIconButton loading={!!isCamLoading} onClick={onCamClick}>
      <_CamIcon />
    </MenuIconButton>
  ) : (
    <></>
  )
}

export const ScreenshareButton = () => {
  const { onScreenshareClick, _ScreenshareIcon, isScreenshareReady } = useMultimediaStateProvider()

  return isScreenshareReady ? (
    <MenuIconButton onClick={onScreenshareClick}>
      <_ScreenshareIcon />
    </MenuIconButton>
  ) : (
    <></>
  )
}

export const MultiVideoButton = () => {
  const { onMultiVideoClick, _MultiVideoIcon, isMultiVideoReady } = useMultimediaStateProvider()

  return isMultiVideoReady ? (
    <MenuIconButton
      tooltip={{
        title: 'user:menu.cycleCamera',
        position: 'left'
      }}
      onClick={onMultiVideoClick}
    >
      <_MultiVideoIcon />
    </MenuIconButton>
  ) : (
    <></>
  )
}

export const VRButton = () => {
  const { t } = useTranslation()

  const { onVRClick, _VRIcon, isVRReady } = useMultimediaStateProvider()

  return isVRReady ? (
    <MenuIconButton
      tooltip={{
        title: 'user:menu.enterVR',
        position: 'left'
      }}
      onClick={onVRClick}
    >
      <_VRIcon />
    </MenuIconButton>
  ) : (
    <></>
  )
}

export const Divider = () => {
  return (
    <div
      className={`
      relative grid h-full
      w-full items-center
      
      px-2 py-4
      
      sm:max-lg:w-auto
      sm:max-lg:px-5
      sm:max-lg:py-0
    `}
    >
      <div
        className={`
        h-[2px] w-full
        rounded-full
        
        bg-white
        
        sm:max-lg:h-6
        sm:max-lg:w-[2px]
      `}
      />
    </div>
  )
}

export const VerticalMenu = ({ children }) => {
  return (
    <div
      className={`
        visible absolute

        -left-6
        top-1/2
        z-10
        -translate-x-full
        -translate-y-1/2
        sm:max-lg:hidden
      `}
    >
      {children}
    </div>
  )
}

export const HorizontalMenu = ({ children }) => {
  return (
    <div
      className={`
        pointer-events-auto

        absolute left-1/2
        right-auto
        top-6
        z-10
        hidden
        -translate-x-1/2
        
        translate-y-0
        sm:max-lg:block
      `}
    >
      {children}
    </div>
  )
}

export const containerStyles = `
  relative
  
  inline-flex
  
  translate-z
  transform-gpu
  
  rounded-full
  border-y-2 border-white/5
  bg-black/10
  
  shadow-[0_0.1rem_2.3rem_-0.5rem_hsla(0,0%,0%,0.15)]
  backdrop-blur-xl transition-transform

  delay-[60ms]
  hover:scale-[1.03]
`

export const gridStyles_base = `
  relative
  flex items-center

  text-[1.8rem]
  text-white
`

const gridStyles = `
  ${gridStyles_base}
  flex-col
  py-4

  sm:max-lg:flex-row
  sm:max-lg:flex-row-reverse
  sm:max-lg:px-5
  sm:max-lg:py-1
  sm:max-lg:pl-6
`

export const sectionStyles_base = `
  flex items-center
  gap-y-7
  gap-x-7
  py-2
`

const collapsableSectionBaseStyles = twMerge(
  sectionStyles_base,
  `
    inline-flex origin-bottom flex-col
    items-center
    px-3

    sm:max-lg:origin-left
    sm:max-lg:flex-row
    sm:max-lg:px-0
  `
)

const collapsableSectionOpenStyles = twMerge(collapsableSectionBaseStyles, ``)

const collapsableSectionCloseStyles = twMerge(
  collapsableSectionBaseStyles,
  `
    hidden
  `
)

const arrowButtonStyles = `
  mb-1

  sm:max-lg:ml-3
  sm:max-lg:-mb-2
`

export const ToolbarMenu = ({ onMessageClick, onShareClick, onSettingsClick, activePath }) => {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const { unreadMessages } = useChatProvider()
  const showMessagesBadge = unreadMessages.value
  const emoteBadgeNumber = 0
  const showEmoteBadge = !!emoteBadgeNumber
  const showBadge = showMessagesBadge && showEmoteBadge
  const [isEmoteMenuOpen, setIsEmoteMenuOpen] = useState(false)

  const showFirstDivider = isMenuOpen && !isEmoteMenuOpen
  const showSecondDivider = isEmoteMenuOpen

  const isMultiMediaOpen = !isEmoteMenuOpen
  const isMainMenuOpen = isMenuOpen && !isEmoteMenuOpen
  const isDefaultArrowDirection = !!isMainMenuOpen

  const handleToggleMenu = () => setIsMenuOpen((prev) => !prev)
  const handleOpenEmoteMenu = () => setIsEmoteMenuOpen(true)
  const handleCloseEmoteMenu = () => setIsEmoteMenuOpen(false)
  const onArrowClick = isEmoteMenuOpen ? handleCloseEmoteMenu : handleToggleMenu

  return (
    <div className={containerStyles}>
      <div className={gridStyles}>
        <Badge show={showBadge} />

        <MenuIconButton className={isEmoteMenuOpen ? `` : arrowButtonStyles} onClick={onArrowClick}>
          <ArrowDownIcon
            className={twMerge(isDefaultArrowDirection ? `scale-[1.2]` : `-scale-[1.2]`, 'sm:max-lg:hidden')}
          />
          <ArrowLeftIcon
            className={twMerge(isDefaultArrowDirection ? `scale-[1.2]` : `-scale-[1.2]`, 'hidden sm:max-lg:block')}
          />
        </MenuIconButton>

        <div
          className={twMerge(
            collapsableSectionBaseStyles,
            isMainMenuOpen ? collapsableSectionOpenStyles : collapsableSectionCloseStyles
          )}
        >
          <MenuIconButton onClick={onSettingsClick}>
            <SettingsIcon />
          </MenuIconButton>
          <MenuIconButton onClick={onShareClick}>
            <ShareIcon />
          </MenuIconButton>
          <MenuIconButton
            onClick={handleOpenEmoteMenu}
            badge={
              {
                show: showEmoteBadge,
                number: emoteBadgeNumber
              } as BaseBadgeProps
            }
          >
            <EmoteIcon className={'relative'} />
          </MenuIconButton>
        </div>

        {showFirstDivider && <Divider />}

        <div className={isMultiMediaOpen ? collapsableSectionOpenStyles : collapsableSectionCloseStyles}>
          <ChatButton badge={{ show: showMessagesBadge }} active={activePath === `chat`} onClick={onMessageClick} />
          <MicButton />
          <CamButton />
          <MultiVideoButton />
          <ScreenshareButton />
          <VRButton />
        </div>

        {showSecondDivider && <Divider />}

        <div className={isEmoteMenuOpen ? collapsableSectionOpenStyles : collapsableSectionCloseStyles}>
          <EmoteMenu />
        </div>
      </div>
    </div>
  )
}
