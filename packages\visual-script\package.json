{"name": "@ir-engine/visual-script", "version": "1.0.3", "main": "index.ts", "publishConfig": {"access": "public"}, "description": "Extendable visual script system for the Infinite Reality Engine", "scripts": {"check-errors": "tsc --noemit && npx cycle-import-check src || true", "test": "cross-env TEST=true vitest run --config=../../vitest.client.config.ts", "test-coverage": "npm run test-coverage-generate ; npm run test-coverage-launch", "test-coverage-generate": "npm run test -- --coverage --silent", "test-coverage-launch": "vite preview --open --outDir coverage", "validate": "npm run test", "generate-doc": "node_modules/.bin/typedoc"}, "repository": "http://github.com/ir-engine/ir-engine", "author": "Infinite Reality Engine", "license": "CPAL", "dependencies": {"@ir-engine/hyperflux": "^1.0.3", "lodash": "4.17.21", "three": "0.176.0", "ts-matches": "5.3.0", "ajv": "^8.11.2", "rambdax": "9.0.0"}, "devDependencies": {"@types/offscreencanvas": "2019.7.0", "@types/sinon": "10.0.13", "@types/three": "0.176.0", "@types/webxr": "0.5.13", "sinon": "15.0.2", "trace-unhandled": "2.0.1", "typedoc": "0.23.28"}}