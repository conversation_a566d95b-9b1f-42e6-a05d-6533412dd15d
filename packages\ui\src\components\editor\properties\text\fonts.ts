/*
CPAL-1.0 License

The contents of this file are subject to the Common Public Attribution License
Version 1.0. (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at
https://github.com/ir-engine/ir-engine/blob/dev/LICENSE.
The License is based on the Mozilla Public License Version 1.1, but Sections 14
and 15 have been added to cover use of software over a computer network and 
provide for limited attribution for the Original Developer. In addition, 
Exhibit A has been modified to be consistent with Exhibit B.

Software distributed under the License is distributed on an "AS IS" basis,
WITHOUT WARRANTY OF ANY KIND, either express or implied. See the License for the
specific language governing rights and limitations under the License.

The Original Code is Infinite Reality Engine.

The Original Developer is the Initial Developer. The Initial Developer of the
Original Code is the Infinite Reality Engine team.

All portions of the code written by the Infinite Reality Engine team are Copyright © 2021-2025
Infinite Reality Engine. All Rights Reserved.
*/

export interface FontOption {
  label: string
  value: string
}

export const fonts: FontOption[] = [
  {
    label: '<PERSON>ee<PERSON><PERSON>',
    value: 'https://fonts.gstatic.com/s/abeezee/v22/esDR31xSG-6AGleN6tKukbcHCpE.ttf'
  },
  {
    label: 'ADLaM Display',
    value: 'https://fonts.gstatic.com/s/adlamdisplay/v1/KFOhCnGXkPOLlhx6jD8_b1ZECsHYkYBPY3o.ttf'
  },
  {
    label: 'AR One Sans',
    value:
      'https://fonts.gstatic.com/s/aronesans/v4/TUZezwhrmbFp0Srr_tH6fv6RcUejHO_u7GF5aXfv-U2QzBLF6gslWn_9DW03no5mBF4.ttf'
  },
  {
    label: 'Abel',
    value: 'https://fonts.gstatic.com/s/abel/v18/MwQ5bhbm2POE6VhLPJp6qGI.ttf'
  },
  {
    label: 'Abhaya Libre',
    value: 'https://fonts.gstatic.com/s/abhayalibre/v17/e3tmeuGtX-Co5MNzeAOqinEge0PWovdU4w.ttf'
  },
  {
    label: 'Aboreto',
    value: 'https://fonts.gstatic.com/s/aboreto/v2/5DCXAKLhwDDQ4N8blKTeA2yuxSY.ttf'
  },
  {
    label: 'Abril Fatface',
    value: 'https://fonts.gstatic.com/s/abrilfatface/v23/zOL64pLDlL1D99S8g8PtiKchm-BsjOLhZBY.ttf'
  },
  {
    label: 'Abyssinica SIL',
    value: 'https://fonts.gstatic.com/s/abyssinicasil/v5/oY1H8ezOqK7iI3rK_45WKoc8J6UZBFOVAXuI.ttf'
  },
  {
    label: 'Aclonica',
    value: 'https://fonts.gstatic.com/s/aclonica/v22/K2FyfZJVlfNNSEBXGb7TCI6oBjLz.ttf'
  },
  {
    label: 'Acme',
    value: 'https://fonts.gstatic.com/s/acme/v25/RrQfboBx-C5_bx3Lb23lzLk.ttf'
  },
  {
    label: 'Actor',
    value: 'https://fonts.gstatic.com/s/actor/v17/wEOzEBbCkc5cO3ekXygtUMIO.ttf'
  },
  {
    label: 'Adamina',
    value: 'https://fonts.gstatic.com/s/adamina/v21/j8_r6-DH1bjoc-dwu-reETl4Bno.ttf'
  },
  {
    label: 'Advent Pro',
    value:
      'https://fonts.gstatic.com/s/adventpro/v28/V8mqoQfxVT4Dvddr_yOwrzaFxV7JtdQgFqXdUAQrGp_zgX5sWCpLQyNPTJoonw1aBA.ttf'
  },
  {
    label: 'Afacad',
    value: 'https://fonts.gstatic.com/s/afacad/v1/6NUK8FKMIQOGaw6wjYT7ZHG_zsBBfhXtWmA08mCgdfM.ttf'
  },
  {
    label: 'Agbalumo',
    value: 'https://fonts.gstatic.com/s/agbalumo/v5/55xvey5uMdT2N37KZcMFirl08KDJ.ttf'
  },
  {
    label: 'Agdasima',
    value: 'https://fonts.gstatic.com/s/agdasima/v4/PN_zRfyxp2f1fUCgAMg6rzjb_-Da.ttf'
  },
  {
    label: 'Aguafina Script',
    value: 'https://fonts.gstatic.com/s/aguafinascript/v22/If2QXTv_ZzSxGIO30LemWEOmt1bHqs4pgicOrg.ttf'
  },
  {
    label: 'Akatab',
    value: 'https://fonts.gstatic.com/s/akatab/v7/VuJwdNrK3Z7gqJEPWIz5NIh-YA.ttf'
  },
  {
    label: 'Akaya Kanadaka',
    value: 'https://fonts.gstatic.com/s/akayakanadaka/v16/N0bM2S5CPO5oOQqvazoRRb-8-PfRS5VBBSSF.ttf'
  },
  {
    label: 'Akaya Telivigala',
    value: 'https://fonts.gstatic.com/s/akayatelivigala/v22/lJwc-oo_iG9wXqU3rCTD395tp0uifdLdsIH0YH8.ttf'
  },
  {
    label: 'Akronim',
    value: 'https://fonts.gstatic.com/s/akronim/v23/fdN-9sqWtWZZlHRp-gBxkFYN-a8.ttf'
  },
  {
    label: 'Akshar',
    value: 'https://fonts.gstatic.com/s/akshar/v9/Yq6I-LyHWTfz9rGoqDaUbHvhkAUsSXYFy9CY94XsnPc.ttf'
  },
  {
    label: 'Aladin',
    value: 'https://fonts.gstatic.com/s/aladin/v24/ZgNSjPJFPrvJV5f16Sf4pGT2Ng.ttf'
  },
  {
    label: 'Alata',
    value: 'https://fonts.gstatic.com/s/alata/v10/PbytFmztEwbIofe6xKcRQEOX.ttf'
  },
  {
    label: 'Alatsi',
    value: 'https://fonts.gstatic.com/s/alatsi/v13/TK3iWkUJAxQ2nLNGHjUHte5fKg.ttf'
  },
  {
    label: 'Albert Sans',
    value: 'https://fonts.gstatic.com/s/albertsans/v1/i7dZIFdwYjGaAMFtZd_QA3xXSKZqhr-TenSHq5P_rI32TxAj1g.ttf'
  },
  {
    label: 'Aldrich',
    value: 'https://fonts.gstatic.com/s/aldrich/v21/MCoTzAn-1s3IGyJMZaAS3pP5H_E.ttf'
  },
  {
    label: 'Alef',
    value: 'https://fonts.gstatic.com/s/alef/v21/FeVfS0NQpLYgrjJbC5FxxbU.ttf'
  },
  {
    label: 'Alegreya',
    value: 'https://fonts.gstatic.com/s/alegreya/v35/4UacrEBBsBhlBjvfkQjt71kZfyBzPgNG9hUI_KCisSGVrw.ttf'
  },
  {
    label: 'Alegreya SC',
    value: 'https://fonts.gstatic.com/s/alegreyasc/v25/taiOGmRtCJ62-O0HhNEa-a6o05E5abe_.ttf'
  },
  {
    label: 'Alegreya Sans',
    value: 'https://fonts.gstatic.com/s/alegreyasans/v24/5aUz9_-1phKLFgshYDvh6Vwt3V1nvEVXlm4.ttf'
  },
  {
    label: 'Alegreya Sans SC',
    value: 'https://fonts.gstatic.com/s/alegreyasanssc/v23/mtGh4-RGJqfMvt7P8FUr0Q1j-Hf1Nk5v9ixALYs.ttf'
  },
  {
    label: 'Aleo',
    value: 'https://fonts.gstatic.com/s/aleo/v14/c4m61nF8G8_s6gHhIOX0IYBo_KJ3GmP9HI4qCBtJ.ttf'
  },
  {
    label: 'Alex Brush',
    value: 'https://fonts.gstatic.com/s/alexbrush/v22/SZc83FzrJKuqFbwMKk6EtUL57DtOmCc.ttf'
  },
  {
    label: 'Alexandria',
    value: 'https://fonts.gstatic.com/s/alexandria/v3/UMBCrPdDqW66y0Y2usFeQCH18mulUxBvI9r7TqbHHJ8BRq0b.ttf'
  },
  {
    label: 'Alfa Slab One',
    value: 'https://fonts.gstatic.com/s/alfaslabone/v19/6NUQ8FmMKwSEKjnm5-4v-4Jh6dVretWvYmE.ttf'
  },
  {
    label: 'Alice',
    value: 'https://fonts.gstatic.com/s/alice/v20/OpNCnoEEmtHa6FcJpA_chzJ0.ttf'
  },
  {
    label: 'Alike',
    value: 'https://fonts.gstatic.com/s/alike/v21/HI_EiYEYI6BIoEjBSZXAQ4-d.ttf'
  },
  {
    label: 'Alike Angular',
    value: 'https://fonts.gstatic.com/s/alikeangular/v25/3qTrojWunjGQtEBlIcwMbSoI3kM6bB7FKjE.ttf'
  },
  {
    label: 'Alkalami',
    value: 'https://fonts.gstatic.com/s/alkalami/v7/zOL_4pfDmqRL95WXi5eLw8BMuvhH.ttf'
  },
  {
    label: 'Alkatra',
    value: 'https://fonts.gstatic.com/s/alkatra/v3/r05EGLZA5qhCYsyJbuChFuK48Medzngmu7cPrNDVemxE.ttf'
  },
  {
    label: 'Allan',
    value: 'https://fonts.gstatic.com/s/allan/v24/ea8XadU7WuTxEtb2P9SF8nZE.ttf'
  },
  {
    label: 'Allerta',
    value: 'https://fonts.gstatic.com/s/allerta/v18/TwMO-IAHRlkbx940UnEdSQqO5uY.ttf'
  },
  {
    label: 'Allerta Stencil',
    value: 'https://fonts.gstatic.com/s/allertastencil/v22/HTx0L209KT-LmIE9N7OR6eiycOeF-zz313DuvQ.ttf'
  },
  {
    label: 'Allison',
    value: 'https://fonts.gstatic.com/s/allison/v11/X7nl4b88AP2nkbvZOCaQ4MTgAgk.ttf'
  },
  {
    label: 'Allura',
    value: 'https://fonts.gstatic.com/s/allura/v21/9oRPNYsQpS4zjuAPjAIXPtrrGA.ttf'
  },
  {
    label: 'Almarai',
    value: 'https://fonts.gstatic.com/s/almarai/v12/tsstApxBaigK_hnnc1qPonC3vqc.ttf'
  },
  {
    label: 'Almendra',
    value: 'https://fonts.gstatic.com/s/almendra/v26/H4ckBXKAlMnTn0CskyY6wr-wg763.ttf'
  },
  {
    label: 'Almendra Display',
    value: 'https://fonts.gstatic.com/s/almendradisplay/v31/0FlPVOGWl1Sb4O3tETtADHRRlZhzXS_eTyer338.ttf'
  },
  {
    label: 'Almendra SC',
    value: 'https://fonts.gstatic.com/s/almendrasc/v29/Iure6Yx284eebowr7hbyTZZJprVA4XQ0.ttf'
  },
  {
    label: 'Alumni Sans',
    value: 'https://fonts.gstatic.com/s/alumnisans/v18/nwpHtKqkOwdO2aOIwhWudEWpx_zq_Xna-Xd9OO9QqFsJ3C8qng.ttf'
  },
  {
    label: 'Alumni Sans Collegiate One',
    value:
      'https://fonts.gstatic.com/s/alumnisanscollegiateone/v5/MQpB-XChK8G5CtmK_AuGxQrdNvPSXkn0RM-XqjWWhjdayDiPw2ta.ttf'
  },
  {
    label: 'Alumni Sans Inline One',
    value: 'https://fonts.gstatic.com/s/alumnisansinlineone/v5/RrQBbpJx9zZ3IXTBOASKp5gJAetBdaihcjbpD3AZcr7xbYw.ttf'
  },
  {
    label: 'Alumni Sans Pinstripe',
    value: 'https://fonts.gstatic.com/s/alumnisanspinstripe/v6/ZgNNjOFFPq_AUJD1umyS30W-Xub8zD1ObhezYrVIpcDA5w.ttf'
  },
  {
    label: 'Amarante',
    value: 'https://fonts.gstatic.com/s/amarante/v28/xMQXuF1KTa6EvGx9bq-3C3rAmD-b.ttf'
  },
  {
    label: 'Amaranth',
    value: 'https://fonts.gstatic.com/s/amaranth/v18/KtkuALODe433f0j1zPnCF9GqwnzW.ttf'
  },
  {
    label: 'Amatic SC',
    value: 'https://fonts.gstatic.com/s/amaticsc/v26/TUZyzwprpvBS1izr_vO0De6ecZQf1A.ttf'
  },
  {
    label: 'Amethysta',
    value: 'https://fonts.gstatic.com/s/amethysta/v16/rP2Fp2K15kgb_F3ibfWIGDWCBl0O8Q.ttf'
  },
  {
    label: 'Amiko',
    value: 'https://fonts.gstatic.com/s/amiko/v14/WwkQxPq1DFK04tqlc17MMZgJ.ttf'
  },
  {
    label: 'Amiri',
    value: 'https://fonts.gstatic.com/s/amiri/v27/J7aRnpd8CGxBHqUpvrIw74NL.ttf'
  },
  {
    label: 'Amiri Quran',
    value: 'https://fonts.gstatic.com/s/amiriquran/v14/_Xmo-Hk0rD6DbUL4_vH8Zq5t7Cycsu-2.ttf'
  },
  {
    label: 'Amita',
    value: 'https://fonts.gstatic.com/s/amita/v18/HhyaU5si9Om7PQlvAfSKEZZL.ttf'
  },
  {
    label: 'Anaheim',
    value: 'https://fonts.gstatic.com/s/anaheim/v15/8vIX7w042Wp87g4Gy0_24JbCiPrl-h5sLqrFIkJQb7zU.ttf'
  },
  {
    label: 'Andada Pro',
    value: 'https://fonts.gstatic.com/s/andadapro/v20/HhyEU5Qi9-SuOEhPe4LtKoVCuWGURPcg3DPJBY8cFLzvIt2S.ttf'
  },
  {
    label: 'Andika',
    value: 'https://fonts.gstatic.com/s/andika/v25/mem_Ya6iyW-LwqgAbbwRWrwGVA.ttf'
  },
  {
    label: 'Anek Bangla',
    value:
      'https://fonts.gstatic.com/s/anekbangla/v5/_gPW1R38qTExHg-17BhM6n66QhabMYB0fBKONtHhRSIUIre5mq3Ofm9ZIocg56yyvt0.ttf'
  },
  {
    label: 'Anek Devanagari',
    value:
      'https://fonts.gstatic.com/s/anekdevanagari/v8/jVyo7nP0CGrUsxB-QiRgw0NlLaVt_QUAkYxLRoCL23mlh20ZVHOMAWbgHLDtku9nFk0LjZ7E.ttf'
  },
  {
    label: 'Anek Gujarati',
    value:
      'https://fonts.gstatic.com/s/anekgujarati/v9/l7g_bj5oysqknvkCo2T_8FuiIRBA7lncQUmbIBEtPKiYYQhRwyBxCD-0F5C7w0KgB7Lm7g.ttf'
  },
  {
    label: 'Anek Gurmukhi',
    value:
      'https://fonts.gstatic.com/s/anekgurmukhi/v8/0QIAMXRO_YSkA0quVLY79JnHybfeEOrXCa9Dmd9Ql6a6R_vEMc5TaLkbd5tpXK41H6DjbA.ttf'
  },
  {
    label: 'Anek Kannada',
    value:
      'https://fonts.gstatic.com/s/anekkannada/v5/raxcHiCNvNMKe1CKFsINYFlgkEIwGa8nL6ruWJg1j--h8pvBKSiw4dFDEQukVReA1oef.ttf'
  },
  {
    label: 'Anek Latin',
    value:
      'https://fonts.gstatic.com/s/aneklatin/v5/co3pmWZulTRoU4a8dqrWiajBS5ByUkvdrluH-xWG5uJTY4x-L3PuR7AZKdClWL3kgw.ttf'
  },
  {
    label: 'Anek Malayalam',
    value:
      'https://fonts.gstatic.com/s/anekmalayalam/v6/6qLjKZActRTs_mZAJUZWWkhke0nYa_vC8_Azq3-gP1SReZeOtqQuDVUTUZu-HMr5PDO71Qs.ttf'
  },
  {
    label: 'Anek Odia',
    value:
      'https://fonts.gstatic.com/s/anekodia/v6/TK3PWkoJARApz5UCd345tuevwwQX0CwsoYkAWgWYevAauivBUnmZfq3mXZAtm_es.ttf'
  },
  {
    label: 'Anek Tamil',
    value:
      'https://fonts.gstatic.com/s/anektamil/v9/XLYJIZH2bYJHGYtPGSbUB8JKTp-_9n55SsLHW0WZez6TjtkDu3uNQid6q4v4oegjOQ.ttf'
  },
  {
    label: 'Anek Telugu',
    value:
      'https://fonts.gstatic.com/s/anektelugu/v8/LhWLMVrUNvsddMtYGCx4FcVWOjlwE1WgXdoJ-5XHMl2DkooGK7i13y--oE2G2ep10_8.ttf'
  },
  {
    label: 'Angkor',
    value: 'https://fonts.gstatic.com/s/angkor/v32/H4cmBXyAlsPdnlb-8iw-4Lqggw.ttf'
  },
  {
    label: 'Annapurna SIL',
    value: 'https://fonts.gstatic.com/s/annapurnasil/v2/yYLv0hDY0f2iu9tPmRWtllid8NN9dZT_PZs.ttf'
  },
  {
    label: 'Annie Use Your Telescope',
    value:
      'https://fonts.gstatic.com/s/annieuseyourtelescope/v18/daaLSS4tI2qYYl3Jq9s_Hu74xwktnlKxH6osGVGjlDfB3UUVZA.ttf'
  },
  {
    label: 'Anonymous Pro',
    value: 'https://fonts.gstatic.com/s/anonymouspro/v21/rP2Bp2a15UIB7Un-bOeISG3pLlw89CH98Ko.ttf'
  },
  {
    label: 'Anta',
    value: 'https://fonts.gstatic.com/s/anta/v1/gyBzhwQ3KsIyZFwxPFimIo0.ttf'
  },
  {
    label: 'Antic',
    value: 'https://fonts.gstatic.com/s/antic/v19/TuGfUVB8XY5DRaZLodgzydtk.ttf'
  },
  {
    label: 'Antic Didone',
    value: 'https://fonts.gstatic.com/s/anticdidone/v16/RWmPoKKX6u8sp8fIWdnDKqDiqYsGBGBzCw.ttf'
  },
  {
    label: 'Antic Slab',
    value: 'https://fonts.gstatic.com/s/anticslab/v16/bWt97fPFfRzkCa9Jlp6IWcJWXW5p5Qo.ttf'
  },
  {
    label: 'Anton',
    value: 'https://fonts.gstatic.com/s/anton/v25/1Ptgg87LROyAm0K08i4gS7lu.ttf'
  },
  {
    label: 'Anton SC',
    value: 'https://fonts.gstatic.com/s/antonsc/v1/4UaBrEBBsgltGn71sxLmzanB44N1.ttf'
  },
  {
    label: 'Antonio',
    value: 'https://fonts.gstatic.com/s/antonio/v19/gNMbW3NwSYq_9WD34ngK5F8vR8T0PVxx8RtIY2DwSXlM.ttf'
  },
  {
    label: 'Anuphan',
    value: 'https://fonts.gstatic.com/s/anuphan/v3/2sDBZGxYgY7LkLT0s2Yrm5UhuLoIZCkY9Q4kGmW927Gu.ttf'
  },
  {
    label: 'Anybody',
    value: 'https://fonts.gstatic.com/s/anybody/v11/VuJbdNvK2Ib2ppdWYq311GH32hxIv0sd5grncSUi2F_Wim4J12DPrsXD_nBPpQ.ttf'
  },
  {
    label: 'Aoboshi One',
    value: 'https://fonts.gstatic.com/s/aoboshione/v10/Gg8xN5kXaAXtHQrFxwl10ysLBmZX_UEg.ttf'
  },
  {
    label: 'Arapey',
    value: 'https://fonts.gstatic.com/s/arapey/v16/-W__XJn-UDDA2RC6Z9AcZkIzeg.ttf'
  },
  {
    label: 'Arbutus',
    value: 'https://fonts.gstatic.com/s/arbutus/v28/NaPYcZ7dG_5J3poob9JtryO8fMU.ttf'
  },
  {
    label: 'Arbutus Slab',
    value: 'https://fonts.gstatic.com/s/arbutusslab/v16/oY1Z8e7OuLXkJGbXtr5ba7ZVa68dJlaFAQ.ttf'
  },
  {
    label: 'Architects Daughter',
    value: 'https://fonts.gstatic.com/s/architectsdaughter/v18/KtkxAKiDZI_td1Lkx62xHZHDtgO_Y-bvfY5q4szgE-Q.ttf'
  },
  {
    label: 'Archivo',
    value: 'https://fonts.gstatic.com/s/archivo/v19/k3k6o8UDI-1M0wlSV9XAw6lQkqWY8Q82sJaRE-NWIDdgffTTNDNp8B1oJ0vyVQ.ttf'
  },
  {
    label: 'Archivo Black',
    value: 'https://fonts.gstatic.com/s/archivoblack/v21/HTxqL289NzCGg4MzN6KJ7eW6OYuP_x7yx3A.ttf'
  },
  {
    label: 'Archivo Narrow',
    value: 'https://fonts.gstatic.com/s/archivonarrow/v30/tss5ApVBdCYD5Q7hcxTE1ArZ0Zz8oY2KRmwvKhhvLFGKpHOtFCQ76Q.ttf'
  },
  {
    label: 'Are You Serious',
    value: 'https://fonts.gstatic.com/s/areyouserious/v12/ll8kK2GVSSr-PtjQ5nONVcNn4306hT9nCGRayg.ttf'
  },
  {
    label: 'Aref Ruqaa',
    value: 'https://fonts.gstatic.com/s/arefruqaa/v25/WwkbxPW1E165rajQKDulEIAiVNo5xNY.ttf'
  },
  {
    label: 'Aref Ruqaa Ink',
    value: 'https://fonts.gstatic.com/s/arefruqaaink/v10/1q2fY5WOGUFlt84GTOkP6Kdx72ThVIGpgnxL.ttf'
  },
  {
    label: 'Arima',
    value: 'https://fonts.gstatic.com/s/arima/v5/neIWzCqmt4Aup_qE1nFWqxI1RZX1YTA-pQGOyYw2fw.ttf'
  },
  {
    label: 'Arimo',
    value: 'https://fonts.gstatic.com/s/arimo/v29/P5sfzZCDf9_T_3cV7NCUECyoxNk37cxsBxDAVQI4aA.ttf'
  },
  {
    label: 'Arizonia',
    value: 'https://fonts.gstatic.com/s/arizonia/v21/neIIzCemt4A5qa7mv6WGHK06UY30.ttf'
  },
  {
    label: 'Armata',
    value: 'https://fonts.gstatic.com/s/armata/v20/gokvH63_HV5jQ-E9lD53Q2u_mQ.ttf'
  },
  {
    label: 'Arsenal',
    value: 'https://fonts.gstatic.com/s/arsenal/v12/wXKrE3kQtZQ4pF3D11_WAewrhXY.ttf'
  },
  {
    label: 'Arsenal SC',
    value: 'https://fonts.gstatic.com/s/arsenalsc/v1/x3dlckLHea6e5BEtsfxiXNossybsHQI.ttf'
  },
  {
    label: 'Artifika',
    value: 'https://fonts.gstatic.com/s/artifika/v21/VEMyRoxzronptCuxu6Wt5jDtreOL.ttf'
  },
  {
    label: 'Arvo',
    value: 'https://fonts.gstatic.com/s/arvo/v22/tDbD2oWUg0MKmSAa7Lzr7vs.ttf'
  },
  {
    label: 'Arya',
    value: 'https://fonts.gstatic.com/s/arya/v19/ga6CawNG-HJd9Ub1-beqdFE.ttf'
  },
  {
    label: 'Asap',
    value: 'https://fonts.gstatic.com/s/asap/v30/KFOOCniXp96a4Tc2DaTeuDAoKsE617JFc49knOIYdjTYkqUsLmOXoA7Glw.ttf'
  },
  {
    label: 'Asap Condensed',
    value: 'https://fonts.gstatic.com/s/asapcondensed/v17/pxidypY1o9NHyXh3WvSbGSggdNeLYk1Mq3ap.ttf'
  },
  {
    label: 'Asar',
    value: 'https://fonts.gstatic.com/s/asar/v22/sZlLdRyI6TBIXkYQDLlTW6E.ttf'
  },
  {
    label: 'Asset',
    value: 'https://fonts.gstatic.com/s/asset/v29/SLXGc1na-mM4cWImRJqExst1.ttf'
  },
  {
    label: 'Assistant',
    value: 'https://fonts.gstatic.com/s/assistant/v19/2sDPZGJYnIjSi6H75xkZZE1I0yCmYzzQtuZnEGGf3qGuvM4.ttf'
  },
  {
    label: 'Astloch',
    value: 'https://fonts.gstatic.com/s/astloch/v26/TuGRUVJ8QI5GSeUjq9wRzMtkH1Q.ttf'
  },
  {
    label: 'Asul',
    value: 'https://fonts.gstatic.com/s/asul/v21/VuJ-dNjKxYr46fMFXK78JIg.ttf'
  },
  {
    label: 'Athiti',
    value: 'https://fonts.gstatic.com/s/athiti/v12/pe0vMISdLIZIv1w4DBhWCtaiAg.ttf'
  },
  {
    label: 'Atkinson Hyperlegible',
    value: 'https://fonts.gstatic.com/s/atkinsonhyperlegible/v11/9Bt23C1KxNDXMspQ1lPyU89-1h6ONRlW45GE5ZgpewSSbQ.ttf'
  },
  {
    label: 'Atma',
    value: 'https://fonts.gstatic.com/s/atma/v16/uK_84rqWc-Eom25bDj8WIv4.ttf'
  },
  {
    label: 'Atomic Age',
    value: 'https://fonts.gstatic.com/s/atomicage/v27/f0Xz0eug6sdmRFkYZZGL58Ht9a8GYeA.ttf'
  },
  {
    label: 'Aubrey',
    value: 'https://fonts.gstatic.com/s/aubrey/v28/q5uGsou7NPBw-p7vugNsCxVEgA.ttf'
  },
  {
    label: 'Audiowide',
    value: 'https://fonts.gstatic.com/s/audiowide/v20/l7gdbjpo0cum0ckerWCtkQXPExpQBw.ttf'
  },
  {
    label: 'Autour One',
    value: 'https://fonts.gstatic.com/s/autourone/v24/UqyVK80cP25l3fJgbdfbk5lWVscxdKE.ttf'
  },
  {
    label: 'Average',
    value: 'https://fonts.gstatic.com/s/average/v18/fC1hPYBHe23MxA7rIeJwVWytTyk.ttf'
  },
  {
    label: 'Average Sans',
    value: 'https://fonts.gstatic.com/s/averagesans/v16/1Ptpg8fLXP2dlAXR-HlJJNJPBdqazVoK4A.ttf'
  },
  {
    label: 'Averia Gruesa Libre',
    value: 'https://fonts.gstatic.com/s/averiagruesalibre/v22/NGSov4nEGEktOaDRKsY-1dhh8eEtIx3ZUmmJw0SLRA8.ttf'
  },
  {
    label: 'Averia Libre',
    value: 'https://fonts.gstatic.com/s/averialibre/v16/2V0aKIcMGZEnV6xygz7eNjEiAqPJZ2Xx8w.ttf'
  },
  {
    label: 'Averia Sans Libre',
    value: 'https://fonts.gstatic.com/s/averiasanslibre/v19/ga6XaxZG_G5OvCf_rt7FH3B6BHLMEeVJGIMYDo_8.ttf'
  },
  {
    label: 'Averia Serif Libre',
    value: 'https://fonts.gstatic.com/s/averiaseriflibre/v18/neIWzD2ms4wxr6GvjeD0X88SHPyX2xY-pQGOyYw2fw.ttf'
  },
  {
    label: 'Azeret Mono',
    value: 'https://fonts.gstatic.com/s/azeretmono/v17/3XF5ErsiyJsY9O_Gepph-FvtTQgMQUdNekSfnPVh0raa-5s3AA.ttf'
  },
  {
    label: 'B612',
    value: 'https://fonts.gstatic.com/s/b612/v12/3JnySDDxiSz32jm4GDigUXw.ttf'
  },
  {
    label: 'B612 Mono',
    value: 'https://fonts.gstatic.com/s/b612mono/v14/kmK_Zq85QVWbN1eW6lJl1wTcquRTtg.ttf'
  },
  {
    label: 'BIZ UDGothic',
    value: 'https://fonts.gstatic.com/s/bizudgothic/v9/daafSTouBF7RUjnbt8p3LuKttQN98z_MbQ.ttf'
  },
  {
    label: 'BIZ UDMincho',
    value: 'https://fonts.gstatic.com/s/bizudmincho/v9/EJRRQgI6eOxFjBdKs38yhtW1dwT7rcpY8Q.ttf'
  },
  {
    label: 'BIZ UDPGothic',
    value: 'https://fonts.gstatic.com/s/bizudpgothic/v10/hES36X5pHAIBjmS84VL0Bue83nUMQWkMUAk.ttf'
  },
  {
    label: 'BIZ UDPMincho',
    value: 'https://fonts.gstatic.com/s/bizudpmincho/v9/ypvfbXOBrmYppy7oWWTg1_58nhhYtUb0gZk.ttf'
  },
  {
    label: 'Babylonica',
    value: 'https://fonts.gstatic.com/s/babylonica/v5/5aUw9_i2qxWVCAE2aHjTqDJ0-VVMoEw.ttf'
  },
  {
    label: 'Bacasime Antique',
    value: 'https://fonts.gstatic.com/s/bacasimeantique/v1/tDbX2pGXkFYEykldjZSrmI6T_XWZOwStSUrV_BE.ttf'
  },
  {
    label: 'Bad Script',
    value: 'https://fonts.gstatic.com/s/badscript/v16/6NUT8F6PJgbFWQn47_x7lOwuzd1AZtw.ttf'
  },
  {
    label: 'Bagel Fat One',
    value: 'https://fonts.gstatic.com/s/bagelfatone/v1/hYkPPucsQOr5dy02WmQr5Zkd0B5mvv0dSbM.ttf'
  },
  {
    label: 'Bahiana',
    value: 'https://fonts.gstatic.com/s/bahiana/v23/uU9PCBUV4YenPWJU7xPb3vyHmlI.ttf'
  },
  {
    label: 'Bahianita',
    value: 'https://fonts.gstatic.com/s/bahianita/v21/yYLr0hTb3vuqqsBUgxWtxTvV2NJPcA.ttf'
  },
  {
    label: 'Bai Jamjuree',
    value: 'https://fonts.gstatic.com/s/baijamjuree/v11/LDI1apSCOBt_aeQQ7ftydoaMWcjKm7sp8g.ttf'
  },
  {
    label: 'Bakbak One',
    value: 'https://fonts.gstatic.com/s/bakbakone/v8/zOL54pXAl6RI-p_ardnuycRuv-hHkOs.ttf'
  },
  {
    label: 'Ballet',
    value: 'https://fonts.gstatic.com/s/ballet/v27/QGYyz_MYZA-HM4NjuGOVnUEXme1I4Xi3C4G-EiAou6Y.ttf'
  },
  {
    label: 'Baloo 2',
    value: 'https://fonts.gstatic.com/s/baloo2/v21/wXK0E3kTposypRydzVT08TS3JnAmtdgazapv9Fat7WcN.ttf'
  },
  {
    label: 'Baloo Bhai 2',
    value: 'https://fonts.gstatic.com/s/baloobhai2/v28/sZlWdRSL-z1VEWZ4YNA7Y5ItevYWUOHDE8FvNighMXeCo-jsZzo.ttf'
  },
  {
    label: 'Baloo Bhaijaan 2',
    value: 'https://fonts.gstatic.com/s/baloobhaijaan2/v19/zYXwKUwuEqdVGqM8tPDdAA_Y-_bMKo1EhQd2tWxo8TyRSqP4L4ppfcyC.ttf'
  },
  {
    label: 'Baloo Bhaina 2',
    value: 'https://fonts.gstatic.com/s/baloobhaina2/v27/qWc-B6yyq4P9Adr3RtoX1q6ySgbwusXwJjkOS-XEssPvRfRLYWmZSA.ttf'
  },
  {
    label: 'Baloo Chettan 2',
    value: 'https://fonts.gstatic.com/s/baloochettan2/v21/vm8hdRbmXEva26PK-NtuX4ynWEzF69-L4gqgkIL5CeKTO1oeH9xI2gc.ttf'
  },
  {
    label: 'Baloo Da 2',
    value: 'https://fonts.gstatic.com/s/balooda2/v24/2-c39J9j0IaUMQZwAJyJaOX1UUnf3GLnYjALsTNe55aRa7UE.ttf'
  },
  {
    label: 'Baloo Paaji 2',
    value: 'https://fonts.gstatic.com/s/baloopaaji2/v27/i7dfIFFzbz-QHZUdV9_UGWZuelmy79QJ1HOSY9AX74fybRUz1r5t.ttf'
  },
  {
    label: 'Baloo Tamma 2',
    value: 'https://fonts.gstatic.com/s/balootamma2/v16/vEFE2_hCAgcR46PaajtrYlBbVUMUJgIC5LHTrMscPp-0IF71SGC5.ttf'
  },
  {
    label: 'Baloo Tammudu 2',
    value: 'https://fonts.gstatic.com/s/balootammudu2/v23/1Pt5g8TIS_SAmkLguUdFP8UaJcKkzlPmMT00GaE_Jf8e4c6PZSlGmAA.ttf'
  },
  {
    label: 'Baloo Thambi 2',
    value: 'https://fonts.gstatic.com/s/baloothambi2/v17/cY9RfjeOW0NHpmOQXranrbDyu5JMJmNp-aDvUBbKzcIzaQRG_n4osQ.ttf'
  },
  {
    label: 'Balsamiq Sans',
    value: 'https://fonts.gstatic.com/s/balsamiqsans/v14/P5sEzZiAbNrN8SB3lQQX7Pnc8dkdIYdNHzs.ttf'
  },
  {
    label: 'Balthazar',
    value: 'https://fonts.gstatic.com/s/balthazar/v17/d6lKkaajS8Gm4CVQjFEvyRTo39l8hw.ttf'
  },
  {
    label: 'Bangers',
    value: 'https://fonts.gstatic.com/s/bangers/v24/FeVQS0BTqb0h60ACL5la2bxii28.ttf'
  },
  {
    label: 'Barlow',
    value: 'https://fonts.gstatic.com/s/barlow/v12/7cHpv4kjgoGqM7EPC8E46HsxnA.ttf'
  },
  {
    label: 'Barlow Condensed',
    value: 'https://fonts.gstatic.com/s/barlowcondensed/v12/HTx3L3I-JCGChYJ8VI-L6OO_au7B2xbZ23n3pKg.ttf'
  },
  {
    label: 'Barlow Semi Condensed',
    value: 'https://fonts.gstatic.com/s/barlowsemicondensed/v15/wlpvgxjLBV1hqnzfr-F8sEYMB0Yybp0mudRnf4CrCEo4gg.ttf'
  },
  {
    label: 'Barriecito',
    value: 'https://fonts.gstatic.com/s/barriecito/v17/WWXXlj-CbBOSLY2QTuY_KdUiYwTO0MU.ttf'
  },
  {
    label: 'Barrio',
    value: 'https://fonts.gstatic.com/s/barrio/v19/wEO8EBXBk8hBIDiEdQYhWdsX1Q.ttf'
  },
  {
    label: 'Basic',
    value: 'https://fonts.gstatic.com/s/basic/v17/xfu_0WLxV2_XKQN34lDVyR7D.ttf'
  },
  {
    label: 'Baskervville',
    value: 'https://fonts.gstatic.com/s/baskervville/v16/YA9Ur0yU4l_XOrogbkun3kQgt5OohvbJ9A.ttf'
  },
  {
    label: 'Baskervville SC',
    value: 'https://fonts.gstatic.com/s/baskervvillesc/v1/X7n94bc_DeKlh6bBbk_WiKnBSUvR71R3tiSx0g.ttf'
  },
  {
    label: 'Battambang',
    value: 'https://fonts.gstatic.com/s/battambang/v24/uk-mEGe7raEw-HjkzZabDnWj4yxx7o8.ttf'
  },
  {
    label: 'Baumans',
    value: 'https://fonts.gstatic.com/s/baumans/v17/-W_-XJj9QyTd3QfpR_oyaksqY5Q.ttf'
  },
  {
    label: 'Bayon',
    value: 'https://fonts.gstatic.com/s/bayon/v34/9XUrlJNmn0LPFl-pOhYEd2NJ.ttf'
  },
  {
    label: 'Be Vietnam Pro',
    value: 'https://fonts.gstatic.com/s/bevietnampro/v11/QdVPSTAyLFyeg_IDWvOJmVES_EwwD3s6ZKAi.ttf'
  },
  {
    label: 'Beau Rivage',
    value: 'https://fonts.gstatic.com/s/beaurivage/v2/UcCi3FIgIG2bH4mMNWJUlmg3NZp8K2sL.ttf'
  },
  {
    label: 'Bebas Neue',
    value: 'https://fonts.gstatic.com/s/bebasneue/v14/JTUSjIg69CK48gW7PXooxW5rygbi49c.ttf'
  },
  {
    label: 'Beiruti',
    value: 'https://fonts.gstatic.com/s/beiruti/v1/JTUIjIU69Cmr9FGcYgRFEb7cdQn2-9a95wujgWg2SGdV.ttf'
  },
  {
    label: 'Belanosima',
    value: 'https://fonts.gstatic.com/s/belanosima/v3/3y9k6bI8ejDo_3MfCDSLxABbF3JBg54.ttf'
  },
  {
    label: 'Belgrano',
    value: 'https://fonts.gstatic.com/s/belgrano/v18/55xvey5tM9rwKWrJZcMFirl08KDJ.ttf'
  },
  {
    label: 'Bellefair',
    value: 'https://fonts.gstatic.com/s/bellefair/v14/kJExBuYY6AAuhiXUxG19__A2pOdvDA.ttf'
  },
  {
    label: 'Belleza',
    value: 'https://fonts.gstatic.com/s/belleza/v17/0nkoC9_pNeMfhX4BtcbyawzruP8.ttf'
  },
  {
    label: 'Bellota',
    value: 'https://fonts.gstatic.com/s/bellota/v16/MwQ2bhXl3_qEpiwAGJJRtGs-lbA.ttf'
  },
  {
    label: 'Bellota Text',
    value: 'https://fonts.gstatic.com/s/bellotatext/v18/0FlTVP2VnlWS4f3-UE9hHXMB-dMOdS7sSg.ttf'
  },
  {
    label: 'BenchNine',
    value: 'https://fonts.gstatic.com/s/benchnine/v16/ahcbv8612zF4jxrwMosrV8N1jU2gog.ttf'
  },
  {
    label: 'Benne',
    value: 'https://fonts.gstatic.com/s/benne/v22/L0xzDFAhn18E6Vjxlt6qTDBN.ttf'
  },
  {
    label: 'Bentham',
    value: 'https://fonts.gstatic.com/s/bentham/v18/VdGeAZQPEpYfmHglKWw7CJaK_y4.ttf'
  },
  {
    label: 'Berkshire Swash',
    value: 'https://fonts.gstatic.com/s/berkshireswash/v20/ptRRTi-cavZOGqCvnNJDl5m5XmNPrcQybX4pQA.ttf'
  },
  {
    label: 'Besley',
    value: 'https://fonts.gstatic.com/s/besley/v19/PlIhFlO1MaNwaNGWUC92IOH_mtG4fbbBSdRoFPOl8-E.ttf'
  },
  {
    label: 'Beth Ellen',
    value: 'https://fonts.gstatic.com/s/bethellen/v21/WwkbxPW2BE-3rb_JNT-qEIAiVNo5xNY.ttf'
  },
  {
    label: 'Bevan',
    value: 'https://fonts.gstatic.com/s/bevan/v24/4iCj6KZ0a9NXjF8aUir7tlSJ.ttf'
  },
  {
    label: 'BhuTuka Expanded One',
    value: 'https://fonts.gstatic.com/s/bhutukaexpandedone/v7/SLXXc0jZ4WUJcClHTtv0t7IaDRsBsWRiJCyX8pg_RVH1.ttf'
  },
  {
    label: 'Big Shoulders Display',
    value:
      'https://fonts.gstatic.com/s/bigshouldersdisplay/v21/fC1MPZJEZG-e9gHhdI4-NBbfd2ys3SjJCx12wPgf9g-_3F0YdY87JF46SRP4yZQ.ttf'
  },
  {
    label: 'Big Shoulders Inline Display',
    value:
      'https://fonts.gstatic.com/s/bigshouldersinlinedisplay/v27/_LOumyfF4eSU_SCrJc9OI24U7siGvBGcZqmqV9-ZZ85CGNOFeNLxoYMPJ0nBE3R5yPc2Huux.ttf'
  },
  {
    label: 'Big Shoulders Inline Text',
    value:
      'https://fonts.gstatic.com/s/bigshouldersinlinetext/v26/vm8XdQDmVECV5-vm5dJ-Tp-6WDeRjL4RV7dP8u-NMyHY74qpoNNcwga0y6GN7Y6Jsc8c.ttf'
  },
  {
    label: 'Big Shoulders Stencil Display',
    value:
      'https://fonts.gstatic.com/s/bigshouldersstencildisplay/v28/6aeZ4LS6U6pR_bp5b_t2ugOhHWFcxSGP9ttD96KCb8xPytKb-oPRU-vkuLm_O0jPKHznJucP9w.ttf'
  },
  {
    label: 'Big Shoulders Stencil Text',
    value:
      'https://fonts.gstatic.com/s/bigshouldersstenciltext/v26/5aUV9-i2oxDMNwY3dHfW7UAt3Q453SM15wNj53bCcab2SJYLLUtk1OGR04TIGS_Py_AWbQ.ttf'
  },
  {
    label: 'Big Shoulders Text',
    value:
      'https://fonts.gstatic.com/s/bigshoulderstext/v24/55xEezRtP9G3CGPIf49hxc8P0eytUxB2l66LmF6xc3kA3Y-q3TIPNl6P2pc.ttf'
  },
  {
    label: 'Bigelow Rules',
    value: 'https://fonts.gstatic.com/s/bigelowrules/v29/RrQWboly8iR_I3KWSzeRuN0zT4cCH8WAJVk.ttf'
  },
  {
    label: 'Bigshot One',
    value: 'https://fonts.gstatic.com/s/bigshotone/v29/u-470qukhRkkO6BD_7cM_gxuUQJBXv_-.ttf'
  },
  {
    label: 'Bilbo',
    value: 'https://fonts.gstatic.com/s/bilbo/v20/o-0EIpgpwWwZ210hpIRz4wxE.ttf'
  },
  {
    label: 'Bilbo Swash Caps',
    value: 'https://fonts.gstatic.com/s/bilboswashcaps/v22/zrf-0GXbz-H3Wb4XBsGrTgq2PVmdqAPopiRfKp8.ttf'
  },
  {
    label: 'BioRhyme',
    value:
      'https://fonts.gstatic.com/s/biorhyme/v17/1cXeaULHBpDMsHYW_GZNh7loEHurwOIGadI205trrbeBgQs4OjIimiaki-gkRDE.ttf'
  },
  {
    label: 'BioRhyme Expanded',
    value: 'https://fonts.gstatic.com/s/biorhymeexpanded/v21/i7dQIE1zZzytGswgU577CDY9LjbffySURXCPYsje.ttf'
  },
  {
    label: 'Birthstone',
    value: 'https://fonts.gstatic.com/s/birthstone/v14/8AtsGs2xO4yLRhy87sv_HLn5jRfZHzM.ttf'
  },
  {
    label: 'Birthstone Bounce',
    value: 'https://fonts.gstatic.com/s/birthstonebounce/v11/ga6XaxZF43lIvTWrktHOTBJZGH7dEeVJGIMYDo_8.ttf'
  },
  {
    label: 'Biryani',
    value: 'https://fonts.gstatic.com/s/biryani/v13/hv-WlzNxIFoO84YdTUwZPTh5T-s.ttf'
  },
  {
    label: 'Bitter',
    value: 'https://fonts.gstatic.com/s/bitter/v36/raxhHiqOu8IVPmnRc6SY1KXhnF_Y8fbfCL_EXFh2reU.ttf'
  },
  {
    label: 'Black And White Picture',
    value: 'https://fonts.gstatic.com/s/blackandwhitepicture/v24/TwMe-JAERlQd3ooUHBUXGmrmioKjjnRSFO-NqI5HbcMi-yWY.ttf'
  },
  {
    label: 'Black Han Sans',
    value: 'https://fonts.gstatic.com/s/blackhansans/v17/ea8Aad44WunzF9a-dL6toA8r8nqVIXSkH-Hc.ttf'
  },
  {
    label: 'Black Ops One',
    value: 'https://fonts.gstatic.com/s/blackopsone/v20/qWcsB6-ypo7xBdr6Xshe96H3WDzRtjkho4M.ttf'
  },
  {
    label: 'Blaka',
    value: 'https://fonts.gstatic.com/s/blaka/v7/8vIG7w8722p_6kdr20D2FV5e.ttf'
  },
  {
    label: 'Blaka Hollow',
    value: 'https://fonts.gstatic.com/s/blakahollow/v7/MCoUzAL91sjRE2FsKsxUtezYB9oFyW_-oA.ttf'
  },
  {
    label: 'Blaka Ink',
    value: 'https://fonts.gstatic.com/s/blakaink/v8/AlZy_zVVtpj22Znag2chdXf4XB0Tow.ttf'
  },
  {
    label: 'Blinker',
    value: 'https://fonts.gstatic.com/s/blinker/v13/cIf9MaFatEE-VTaPxCmrYGkHgIs.ttf'
  },
  {
    label: 'Bodoni Moda',
    value:
      'https://fonts.gstatic.com/s/bodonimoda/v25/aFT67PxzY382XsXX63LUYL6GYFcan6NJrKp-VPjfJMShrpsGFUt8oU7awIBytVjMYwE.ttf'
  },
  {
    label: 'Bodoni Moda SC',
    value:
      'https://fonts.gstatic.com/s/bodonimodasc/v1/LYjhdGTykkIgA8197UwkzHp8F-XUUG5UNs2KqliEb-g_DaUdIA6SfiYHbA7uXJyb9N7yr8E.ttf'
  },
  {
    label: 'Bokor',
    value: 'https://fonts.gstatic.com/s/bokor/v30/m8JcjfpeeaqTiR2WdInbcaxE.ttf'
  },
  {
    label: 'Bona Nova',
    value: 'https://fonts.gstatic.com/s/bonanova/v10/B50NF7ZCpX7fcHfvIUBJi6hqHK-CLA.ttf'
  },
  {
    label: 'Bona Nova SC',
    value: 'https://fonts.gstatic.com/s/bonanovasc/v1/mem5YaShyGWDiYdPG_c1Af4-VeJoCqeDjg.ttf'
  },
  {
    label: 'Bonbon',
    value: 'https://fonts.gstatic.com/s/bonbon/v30/0FlVVPeVlFec4ee_cDEAbQY5-A.ttf'
  },
  {
    label: 'Bonheur Royale',
    value: 'https://fonts.gstatic.com/s/bonheurroyale/v13/c4m51nt_GMTrtX-b9GcG4-YRmYK_c0f1N5Ij.ttf'
  },
  {
    label: 'Boogaloo',
    value: 'https://fonts.gstatic.com/s/boogaloo/v23/kmK-Zq45GAvOdnaW6x1F_SrQo_1K.ttf'
  },
  {
    label: 'Borel',
    value: 'https://fonts.gstatic.com/s/borel/v3/6qLOKZsftAPisgshYyMnOjwE.ttf'
  },
  {
    label: 'Bowlby One',
    value: 'https://fonts.gstatic.com/s/bowlbyone/v23/taiPGmVuC4y96PFeqp8smo6C_Z0wcK4.ttf'
  },
  {
    label: 'Bowlby One SC',
    value: 'https://fonts.gstatic.com/s/bowlbyonesc/v25/DtVlJxerQqQm37tzN3wMug9Pzgj8owhNjuE.ttf'
  },
  {
    label: 'Braah One',
    value: 'https://fonts.gstatic.com/s/braahone/v6/KFOlCnWUpt6LsxxxiylvAx05IsDqlA.ttf'
  },
  {
    label: 'Brawler',
    value: 'https://fonts.gstatic.com/s/brawler/v19/xn7gYHE3xXewAscGsgC7S9XdZN8.ttf'
  },
  {
    label: 'Bree Serif',
    value: 'https://fonts.gstatic.com/s/breeserif/v17/4UaHrEJCrhhnVA3DgluAx63j5pN1MwI.ttf'
  },
  {
    label: 'Bricolage Grotesque',
    value:
      'https://fonts.gstatic.com/s/bricolagegrotesque/v2/3y9U6as8bTXq_nANBjzKo3IeZx8z6up5BeSl5jBNz_19PpbpMXuECpwUxJBOm_OJWiaaD30YfKfjZZoLvRviyM0vs-wJDtw.ttf'
  },
  {
    label: 'Bruno Ace',
    value: 'https://fonts.gstatic.com/s/brunoace/v5/WwkcxPa2E06x4trkOj_kMKoMWNMg3Q.ttf'
  },
  {
    label: 'Bruno Ace SC',
    value: 'https://fonts.gstatic.com/s/brunoacesc/v5/ptROTiycffFLBuiHjdJDl634LSFrpe8uZA.ttf'
  },
  {
    label: 'Brygada 1918',
    value: 'https://fonts.gstatic.com/s/brygada1918/v22/pe08MI6eKpdGqlF5LANrM--ACNaeo8mTUIR_y2-f-V8Wu5O3gbo.ttf'
  },
  {
    label: 'Bubblegum Sans',
    value: 'https://fonts.gstatic.com/s/bubblegumsans/v20/AYCSpXb_Z9EORv1M5QTjEzMEtdaHzoPPb7R4.ttf'
  },
  {
    label: 'Bubbler One',
    value: 'https://fonts.gstatic.com/s/bubblerone/v21/f0Xy0eqj68ppQV9KBLmAouHH26MPePkt.ttf'
  },
  {
    label: 'Buenard',
    value: 'https://fonts.gstatic.com/s/buenard/v17/OD5DuM6Cyma8FnnsPzf9qGi9HL4.ttf'
  },
  {
    label: 'Bungee',
    value: 'https://fonts.gstatic.com/s/bungee/v14/N0bU2SZBIuF2PU_ECn50Kd_PmA.ttf'
  },
  {
    label: 'Bungee Hairline',
    value: 'https://fonts.gstatic.com/s/bungeehairline/v23/snfys0G548t04270a_ljTLUVrv-7YB2dQ5ZPqQ.ttf'
  },
  {
    label: 'Bungee Inline',
    value: 'https://fonts.gstatic.com/s/bungeeinline/v16/Gg8zN58UcgnlCweMrih332VuDGJ1-FEglsc.ttf'
  },
  {
    label: 'Bungee Outline',
    value: 'https://fonts.gstatic.com/s/bungeeoutline/v21/_6_mEDvmVP24UvU2MyiGDslL3Qg3YhJqPXxo.ttf'
  },
  {
    label: 'Bungee Shade',
    value: 'https://fonts.gstatic.com/s/bungeeshade/v14/DtVkJxarWL0t2KdzK3oI_jks7iLSrwFUlw.ttf'
  },
  {
    label: 'Bungee Spice',
    value: 'https://fonts.gstatic.com/s/bungeespice/v12/nwpTtK2nIhxE0q-IwgSpZBqCzyI-aMPF7Q.ttf'
  },
  {
    label: 'Butcherman',
    value: 'https://fonts.gstatic.com/s/butcherman/v24/2EbiL-thF0loflXUBOdb1zWzq_5uT84.ttf'
  },
  {
    label: 'Butterfly Kids',
    value: 'https://fonts.gstatic.com/s/butterflykids/v25/ll8lK2CWTjuqAsXDqlnIbMNs5S4arxFrAX1D.ttf'
  },
  {
    label: 'Cabin',
    value: 'https://fonts.gstatic.com/s/cabin/v27/u-4X0qWljRw-PfU81xCKCpdpbgZJl6XFpfEd7eA9BIxxkV2EL7Gvxm7rE_s.ttf'
  },
  {
    label: 'Cabin Condensed',
    value: 'https://fonts.gstatic.com/s/cabincondensed/v20/nwpMtK6mNhBK2err_hqkYhHRqmwaYOjZ5HZl8Q.ttf'
  },
  {
    label: 'Cabin Sketch',
    value: 'https://fonts.gstatic.com/s/cabinsketch/v21/QGYpz_kZZAGCONcK2A4bGOjMn9JM6fnuKg.ttf'
  },
  {
    label: 'Cactus Classical Serif',
    value: 'https://fonts.gstatic.com/s/cactusclassicalserif/v2/sZlVdQ6K-zJOCzUaS90zMNN-Ep-OoC8dZr0JFuBIFX-pv-E.ttf'
  },
  {
    label: 'Caesar Dressing',
    value: 'https://fonts.gstatic.com/s/caesardressing/v21/yYLx0hLa3vawqtwdswbotmK4vrR3cbb6LZttyg.ttf'
  },
  {
    label: 'Cagliostro',
    value: 'https://fonts.gstatic.com/s/cagliostro/v21/ZgNWjP5HM73BV5amnX-TjGXEM4COoE4.ttf'
  },
  {
    label: 'Cairo',
    value: 'https://fonts.gstatic.com/s/cairo/v28/SLXgc1nY6HkvangtZmpQdkhzfH5lkSs2SgRjCAGMQ1z0hOA-W1ToLQ-HmkA.ttf'
  },
  {
    label: 'Cairo Play',
    value:
      'https://fonts.gstatic.com/s/cairoplay/v7/wXKEE3QSpo4vpRz_mz6FP-8iaauCLt_Hjopv3miu5IvcJo49mOo1knYq9yXa8GvzaA.ttf'
  },
  {
    label: 'Caladea',
    value: 'https://fonts.gstatic.com/s/caladea/v7/kJEzBugZ7AAjhybUjR93-9IztOc.ttf'
  },
  {
    label: 'Calistoga',
    value: 'https://fonts.gstatic.com/s/calistoga/v16/6NUU8F2OJg6MeR7l4e0vtMYAwdRZfw.ttf'
  },
  {
    label: 'Calligraffitti',
    value: 'https://fonts.gstatic.com/s/calligraffitti/v19/46k2lbT3XjDVqJw3DCmCFjE0vnFZM5ZBpYN-.ttf'
  },
  {
    label: 'Cambay',
    value: 'https://fonts.gstatic.com/s/cambay/v12/SLXJc1rY6H0_ZDsGbrSIz9JsaA.ttf'
  },
  {
    label: 'Cambo',
    value: 'https://fonts.gstatic.com/s/cambo/v17/IFSqHeNEk8FJk416ok7xkPm8.ttf'
  },
  {
    label: 'Candal',
    value: 'https://fonts.gstatic.com/s/candal/v15/XoHn2YH6T7-t_8cNAR4Jt9Yxlw.ttf'
  },
  {
    label: 'Cantarell',
    value: 'https://fonts.gstatic.com/s/cantarell/v17/B50NF7ZDq37KMUvlO01Ji6hqHK-CLA.ttf'
  },
  {
    label: 'Cantata One',
    value: 'https://fonts.gstatic.com/s/cantataone/v15/PlI5Fl60Nb5obNzNe2jslVxEt8CwfGaD.ttf'
  },
  {
    label: 'Cantora One',
    value: 'https://fonts.gstatic.com/s/cantoraone/v19/gyB4hws1JdgnKy56GB_JX6zdZ4vZVbgZ.ttf'
  },
  {
    label: 'Caprasimo',
    value: 'https://fonts.gstatic.com/s/caprasimo/v5/esDT31JQOPuXIUGBp72klZUCGpG-GQ.ttf'
  },
  {
    label: 'Capriola',
    value: 'https://fonts.gstatic.com/s/capriola/v14/wXKoE3YSppcvo1PDln_8L-AinG8y.ttf'
  },
  {
    label: 'Caramel',
    value: 'https://fonts.gstatic.com/s/caramel/v7/P5sCzZKBbMTf_ShyxCRuiZ-uydg.ttf'
  },
  {
    label: 'Carattere',
    value: 'https://fonts.gstatic.com/s/carattere/v7/4iCv6Kp1b9dXlgt_CkvTt2aMH4V_gg.ttf'
  },
  {
    label: 'Cardo',
    value: 'https://fonts.gstatic.com/s/cardo/v19/wlp_gwjKBV1pqiv_1oAZ2H5O.ttf'
  },
  {
    label: 'Carlito',
    value: 'https://fonts.gstatic.com/s/carlito/v3/3Jn9SDPw3m-pk039PDCLTXUETuE.ttf'
  },
  {
    label: 'Carme',
    value: 'https://fonts.gstatic.com/s/carme/v16/ptRHTiWdbvZIDOjGxLNrxfbZ.ttf'
  },
  {
    label: 'Carrois Gothic',
    value: 'https://fonts.gstatic.com/s/carroisgothic/v16/Z9XPDmFATg-N1PLtLOOxvIHl9ZmD3i7ajcJ-.ttf'
  },
  {
    label: 'Carrois Gothic SC',
    value: 'https://fonts.gstatic.com/s/carroisgothicsc/v15/ZgNJjOVHM6jfUZCmyUqT2A2HVKjc-28nNHabY4dN.ttf'
  },
  {
    label: 'Carter One',
    value: 'https://fonts.gstatic.com/s/carterone/v17/q5uCsoe5IOB2-pXv9UcNIxR2hYxREMs.ttf'
  },
  {
    label: 'Castoro',
    value: 'https://fonts.gstatic.com/s/castoro/v19/1q2GY5yMCld3-O4cHYhEzOYenEU.ttf'
  },
  {
    label: 'Castoro Titling',
    value: 'https://fonts.gstatic.com/s/castorotitling/v8/buEupouwccj03leTfjUAhEZWlrNqYgckeo9RMw.ttf'
  },
  {
    label: 'Catamaran',
    value: 'https://fonts.gstatic.com/s/catamaran/v19/o-0bIpQoyXQa2RxT7-5B6Ryxs2E_6n1iPHjd1anXuluiLyw.ttf'
  },
  {
    label: 'Caudex',
    value: 'https://fonts.gstatic.com/s/caudex/v17/esDQ311QOP6BJUrIyviAnb4eEw.ttf'
  },
  {
    label: 'Caveat',
    value: 'https://fonts.gstatic.com/s/caveat/v18/WnznHAc5bAfYB2QRah7pcpNvOx-pjfJ9SIKjYBxPigs.ttf'
  },
  {
    label: 'Caveat Brush',
    value: 'https://fonts.gstatic.com/s/caveatbrush/v11/EYq0maZfwr9S9-ETZc3fKXtMW7mT03pdQw.ttf'
  },
  {
    label: 'Cedarville Cursive',
    value: 'https://fonts.gstatic.com/s/cedarvillecursive/v17/yYL00g_a2veiudhUmxjo5VKkoqA-B_neJbBxw8BeTg.ttf'
  },
  {
    label: 'Ceviche One',
    value: 'https://fonts.gstatic.com/s/cevicheone/v16/gyB4hws1IcA6JzR-GB_JX6zdZ4vZVbgZ.ttf'
  },
  {
    label: 'Chakra Petch',
    value: 'https://fonts.gstatic.com/s/chakrapetch/v11/cIf6MapbsEk7TDLdtEz1BwkmmKBhSL7Y1Q.ttf'
  },
  {
    label: 'Changa',
    value: 'https://fonts.gstatic.com/s/changa/v27/2-c79JNi2YuVOUcOarRPgnNGooxCZ62xQjDp9htf1ZM.ttf'
  },
  {
    label: 'Changa One',
    value: 'https://fonts.gstatic.com/s/changaone/v20/xfu00W3wXn3QLUJXhzq46AbouLfbK64.ttf'
  },
  {
    label: 'Chango',
    value: 'https://fonts.gstatic.com/s/chango/v27/2V0cKI0OB5U7WaJyz324TFUaAw.ttf'
  },
  {
    label: 'Charis SIL',
    value: 'https://fonts.gstatic.com/s/charissil/v2/oPWK_kV3l-s-Q8govXvKrPrmYjZ2Xn0.ttf'
  },
  {
    label: 'Charm',
    value: 'https://fonts.gstatic.com/s/charm/v11/7cHmv4oii5K0MeYvIe804WIo.ttf'
  },
  {
    label: 'Charmonman',
    value: 'https://fonts.gstatic.com/s/charmonman/v18/MjQDmiR3vP_nuxDv47jiWJGovLdh6OE.ttf'
  },
  {
    label: 'Chathura',
    value: 'https://fonts.gstatic.com/s/chathura/v20/_gP71R7-rzUuVjim418goUC5S-Zy.ttf'
  },
  {
    label: 'Chau Philomene One',
    value: 'https://fonts.gstatic.com/s/chauphilomeneone/v15/55xxezRsPtfie1vPY49qzdgSlJiHRQFsnIx7QMISdQ.ttf'
  },
  {
    label: 'Chela One',
    value: 'https://fonts.gstatic.com/s/chelaone/v21/6ae-4KC7Uqgdz_JZdPIy31vWNTMwoQ.ttf'
  },
  {
    label: 'Chelsea Market',
    value: 'https://fonts.gstatic.com/s/chelseamarket/v13/BCawqZsHqfr89WNP_IApC8tzKBhlLA4uKkWk.ttf'
  },
  {
    label: 'Chenla',
    value: 'https://fonts.gstatic.com/s/chenla/v25/SZc43FDpIKu8WZ9eXxfonUPL6Q.ttf'
  },
  {
    label: 'Cherish',
    value: 'https://fonts.gstatic.com/s/cherish/v8/ll88K2mXUyqsDsTN5iDCI6IJjg8.ttf'
  },
  {
    label: 'Cherry Bomb One',
    value: 'https://fonts.gstatic.com/s/cherrybombone/v8/y83DW4od1h6KlV3c6JJhRhGOdhrKDNpF41fr-w.ttf'
  },
  {
    label: 'Cherry Cream Soda',
    value: 'https://fonts.gstatic.com/s/cherrycreamsoda/v21/UMBIrOxBrW6w2FFyi9paG0fdVdRciTd6Cd47DJ7G.ttf'
  },
  {
    label: 'Cherry Swash',
    value: 'https://fonts.gstatic.com/s/cherryswash/v20/i7dNIFByZjaNAMxtZcnfAy58QHi-EwWMbg.ttf'
  },
  {
    label: 'Chewy',
    value: 'https://fonts.gstatic.com/s/chewy/v18/uK_94ruUb-k-wk5xIDMfO-ed.ttf'
  },
  {
    label: 'Chicle',
    value: 'https://fonts.gstatic.com/s/chicle/v25/lJwG-pw9i2dqU-BDyWKuobYSxw.ttf'
  },
  {
    label: 'Chilanka',
    value: 'https://fonts.gstatic.com/s/chilanka/v20/WWXRlj2DZQiMJYaYRrJQI9EAZhTO.ttf'
  },
  {
    label: 'Chivo',
    value: 'https://fonts.gstatic.com/s/chivo/v18/va9b4kzIxd1KFppkaRKvDRPJVDf_vB_ul2DSFXjQiQ.ttf'
  },
  {
    label: 'Chivo Mono',
    value: 'https://fonts.gstatic.com/s/chivomono/v9/mFThWbgRxKvF_Z5eQMO9qRMrJJrnKNtC3D7hr6fVKphL03l4.ttf'
  },
  {
    label: 'Chocolate Classical Sans',
    value:
      'https://fonts.gstatic.com/s/chocolateclassicalsans/v2/nuFqD-PLTZX4XIgT-P2ToCDudWHHflqUpTpfjWdDPI2J9mHITw.ttf'
  },
  {
    label: 'Chokokutai',
    value: 'https://fonts.gstatic.com/s/chokokutai/v9/kmK4Zqw4HwvCeHGM8Fws9y7ypu1Kr7I.ttf'
  },
  {
    label: 'Chonburi',
    value: 'https://fonts.gstatic.com/s/chonburi/v12/8AtqGs-wOpGRTBq66IWaFr3biAfZ.ttf'
  },
  {
    label: 'Cinzel',
    value: 'https://fonts.gstatic.com/s/cinzel/v23/8vIU7ww63mVu7gtR-kwKxNvkNOjw-tbnTYrvDE5ZdqU.ttf'
  },
  {
    label: 'Cinzel Decorative',
    value: 'https://fonts.gstatic.com/s/cinzeldecorative/v16/daaCSScvJGqLYhG8nNt8KPPswUAPnh7URs1LaCyC.ttf'
  },
  {
    label: 'Clicker Script',
    value: 'https://fonts.gstatic.com/s/clickerscript/v13/raxkHiKPvt8CMH6ZWP8PdlEq72rY2zqUKafv.ttf'
  },
  {
    label: 'Climate Crisis',
    value: 'https://fonts.gstatic.com/s/climatecrisis/v8/wEOpEB3AntNeKCPBVW9XOKlmp3AUgWFN1DvIvcM0gFp6jaUrGb7PsQ.ttf'
  },
  {
    label: 'Coda',
    value: 'https://fonts.gstatic.com/s/coda/v21/SLXHc1jY5nQ8JUIMapaN39I.ttf'
  },
  {
    label: 'Codystar',
    value: 'https://fonts.gstatic.com/s/codystar/v17/FwZY7-Q1xVk-40qxOt6A4sijpFu_.ttf'
  },
  {
    label: 'Coiny',
    value: 'https://fonts.gstatic.com/s/coiny/v16/gyByhwU1K989PXwbElSvO5Tc.ttf'
  },
  {
    label: 'Combo',
    value: 'https://fonts.gstatic.com/s/combo/v21/BXRlvF3Jh_fIhg0iBu9y8Hf0.ttf'
  },
  {
    label: 'Comfortaa',
    value: 'https://fonts.gstatic.com/s/comfortaa/v45/1Pt_g8LJRfWJmhDAuUsSQamb1W0lwk4S4WjMPrQVIT9c2c8.ttf'
  },
  {
    label: 'Comforter',
    value: 'https://fonts.gstatic.com/s/comforter/v7/H4clBXOCl8nQnlaql3Qa6JG8iqeuag.ttf'
  },
  {
    label: 'Comforter Brush',
    value: 'https://fonts.gstatic.com/s/comforterbrush/v7/Y4GTYa1xVSggrfzZI5WMjxRaOz0jwLL9Th8YYA.ttf'
  },
  {
    label: 'Comic Neue',
    value: 'https://fonts.gstatic.com/s/comicneue/v8/4UaHrEJDsxBrF37olUeDx63j5pN1MwI.ttf'
  },
  {
    label: 'Coming Soon',
    value: 'https://fonts.gstatic.com/s/comingsoon/v19/qWcuB6mzpYL7AJ2VfdQR1u-SUjjzsykh.ttf'
  },
  {
    label: 'Comme',
    value: 'https://fonts.gstatic.com/s/comme/v2/8QIUdirKhMbn-uG1kHz0MgviDe1z5cFr644fWsRO9w.ttf'
  },
  {
    label: 'Commissioner',
    value:
      'https://fonts.gstatic.com/s/commissioner/v20/tDaH2o2WnlgI0FNDgduEk4jAhwgumbU1SVfU5BD8OuRL8OstC6KOhgvBYWSFJ-Mgdrgiju6fF8meZm0rk4eF-ZugTMNdGPe7Fu0jUdk.ttf'
  },
  {
    label: 'Concert One',
    value: 'https://fonts.gstatic.com/s/concertone/v22/VEM1Ro9xs5PjtzCu-srDqRTlhv-CuVAQ.ttf'
  },
  {
    label: 'Condiment',
    value: 'https://fonts.gstatic.com/s/condiment/v24/pONk1hggFNmwvXALyH6Sq4n4o1vyCQ.ttf'
  },
  {
    label: 'Content',
    value: 'https://fonts.gstatic.com/s/content/v24/zrfl0HLayePhU_AwUaDyIiL0RCg.ttf'
  },
  {
    label: 'Contrail One',
    value: 'https://fonts.gstatic.com/s/contrailone/v19/eLGbP-j_JA-kG0_Zo51noafdZUvt_c092w.ttf'
  },
  {
    label: 'Convergence',
    value: 'https://fonts.gstatic.com/s/convergence/v15/rax5HiePvdgXPmmMHcIPYRhasU7Q8Cad.ttf'
  },
  {
    label: 'Cookie',
    value: 'https://fonts.gstatic.com/s/cookie/v21/syky-y18lb0tSbfNlQCT9tPdpw.ttf'
  },
  {
    label: 'Copse',
    value: 'https://fonts.gstatic.com/s/copse/v15/11hPGpDKz1rGb0djHkihUb-A.ttf'
  },
  {
    label: 'Corben',
    value: 'https://fonts.gstatic.com/s/corben/v21/LYjDdGzzklQtCMp9oAlEpVs3VQ.ttf'
  },
  {
    label: 'Corinthia',
    value: 'https://fonts.gstatic.com/s/corinthia/v11/wEO_EBrAnchaJyPMHE0FUfAL3EsHiA.ttf'
  },
  {
    label: 'Cormorant',
    value: 'https://fonts.gstatic.com/s/cormorant/v21/H4c2BXOCl9bbnla_nHIA47NMUjsNbCVrFhFTQ7Rg7A2uwYs.ttf'
  },
  {
    label: 'Cormorant Garamond',
    value: 'https://fonts.gstatic.com/s/cormorantgaramond/v16/co3bmX5slCNuHLi8bLeY9MK7whWMhyjornFLsS6V7w.ttf'
  },
  {
    label: 'Cormorant Infant',
    value: 'https://fonts.gstatic.com/s/cormorantinfant/v17/HhyPU44g9vKiM1sORYSiWeAsLN993_Af2DsAXq4.ttf'
  },
  {
    label: 'Cormorant SC',
    value: 'https://fonts.gstatic.com/s/cormorantsc/v18/0yb5GD4kxqXBmOVLG30OGwserDow9Tbu-Q.ttf'
  },
  {
    label: 'Cormorant Unicase',
    value: 'https://fonts.gstatic.com/s/cormorantunicase/v24/HI_QiZUaILtOqhqgDeXoF_n1_fTGX-vTnsMnx3C9.ttf'
  },
  {
    label: 'Cormorant Upright',
    value: 'https://fonts.gstatic.com/s/cormorantupright/v18/VuJrdM3I2Y35poFONtLdafkUCHw1y2vVjjTkeMnz.ttf'
  },
  {
    label: 'Courgette',
    value: 'https://fonts.gstatic.com/s/courgette/v17/wEO_EBrAnc9BLjLQAUkFUfAL3EsHiA.ttf'
  },
  {
    label: 'Courier Prime',
    value: 'https://fonts.gstatic.com/s/courierprime/v9/u-450q2lgwslOqpF_6gQ8kELWwZjW-_-tvg.ttf'
  },
  {
    label: 'Cousine',
    value: 'https://fonts.gstatic.com/s/cousine/v27/d6lIkaiiRdih4SpPzSMlzTbtz9k.ttf'
  },
  {
    label: 'Coustard',
    value: 'https://fonts.gstatic.com/s/coustard/v16/3XFpErgg3YsZ5fqUU9UPvWXuROTd.ttf'
  },
  {
    label: 'Covered By Your Grace',
    value: 'https://fonts.gstatic.com/s/coveredbyyourgrace/v15/QGYwz-AZahWOJJI9kykWW9mD6opopoqXSOS0FgItq6bFIg.ttf'
  },
  {
    label: 'Crafty Girls',
    value: 'https://fonts.gstatic.com/s/craftygirls/v16/va9B4kXI39VaDdlPJo8N_NvuQR37fF3Wlg.ttf'
  },
  {
    label: 'Creepster',
    value: 'https://fonts.gstatic.com/s/creepster/v13/AlZy_zVUqJz4yMrniH4hdXf4XB0Tow.ttf'
  },
  {
    label: 'Crete Round',
    value: 'https://fonts.gstatic.com/s/creteround/v15/55xoey1sJNPjPiv1ZZZrxJ1827zAKnxN.ttf'
  },
  {
    label: 'Crimson Pro',
    value: 'https://fonts.gstatic.com/s/crimsonpro/v24/q5uUsoa5M_tv7IihmnkabC5XiXCAlXGks1WZzm18OJE_VNWoyQ.ttf'
  },
  {
    label: 'Crimson Text',
    value: 'https://fonts.gstatic.com/s/crimsontext/v19/wlp2gwHKFkZgtmSR3NB0oRJvaAJSA_JN3Q.ttf'
  },
  {
    label: 'Croissant One',
    value: 'https://fonts.gstatic.com/s/croissantone/v26/3y9n6bU9bTPg4m8NDy3Kq24UM3pqn5cdJ-4.ttf'
  },
  {
    label: 'Crushed',
    value: 'https://fonts.gstatic.com/s/crushed/v29/U9Mc6dym6WXImTlFT1kfuIqyLzA.ttf'
  },
  {
    label: 'Cuprum',
    value: 'https://fonts.gstatic.com/s/cuprum/v25/dg45_pLmvrkcOkBnKsOzXyGWTBcmg-X6ZjzSJjQjgnU.ttf'
  },
  {
    label: 'Cute Font',
    value: 'https://fonts.gstatic.com/s/cutefont/v22/Noaw6Uny2oWPbSHMrY6vmJNVNC9hkw.ttf'
  },
  {
    label: 'Cutive',
    value: 'https://fonts.gstatic.com/s/cutive/v22/NaPZcZ_fHOhV3Ip7T_hDoyqlZQ.ttf'
  },
  {
    label: 'Cutive Mono',
    value: 'https://fonts.gstatic.com/s/cutivemono/v21/m8JWjfRfY7WVjVi2E-K9H5RFRG-K3Mud.ttf'
  },
  {
    label: 'DM Mono',
    value: 'https://fonts.gstatic.com/s/dmmono/v14/aFTU7PB1QTsUX8KYhh2aBYyMcKw.ttf'
  },
  {
    label: 'DM Sans',
    value: 'https://fonts.gstatic.com/s/dmsans/v15/rP2tp2ywxg089UriI5-g4vlH9VoD8CmcqZG40F9JadbnoEwAopxhTmf3ZGMZpg.ttf'
  },
  {
    label: 'DM Serif Display',
    value: 'https://fonts.gstatic.com/s/dmserifdisplay/v15/-nFnOHM81r4j6k0gjAW3mujVU2B2K_d709jy92k.ttf'
  },
  {
    label: 'DM Serif Text',
    value: 'https://fonts.gstatic.com/s/dmseriftext/v12/rnCu-xZa_krGokauCeNq1wWyafOPXHIJErY.ttf'
  },
  {
    label: 'Dai Banna SIL',
    value: 'https://fonts.gstatic.com/s/daibannasil/v2/lW-4wj0AJWmpwGyJ2uEoA4I7jS6AKsLhJgo.ttf'
  },
  {
    label: 'Damion',
    value: 'https://fonts.gstatic.com/s/damion/v15/hv-XlzJ3KEUe_YZUbWY3MTFgVg.ttf'
  },
  {
    label: 'Dancing Script',
    value: 'https://fonts.gstatic.com/s/dancingscript/v25/If2cXTr6YS-zF4S-kcSWSVi_sxjsohD9F50Ruu7BMSoHTeB9ptDqpw.ttf'
  },
  {
    label: 'Danfo',
    value: 'https://fonts.gstatic.com/s/danfo/v2/snf3s0u_98t16THfK1Csj3N41ZqbYDe5S71ToPrNKQ.ttf'
  },
  {
    label: 'Dangrek',
    value: 'https://fonts.gstatic.com/s/dangrek/v30/LYjCdG30nEgoH8E2gCNqqVIuTN4.ttf'
  },
  {
    label: 'Darker Grotesque',
    value: 'https://fonts.gstatic.com/s/darkergrotesque/v8/U9MK6cuh-mLQlC4BKCtayOfARkSVgb381b-W8-QDqXw3qn7y-XFyZFUB.ttf'
  },
  {
    label: 'Darumadrop One',
    value: 'https://fonts.gstatic.com/s/darumadropone/v10/cY9cfjeIW11dpCKgRLi675a87IhHBJOxZQPp.ttf'
  },
  {
    label: 'David Libre',
    value: 'https://fonts.gstatic.com/s/davidlibre/v16/snfus0W_99N64iuYSvp4W_l86p6TYS-Y.ttf'
  },
  {
    label: 'Dawning of a New Day',
    value: 'https://fonts.gstatic.com/s/dawningofanewday/v20/t5t_IQMbOp2SEwuncwLRjMfIg1yYit_nAz8bhWJGNoBE.ttf'
  },
  {
    label: 'Days One',
    value: 'https://fonts.gstatic.com/s/daysone/v18/mem9YaCnxnKRiYZOCLYVeLkWVNBt.ttf'
  },
  {
    label: 'Dekko',
    value: 'https://fonts.gstatic.com/s/dekko/v21/46khlb_wWjfSrttFR0vsfl1B.ttf'
  },
  {
    label: 'Dela Gothic One',
    value: 'https://fonts.gstatic.com/s/delagothicone/v15/hESp6XxvMDRA-2eD0lXpDa6QkBAGRUsJQAlbUA.ttf'
  },
  {
    label: 'Delicious Handrawn',
    value: 'https://fonts.gstatic.com/s/delicioushandrawn/v8/wlpsgx_NAUNkpmKQifcxkQchDFo3fJ113JpDd6u3AQ.ttf'
  },
  {
    label: 'Delius',
    value: 'https://fonts.gstatic.com/s/delius/v19/PN_xRfK0pW_9e1rtYcI-jT3L_w.ttf'
  },
  {
    label: 'Delius Swash Caps',
    value: 'https://fonts.gstatic.com/s/deliusswashcaps/v23/oY1E8fPLr7v4JWCExZpWebxVKORpXXedKmeBvEYs.ttf'
  },
  {
    label: 'Delius Unicase',
    value: 'https://fonts.gstatic.com/s/deliusunicase/v28/845BNMEwEIOVT8BmgfSzIr_6mmLHd-73LXWs.ttf'
  },
  {
    label: 'Della Respira',
    value: 'https://fonts.gstatic.com/s/dellarespira/v22/RLp5K5v44KaueWI6iEJQBiGPRfkSu6EuTHo.ttf'
  },
  {
    label: 'Denk One',
    value: 'https://fonts.gstatic.com/s/denkone/v19/dg4m_pzhrqcFb2IzROtHpbglShon.ttf'
  },
  {
    label: 'Devonshire',
    value: 'https://fonts.gstatic.com/s/devonshire/v27/46kqlbDwWirWr4gtBD2BX0Vq01lYAZM.ttf'
  },
  {
    label: 'Dhurjati',
    value: 'https://fonts.gstatic.com/s/dhurjati/v24/_6_8ED3gSeatXfFiFX3ySKQtuTA2.ttf'
  },
  {
    label: 'Didact Gothic',
    value: 'https://fonts.gstatic.com/s/didactgothic/v20/ahcfv8qz1zt6hCC5G4F_P4ASpUySp0LlcyQ.ttf'
  },
  {
    label: 'Diphylleia',
    value: 'https://fonts.gstatic.com/s/diphylleia/v1/DtVmJxCtRKMixK4_HXsIulwm6gDXvwE.ttf'
  },
  {
    label: 'Diplomata',
    value: 'https://fonts.gstatic.com/s/diplomata/v31/Cn-0JtiMXwhNwp-wKxyfYGxYrdM9Sg.ttf'
  },
  {
    label: 'Diplomata SC',
    value: 'https://fonts.gstatic.com/s/diplomatasc/v28/buExpoi3ecvs3kidKgBJo2kf-P5Oaiw4cw.ttf'
  },
  {
    label: 'Do Hyeon',
    value: 'https://fonts.gstatic.com/s/dohyeon/v19/TwMN-I8CRRU2zM86HFE3ZwaH__-C.ttf'
  },
  {
    label: 'Dokdo',
    value: 'https://fonts.gstatic.com/s/dokdo/v17/esDf315XNuCBLxLo4NaMlKcH.ttf'
  },
  {
    label: 'Domine',
    value: 'https://fonts.gstatic.com/s/domine/v23/L0xhDFMnlVwD4h3Lt9JWnbX3jG-2X3LAI10VErGuW8Q.ttf'
  },
  {
    label: 'Donegal One',
    value: 'https://fonts.gstatic.com/s/donegalone/v21/m8JWjfRYea-ZnFz6fsK9FZRFRG-K3Mud.ttf'
  },
  {
    label: 'Dongle',
    value: 'https://fonts.gstatic.com/s/dongle/v12/sJoF3Ltdjt6VPkqmveRPah6RxA.ttf'
  },
  {
    label: 'Doppio One',
    value: 'https://fonts.gstatic.com/s/doppioone/v13/Gg8wN5gSaBfyBw2MqCh-lgshKGpe5Fg.ttf'
  },
  {
    label: 'Dorsa',
    value: 'https://fonts.gstatic.com/s/dorsa/v27/yYLn0hjd0OGwqo493XCFxAnQ.ttf'
  },
  {
    label: 'Dosis',
    value: 'https://fonts.gstatic.com/s/dosis/v32/HhyJU5sn9vOmLxNkIwRSjTVNWLEJN7MV3BkFTq4EPw.ttf'
  },
  {
    label: 'DotGothic16',
    value: 'https://fonts.gstatic.com/s/dotgothic16/v17/v6-QGYjBJFKgyw5nSoDAGE7L435YPFrT.ttf'
  },
  {
    label: 'Dr Sugiyama',
    value: 'https://fonts.gstatic.com/s/drsugiyama/v28/HTxoL2k4N3O9n5I1boGI7abRM4-t-g7y.ttf'
  },
  {
    label: 'Duru Sans',
    value: 'https://fonts.gstatic.com/s/durusans/v20/xn7iYH8xwmSyTvEV_HOxT_fYdN-WZw.ttf'
  },
  {
    label: 'DynaPuff',
    value: 'https://fonts.gstatic.com/s/dynapuff/v4/z7N5dRvsZDIVHbYPMhZJ3HQ83UaSu4uhr7-ZFeoYkgAr1x8RSxYu6YjrSRs4wn8.ttf'
  },
  {
    label: 'Dynalight',
    value: 'https://fonts.gstatic.com/s/dynalight/v22/1Ptsg8LOU_aOmQvTsF4ISotrDfGGxA.ttf'
  },
  {
    label: 'EB Garamond',
    value: 'https://fonts.gstatic.com/s/ebgaramond/v27/SlGDmQSNjdsmc35JDF1K5E55YMjF_7DPuGi-6_RUA4V-e6yHgQ.ttf'
  },
  {
    label: 'Eagle Lake',
    value: 'https://fonts.gstatic.com/s/eaglelake/v24/ptRMTiqbbuNJDOiKj9wG5O7yKQNute8.ttf'
  },
  {
    label: 'East Sea Dokdo',
    value: 'https://fonts.gstatic.com/s/eastseadokdo/v22/xfuo0Wn2V2_KanASqXSZp22m05_aGavYS18y.ttf'
  },
  {
    label: 'Eater',
    value: 'https://fonts.gstatic.com/s/eater/v25/mtG04_FCK7bOvpu2u3FwsXsR.ttf'
  },
  {
    label: 'Economica',
    value: 'https://fonts.gstatic.com/s/economica/v15/Qw3fZQZaHCLgIWa29ZBrMcgAAl1lfQ.ttf'
  },
  {
    label: 'Eczar',
    value: 'https://fonts.gstatic.com/s/eczar/v22/BXR2vF3Pi-DLmxcpJB-qbNTyTMDXHd6WqTIVKWJKWg.ttf'
  },
  {
    label: 'Edu AU VIC WA NT Hand',
    value:
      'https://fonts.gstatic.com/s/eduauvicwanthand/v1/C8cO4dY1tX2x0uuiUHFS4y7ERV-jfqJ6x063HfvcsxiYKifhtCJ1lKtFbYfTEUU.ttf'
  },
  {
    label: 'Edu NSW ACT Foundation',
    value:
      'https://fonts.gstatic.com/s/edunswactfoundation/v2/raxRHjqJtsNBFUi8WO0vUBgc9D-2lV_oQdCAYlt_QTQ0vUxJki9tovGLeC-sfguJ.ttf'
  },
  {
    label: 'Edu QLD Beginner',
    value: 'https://fonts.gstatic.com/s/eduqldbeginner/v3/AMOHz5iUuHLEMNXyohhc_Y56PR3A8dNLF_w3Ka4HKE4E3oebi6vyVWCN.ttf'
  },
  {
    label: 'Edu SA Beginner',
    value: 'https://fonts.gstatic.com/s/edusabeginner/v3/rnC_-xRb1x-1lHXnLaZZ2xOoLIGfU3L82irpr_3C9989fo1yBydUEDs.ttf'
  },
  {
    label: 'Edu TAS Beginner',
    value: 'https://fonts.gstatic.com/s/edutasbeginner/v3/ZXuwe04WubHfGVY-1TcNg7AFUmshg8jIUTzK3r34f_HwemkrBWRhvk02.ttf'
  },
  {
    label: 'Edu VIC WA NT Beginner',
    value:
      'https://fonts.gstatic.com/s/eduvicwantbeginner/v4/jiz2RF1BuW9OwcnNPxLl4KfZCHd9nFtd5Tu7stCpElYpvPfZZ-OXlPmFXwnpkeGR.ttf'
  },
  {
    label: 'El Messiri',
    value: 'https://fonts.gstatic.com/s/elmessiri/v22/K2FhfZBRmr9vQ1pHEey6GIGo8_pv3myYjuXwe65ghj3OoapG.ttf'
  },
  {
    label: 'Electrolize',
    value: 'https://fonts.gstatic.com/s/electrolize/v18/cIf5Ma1dtE0zSiGSiED7AUEGso5tQafB.ttf'
  },
  {
    label: 'Elsie',
    value: 'https://fonts.gstatic.com/s/elsie/v24/BCanqZABrez54yYu9slAeLgX.ttf'
  },
  {
    label: 'Elsie Swash Caps',
    value: 'https://fonts.gstatic.com/s/elsieswashcaps/v24/845DNN8xGZyVX5MVo_upKf7KnjK0ferVKGWsUo8.ttf'
  },
  {
    label: 'Emblema One',
    value: 'https://fonts.gstatic.com/s/emblemaone/v21/nKKT-GQ0F5dSY8vzG0rOEIRBHl57G_f_.ttf'
  },
  {
    label: 'Emilys Candy',
    value: 'https://fonts.gstatic.com/s/emilyscandy/v19/2EbgL-1mD1Rnb0OGKudbk0y5r9xrX84JjA.ttf'
  },
  {
    label: 'Encode Sans',
    value:
      'https://fonts.gstatic.com/s/encodesans/v19/LDIcapOFNxEwR-Bd1O9uYNmnUQomAgE25imKSbHhROjLsZBWTSrQGGHjZtWP7FJCt2c.ttf'
  },
  {
    label: 'Encode Sans Condensed',
    value: 'https://fonts.gstatic.com/s/encodesanscondensed/v10/j8_16_LD37rqfuwxyIuaZhE6cRXOLtm2gfTGgaWNDw8VIw.ttf'
  },
  {
    label: 'Encode Sans Expanded',
    value: 'https://fonts.gstatic.com/s/encodesansexpanded/v11/c4m_1mF4GcnstG_Jh1QH6ac4hNLeNyeYUqoiIwdAd5Ab.ttf'
  },
  {
    label: 'Encode Sans SC',
    value:
      'https://fonts.gstatic.com/s/encodesanssc/v9/jVyp7nLwCGzQ9zE7ZyRg0QRXHPZc_uUA6Kb3VJWLE_Pdtm7lcD6qvXT1HHhm8c9NOEEClIc.ttf'
  },
  {
    label: 'Encode Sans Semi Condensed',
    value:
      'https://fonts.gstatic.com/s/encodesanssemicondensed/v10/3qT4oiKqnDuUtQUEHMoXcmspmy55SFWrXFRp9FTOG2yR_sVPRsjp.ttf'
  },
  {
    label: 'Encode Sans Semi Expanded',
    value:
      'https://fonts.gstatic.com/s/encodesanssemiexpanded/v19/ke83OhAPMEZs-BDuzwftTNJ85JvwMOzE9d9Cca5TC4o_LyjgOXc.ttf'
  },
  {
    label: 'Engagement',
    value: 'https://fonts.gstatic.com/s/engagement/v26/x3dlckLDZbqa7RUs9MFVXNossybsHQI.ttf'
  },
  {
    label: 'Englebert',
    value: 'https://fonts.gstatic.com/s/englebert/v21/xn7iYH8w2XGrC8AR4HSxT_fYdN-WZw.ttf'
  },
  {
    label: 'Enriqueta',
    value: 'https://fonts.gstatic.com/s/enriqueta/v17/goksH6L7AUFrRvV44HVTS0CjkP1Yog.ttf'
  },
  {
    label: 'Ephesis',
    value: 'https://fonts.gstatic.com/s/ephesis/v9/uU9PCBUS8IerL2VG7xPb3vyHmlI.ttf'
  },
  {
    label: 'Epilogue',
    value: 'https://fonts.gstatic.com/s/epilogue/v17/O4ZMFGj5hxF0EhjimngomvnCCtqb30OXMDPiDJXVigHPVA.ttf'
  },
  {
    label: 'Erica One',
    value: 'https://fonts.gstatic.com/s/ericaone/v27/WBLnrEXccV9VGrOKmGD1W0_MJMGxiQ.ttf'
  },
  {
    label: 'Esteban',
    value: 'https://fonts.gstatic.com/s/esteban/v15/r05bGLZE-bdGdN-GdOuD5jokU8E.ttf'
  },
  {
    label: 'Estonia',
    value: 'https://fonts.gstatic.com/s/estonia/v11/7Au_p_4ijSecA1yHCCL8zkwMIFg.ttf'
  },
  {
    label: 'Euphoria Script',
    value: 'https://fonts.gstatic.com/s/euphoriascript/v20/mFTpWb0X2bLb_cx6To2B8GpKoD5ak_ZT1D8x7Q.ttf'
  },
  {
    label: 'Ewert',
    value: 'https://fonts.gstatic.com/s/ewert/v25/va9I4kzO2tFODYBvS-J3kbDP.ttf'
  },
  {
    label: 'Exo',
    value: 'https://fonts.gstatic.com/s/exo/v21/4UaZrEtFpBI4f1ZSIK9d4LjJ4lM3CwNsOl4p5Is.ttf'
  },
  {
    label: 'Exo 2',
    value: 'https://fonts.gstatic.com/s/exo2/v21/7cH1v4okm5zmbvwkAx_sfcEuiD8jvvKcPtq-rpvLpQ.ttf'
  },
  {
    label: 'Expletus Sans',
    value: 'https://fonts.gstatic.com/s/expletussans/v29/RLpqK5v5_bqufTYdnhFzDj2dX_IwS3my73zcDaSY2s1oFQTcXfMm.ttf'
  },
  {
    label: 'Explora',
    value: 'https://fonts.gstatic.com/s/explora/v9/tsstApxFfjUH4wrvc1qPonC3vqc.ttf'
  },
  {
    label: 'Fahkwang',
    value: 'https://fonts.gstatic.com/s/fahkwang/v16/Noax6Uj3zpmBOgbNpNqPsr1ZPTZ4.ttf'
  },
  {
    label: 'Familjen Grotesk',
    value: 'https://fonts.gstatic.com/s/familjengrotesk/v8/Qw3LZR9ZHiDnImG6-NEMQ41wby8WRnYsfkunR_eGfMGJaSztc1jcEYq2.ttf'
  },
  {
    label: 'Fanwood Text',
    value: 'https://fonts.gstatic.com/s/fanwoodtext/v15/3XFtErwl05Ad_vSCF6Fq7xXGRdbY1P1Sbg.ttf'
  },
  {
    label: 'Farro',
    value: 'https://fonts.gstatic.com/s/farro/v14/i7dEIFl3byGNHZVNHLq2cV5d.ttf'
  },
  {
    label: 'Farsan',
    value: 'https://fonts.gstatic.com/s/farsan/v22/VEMwRoJ0vY_zsyz62q-pxDX9rQ.ttf'
  },
  {
    label: 'Fascinate',
    value: 'https://fonts.gstatic.com/s/fascinate/v21/z7NWdRrufC8XJK0IIEli1LbQRPyNrw.ttf'
  },
  {
    label: 'Fascinate Inline',
    value: 'https://fonts.gstatic.com/s/fascinateinline/v22/jVyR7mzzB3zc-jp6QCAu60poNqIy1g3CfRXxWZQ.ttf'
  },
  {
    label: 'Faster One',
    value: 'https://fonts.gstatic.com/s/fasterone/v19/H4ciBXCHmdfClFb-vWhfyLuShq63czE.ttf'
  },
  {
    label: 'Fasthand',
    value: 'https://fonts.gstatic.com/s/fasthand/v30/0yb9GDohyKTYn_ZEESkuYkw2rQg1.ttf'
  },
  {
    label: 'Fauna One',
    value: 'https://fonts.gstatic.com/s/faunaone/v15/wlpzgwTPBVpjpCuwkuEx2UxLYClOCg.ttf'
  },
  {
    label: 'Faustina',
    value: 'https://fonts.gstatic.com/s/faustina/v20/XLY4IZPxYpJfTbZAFXWzNT2SO8wpWHlsgoEvGVWWe8tbEg.ttf'
  },
  {
    label: 'Federant',
    value: 'https://fonts.gstatic.com/s/federant/v29/2sDdZGNfip_eirT0_U0jRUG0AqUc.ttf'
  },
  {
    label: 'Federo',
    value: 'https://fonts.gstatic.com/s/federo/v19/iJWFBX-cbD_ETsbmjVOe2WTG7Q.ttf'
  },
  {
    label: 'Felipa',
    value: 'https://fonts.gstatic.com/s/felipa/v25/FwZa7-owz1Eu4F_wSNSEwM2zpA.ttf'
  },
  {
    label: 'Fenix',
    value: 'https://fonts.gstatic.com/s/fenix/v20/XoHo2YL_S7-g5ostKzAFvs8o.ttf'
  },
  {
    label: 'Festive',
    value: 'https://fonts.gstatic.com/s/festive/v9/cY9Ffj6KX1xcoDWhFtfgy9HTkak.ttf'
  },
  {
    label: 'Figtree',
    value: 'https://fonts.gstatic.com/s/figtree/v5/_Xmz-HUzqDCFdgfMsYiV_F7wfS-Bs_d_QF5ewkEU4HTy.ttf'
  },
  {
    label: 'Finger Paint',
    value: 'https://fonts.gstatic.com/s/fingerpaint/v19/0QInMXVJ-o-oRn_7dron8YWO85bS8ANesw.ttf'
  },
  {
    label: 'Finlandica',
    value: 'https://fonts.gstatic.com/s/finlandica/v8/-nFsOGk-8vAc7lEtg0aSyZCty9GSsPBE19A7rEjx9i5ss3a3.ttf'
  },
  {
    label: 'Fira Code',
    value: 'https://fonts.gstatic.com/s/firacode/v22/uU9eCBsR6Z2vfE9aq3bL0fxyUs4tcw4W_D1sFVfxN87gsj0.ttf'
  },
  {
    label: 'Fira Mono',
    value: 'https://fonts.gstatic.com/s/firamono/v14/N0bX2SlFPv1weGeLZDtQIfTTkdbJYA.ttf'
  },
  {
    label: 'Fira Sans',
    value: 'https://fonts.gstatic.com/s/firasans/v17/va9E4kDNxMZdWfMOD5VfkILKSTbndQ.ttf'
  },
  {
    label: 'Fira Sans Condensed',
    value: 'https://fonts.gstatic.com/s/firasanscondensed/v10/wEOhEADFm8hSaQTFG18FErVhsC9x-tarYfHnrMtVbx8.ttf'
  },
  {
    label: 'Fira Sans Extra Condensed',
    value:
      'https://fonts.gstatic.com/s/firasansextracondensed/v10/NaPKcYDaAO5dirw6IaFn7lPJFqXmS-M9Atn3wgda5fiku3efvE8.ttf'
  },
  {
    label: 'Fjalla One',
    value: 'https://fonts.gstatic.com/s/fjallaone/v15/Yq6R-LCAWCX3-6Ky7FAFnOZwkxgtUb8.ttf'
  },
  {
    label: 'Fjord One',
    value: 'https://fonts.gstatic.com/s/fjordone/v21/zOL-4pbEnKBY_9S1jNKr6e5As-FeiQ.ttf'
  },
  {
    label: 'Flamenco',
    value: 'https://fonts.gstatic.com/s/flamenco/v18/neIIzCehqYguo67ssaWGHK06UY30.ttf'
  },
  {
    label: 'Flavors',
    value: 'https://fonts.gstatic.com/s/flavors/v26/FBV2dDrhxqmveJTpbkzlNqkG9UY.ttf'
  },
  {
    label: 'Fleur De Leah',
    value: 'https://fonts.gstatic.com/s/fleurdeleah/v9/AYCNpXX7ftYZWLhv9UmPJTMC5vat4I_Gdq0.ttf'
  },
  {
    label: 'Flow Block',
    value: 'https://fonts.gstatic.com/s/flowblock/v11/wlp0gwfPCEB65UmTk-d6-WZlbCBXE_I.ttf'
  },
  {
    label: 'Flow Circular',
    value: 'https://fonts.gstatic.com/s/flowcircular/v11/lJwB-pc4j2F-H8YKuyvfxdZ45ifpWdr2rIg.ttf'
  },
  {
    label: 'Flow Rounded',
    value: 'https://fonts.gstatic.com/s/flowrounded/v11/-zki91mtwsU9qlLiGwD4oQX3oZX-Xup87g.ttf'
  },
  {
    label: 'Foldit',
    value: 'https://fonts.gstatic.com/s/foldit/v5/aFTI7PF3Y3c9WdjNrRVE0Rk2b7j8XpAMmapUYLHkN80.ttf'
  },
  {
    label: 'Fondamento',
    value: 'https://fonts.gstatic.com/s/fondamento/v20/4UaHrEJGsxNmFTPDnkaJx63j5pN1MwI.ttf'
  },
  {
    label: 'Fontdiner Swanky',
    value: 'https://fonts.gstatic.com/s/fontdinerswanky/v23/ijwOs4XgRNsiaI5-hcVb4hQgMvCD4uEfKiGvxts.ttf'
  },
  {
    label: 'Forum',
    value: 'https://fonts.gstatic.com/s/forum/v18/6aey4Ky-Vb8Ew_IWMJMa3mnT.ttf'
  },
  {
    label: 'Fragment Mono',
    value: 'https://fonts.gstatic.com/s/fragmentmono/v4/4iCr6K5wfMRRjxp0DA6-2CLnN4RNh4UI_1U.ttf'
  },
  {
    label: 'Francois One',
    value: 'https://fonts.gstatic.com/s/francoisone/v21/_Xmr-H4zszafZw3A-KPSZutNxgKQu_avAg.ttf'
  },
  {
    label: 'Frank Ruhl Libre',
    value: 'https://fonts.gstatic.com/s/frankruhllibre/v21/j8_96_fAw7jrcalD7oKYNX0QfAnPcbzNEEB7OoicBw7FYVqQPxR2EUR_.ttf'
  },
  {
    label: 'Fraunces',
    value:
      'https://fonts.gstatic.com/s/fraunces/v31/6NUh8FyLNQOQZAnv9bYEvDiIdE9Ea92uemAk_WBq8U_9v0c2Wa0K7iN7hzFUPJH58nib1603gg7S2nfgRYIctxujDvTShUtWNg.ttf'
  },
  {
    label: 'Freckle Face',
    value: 'https://fonts.gstatic.com/s/freckleface/v15/AMOWz4SXrmKHCvXTohxY-YI0U1K2w9lb4g.ttf'
  },
  {
    label: 'Fredericka the Great',
    value: 'https://fonts.gstatic.com/s/frederickathegreat/v21/9Bt33CxNwt7aOctW2xjbCstzwVKsIBVV-9Skz7Ylch2L.ttf'
  },
  {
    label: 'Fredoka',
    value: 'https://fonts.gstatic.com/s/fredoka/v14/X7nP4b87HvSqjb_WIi2yDCRwoQ_k7367_B-i2yQag0-mac3O8SLMFuOLlNldbw.ttf'
  },
  {
    label: 'Freehand',
    value: 'https://fonts.gstatic.com/s/freehand/v31/cIf-Ma5eqk01VjKTgAmBTmUOmZJk.ttf'
  },
  {
    label: 'Freeman',
    value: 'https://fonts.gstatic.com/s/freeman/v1/S6u9w4NGQiLN8nh-ew-FGC_p9dw.ttf'
  },
  {
    label: 'Fresca',
    value: 'https://fonts.gstatic.com/s/fresca/v22/6ae94K--SKgCzbM2Gr0W13DKPA.ttf'
  },
  {
    label: 'Frijole',
    value: 'https://fonts.gstatic.com/s/frijole/v14/uU9PCBUR8oakM2BQ7xPb3vyHmlI.ttf'
  },
  {
    label: 'Fruktur',
    value: 'https://fonts.gstatic.com/s/fruktur/v27/SZc53FHsOru5QYsMfz3GkUrS8DI.ttf'
  },
  {
    label: 'Fugaz One',
    value: 'https://fonts.gstatic.com/s/fugazone/v19/rax_HiWKp9EAITukFslMBBJek0vA8A.ttf'
  },
  {
    label: 'Fuggles',
    value: 'https://fonts.gstatic.com/s/fuggles/v12/k3kQo8UEJOlD1hpOTd7iL0nAMaM.ttf'
  },
  {
    label: 'Fustat',
    value: 'https://fonts.gstatic.com/s/fustat/v1/NaPEcZ_aHO9Iy5thRNqze7YGY9HGCEQQRryztWo_3fk.ttf'
  },
  {
    label: 'Fuzzy Bubbles',
    value: 'https://fonts.gstatic.com/s/fuzzybubbles/v7/6qLGKZMbrgv9pwtjPEVNV0F2NnP5Zxsreko.ttf'
  },
  {
    label: 'GFS Didot',
    value: 'https://fonts.gstatic.com/s/gfsdidot/v16/Jqzh5TybZ9vZMWFssvwiF-fGFSCGAA.ttf'
  },
  {
    label: 'GFS Neohellenic',
    value: 'https://fonts.gstatic.com/s/gfsneohellenic/v25/8QIRdiDOrfiq0b7R8O1Iw9WLcY5TLahP46UDUw.ttf'
  },
  {
    label: 'Ga Maamli',
    value: 'https://fonts.gstatic.com/s/gamaamli/v1/uU9NCBsQ4c-DPW1Yo3rR2t6CilKOdQ.ttf'
  },
  {
    label: 'Gabarito',
    value: 'https://fonts.gstatic.com/s/gabarito/v7/QGYwz_0dZAGKJJ4t3FFkc3Q8AkNP9Pj248K0FgItq6bFIg.ttf'
  },
  {
    label: 'Gabriela',
    value: 'https://fonts.gstatic.com/s/gabriela/v21/qkBWXvsO6sreR8E-b_m-zrpHmRzC.ttf'
  },
  {
    label: 'Gaegu',
    value: 'https://fonts.gstatic.com/s/gaegu/v17/TuGfUVB6Up9NU6ZLodgzydtk.ttf'
  },
  {
    label: 'Gafata',
    value: 'https://fonts.gstatic.com/s/gafata/v20/XRXV3I6Cn0VJKon4MuyAbsrVcA.ttf'
  },
  {
    label: 'Gajraj One',
    value: 'https://fonts.gstatic.com/s/gajrajone/v5/1cX2aUDCDpXsuWVb1jIjr1GqhcitzeM.ttf'
  },
  {
    label: 'Galada',
    value: 'https://fonts.gstatic.com/s/galada/v18/H4cmBXyGmcjXlUX-8iw-4Lqggw.ttf'
  },
  {
    label: 'Galdeano',
    value: 'https://fonts.gstatic.com/s/galdeano/v22/uU9MCBoQ4YOqOW1boDPx8PCOg0uX.ttf'
  },
  {
    label: 'Galindo',
    value: 'https://fonts.gstatic.com/s/galindo/v24/HI_KiYMeLqVKqwyuQ5HiRp-dhpQ.ttf'
  },
  {
    label: 'Gamja Flower',
    value: 'https://fonts.gstatic.com/s/gamjaflower/v22/6NUR8FiKJg-Pa0rM6uN40Z4kyf9Fdty2ew.ttf'
  },
  {
    label: 'Gantari',
    value: 'https://fonts.gstatic.com/s/gantari/v1/jVyV7nvyB2HL8iZyDk4GVvSZ5MtC9g0gOj3wa5GD2qnm.ttf'
  },
  {
    label: 'Gasoek One',
    value: 'https://fonts.gstatic.com/s/gasoekone/v3/EJRTQgQ_UMUKvDgnlX80zrq_cyb-vco.ttf'
  },
  {
    label: 'Gayathri',
    value: 'https://fonts.gstatic.com/s/gayathri/v17/MCoQzAb429DbBilWLIA48J_wBugA.ttf'
  },
  {
    label: 'Gelasio',
    value: 'https://fonts.gstatic.com/s/gelasio/v12/cIfiMaFfvUQxTTqS3iKJkLGbI41wQL8Ilycs-TDO8Aba.ttf'
  },
  {
    label: 'Gemunu Libre',
    value: 'https://fonts.gstatic.com/s/gemunulibre/v14/X7n34bQ6Cfy7jKGXVE_YlqnbEQAFP-PIuTCp0xiJPvSLeMXPIWA.ttf'
  },
  {
    label: 'Genos',
    value: 'https://fonts.gstatic.com/s/genos/v12/SlGNmQqPqpUOYTYjacb0Hc91fTwVqkjorUK6K7ZsAg.ttf'
  },
  {
    label: 'Gentium Book Plus',
    value: 'https://fonts.gstatic.com/s/gentiumbookplus/v1/vEFL2-RHBgUK5fbjKxRpbBtJPyRpofKfdbLOrdPV.ttf'
  },
  {
    label: 'Gentium Plus',
    value: 'https://fonts.gstatic.com/s/gentiumplus/v2/Iurd6Ytw-oSPaZ00r2bNe8VpjJtM6G0t9w.ttf'
  },
  {
    label: 'Geo',
    value: 'https://fonts.gstatic.com/s/geo/v21/CSRz4zRZlufVL3BmQjlCbQ.ttf'
  },
  {
    label: 'Geologica',
    value:
      'https://fonts.gstatic.com/s/geologica/v1/oY1o8evIr7j9P3TN9YwNAdyjzUyDKkKdAGOJh1UlCDUIhAIdhCZOn1fLsig7jfvCCPHZckU8H3G11_z-_OZqDx_rQ-MYAXWnqFs.ttf'
  },
  {
    label: 'Georama',
    value: 'https://fonts.gstatic.com/s/georama/v13/MCo5zAn438bIEyxFf6swMnNpvPcUwW4u4yRcDh-ZjxApn9K5GvgtmQsL5_tgbg.ttf'
  },
  {
    label: 'Geostar',
    value: 'https://fonts.gstatic.com/s/geostar/v26/sykz-yx4n701VLOftSq9-trEvlQ.ttf'
  },
  {
    label: 'Geostar Fill',
    value: 'https://fonts.gstatic.com/s/geostarfill/v26/AMOWz4SWuWiXFfjEohxQ9os0U1K2w9lb4g.ttf'
  },
  {
    label: 'Germania One',
    value: 'https://fonts.gstatic.com/s/germaniaone/v20/Fh4yPjrqIyv2ucM2qzBjeS3ezAJONau6ew.ttf'
  },
  {
    label: 'Gideon Roman',
    value: 'https://fonts.gstatic.com/s/gideonroman/v11/e3tmeuGrVOys8sxzZgWlmXoge0PWovdU4w.ttf'
  },
  {
    label: 'Gidugu',
    value: 'https://fonts.gstatic.com/s/gidugu/v25/L0x8DFMkk1Uf6w3RvPCmRSlUig.ttf'
  },
  {
    label: 'Gilda Display',
    value: 'https://fonts.gstatic.com/s/gildadisplay/v18/t5tmIRoYMoaYG0WEOh7HwMeR7TnFrpOHYh4.ttf'
  },
  {
    label: 'Girassol',
    value: 'https://fonts.gstatic.com/s/girassol/v22/JTUUjIo_-DK48laaNC9Nz2pJzxbi.ttf'
  },
  {
    label: 'Give You Glory',
    value: 'https://fonts.gstatic.com/s/giveyouglory/v15/8QIQdiHOgt3vv4LR7ahjw9-XYc1zB4ZD6rwa.ttf'
  },
  {
    label: 'Glass Antiqua',
    value: 'https://fonts.gstatic.com/s/glassantiqua/v24/xfu30Wr0Wn3NOQM2piC0uXOjnL_wN6fRUkY.ttf'
  },
  {
    label: 'Glegoo',
    value: 'https://fonts.gstatic.com/s/glegoo/v16/_Xmt-HQyrTKWaw2Ji6mZAI91xw.ttf'
  },
  {
    label: 'Gloock',
    value: 'https://fonts.gstatic.com/s/gloock/v6/Iurb6YFw84WUY4N5jxylBrdRjQ.ttf'
  },
  {
    label: 'Gloria Hallelujah',
    value: 'https://fonts.gstatic.com/s/gloriahallelujah/v21/LYjYdHv3kUk9BMV96EIswT9DIbW-MLSy3TKEvkCF.ttf'
  },
  {
    label: 'Glory',
    value: 'https://fonts.gstatic.com/s/glory/v15/q5uasoi9Lf1w5t3Est24nq9blIRQwImDpn-dDi9EOQ.ttf'
  },
  {
    label: 'Gluten',
    value: 'https://fonts.gstatic.com/s/gluten/v16/Hhy_U5gk9fW7OUdVIPh2zD_RSqQJ__A15jgJsn-Bhb_yI8Vb7R1Luni7ciJh.ttf'
  },
  {
    label: 'Goblin One',
    value: 'https://fonts.gstatic.com/s/goblinone/v26/CSR64z1ZnOqZRjRCBVY_TOcATNt_pOU.ttf'
  },
  {
    label: 'Gochi Hand',
    value: 'https://fonts.gstatic.com/s/gochihand/v23/hES06XlsOjtJsgCkx1PkTo71-n0nXWA.ttf'
  },
  {
    label: 'Goldman',
    value: 'https://fonts.gstatic.com/s/goldman/v19/pe0uMIWbN4JFplR2LDJ4Bt-7G98.ttf'
  },
  {
    label: 'Golos Text',
    value: 'https://fonts.gstatic.com/s/golostext/v4/q5uXsoe9Lv5t7Meb31EcOR9UdVTNs822plVRRQ5cEr8zXcyx.ttf'
  },
  {
    label: 'Gorditas',
    value: 'https://fonts.gstatic.com/s/gorditas/v22/ll8_K2aTVD26DsPEtQDoDa4AlxYb.ttf'
  },
  {
    label: 'Gothic A1',
    value: 'https://fonts.gstatic.com/s/gothica1/v13/CSR94z5ZnPydRjlCCwl6bM0uQNJmvQ.ttf'
  },
  {
    label: 'Gotu',
    value: 'https://fonts.gstatic.com/s/gotu/v16/o-0FIpksx3QOlH0Lioh6-hU.ttf'
  },
  {
    label: 'Goudy Bookletter 1911',
    value: 'https://fonts.gstatic.com/s/goudybookletter1911/v19/sykt-z54laciWfKv-kX8krex0jDiD2HbY6I5tRbXZ4IXAA.ttf'
  },
  {
    label: 'Gowun Batang',
    value: 'https://fonts.gstatic.com/s/gowunbatang/v7/ijwSs5nhRMIjYsdSgcMa3wRhXLH-yuAtLw.ttf'
  },
  {
    label: 'Gowun Dodum',
    value: 'https://fonts.gstatic.com/s/gowundodum/v7/3Jn5SD_00GqwlBnWc1TUJF0FfORL0fNy.ttf'
  },
  {
    label: 'Graduate',
    value: 'https://fonts.gstatic.com/s/graduate/v17/C8cg4cs3o2n15t_2YxgR6X2NZAn2.ttf'
  },
  {
    label: 'Grand Hotel',
    value: 'https://fonts.gstatic.com/s/grandhotel/v19/7Au7p_IgjDKdCRWuR1azpmQNEl0O0kEx.ttf'
  },
  {
    label: 'Grandiflora One',
    value: 'https://fonts.gstatic.com/s/grandifloraone/v3/0ybmGD0g27bCk_5MGWZcKWhxwnUU_R3y8DOWGA.ttf'
  },
  {
    label: 'Grandstander',
    value: 'https://fonts.gstatic.com/s/grandstander/v17/ga6fawtA-GpSsTWrnNHPCSIMZhhKpFjyNZIQD1--D3jWttFGmQk.ttf'
  },
  {
    label: 'Grape Nuts',
    value: 'https://fonts.gstatic.com/s/grapenuts/v5/syk2-yF4iLM2RfKj4F7k3tLvol2RN1E.ttf'
  },
  {
    label: 'Gravitas One',
    value: 'https://fonts.gstatic.com/s/gravitasone/v19/5h1diZ4hJ3cblKy3LWakKQmaDWRNr3DzbQ.ttf'
  },
  {
    label: 'Great Vibes',
    value: 'https://fonts.gstatic.com/s/greatvibes/v19/RWmMoKWR9v4ksMfaWd_JN-XCg6UKDXlq.ttf'
  },
  {
    label: 'Grechen Fuemen',
    value: 'https://fonts.gstatic.com/s/grechenfuemen/v9/vEFI2_tHEQ4d5ObgKxBzZh0MAWgc-NaXXq7H.ttf'
  },
  {
    label: 'Grenze',
    value: 'https://fonts.gstatic.com/s/grenze/v15/O4ZTFGb7hR12Bxq3_2gnmgwKlg.ttf'
  },
  {
    label: 'Grenze Gotisch',
    value: 'https://fonts.gstatic.com/s/grenzegotisch/v18/Fh4hPjjqNDz1osh_jX9YfjudpBJBNV5y5wf_k1i5Lz9UcICdYPSd_w.ttf'
  },
  {
    label: 'Grey Qo',
    value: 'https://fonts.gstatic.com/s/greyqo/v9/BXRrvF_Nmv_TyXxNDOtQ9Wf0QcE.ttf'
  },
  {
    label: 'Griffy',
    value: 'https://fonts.gstatic.com/s/griffy/v22/FwZa7-ox2FQh9kfwSNSEwM2zpA.ttf'
  },
  {
    label: 'Gruppo',
    value: 'https://fonts.gstatic.com/s/gruppo/v21/WwkfxPmzE06v_ZWFWXDAOIEQUQ.ttf'
  },
  {
    label: 'Gudea',
    value: 'https://fonts.gstatic.com/s/gudea/v15/neIFzCqgsI0mp-CP9IGON7Ez.ttf'
  },
  {
    label: 'Gugi',
    value: 'https://fonts.gstatic.com/s/gugi/v15/A2BVn5dXywshVA6A9DEfgqM.ttf'
  },
  {
    label: 'Gulzar',
    value: 'https://fonts.gstatic.com/s/gulzar/v12/Wnz6HAc9eB3HB2ILYTwZqg_MPQ.ttf'
  },
  {
    label: 'Gupter',
    value: 'https://fonts.gstatic.com/s/gupter/v14/2-cm9JNmxJqPO1QUYZa_Wu_lpA.ttf'
  },
  {
    label: 'Gurajada',
    value: 'https://fonts.gstatic.com/s/gurajada/v19/FwZY7-Qx308m-l-0Kd6A4sijpFu_.ttf'
  },
  {
    label: 'Gwendolyn',
    value: 'https://fonts.gstatic.com/s/gwendolyn/v7/qkBXXvoO_M3CSss-d7ee5JRLkAXbMQ.ttf'
  },
  {
    label: 'Habibi',
    value: 'https://fonts.gstatic.com/s/habibi/v21/CSR-4zFWkuqcTTNCShJeZOYySQ.ttf'
  },
  {
    label: 'Hachi Maru Pop',
    value: 'https://fonts.gstatic.com/s/hachimarupop/v19/HI_TiYoRLqpLrEiMAuO9Ysfz7rW1EM_btd8u.ttf'
  },
  {
    label: 'Hahmlet',
    value: 'https://fonts.gstatic.com/s/hahmlet/v13/BngXUXpCQ3nKpIo0TfPyfCdXfaeU4RhKONjobsO-aVxn.ttf'
  },
  {
    label: 'Halant',
    value: 'https://fonts.gstatic.com/s/halant/v14/u-4-0qaujRI2PbsX39Jmky12eg.ttf'
  },
  {
    label: 'Hammersmith One',
    value: 'https://fonts.gstatic.com/s/hammersmithone/v17/qWcyB624q4L_C4jGQ9IK0O_dFlnbshsks4MRXw.ttf'
  },
  {
    label: 'Hanalei',
    value: 'https://fonts.gstatic.com/s/hanalei/v23/E21n_dD8iufIjBRHXzgmVydREus.ttf'
  },
  {
    label: 'Hanalei Fill',
    value: 'https://fonts.gstatic.com/s/hanaleifill/v22/fC1mPYtObGbfyQznIaQzPQiMVwLBplm9aw.ttf'
  },
  {
    label: 'Handjet',
    value:
      'https://fonts.gstatic.com/s/handjet/v19/oY1n8eXHq7n1OnbQrOY_2FrEwYEMLlcdP1mCtZaLaTutCwcIhGZ0lGU0akFcO3XFHTmaUkUsEHFAH2A.ttf'
  },
  {
    label: 'Handlee',
    value: 'https://fonts.gstatic.com/s/handlee/v18/-F6xfjBsISg9aMakDmr6oilJ3ik.ttf'
  },
  {
    label: 'Hanken Grotesk',
    value: 'https://fonts.gstatic.com/s/hankengrotesk/v8/ieVq2YZDLWuGJpnzaiwFXS9tYvBRzyFLlZg_f_Ncs2Za4fpNzXhRKA.ttf'
  },
  {
    label: 'Hanuman',
    value: 'https://fonts.gstatic.com/s/hanuman/v22/VuJxdNvD15HhpJJBeKbXOIFneRo.ttf'
  },
  {
    label: 'Happy Monkey',
    value: 'https://fonts.gstatic.com/s/happymonkey/v14/K2F2fZZcl-9SXwl5F_C4R_OABwD2bWqVjw.ttf'
  },
  {
    label: 'Harmattan',
    value: 'https://fonts.gstatic.com/s/harmattan/v19/goksH6L2DkFvVvRp9XpTS0CjkP1Yog.ttf'
  },
  {
    label: 'Headland One',
    value: 'https://fonts.gstatic.com/s/headlandone/v16/yYLu0hHR2vKnp89Tk1TCq3Tx0PlTeZ3mJA.ttf'
  },
  {
    label: 'Hedvig Letters Sans',
    value: 'https://fonts.gstatic.com/s/hedvigletterssans/v2/CHy_V_PfGVjobSBkihHWDT98RVp37w8jQJ1N3Twgi1w.ttf'
  },
  {
    label: 'Hedvig Letters Serif',
    value:
      'https://fonts.gstatic.com/s/hedviglettersserif/v2/OD5puN2I2mekHmyoU1Kj2AXOd5_7v7gIDlX8quj7viQ_N1HixEAZf7fcvSGpZg.ttf'
  },
  {
    label: 'Heebo',
    value: 'https://fonts.gstatic.com/s/heebo/v26/NGSpv5_NC0k9P_v6ZUCbLRAHxK1EiSycckOnz02SXQ.ttf'
  },
  {
    label: 'Henny Penny',
    value: 'https://fonts.gstatic.com/s/hennypenny/v17/wXKvE3UZookzsxz_kjGSfMQqt3M7tMDT.ttf'
  },
  {
    label: 'Hepta Slab',
    value: 'https://fonts.gstatic.com/s/heptaslab/v23/ea8JadoyU_jkHdalebHvyWVNdYoIsHe5HvkV5zfbY5B0NBkz.ttf'
  },
  {
    label: 'Herr Von Muellerhoff',
    value: 'https://fonts.gstatic.com/s/herrvonmuellerhoff/v21/WBL6rFjRZkREW8WqmCWYLgCkQKXb4CAft3c6_qJY3QPQ.ttf'
  },
  {
    label: 'Hi Melody',
    value: 'https://fonts.gstatic.com/s/himelody/v15/46ktlbP8Vnz0pJcqCTbEf29E31BBGA.ttf'
  },
  {
    label: 'Hina Mincho',
    value: 'https://fonts.gstatic.com/s/hinamincho/v12/2sDaZGBRhpXa2Jjz5w5LAGW8KbkVZTHR.ttf'
  },
  {
    label: 'Hind',
    value: 'https://fonts.gstatic.com/s/hind/v16/5aU69_a8oxmIRG5yBROzkDM.ttf'
  },
  {
    label: 'Hind Guntur',
    value: 'https://fonts.gstatic.com/s/hindguntur/v12/wXKvE3UZrok56nvamSuJd8Qqt3M7tMDT.ttf'
  },
  {
    label: 'Hind Madurai',
    value: 'https://fonts.gstatic.com/s/hindmadurai/v11/f0Xx0e2p98ZvDXdZQIOcpqjn8Y0DceA0OQ.ttf'
  },
  {
    label: 'Hind Siliguri',
    value: 'https://fonts.gstatic.com/s/hindsiliguri/v12/ijwTs5juQtsyLLR5jN4cxBEofJvQxuk0Nig.ttf'
  },
  {
    label: 'Hind Vadodara',
    value: 'https://fonts.gstatic.com/s/hindvadodara/v13/neINzCKvrIcn5pbuuuriV9tTcJXfrXsfvSo.ttf'
  },
  {
    label: 'Holtwood One SC',
    value: 'https://fonts.gstatic.com/s/holtwoodonesc/v21/yYLx0hLR0P-3vMFSk1TCq3Txg5B3cbb6LZttyg.ttf'
  },
  {
    label: 'Homemade Apple',
    value: 'https://fonts.gstatic.com/s/homemadeapple/v22/Qw3EZQFXECDrI2q789EKQZJob3x9Vnksi4M7.ttf'
  },
  {
    label: 'Homenaje',
    value: 'https://fonts.gstatic.com/s/homenaje/v16/FwZY7-Q-xVAi_l-6Ld6A4sijpFu_.ttf'
  },
  {
    label: 'Honk',
    value: 'https://fonts.gstatic.com/s/honk/v1/m8J7jftUea-XwTaemClumrBQbmvynOmXBji9zFhHRr8WFgVLo7tNepQKvg.ttf'
  },
  {
    label: 'Hubballi',
    value: 'https://fonts.gstatic.com/s/hubballi/v7/o-0JIpUj3WIZ1RFN56B7yBBNYuSF.ttf'
  },
  {
    label: 'Hurricane',
    value: 'https://fonts.gstatic.com/s/hurricane/v7/pe0sMIuULZxTolZ5YldyAv2-C99ycg.ttf'
  },
  {
    label: 'IBM Plex Mono',
    value: 'https://fonts.gstatic.com/s/ibmplexmono/v19/-F63fjptAgt5VM-kVkqdyU8n5igg1l9kn-s.ttf'
  },
  {
    label: 'IBM Plex Sans',
    value: 'https://fonts.gstatic.com/s/ibmplexsans/v19/zYXgKVElMYYaJe8bpLHnCwDKtdbUFI5NadY.ttf'
  },
  {
    label: 'IBM Plex Sans Arabic',
    value: 'https://fonts.gstatic.com/s/ibmplexsansarabic/v12/Qw3CZRtWPQCuHme67tEYUIx3Kh0PHR9N6bs61vSbfdlA.ttf'
  },
  {
    label: 'IBM Plex Sans Condensed',
    value: 'https://fonts.gstatic.com/s/ibmplexsanscondensed/v14/Gg8lN4UfRSqiPg7Jn2ZI12V4DCEwkj1E4LVeHbauwq_jhJsM.ttf'
  },
  {
    label: 'IBM Plex Sans Devanagari',
    value:
      'https://fonts.gstatic.com/s/ibmplexsansdevanagari/v11/XRXH3JCMvG4IDoS9SubXB6W-UX5iehIMBFR2-O__VUL0c83gCA.ttf'
  },
  {
    label: 'IBM Plex Sans Hebrew',
    value: 'https://fonts.gstatic.com/s/ibmplexsanshebrew/v11/BCa2qYENg9Kw1mpLpO0bGM5lfHAAZHhDXH2l8Fk3rSaM.ttf'
  },
  {
    label: 'IBM Plex Sans JP',
    value: 'https://fonts.gstatic.com/s/ibmplexsansjp/v5/Z9XNDn9KbTDf6_f7dISNqYf_tvPT1Cr4iNJ-pwc.ttf'
  },
  {
    label: 'IBM Plex Sans KR',
    value: 'https://fonts.gstatic.com/s/ibmplexsanskr/v10/vEFK2-VJISZe3O_rc3ZVYh4aTwNO8tK1W77HtMo.ttf'
  },
  {
    label: 'IBM Plex Sans Thai',
    value: 'https://fonts.gstatic.com/s/ibmplexsansthai/v10/m8JPje1VVIzcq1HzJq2AEdo2Tj_qvLq8DtwhZcNaUg.ttf'
  },
  {
    label: 'IBM Plex Sans Thai Looped',
    value:
      'https://fonts.gstatic.com/s/ibmplexsansthailooped/v11/tss_AoJJRAhL3BTrK3r2xxbFhvKfyBB6l7hHT30LxBKAoFGoBCQ.ttf'
  },
  {
    label: 'IBM Plex Serif',
    value: 'https://fonts.gstatic.com/s/ibmplexserif/v19/jizDREVNn1dOx-zrZ2X3pZvkThUY0TY7ikbI.ttf'
  },
  {
    label: 'IM Fell DW Pica',
    value: 'https://fonts.gstatic.com/s/imfelldwpica/v16/2sDGZGRQotv9nbn2qSl0TxXVYNw9ZAPUvi88MQ.ttf'
  },
  {
    label: 'IM Fell DW Pica SC',
    value: 'https://fonts.gstatic.com/s/imfelldwpicasc/v21/0ybjGCAu5PfqkvtGVU15aBhXz3EUrnTW-BiKEUiBGA.ttf'
  },
  {
    label: 'IM Fell Double Pica',
    value: 'https://fonts.gstatic.com/s/imfelldoublepica/v14/3XF2EqMq_94s9PeKF7Fg4gOKINyMtZ8rT0S1UL5Ayp0.ttf'
  },
  {
    label: 'IM Fell Double Pica SC',
    value: 'https://fonts.gstatic.com/s/imfelldoublepicasc/v21/neIazDmuiMkFo6zj_sHpQ8teNbWlwBB_hXjJ4Y0Eeru2dGg.ttf'
  },
  {
    label: 'IM Fell English',
    value: 'https://fonts.gstatic.com/s/imfellenglish/v14/Ktk1ALSLW8zDe0rthJysWrnLsAz3F6mZVY9Y5w.ttf'
  },
  {
    label: 'IM Fell English SC',
    value: 'https://fonts.gstatic.com/s/imfellenglishsc/v16/a8IENpD3CDX-4zrWfr1VY879qFF05pZLO4gOg0shzA.ttf'
  },
  {
    label: 'IM Fell French Canon',
    value: 'https://fonts.gstatic.com/s/imfellfrenchcanon/v21/-F6ufiNtDWYfYc-tDiyiw08rrghJszkK6coVPt1ozoPz.ttf'
  },
  {
    label: 'IM Fell French Canon SC',
    value: 'https://fonts.gstatic.com/s/imfellfrenchcanonsc/v22/FBVmdCru5-ifcor2bgq9V89khWcmQghEURY7H3c0UBCVIVqH.ttf'
  },
  {
    label: 'IM Fell Great Primer',
    value: 'https://fonts.gstatic.com/s/imfellgreatprimer/v21/bx6aNwSJtayYxOkbYFsT6hMsLzX7u85rJorXvDo3SQY1.ttf'
  },
  {
    label: 'IM Fell Great Primer SC',
    value: 'https://fonts.gstatic.com/s/imfellgreatprimersc/v21/ga6daxBOxyt6sCqz3fjZCTFCTUDMHagsQKdDTLf9BXz0s8FG.ttf'
  },
  {
    label: 'Ibarra Real Nova',
    value: 'https://fonts.gstatic.com/s/ibarrarealnova/v27/sZlSdQiA-DBIDCcaWtQzL4BZHoiDundw4ATyjed3EXdg5MDtVT9TWIvS.ttf'
  },
  {
    label: 'Iceberg',
    value: 'https://fonts.gstatic.com/s/iceberg/v24/8QIJdijAiM7o-qnZuIgOq7jkAOw.ttf'
  },
  {
    label: 'Iceland',
    value: 'https://fonts.gstatic.com/s/iceland/v20/rax9HiuFsdMNOnWPWKxGADBbg0s.ttf'
  },
  {
    label: 'Imbue',
    value: 'https://fonts.gstatic.com/s/imbue/v27/RLpXK5P16Ki3fXhj5cvGrqjocPk4n-gVX3M93TnrnvhoP8iXfOsNNK-Q4xY.ttf'
  },
  {
    label: 'Imperial Script',
    value: 'https://fonts.gstatic.com/s/imperialscript/v6/5DCPAKrpzy_H98IV2ISnZBbGrVNvPenlvttWNg.ttf'
  },
  {
    label: 'Imprima',
    value: 'https://fonts.gstatic.com/s/imprima/v18/VEMxRoN7sY3yuy-7-oWHyDzktPo.ttf'
  },
  {
    label: 'Inclusive Sans',
    value: 'https://fonts.gstatic.com/s/inclusivesans/v1/0nkxC9biPuwflXcJ46P4PGWE0971owa2LB4i.ttf'
  },
  {
    label: 'Inconsolata',
    value:
      'https://fonts.gstatic.com/s/inconsolata/v32/QldgNThLqRwH-OJ1UHjlKENVzkWGVkL3GZQmAwLYxYWI2qfdm7Lpp4U8aRr8lleY2co.ttf'
  },
  {
    label: 'Inder',
    value: 'https://fonts.gstatic.com/s/inder/v14/w8gUH2YoQe8_4vq6pw-P3U4O.ttf'
  },
  {
    label: 'Indie Flower',
    value: 'https://fonts.gstatic.com/s/indieflower/v21/m8JVjfNVeKWVnh3QMuKkFcZlbkGG1dKEDw.ttf'
  },
  {
    label: 'Ingrid Darling',
    value: 'https://fonts.gstatic.com/s/ingriddarling/v5/LDIrapaJNxUtSuFdw-9yf4rCPsLOub458jGL.ttf'
  },
  {
    label: 'Inika',
    value: 'https://fonts.gstatic.com/s/inika/v21/rnCm-x5X3QP-phTHRcc2s2XH.ttf'
  },
  {
    label: 'Inknut Antiqua',
    value: 'https://fonts.gstatic.com/s/inknutantiqua/v14/Y4GSYax7VC4ot_qNB4nYpBdaKXUD6pzxRwYB.ttf'
  },
  {
    label: 'Inria Sans',
    value: 'https://fonts.gstatic.com/s/inriasans/v14/ptRMTiqXYfZMCOiVj9kQ5O7yKQNute8.ttf'
  },
  {
    label: 'Inria Serif',
    value: 'https://fonts.gstatic.com/s/inriaserif/v16/fC1lPYxPY3rXxEndZJAzN0SsfSzNr0Ck.ttf'
  },
  {
    label: 'Inspiration',
    value: 'https://fonts.gstatic.com/s/inspiration/v6/x3dkckPPZa6L4wIg5cZOEvoGnSrlBBsy.ttf'
  },
  {
    label: 'Instrument Sans',
    value:
      'https://fonts.gstatic.com/s/instrumentsans/v1/pximypc9vsFDm051Uf6KVwgkfoSxQ0GsQv8ToedPibnr-yp2JGEJOH9npSTF-Qf1mS0v3_7Y.ttf'
  },
  {
    label: 'Instrument Serif',
    value: 'https://fonts.gstatic.com/s/instrumentserif/v4/jizBRFtNs2ka5fXjeivQ4LroWlx-2zIZj1bIkNo.ttf'
  },
  {
    label: 'Inter',
    value: 'https://fonts.gstatic.com/s/inter/v13/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuLyfMZhrib2Bg-4.ttf'
  },
  {
    label: 'Inter Tight',
    value: 'https://fonts.gstatic.com/s/intertight/v7/NGSnv5HMAFg6IuGlBNMjxJEL2VmU3NS7Z2mjDw-qXCRToK8EPg.ttf'
  },
  {
    label: 'Irish Grover',
    value: 'https://fonts.gstatic.com/s/irishgrover/v23/buExpoi6YtLz2QW7LA4flVgf-P5Oaiw4cw.ttf'
  },
  {
    label: 'Island Moments',
    value: 'https://fonts.gstatic.com/s/islandmoments/v6/NaPBcZfVGvBdxIt7Ar0qzkXJF-TGIohbZ6SY.ttf'
  },
  {
    label: 'Istok Web',
    value: 'https://fonts.gstatic.com/s/istokweb/v24/3qTvojGmgSyUukBzKslZAWF-9kIIaQ.ttf'
  },
  {
    label: 'Italiana',
    value: 'https://fonts.gstatic.com/s/italiana/v20/QldNNTtLsx4E__B0XTmRY31Wx7Vv.ttf'
  },
  {
    label: 'Italianno',
    value: 'https://fonts.gstatic.com/s/italianno/v17/dg4n_p3sv6gCJkwzT6Rnj5YpQwM-gg.ttf'
  },
  {
    label: 'Itim',
    value: 'https://fonts.gstatic.com/s/itim/v14/0nknC9ziJOYewARKkc7ZdwU.ttf'
  },
  {
    label: 'Jacquard 12',
    value: 'https://fonts.gstatic.com/s/jacquard12/v6/vm8ydRLuXETEweL79J4rGc3JUnr34c9-.ttf'
  },
  {
    label: 'Jacquard 12 Charted',
    value: 'https://fonts.gstatic.com/s/jacquard12charted/v2/i7dWIE97bzCOB9Q_Up6PQmYfKDPIb2HwT3StZ9jetKY.ttf'
  },
  {
    label: 'Jacquard 24',
    value: 'https://fonts.gstatic.com/s/jacquard24/v2/jVyO7nf_B2zO5jVpUGU8lgQEdchf9xXp.ttf'
  },
  {
    label: 'Jacquard 24 Charted',
    value: 'https://fonts.gstatic.com/s/jacquard24charted/v3/mtGm4-dNK6HaudrE9VVKhENTsEXEYish0iRrMYJ_K-4.ttf'
  },
  {
    label: 'Jacquarda Bastarda 9',
    value: 'https://fonts.gstatic.com/s/jacquardabastarda9/v4/f0Xp0fWr_8t6WFtKQJfOhaC0hcZ1HYAMAbwD1TB_JHHY.ttf'
  },
  {
    label: 'Jacquarda Bastarda 9 Charted',
    value:
      'https://fonts.gstatic.com/s/jacquardabastarda9charted/v2/Yq6D-KaMUyfq4qLgx19A_ocp43FeLd9m0vDxm-yf8JPuf0cPaL8pmQg.ttf'
  },
  {
    label: 'Jacques Francois',
    value: 'https://fonts.gstatic.com/s/jacquesfrancois/v24/ZXu9e04ZvKeOOHIe1TMahbcIU2cgmcPqoeRWfbs.ttf'
  },
  {
    label: 'Jacques Francois Shadow',
    value: 'https://fonts.gstatic.com/s/jacquesfrancoisshadow/v25/KR1FBtOz8PKTMk-kqdkLVrvR0ECFrB6Pin-2_q8VsHuV5ULS.ttf'
  },
  {
    label: 'Jaini',
    value: 'https://fonts.gstatic.com/s/jaini/v1/fC1vPYJMbGHQzEmOK-ZSUHyt.ttf'
  },
  {
    label: 'Jaini Purva',
    value: 'https://fonts.gstatic.com/s/jainipurva/v1/CHynV-vdHVXwbWcUswbUGHoOHH4sj3lR.ttf'
  },
  {
    label: 'Jaldi',
    value: 'https://fonts.gstatic.com/s/jaldi/v12/or3sQ67z0_CI30NUZpD_B6g8.ttf'
  },
  {
    label: 'Jaro',
    value: 'https://fonts.gstatic.com/s/jaro/v5/ea8DadQwV_r_Rv3-7zZMCGhQgVr1SlCsNP3VpQ_a.ttf'
  },
  {
    label: 'Jersey 10',
    value: 'https://fonts.gstatic.com/s/jersey10/v2/GftH7vZKsggXMf9n_J5X-JLgy1wtSw.ttf'
  },
  {
    label: 'Jersey 10 Charted',
    value: 'https://fonts.gstatic.com/s/jersey10charted/v2/oY1E8fPFr6XiNWqEp90XSbwUGfF8SnedKmeBvEYs.ttf'
  },
  {
    label: 'Jersey 15',
    value: 'https://fonts.gstatic.com/s/jersey15/v2/_6_9EDzuROGsUuk2TWjSYoohsCkvSQ.ttf'
  },
  {
    label: 'Jersey 15 Charted',
    value: 'https://fonts.gstatic.com/s/jersey15charted/v2/nuFjD-rCQIjoVp1Sva2ToCTudGbLeRv4r2024gxi.ttf'
  },
  {
    label: 'Jersey 20',
    value: 'https://fonts.gstatic.com/s/jersey20/v2/ZgNRjP1ON6jeW4D12z3crE_qP4mXuQ.ttf'
  },
  {
    label: 'Jersey 20 Charted',
    value: 'https://fonts.gstatic.com/s/jersey20charted/v2/JTUNjJMy9DKq5FzVaj9tpgYgvHqGn_Z1ji-rqnQ_.ttf'
  },
  {
    label: 'Jersey 25',
    value: 'https://fonts.gstatic.com/s/jersey25/v2/ll8-K2eeXj2tAs6F9BXIJ4AMng8ChA.ttf'
  },
  {
    label: 'Jersey 25 Charted',
    value: 'https://fonts.gstatic.com/s/jersey25charted/v2/6NUM8EWHIhCWbxOqtLkv94Rlu6EkGv2uUGQW93Cg.ttf'
  },
  {
    label: 'JetBrains Mono',
    value: 'https://fonts.gstatic.com/s/jetbrainsmono/v18/tDbY2o-flEEny0FZhsfKu5WU4zr3E_BX0PnT8RD8yKxjPVmUsaaDhw.ttf'
  },
  {
    label: 'Jim Nightshade',
    value: 'https://fonts.gstatic.com/s/jimnightshade/v20/PlIkFlu9Pb08Q8HLM1PxmB0g-OS4V3qKaMxD.ttf'
  },
  {
    label: 'Joan',
    value: 'https://fonts.gstatic.com/s/joan/v11/ZXupe1oZsqWRbRdH8X1p_Ng.ttf'
  },
  {
    label: 'Jockey One',
    value: 'https://fonts.gstatic.com/s/jockeyone/v21/HTxpL2g2KjCFj4x8WI6ArIb7HYOk4xc.ttf'
  },
  {
    label: 'Jolly Lodger',
    value: 'https://fonts.gstatic.com/s/jollylodger/v20/BXRsvFTAh_bGkA1uQ48dlB3VWerT3ZyuqA.ttf'
  },
  {
    label: 'Jomhuria',
    value: 'https://fonts.gstatic.com/s/jomhuria/v20/Dxxp8j-TMXf-llKur2b1MOGbC3Dh.ttf'
  },
  {
    label: 'Jomolhari',
    value: 'https://fonts.gstatic.com/s/jomolhari/v18/EvONzA1M1Iw_CBd2hsQCF1IZKq5INg.ttf'
  },
  {
    label: 'Josefin Sans',
    value: 'https://fonts.gstatic.com/s/josefinsans/v32/Qw3PZQNVED7rKGKxtqIqX5E-AVSJrOCfjY46_DjQXMFrLgTsQV0.ttf'
  },
  {
    label: 'Josefin Slab',
    value: 'https://fonts.gstatic.com/s/josefinslab/v26/lW-swjwOK3Ps5GSJlNNkMalNpiZe_ldbOR4W71msd3k3K6CcEyI.ttf'
  },
  {
    label: 'Jost',
    value: 'https://fonts.gstatic.com/s/jost/v15/92zPtBhPNqw79Ij1E865zBUv7myjJQVGPokMmuHL.ttf'
  },
  {
    label: 'Joti One',
    value: 'https://fonts.gstatic.com/s/jotione/v26/Z9XVDmdJQAmWm9TwaYTe4u2El6GC.ttf'
  },
  {
    label: 'Jua',
    value: 'https://fonts.gstatic.com/s/jua/v16/co3KmW9ljjAjc-DZCsKgsg.ttf'
  },
  {
    label: 'Judson',
    value: 'https://fonts.gstatic.com/s/judson/v19/FeVRS0Fbvbc14VxRD7N01bV7kg.ttf'
  },
  {
    label: 'Julee',
    value: 'https://fonts.gstatic.com/s/julee/v25/TuGfUVB3RpZPQ6ZLodgzydtk.ttf'
  },
  {
    label: 'Julius Sans One',
    value: 'https://fonts.gstatic.com/s/juliussansone/v18/1Pt2g8TAX_SGgBGUi0tGOYEga5W-xXEW6aGXHw.ttf'
  },
  {
    label: 'Junge',
    value: 'https://fonts.gstatic.com/s/junge/v24/gokgH670Gl1lUqAdvhB7SnKm.ttf'
  },
  {
    label: 'Jura',
    value: 'https://fonts.gstatic.com/s/jura/v31/z7NOdRfiaC4Vd8hhoPzfb5vBTP1d7auhTfmrH_rt.ttf'
  },
  {
    label: 'Just Another Hand',
    value: 'https://fonts.gstatic.com/s/justanotherhand/v19/845CNN4-AJyIGvIou-6yJKyptyOpOcr_BmmlS5aw.ttf'
  },
  {
    label: 'Just Me Again Down Here',
    value: 'https://fonts.gstatic.com/s/justmeagaindownhere/v24/MwQmbgXtz-Wc6RUEGNMc0QpRrfUh2hSdBBMoAuwHvqDwc_fg.ttf'
  },
  {
    label: 'K2D',
    value: 'https://fonts.gstatic.com/s/k2d/v11/J7aTnpF2V0ETd68tnLcg7w.ttf'
  },
  {
    label: 'Kablammo',
    value: 'https://fonts.gstatic.com/s/kablammo/v1/bWtm7fHPcgrhC-J3lcXhcQTY5Ixs6Au9YgCjjx0Rf4YDKw.ttf'
  },
  {
    label: 'Kadwa',
    value: 'https://fonts.gstatic.com/s/kadwa/v10/rnCm-x5V0g7iphTHRcc2s2XH.ttf'
  },
  {
    label: 'Kaisei Decol',
    value: 'https://fonts.gstatic.com/s/kaiseidecol/v8/bMrwmSqP45sidWf3QmfFW6iyW1EP22OjoA.ttf'
  },
  {
    label: 'Kaisei HarunoUmi',
    value: 'https://fonts.gstatic.com/s/kaiseiharunoumi/v8/HI_RiZQSLqBQoAHhK_C6N_nzy_jcGsv5sM8u3mk.ttf'
  },
  {
    label: 'Kaisei Opti',
    value: 'https://fonts.gstatic.com/s/kaiseiopti/v8/QldKNThJphYb8_g6c2nlIFle7KlmxuHx.ttf'
  },
  {
    label: 'Kaisei Tokumin',
    value: 'https://fonts.gstatic.com/s/kaiseitokumin/v8/Gg8sN5wdZg7xCwuMsylww2ZiQkJf1l0pj946.ttf'
  },
  {
    label: 'Kalam',
    value: 'https://fonts.gstatic.com/s/kalam/v16/YA9dr0Wd4kDdMuhWMibDszkB.ttf'
  },
  {
    label: 'Kalnia',
    value: 'https://fonts.gstatic.com/s/kalnia/v2/11hqGpPCwUbbYwZZP0RBuDY62BQZhjvOytM_sLzi-TFWAW9ilmxRGNcykIZc.ttf'
  },
  {
    label: 'Kalnia Glaze',
    value:
      'https://fonts.gstatic.com/s/kalniaglaze/v1/wlpQgwHCBUNjrGrfu-hwowN1YyC-42Lu26VHf2LtOkAod0wTxm2tFYdL6nnZKfhpVTvN.ttf'
  },
  {
    label: 'Kameron',
    value: 'https://fonts.gstatic.com/s/kameron/v16/vm8pdR7vXErQxuznqrUS3z1Uw3nq4Ne3cbcbvZPxCDLR.ttf'
  },
  {
    label: 'Kanit',
    value: 'https://fonts.gstatic.com/s/kanit/v15/nKKZ-Go6G5tXcoaSEQGodLxA.ttf'
  },
  {
    label: 'Kantumruy Pro',
    value: 'https://fonts.gstatic.com/s/kantumruypro/v8/1q2TY5aECkp34vEBSPFOmJxwvk_pilU8OGNfyg1urEs0M34dR6dW.ttf'
  },
  {
    label: 'Karantina',
    value: 'https://fonts.gstatic.com/s/karantina/v11/buE0po24ccnh31GVMABJ8AA78NVSYw.ttf'
  },
  {
    label: 'Karla',
    value: 'https://fonts.gstatic.com/s/karla/v31/qkBIXvYC6trAT55ZBi1ueQVIjQTD-JqqFENLR7fHGw.ttf'
  },
  {
    label: 'Karma',
    value: 'https://fonts.gstatic.com/s/karma/v16/va9I4kzAzMZRGIBvS-J3kbDP.ttf'
  },
  {
    label: 'Katibeh',
    value: 'https://fonts.gstatic.com/s/katibeh/v19/ZGjXol5MQJog4bxDaC1RVDNdGDs.ttf'
  },
  {
    label: 'Kaushan Script',
    value: 'https://fonts.gstatic.com/s/kaushanscript/v18/vm8vdRfvXFLG3OLnsO15WYS5DF7_ytN3M48a.ttf'
  },
  {
    label: 'Kavivanar',
    value: 'https://fonts.gstatic.com/s/kavivanar/v20/o-0IIpQgyXYSwhxP7_Jb4j5Ba_2c7A.ttf'
  },
  {
    label: 'Kavoon',
    value: 'https://fonts.gstatic.com/s/kavoon/v23/pxiFyp4_scRYhlU4NLr6f1pdEQ.ttf'
  },
  {
    label: 'Kay Pho Du',
    value: 'https://fonts.gstatic.com/s/kayphodu/v2/jizfREFPvGNOx-jhPwHR4OmnLD0Z4zM.ttf'
  },
  {
    label: 'Kdam Thmor Pro',
    value: 'https://fonts.gstatic.com/s/kdamthmorpro/v4/EJRPQgAzVdcI-Qdvt34jzurnGA7_j89I8ZWb.ttf'
  },
  {
    label: 'Keania One',
    value: 'https://fonts.gstatic.com/s/keaniaone/v24/zOL54pXJk65E8pXardnuycRuv-hHkOs.ttf'
  },
  {
    label: 'Kelly Slab',
    value: 'https://fonts.gstatic.com/s/kellyslab/v17/-W_7XJX0Rz3cxUnJC5t6TkMBf50kbiM.ttf'
  },
  {
    label: 'Kenia',
    value: 'https://fonts.gstatic.com/s/kenia/v28/jizURE5PuHQH9qCONUGswfGM.ttf'
  },
  {
    label: 'Khand',
    value: 'https://fonts.gstatic.com/s/khand/v17/TwMA-IINQlQQ0YpVWHU_TBqO.ttf'
  },
  {
    label: 'Khmer',
    value: 'https://fonts.gstatic.com/s/khmer/v29/MjQImit_vPPwpF-BpN2EeYmD.ttf'
  },
  {
    label: 'Khula',
    value: 'https://fonts.gstatic.com/s/khula/v12/OpNCnoEOns3V7FcJpA_chzJ0.ttf'
  },
  {
    label: 'Kings',
    value: 'https://fonts.gstatic.com/s/kings/v7/8AtnGsK4O5CYXU_Iq6GSPaHS.ttf'
  },
  {
    label: 'Kirang Haerang',
    value: 'https://fonts.gstatic.com/s/kiranghaerang/v21/E21-_dn_gvvIjhYON1lpIU4-bcqvWPaJq4no.ttf'
  },
  {
    label: 'Kite One',
    value: 'https://fonts.gstatic.com/s/kiteone/v22/70lQu7shLnA_E02vyq1b6HnGO4uA.ttf'
  },
  {
    label: 'Kiwi Maru',
    value: 'https://fonts.gstatic.com/s/kiwimaru/v14/R70YjykGkuuDep-hRg6YmACQXzLhTg.ttf'
  },
  {
    label: 'Klee One',
    value: 'https://fonts.gstatic.com/s/kleeone/v7/LDIxapCLNRc6A8oT4q4AOeekWPrP.ttf'
  },
  {
    label: 'Knewave',
    value: 'https://fonts.gstatic.com/s/knewave/v14/sykz-yx0lLcxQaSItSq9-trEvlQ.ttf'
  },
  {
    label: 'KoHo',
    value: 'https://fonts.gstatic.com/s/koho/v16/K2F-fZ5fmddNBikefJbSOos.ttf'
  },
  {
    label: 'Kodchasan',
    value: 'https://fonts.gstatic.com/s/kodchasan/v17/1cXxaUPOAJv9sG4I-DJmj3uEicG01A.ttf'
  },
  {
    label: 'Kode Mono',
    value: 'https://fonts.gstatic.com/s/kodemono/v2/A2BLn5pb0QgtVEPFnlYkkaoBgw4qv9odq5my9DqTaOW2A3k.ttf'
  },
  {
    label: 'Koh Santepheap',
    value: 'https://fonts.gstatic.com/s/kohsantepheap/v11/gNMdW3p6SJbwyGj2rBZyeOrTjOPhF1ixsyNJ.ttf'
  },
  {
    label: 'Kolker Brush',
    value: 'https://fonts.gstatic.com/s/kolkerbrush/v6/iJWDBXWRZjfKWdvmzwvvog3-7KJ6x8qNUQ.ttf'
  },
  {
    label: 'Konkhmer Sleokchher',
    value: 'https://fonts.gstatic.com/s/konkhmersleokchher/v2/_Xmw-GE-rjmabA_M-aPOZOsCrUv825LFI3507E0d-W0.ttf'
  },
  {
    label: 'Kosugi',
    value: 'https://fonts.gstatic.com/s/kosugi/v15/pxiFyp4_v8FCjlI4NLr6f1pdEQ.ttf'
  },
  {
    label: 'Kosugi Maru',
    value: 'https://fonts.gstatic.com/s/kosugimaru/v14/0nksC9PgP_wGh21A2KeqGiTqivr9iBq_.ttf'
  },
  {
    label: 'Kotta One',
    value: 'https://fonts.gstatic.com/s/kottaone/v20/S6u_w41LXzPc_jlfNWqPHA3s5dwt7w.ttf'
  },
  {
    label: 'Koulen',
    value: 'https://fonts.gstatic.com/s/koulen/v28/AMOQz46as3KIBPeWgnA9kuYMUg.ttf'
  },
  {
    label: 'Kranky',
    value: 'https://fonts.gstatic.com/s/kranky/v28/hESw6XVgJzlPsFnMpheEZo_H_w.ttf'
  },
  {
    label: 'Kreon',
    value: 'https://fonts.gstatic.com/s/kreon/v37/t5t9IRIUKY-TFF_LW5lnMR3v2DnvYtimejUfp2dWNg.ttf'
  },
  {
    label: 'Kristi',
    value: 'https://fonts.gstatic.com/s/kristi/v21/uK_y4ricdeU6zwdRCh0TMv6EXw.ttf'
  },
  {
    label: 'Krona One',
    value: 'https://fonts.gstatic.com/s/kronaone/v14/jAnEgHdjHcjgfIb1ZcUCMY-h3cWkWg.ttf'
  },
  {
    label: 'Krub',
    value: 'https://fonts.gstatic.com/s/krub/v9/sZlLdRyC6CRYXkYQDLlTW6E.ttf'
  },
  {
    label: 'Kufam',
    value: 'https://fonts.gstatic.com/s/kufam/v21/C8c-4cY7pG7w_oSJDszBXsKCcBH3lqk7qQCJHvIwYg.ttf'
  },
  {
    label: 'Kulim Park',
    value: 'https://fonts.gstatic.com/s/kulimpark/v14/fdN79secq3hflz1Uu3IwtF4m5aZxebw.ttf'
  },
  {
    label: 'Kumar One',
    value: 'https://fonts.gstatic.com/s/kumarone/v21/bMr1mS-P958wYi6YaGeGNO6WU3oT0g.ttf'
  },
  {
    label: 'Kumar One Outline',
    value: 'https://fonts.gstatic.com/s/kumaroneoutline/v17/Noao6VH62pyLP0fsrZ-v18wlUEcX9zDwRQu8EGKF.ttf'
  },
  {
    label: 'Kumbh Sans',
    value:
      'https://fonts.gstatic.com/s/kumbhsans/v22/c4mP1n92AsfhuCq6tVsaoIx1LQICk0boNoq0SjlDfnzKo-bF3mdQkZYA8bTuUkqaLg.ttf'
  },
  {
    label: 'Kurale',
    value: 'https://fonts.gstatic.com/s/kurale/v12/4iCs6KV9e9dXjho6eAT3v02QFg.ttf'
  },
  {
    label: 'LXGW WenKai Mono TC',
    value: 'https://fonts.gstatic.com/s/lxgwwenkaimonotc/v2/pxiYyos4iPVgyWx9WtufHnsIf5nkaB0Him6CovpOkXA.ttf'
  },
  {
    label: 'LXGW WenKai TC',
    value: 'https://fonts.gstatic.com/s/lxgwwenkaitc/v2/w8gDH20td8wNsI3f40DmtXZb48uKLd0hZzVB.ttf'
  },
  {
    label: 'La Belle Aurore',
    value: 'https://fonts.gstatic.com/s/labelleaurore/v20/RrQIbot8-mNYKnGNDkWlocovHeIIG-eFNVmULg.ttf'
  },
  {
    label: 'Labrada',
    value: 'https://fonts.gstatic.com/s/labrada/v2/ieVh2Y9HLWefIpOyF1Vi3ZqDss1Px9VTSwM4QPdUej17.ttf'
  },
  {
    label: 'Lacquer',
    value: 'https://fonts.gstatic.com/s/lacquer/v15/EYqzma1QwqpG4_BBB7-AXhttQ5I.ttf'
  },
  {
    label: 'Laila',
    value: 'https://fonts.gstatic.com/s/laila/v15/LYjMdG_8nE8jDIRdiidIrEIu.ttf'
  },
  {
    label: 'Lakki Reddy',
    value: 'https://fonts.gstatic.com/s/lakkireddy/v21/S6u5w49MUSzD9jlCPmvLZQfox9k97-xZ.ttf'
  },
  {
    label: 'Lalezar',
    value: 'https://fonts.gstatic.com/s/lalezar/v14/zrfl0HLVx-HwTP82UaDyIiL0RCg.ttf'
  },
  {
    label: 'Lancelot',
    value: 'https://fonts.gstatic.com/s/lancelot/v26/J7acnppxBGtQEulG4JY4xJ9CGyAa.ttf'
  },
  {
    label: 'Langar',
    value: 'https://fonts.gstatic.com/s/langar/v27/kJEyBukW7AIlgjGVrTVZ99sqrQ.ttf'
  },
  {
    label: 'Lateef',
    value: 'https://fonts.gstatic.com/s/lateef/v30/hESw6XVnNCxEvkbMpheEZo_H_w.ttf'
  },
  {
    label: 'Lato',
    value: 'https://fonts.gstatic.com/s/lato/v24/S6uyw4BMUTPHvxk6XweuBCY.ttf'
  },
  {
    label: 'Lavishly Yours',
    value: 'https://fonts.gstatic.com/s/lavishlyyours/v5/jizDREVIvGwH5OjiZmX9r5z_WxUY0TY7ikbI.ttf'
  },
  {
    label: 'League Gothic',
    value: 'https://fonts.gstatic.com/s/leaguegothic/v11/qFdR35CBi4tvBz81xy7WG7ep-BQAY7Krj7feObpH_-amidQ6Q9hn.ttf'
  },
  {
    label: 'League Script',
    value: 'https://fonts.gstatic.com/s/leaguescript/v28/CSR54zpSlumSWj9CGVsoBZdeaNNUuOwkC2s.ttf'
  },
  {
    label: 'League Spartan',
    value: 'https://fonts.gstatic.com/s/leaguespartan/v11/kJEnBuEW6A0lliaV_m88ja5Twtx8BWhtkDVmjZvM_oTpBMdcFguczA.ttf'
  },
  {
    label: 'Leckerli One',
    value: 'https://fonts.gstatic.com/s/leckerlione/v20/V8mCoQH8VCsNttEnxnGQ-1itLZxcBtItFw.ttf'
  },
  {
    label: 'Ledger',
    value: 'https://fonts.gstatic.com/s/ledger/v16/j8_q6-HK1L3if_sxm8DwHTBhHw.ttf'
  },
  {
    label: 'Lekton',
    value: 'https://fonts.gstatic.com/s/lekton/v20/SZc43FDmLaWmWpBeXxfonUPL6Q.ttf'
  },
  {
    label: 'Lemon',
    value: 'https://fonts.gstatic.com/s/lemon/v17/HI_EiYEVKqRMq0jBSZXAQ4-d.ttf'
  },
  {
    label: 'Lemonada',
    value: 'https://fonts.gstatic.com/s/lemonada/v28/0QI-MXFD9oygTWy_R-FFlwV-bgfR7QJGeut2mfWc3Z2pTg.ttf'
  },
  {
    label: 'Lexend',
    value: 'https://fonts.gstatic.com/s/lexend/v19/wlptgwvFAVdoq2_F94zlCfv0bz1WCzsW_LBte6KuGEo.ttf'
  },
  {
    label: 'Lexend Deca',
    value: 'https://fonts.gstatic.com/s/lexenddeca/v21/K2FifZFYk-dHSE0UPPuwQ7CrD94i-NCKm-U48M1ArBPCqLNflg.ttf'
  },
  {
    label: 'Lexend Exa',
    value: 'https://fonts.gstatic.com/s/lexendexa/v30/UMBCrPdOoHOnxExyjdBeQCH18mulUxBvI9r7TqbHHJ8BRq0b.ttf'
  },
  {
    label: 'Lexend Giga',
    value: 'https://fonts.gstatic.com/s/lexendgiga/v25/PlIuFl67Mah5Y8yMHE7lkUZPlTBo4MWFfNRC2LiE68oo6eepYQ.ttf'
  },
  {
    label: 'Lexend Mega',
    value: 'https://fonts.gstatic.com/s/lexendmega/v25/qFdX35aBi5JtHD41zSTFEuTByuvYFuE9IbDL8fmvveyiq9EqQw.ttf'
  },
  {
    label: 'Lexend Peta',
    value: 'https://fonts.gstatic.com/s/lexendpeta/v27/BXR4vFPGjeLPh0kCfI4OkFX-UTQHSCaxvBgR6SByW1YuRTsnfw.ttf'
  },
  {
    label: 'Lexend Tera',
    value: 'https://fonts.gstatic.com/s/lexendtera/v27/RrQDbo98_jt_IXnBPwCWtYJLZ3P4hnaGKFiM5zMTdpz0fYxcrQ.ttf'
  },
  {
    label: 'Lexend Zetta',
    value: 'https://fonts.gstatic.com/s/lexendzetta/v28/ll8uK2KYXje7CdOFnEWcU8synQbuVYjYB3BCy9bG0z5jbs8qbts.ttf'
  },
  {
    label: 'Libre Barcode 128',
    value: 'https://fonts.gstatic.com/s/librebarcode128/v28/cIfnMbdUsUoiW3O_hVviCwVjuLtXeJ_A_gMk0izH.ttf'
  },
  {
    label: 'Libre Barcode 128 Text',
    value: 'https://fonts.gstatic.com/s/librebarcode128text/v28/fdNv9tubt3ZEnz1Gu3I4-zppwZ9CWZ16Z0w5cV3Y6M90w4k.ttf'
  },
  {
    label: 'Libre Barcode 39',
    value: 'https://fonts.gstatic.com/s/librebarcode39/v21/-nFnOHM08vwC6h8Li1eQnP_AHzI2K_d709jy92k.ttf'
  },
  {
    label: 'Libre Barcode 39 Extended',
    value:
      'https://fonts.gstatic.com/s/librebarcode39extended/v27/8At7Gt6_O5yNS0-K4Nf5U922qSzhJ3dUdfJpwNUgfNRCOZ1GOBw.ttf'
  },
  {
    label: 'Libre Barcode 39 Extended Text',
    value:
      'https://fonts.gstatic.com/s/librebarcode39extendedtext/v27/eLG1P_rwIgOiDA7yrs9LoKaYRVLQ1YldrrOnnL7xPO4jNP68fLIiPopNNA.ttf'
  },
  {
    label: 'Libre Barcode 39 Text',
    value: 'https://fonts.gstatic.com/s/librebarcode39text/v28/sJoa3KhViNKANw_E3LwoDXvs5Un0HQ1vT-031RRL-9rYaw.ttf'
  },
  {
    label: 'Libre Barcode EAN13 Text',
    value:
      'https://fonts.gstatic.com/s/librebarcodeean13text/v21/wlpigxXFDU1_oCu9nfZytgIqSG0XRcJm_OQiB96PAGEki52WfA.ttf'
  },
  {
    label: 'Libre Baskerville',
    value: 'https://fonts.gstatic.com/s/librebaskerville/v14/kmKnZrc3Hgbbcjq75U4uslyuy4kn0pNeYRI4CN2V.ttf'
  },
  {
    label: 'Libre Bodoni',
    value: 'https://fonts.gstatic.com/s/librebodoni/v5/_Xm--H45qDWDYULr5OfyZudXzSBgY2oMBGte6I1fwWzZcOb3U3s.ttf'
  },
  {
    label: 'Libre Caslon Display',
    value: 'https://fonts.gstatic.com/s/librecaslondisplay/v16/TuGOUUFxWphYQ6YI6q9Xp61FQzxDRKmzr2lRdRhtCC4d.ttf'
  },
  {
    label: 'Libre Caslon Text',
    value: 'https://fonts.gstatic.com/s/librecaslontext/v5/DdT878IGsGw1aF1JU10PUbTvNNaDMcq_3eNrHgO1.ttf'
  },
  {
    label: 'Libre Franklin',
    value: 'https://fonts.gstatic.com/s/librefranklin/v14/jizOREVItHgc8qDIbSTKq4XkRg8T88bjFuXOnduhLsWUB9rIb-JH1g.ttf'
  },
  {
    label: 'Licorice',
    value: 'https://fonts.gstatic.com/s/licorice/v6/t5tjIR8TMomTCAyjNk23hqLgzCHu.ttf'
  },
  {
    label: 'Life Savers',
    value: 'https://fonts.gstatic.com/s/lifesavers/v21/ZXuie1UftKKabUQMgxAal_lrFgpbuNvB.ttf'
  },
  {
    label: 'Lilita One',
    value: 'https://fonts.gstatic.com/s/lilitaone/v15/i7dPIFZ9Zz-WBtRtedDbUEZ2RFq7AwU.ttf'
  },
  {
    label: 'Lily Script One',
    value: 'https://fonts.gstatic.com/s/lilyscriptone/v15/LhW9MV7ZMfIPdMxeBjBvFN8SXLS4gsSjQNsRMg.ttf'
  },
  {
    label: 'Limelight',
    value: 'https://fonts.gstatic.com/s/limelight/v19/XLYkIZL7aopJVbZJHDuYPeNGrnY2TA.ttf'
  },
  {
    label: 'Linden Hill',
    value: 'https://fonts.gstatic.com/s/lindenhill/v24/-F61fjxoKSg9Yc3hZgO8ygFI7CwC009k.ttf'
  },
  {
    label: 'Linefont',
    value: 'https://fonts.gstatic.com/s/linefont/v5/dg4I_pzpoqcLKUIzVfFMh1TF2rkhli25jn7CKTTWSumsFuSnY4UMbu7tmdXux3U.ttf'
  },
  {
    label: 'Lisu Bosa',
    value: 'https://fonts.gstatic.com/s/lisubosa/v2/3XFoErkv240fsdmJRJQvl0viTf3E3Q.ttf'
  },
  {
    label: 'Literata',
    value:
      'https://fonts.gstatic.com/s/literata/v35/or3PQ6P12-iJxAIgLa78DkrbXsDgk0oVDaDPYLanFLHpPf2TbBG_F_bcTWCWp8g.ttf'
  },
  {
    label: 'Liu Jian Mao Cao',
    value: 'https://fonts.gstatic.com/s/liujianmaocao/v20/845DNN84HJrccNonurqXILGpvCOoferVKGWsUo8.ttf'
  },
  {
    label: 'Livvic',
    value: 'https://fonts.gstatic.com/s/livvic/v14/rnCp-x1S2hzjrlfnb-k6unzeSA.ttf'
  },
  {
    label: 'Lobster',
    value: 'https://fonts.gstatic.com/s/lobster/v30/neILzCirqoswsqX9_oWsMqEzSJQ.ttf'
  },
  {
    label: 'Lobster Two',
    value: 'https://fonts.gstatic.com/s/lobstertwo/v20/BngMUXZGTXPUvIoyV6yN59fK7KSJ4ACD.ttf'
  },
  {
    label: 'Londrina Outline',
    value: 'https://fonts.gstatic.com/s/londrinaoutline/v27/C8c44dM8vmb14dfsZxhetg3pDH-SfuoxrSKMDvI.ttf'
  },
  {
    label: 'Londrina Shadow',
    value: 'https://fonts.gstatic.com/s/londrinashadow/v26/oPWX_kB4kOQoWNJmjxLV5JuoCUlXRlaSxkrMCQ.ttf'
  },
  {
    label: 'Londrina Sketch',
    value: 'https://fonts.gstatic.com/s/londrinasketch/v25/c4m41npxGMTnomOHtRU68eIJn8qfWWn5Pos6CA.ttf'
  },
  {
    label: 'Londrina Solid',
    value: 'https://fonts.gstatic.com/s/londrinasolid/v17/flUhRq6sw40kQEJxWNgkLuudGcNZIhI8tIHh.ttf'
  },
  {
    label: 'Long Cang',
    value: 'https://fonts.gstatic.com/s/longcang/v17/LYjAdGP8kkgoTec8zkRgrXArXN7HWQ.ttf'
  },
  {
    label: 'Lora',
    value: 'https://fonts.gstatic.com/s/lora/v35/0QI6MX1D_JOuGQbT0gvTJPa787weuyJGmKxemMeZ.ttf'
  },
  {
    label: 'Love Light',
    value: 'https://fonts.gstatic.com/s/lovelight/v6/t5tlIR0TNJyZWimpNAXDjKbCyTHuspo.ttf'
  },
  {
    label: 'Love Ya Like A Sister',
    value: 'https://fonts.gstatic.com/s/loveyalikeasister/v22/R70EjzUBlOqPeouhFDfR80-0FhOqJubN-Be78nZcsGGycA.ttf'
  },
  {
    label: 'Loved by the King',
    value: 'https://fonts.gstatic.com/s/lovedbytheking/v21/Gw6gwdP76VDVJNXerebZxUMeRXUF2PiNlXFu2R64.ttf'
  },
  {
    label: 'Lovers Quarrel',
    value: 'https://fonts.gstatic.com/s/loversquarrel/v23/Yq6N-LSKXTL-5bCy8ksBzpQ_-zAsY7pO6siz.ttf'
  },
  {
    label: 'Luckiest Guy',
    value: 'https://fonts.gstatic.com/s/luckiestguy/v22/_gP_1RrxsjcxVyin9l9n_j2RStR3qDpraA.ttf'
  },
  {
    label: 'Lugrasimo',
    value: 'https://fonts.gstatic.com/s/lugrasimo/v4/qkBXXvoF_s_eT9c7Y7ae5JRLkAXbMQ.ttf'
  },
  {
    label: 'Lumanosimo',
    value: 'https://fonts.gstatic.com/s/lumanosimo/v4/K2F0fZBYg_JDSEZHEfO8AoqKAyLzfWo.ttf'
  },
  {
    label: 'Lunasima',
    value: 'https://fonts.gstatic.com/s/lunasima/v1/wEO-EBvPh9RSOj7JFAwle94H1VIe.ttf'
  },
  {
    label: 'Lusitana',
    value: 'https://fonts.gstatic.com/s/lusitana/v13/CSR84z9ShvucWzsMKxhaRuMiSct_.ttf'
  },
  {
    label: 'Lustria',
    value: 'https://fonts.gstatic.com/s/lustria/v13/9oRONYodvDEyjuhOrCg5MtPyAcg.ttf'
  },
  {
    label: 'Luxurious Roman',
    value: 'https://fonts.gstatic.com/s/luxuriousroman/v8/buEupou_ZcP1w0yTKxJJokVSmbpqYgckeo9RMw.ttf'
  },
  {
    label: 'Luxurious Script',
    value: 'https://fonts.gstatic.com/s/luxuriousscript/v7/ahcCv9e7yydulT32KZ0rBIoD7DzMg0rOby1JtYk.ttf'
  },
  {
    label: 'M PLUS 1',
    value: 'https://fonts.gstatic.com/s/mplus1/v7/R70EjygA28ymD4HgBUGzkN5Eyoj-WpW5VSa78nZcsGGycA.ttf'
  },
  {
    label: 'M PLUS 1 Code',
    value: 'https://fonts.gstatic.com/s/mplus1code/v12/ypvMbXOOx2xFpzmYJS3N2_J2hBN6RZ5oIp8m_7iN0HHpapwmdZhY.ttf'
  },
  {
    label: 'M PLUS 1p',
    value: 'https://fonts.gstatic.com/s/mplus1p/v28/e3tjeuShHdiFyPFzBRro-D4Ec2jKqw.ttf'
  },
  {
    label: 'M PLUS 2',
    value: 'https://fonts.gstatic.com/s/mplus2/v7/7Auhp_Eq3gO_OGbGGhjdwrDdpeIBxlkwOa6VxlqHrzNgAw.ttf'
  },
  {
    label: 'M PLUS Code Latin',
    value:
      'https://fonts.gstatic.com/s/mpluscodelatin/v13/hv-ylyV-aXg7x7tULiNXXBA0Np4WMS8fDIymHY8fy8wn4_ifLAtrObKDO0Xf1EbA6i5MqF9TRwg.ttf'
  },
  {
    label: 'M PLUS Rounded 1c',
    value: 'https://fonts.gstatic.com/s/mplusrounded1c/v15/VdGEAYIAV6gnpUpoWwNkYvrugw9RuPWGzr8C7vav.ttf'
  },
  {
    label: 'Ma Shan Zheng',
    value: 'https://fonts.gstatic.com/s/mashanzheng/v10/NaPecZTRCLxvwo41b4gvzkXaRMTsDIRSfr0.ttf'
  },
  {
    label: 'Macondo',
    value: 'https://fonts.gstatic.com/s/macondo/v25/RrQQboN9-iB1IXmOS2XO0LBBd4Y.ttf'
  },
  {
    label: 'Macondo Swash Caps',
    value: 'https://fonts.gstatic.com/s/macondoswashcaps/v24/6NUL8EaAJgGKZA7lpt941Z9s6ZYgDq6Oekoa_mm5bA.ttf'
  },
  {
    label: 'Mada',
    value: 'https://fonts.gstatic.com/s/mada/v19/7Aulp_0qnzeSVz7u3PJLcUMYOFnOkHkw2-m9x2iC.ttf'
  },
  {
    label: 'Madimi One',
    value: 'https://fonts.gstatic.com/s/madimione/v1/2V0YKIEADpA8U6RygDnZZFQoBoHMd2U.ttf'
  },
  {
    label: 'Magra',
    value: 'https://fonts.gstatic.com/s/magra/v14/uK_94ruaZus72k5xIDMfO-ed.ttf'
  },
  {
    label: 'Maiden Orange',
    value: 'https://fonts.gstatic.com/s/maidenorange/v29/kJE1BuIX7AUmhi2V4m08kb1XjOZdCZS8FY8.ttf'
  },
  {
    label: 'Maitree',
    value: 'https://fonts.gstatic.com/s/maitree/v10/MjQGmil5tffhpBrkrtmmfJmDoL4.ttf'
  },
  {
    label: 'Major Mono Display',
    value: 'https://fonts.gstatic.com/s/majormonodisplay/v16/RWmVoLyb5fEqtsfBX9PDZIGr2tFubRhLCn2QIndPww.ttf'
  },
  {
    label: 'Mako',
    value: 'https://fonts.gstatic.com/s/mako/v19/H4coBX6Mmc_Z0ST09g478Lo.ttf'
  },
  {
    label: 'Mali',
    value: 'https://fonts.gstatic.com/s/mali/v10/N0ba2SRONuN4eCrODlxxOd8.ttf'
  },
  {
    label: 'Mallanna',
    value: 'https://fonts.gstatic.com/s/mallanna/v14/hv-Vlzx-KEQb84YaDGwzEzRwVvJ-.ttf'
  },
  {
    label: 'Maname',
    value: 'https://fonts.gstatic.com/s/maname/v1/gNMFW3J8RpCx9my42FkGGI6q_Q.ttf'
  },
  {
    label: 'Mandali',
    value: 'https://fonts.gstatic.com/s/mandali/v15/LhWlMVbYOfASNfNUVFk1ZPdcKtA.ttf'
  },
  {
    label: 'Manjari',
    value: 'https://fonts.gstatic.com/s/manjari/v12/k3kQo8UPMOBO2w1UTd7iL0nAMaM.ttf'
  },
  {
    label: 'Manrope',
    value: 'https://fonts.gstatic.com/s/manrope/v15/xn7_YHE41ni1AdIRqAuZuw1Bx9mbZk79FO_F87jxeN7B.ttf'
  },
  {
    label: 'Mansalva',
    value: 'https://fonts.gstatic.com/s/mansalva/v14/aWB4m0aacbtDfvq5NJllI47vdyBg.ttf'
  },
  {
    label: 'Manuale',
    value: 'https://fonts.gstatic.com/s/manuale/v28/f0Xp0eas_8Z-TFZdHv3mMxFaSqASeeHke7wD1TB_JHHY.ttf'
  },
  {
    label: 'Marcellus',
    value: 'https://fonts.gstatic.com/s/marcellus/v13/wEO_EBrOk8hQLDvIAF8FUfAL3EsHiA.ttf'
  },
  {
    label: 'Marcellus SC',
    value: 'https://fonts.gstatic.com/s/marcellussc/v13/ke8iOgUHP1dg-Rmi6RWjbLEPgdydGKikhA.ttf'
  },
  {
    label: 'Marck Script',
    value: 'https://fonts.gstatic.com/s/marckscript/v20/nwpTtK2oNgBA3Or78gapdwuCzyI-aMPF7Q.ttf'
  },
  {
    label: 'Margarine',
    value: 'https://fonts.gstatic.com/s/margarine/v25/qkBXXvoE6trLT9Y7YLye5JRLkAXbMQ.ttf'
  },
  {
    label: 'Marhey',
    value: 'https://fonts.gstatic.com/s/marhey/v6/x3d8ck7Laq-T7wl7mqfVrEe9sDvtBctwO2cXiGevOMw.ttf'
  },
  {
    label: 'Markazi Text',
    value: 'https://fonts.gstatic.com/s/markazitext/v23/sykh-ydym6AtQaiEtX7yhqb_rV1k_81ZVYYZtfSQT4MlBekmJLo.ttf'
  },
  {
    label: 'Marko One',
    value: 'https://fonts.gstatic.com/s/markoone/v23/9Btq3DFG0cnVM5lw1haaKpUfrHPzUw.ttf'
  },
  {
    label: 'Marmelad',
    value: 'https://fonts.gstatic.com/s/marmelad/v18/Qw3eZQdSHj_jK2e-8tFLG-YMC0R8.ttf'
  },
  {
    label: 'Martel',
    value: 'https://fonts.gstatic.com/s/martel/v11/PN_xRfK9oXHga0XtYcI-jT3L_w.ttf'
  },
  {
    label: 'Martel Sans',
    value: 'https://fonts.gstatic.com/s/martelsans/v13/h0GsssGi7VdzDgKjM-4d8ijfze-PPlUu.ttf'
  },
  {
    label: 'Martian Mono',
    value:
      'https://fonts.gstatic.com/s/martianmono/v3/2V08KIcADoYhV6w87xrTKjs4CYElh_VS9YA4TlTnQzaVMIE6j15dYY1qu86WD75kdpF2.ttf'
  },
  {
    label: 'Marvel',
    value: 'https://fonts.gstatic.com/s/marvel/v16/nwpVtKeoNgBV0qaIkV7ED366zg.ttf'
  },
  {
    label: 'Mate',
    value: 'https://fonts.gstatic.com/s/mate/v17/m8JdjftRd7WZ2z28WoXSaLU.ttf'
  },
  {
    label: 'Mate SC',
    value: 'https://fonts.gstatic.com/s/matesc/v22/-nF8OGQ1-uoVr2wKyiXZ95OkJwA.ttf'
  },
  {
    label: 'Material Icons',
    value: 'https://fonts.gstatic.com/s/materialicons/v142/flUhRq6tzZclQEJ-Vdg-IuiaDsNZIhI8tIHh.ttf'
  },
  {
    label: 'Material Icons Outlined',
    value: 'https://fonts.gstatic.com/s/materialiconsoutlined/v109/gok-H7zzDkdnRel8-DQ6KAXJ69wP1tGnf4ZGhUcdl5GuI2Ze.otf'
  },
  {
    label: 'Material Icons Round',
    value: 'https://fonts.gstatic.com/s/materialiconsround/v108/LDItaoyNOAY6Uewc665JcIzCKsKc_M9flwmMq_fTTvg-.otf'
  },
  {
    label: 'Material Icons Sharp',
    value: 'https://fonts.gstatic.com/s/materialiconssharp/v109/oPWQ_lt5nv4pWNJpghLP75WiFR4kLh3kvmvSImEyc0vd.otf'
  },
  {
    label: 'Material Icons Two Tone',
    value: 'https://fonts.gstatic.com/s/materialiconstwotone/v112/hESh6WRmNCxEqUmNyh3JDeGxjVVyMg4tHGctNCu3NjDrH_77.otf'
  },
  {
    label: 'Material Symbols Outlined',
    value:
      'https://fonts.gstatic.com/s/materialsymbolsoutlined/v199/kJF1BvYX7BgnkSrUwT8OhrdQw4oELdPIeeII9v6oDMzByHX9rA6RzaxHMPdY43zj-jCxv3fzvRNU22ZXGJpEpjC_1v-p_4MrImHCIJIZrDCvHOembd5zrTgt.ttf'
  },
  {
    label: 'Material Symbols Rounded',
    value:
      'https://fonts.gstatic.com/s/materialsymbolsrounded/v197/syl0-zNym6YjUruM-QrEh7-nyTnjDwKNJ_190FjpZIvDmUSVOK7BDB_Qb9vUSzq3wzLK-P0J-V_Zs-QtQth3-jOcbTCVpeRL2w5rwZu2rIelXxKJKJBjAa8.ttf'
  },
  {
    label: 'Material Symbols Sharp',
    value:
      'https://fonts.gstatic.com/s/materialsymbolssharp/v195/gNNBW2J8Roq16WD5tFNRaeLQk6-SHQ_R00k4c2_whPnoY9ruReaU4bHmz74m0ZkGH-VBYe1x0TV6x4yFH8F-H5OdzEL3sVTgJtfbYxOLojCOJ1H7-knk.ttf'
  },
  {
    label: 'Maven Pro',
    value: 'https://fonts.gstatic.com/s/mavenpro/v36/7Auup_AqnyWWAxW2Wk3swUz56MS91Eww8SX25nCpozp5GvU.ttf'
  },
  {
    label: 'McLaren',
    value: 'https://fonts.gstatic.com/s/mclaren/v17/2EbnL-ZuAXFqZFXISYYf8z2Yt_c.ttf'
  },
  {
    label: 'Mea Culpa',
    value: 'https://fonts.gstatic.com/s/meaculpa/v6/AMOTz4GcuWbEIuza8jsZms0QW3mqyg.ttf'
  },
  {
    label: 'Meddon',
    value: 'https://fonts.gstatic.com/s/meddon/v24/kmK8ZqA2EgDNeHTZhBdB3y_Aow.ttf'
  },
  {
    label: 'MedievalSharp',
    value: 'https://fonts.gstatic.com/s/medievalsharp/v26/EvOJzAlL3oU5AQl2mP5KdgptAq96MwvXLDk.ttf'
  },
  {
    label: 'Medula One',
    value: 'https://fonts.gstatic.com/s/medulaone/v19/YA9Wr0qb5kjJM6l2V0yukiEqs7GtlvY.ttf'
  },
  {
    label: 'Meera Inimai',
    value: 'https://fonts.gstatic.com/s/meerainimai/v12/845fNMM5EIqOW5MPuvO3ILep_2jDVevnLQ.ttf'
  },
  {
    label: 'Megrim',
    value: 'https://fonts.gstatic.com/s/megrim/v16/46kulbz5WjvLqJZlbWXgd0RY1g.ttf'
  },
  {
    label: 'Meie Script',
    value: 'https://fonts.gstatic.com/s/meiescript/v21/_LOImzDK7erRjhunIspaMjxn5IXg0WDz.ttf'
  },
  {
    label: 'Meow Script',
    value: 'https://fonts.gstatic.com/s/meowscript/v5/0FlQVPqanlaJrtr8AnJ0ESch0_0CfDf1.ttf'
  },
  {
    label: 'Merienda',
    value: 'https://fonts.gstatic.com/s/merienda/v19/gNMaW3x8Qoy5_mf8uUkJGHtiYXjmKFy5enhoSU78QGBV0A.ttf'
  },
  {
    label: 'Merriweather',
    value: 'https://fonts.gstatic.com/s/merriweather/v30/u-440qyriQwlOrhSvowK_l5OeyxNV-bnrw.ttf'
  },
  {
    label: 'Merriweather Sans',
    value:
      'https://fonts.gstatic.com/s/merriweathersans/v26/2-cO9IRs1JiJN1FRAMjTN5zd9vgsFF_5asQTb6hZ2JKZou4ljuEG7xFHnQ.ttf'
  },
  {
    label: 'Metal',
    value: 'https://fonts.gstatic.com/s/metal/v30/lW-wwjUJIXTo7i3nnoQAUdN2.ttf'
  },
  {
    label: 'Metal Mania',
    value: 'https://fonts.gstatic.com/s/metalmania/v22/RWmMoKWb4e8kqMfBUdPFJeXCg6UKDXlq.ttf'
  },
  {
    label: 'Metamorphous',
    value: 'https://fonts.gstatic.com/s/metamorphous/v20/Wnz8HA03aAXcC39ZEX5y1330PCCthTsmaQ.ttf'
  },
  {
    label: 'Metrophobic',
    value: 'https://fonts.gstatic.com/s/metrophobic/v23/sJoA3LZUhMSAPV_u0qwiAT-J737FPEEL.ttf'
  },
  {
    label: 'Michroma',
    value: 'https://fonts.gstatic.com/s/michroma/v19/PN_zRfy9qWD8fEagAMg6rzjb_-Da.ttf'
  },
  {
    label: 'Micro 5',
    value: 'https://fonts.gstatic.com/s/micro5/v1/H4cnBX2MkcfEngTr0gYQ7LO5mqc.ttf'
  },
  {
    label: 'Micro 5 Charted',
    value: 'https://fonts.gstatic.com/s/micro5charted/v1/hESp6XxmPDtTtADZhn7oD_yrmxEGRUsJQAlbUA.ttf'
  },
  {
    label: 'Milonga',
    value: 'https://fonts.gstatic.com/s/milonga/v22/SZc53FHnIaK9W5kffz3GkUrS8DI.ttf'
  },
  {
    label: 'Miltonian',
    value: 'https://fonts.gstatic.com/s/miltonian/v30/zOL-4pbPn6Ne9JqTg9mr6e5As-FeiQ.ttf'
  },
  {
    label: 'Miltonian Tattoo',
    value: 'https://fonts.gstatic.com/s/miltoniantattoo/v32/EvOUzBRL0o0kCxF-lcMCQxlpVsA_FwP8MDBku-s.ttf'
  },
  {
    label: 'Mina',
    value: 'https://fonts.gstatic.com/s/mina/v11/-nFzOGc18vARrz9j7i3y65o.ttf'
  },
  {
    label: 'Mingzat',
    value: 'https://fonts.gstatic.com/s/mingzat/v8/0QIgMX5C-o-oWWyvBttkm_mv670.ttf'
  },
  {
    label: 'Miniver',
    value: 'https://fonts.gstatic.com/s/miniver/v25/eLGcP-PxIg-5H0vC770Cy8r8fWA.ttf'
  },
  {
    label: 'Miriam Libre',
    value: 'https://fonts.gstatic.com/s/miriamlibre/v14/DdTh798HsHwubBAqfkcBTL_vYJn_Teun9g.ttf'
  },
  {
    label: 'Mirza',
    value: 'https://fonts.gstatic.com/s/mirza/v17/co3ImWlikiN5EurdKMewsrvI.ttf'
  },
  {
    label: 'Miss Fajardose',
    value: 'https://fonts.gstatic.com/s/missfajardose/v22/E21-_dn5gvrawDdPFVl-N0Ajb8qvWPaJq4no.ttf'
  },
  {
    label: 'Mitr',
    value: 'https://fonts.gstatic.com/s/mitr/v11/pxiLypw5ucZFyTsyMJj_b1o.ttf'
  },
  {
    label: 'Mochiy Pop One',
    value: 'https://fonts.gstatic.com/s/mochiypopone/v9/QdVPSTA9Jh-gg-5XZP2UmU4O9kwwD3s6ZKAi.ttf'
  },
  {
    label: 'Mochiy Pop P One',
    value: 'https://fonts.gstatic.com/s/mochiypoppone/v9/Ktk2AKuPeY_td1-h9LayHYWCjAqyN4O3WYZB_sU.ttf'
  },
  {
    label: 'Modak',
    value: 'https://fonts.gstatic.com/s/modak/v18/EJRYQgs1XtIEsnMH8BVZ76KU.ttf'
  },
  {
    label: 'Modern Antiqua',
    value: 'https://fonts.gstatic.com/s/modernantiqua/v24/NGStv5TIAUg6Iq_RLNo_2dp1sI1Ea2u0c3Gi.ttf'
  },
  {
    label: 'Mogra',
    value: 'https://fonts.gstatic.com/s/mogra/v19/f0X40eSs8c95TBo4DvLmxtnG.ttf'
  },
  {
    label: 'Mohave',
    value: 'https://fonts.gstatic.com/s/mohave/v9/7cH0v4ksjJunKqMVAOPIMOeSmiojdnn_HvCQopLSvBk.ttf'
  },
  {
    label: 'Moirai One',
    value: 'https://fonts.gstatic.com/s/moiraione/v1/2sDbZGFUgJLJmby6xgNGT0WWB7UcfCg.ttf'
  },
  {
    label: 'Molengo',
    value: 'https://fonts.gstatic.com/s/molengo/v16/I_uuMpWeuBzZNBtQbbRQkiCvs5Y.ttf'
  },
  {
    label: 'Monda',
    value: 'https://fonts.gstatic.com/s/monda/v17/TK3-WkYFABsmjuBtFuvTIFRAPpWsH3oMoWtGaA-Ijw.ttf'
  },
  {
    label: 'Monofett',
    value: 'https://fonts.gstatic.com/s/monofett/v23/mFTyWbofw6zc9NtnW43SuRwr0VJ7.ttf'
  },
  {
    label: 'Monomaniac One',
    value: 'https://fonts.gstatic.com/s/monomaniacone/v11/4iC06K17YctZjx50EU-QlwPmcqRnqYkB5kwI.ttf'
  },
  {
    label: 'Monoton',
    value: 'https://fonts.gstatic.com/s/monoton/v19/5h1aiZUrOngCibe4fkbBQ2S7FU8.ttf'
  },
  {
    label: 'Monsieur La Doulaise',
    value: 'https://fonts.gstatic.com/s/monsieurladoulaise/v18/_Xmz-GY4rjmCbQfc-aPRaa4pqV340p7EZl5ewkEU4HTy.ttf'
  },
  {
    label: 'Montaga',
    value: 'https://fonts.gstatic.com/s/montaga/v13/H4cnBX2Ml8rCkEO_0gYQ7LO5mqc.ttf'
  },
  {
    label: 'Montagu Slab',
    value:
      'https://fonts.gstatic.com/s/montaguslab/v12/6qLhKZIQtB_zv0xUaXRDWkY_HXsphdLRZF40vm_jzR2jhk_n3T6ACkDbEnP9Fs7bOSO7.ttf'
  },
  {
    label: 'MonteCarlo',
    value: 'https://fonts.gstatic.com/s/montecarlo/v11/buEzpo6-f9X01GadLA0G0CoV_NxLeiw.ttf'
  },
  {
    label: 'Montez',
    value: 'https://fonts.gstatic.com/s/montez/v22/845ZNMk5GoGIX8lm1LDeSd-R_g.ttf'
  },
  {
    label: 'Montserrat',
    value: 'https://fonts.gstatic.com/s/montserrat/v26/JTUHjIg1_i6t8kCHKm4532VJOt5-QNFgpCtr6Ew-Y3tcoqK5.ttf'
  },
  {
    label: 'Montserrat Alternates',
    value: 'https://fonts.gstatic.com/s/montserratalternates/v17/mFTvWacfw6zH4dthXcyms1lPpC8I_b0juU0J7K3RCJ1b0w.ttf'
  },
  {
    label: 'Montserrat Subrayada',
    value: 'https://fonts.gstatic.com/s/montserratsubrayada/v19/U9MD6c-o9H7PgjlTHThBnNHGVUORwteQQE8LYuceqGT-.ttf'
  },
  {
    label: 'Moo Lah Lah',
    value: 'https://fonts.gstatic.com/s/moolahlah/v6/dg4h_p_opKZOA0w1AYcm55wtYQYugjW4.ttf'
  },
  {
    label: 'Mooli',
    value: 'https://fonts.gstatic.com/s/mooli/v1/-F6_fjJpLyk1bYPBBG7YpzlJ.ttf'
  },
  {
    label: 'Moon Dance',
    value: 'https://fonts.gstatic.com/s/moondance/v6/WBLgrEbUbFlYW9ekmGawe2XiKMiokE4.ttf'
  },
  {
    label: 'Moul',
    value: 'https://fonts.gstatic.com/s/moul/v27/nuF2D__FSo_3E-RYiJCy-00.ttf'
  },
  {
    label: 'Moulpali',
    value: 'https://fonts.gstatic.com/s/moulpali/v30/H4ckBXKMl9HagUWymyY6wr-wg763.ttf'
  },
  {
    label: 'Mountains of Christmas',
    value: 'https://fonts.gstatic.com/s/mountainsofchristmas/v22/3y9w6a4zcCnn5X0FDyrKi2ZRUBIy8uxoUo7ePNamMPNpJpc.ttf'
  },
  {
    label: 'Mouse Memoirs',
    value: 'https://fonts.gstatic.com/s/mousememoirs/v17/t5tmIRoSNJ-PH0WNNgDYxdSb7TnFrpOHYh4.ttf'
  },
  {
    label: 'Mr Bedfort',
    value: 'https://fonts.gstatic.com/s/mrbedfort/v22/MQpR-WCtNZSWAdTMwBicliq0XZe_Iy8.ttf'
  },
  {
    label: 'Mr Dafoe',
    value: 'https://fonts.gstatic.com/s/mrdafoe/v14/lJwE-pIzkS5NXuMMrGiqg7MCxz_C.ttf'
  },
  {
    label: 'Mr De Haviland',
    value: 'https://fonts.gstatic.com/s/mrdehaviland/v14/OpNVnooIhJj96FdB73296ksbOj3C4ULVNTlB.ttf'
  },
  {
    label: 'Mrs Saint Delafield',
    value: 'https://fonts.gstatic.com/s/mrssaintdelafield/v13/v6-IGZDIOVXH9xtmTZfRagunqBw5WC62cK4tLsubB2w.ttf'
  },
  {
    label: 'Mrs Sheppards',
    value: 'https://fonts.gstatic.com/s/mrssheppards/v23/PN_2Rfm9snC0XUGoEZhb91ig3vjxynMix4Y.ttf'
  },
  {
    label: 'Ms Madi',
    value: 'https://fonts.gstatic.com/s/msmadi/v2/HTxsL2UxNnOji5E1N-DPiI7QAYo.ttf'
  },
  {
    label: 'Mukta',
    value: 'https://fonts.gstatic.com/s/mukta/v14/iJWKBXyXfDDVXYnGp32S0H3f.ttf'
  },
  {
    label: 'Mukta Mahee',
    value: 'https://fonts.gstatic.com/s/muktamahee/v16/XRXQ3IOIi0hcP8iVU67hA-vNWz4PDWtj.ttf'
  },
  {
    label: 'Mukta Malar',
    value: 'https://fonts.gstatic.com/s/muktamalar/v12/MCoXzAXyz8LOE2FpJMxZqLv4LfQJwHbn.ttf'
  },
  {
    label: 'Mukta Vaani',
    value: 'https://fonts.gstatic.com/s/muktavaani/v13/3Jn5SD_-ynaxmxnEfVHPIF0FfORL0fNy.ttf'
  },
  {
    label: 'Mulish',
    value: 'https://fonts.gstatic.com/s/mulish/v13/1Ptyg83HX_SGhgqO0yLcmjzUAuWexZNRwaClGrw-PTY.ttf'
  },
  {
    label: 'Murecho',
    value: 'https://fonts.gstatic.com/s/murecho/v11/q5uYsoq3NOBn_I-ggCJg98TBOoNFCMpr5XWZLCpUOaM6.ttf'
  },
  {
    label: 'MuseoModerno',
    value: 'https://fonts.gstatic.com/s/museomoderno/v27/zrf30HnU0_7wWdMrFcWqSEXPVyEaWJ55pTleMlZEuewajeKlCdo.ttf'
  },
  {
    label: 'My Soul',
    value: 'https://fonts.gstatic.com/s/mysoul/v5/3XFqErcuy945_u6KF_Ulk2nnXf0.ttf'
  },
  {
    label: 'Mynerve',
    value: 'https://fonts.gstatic.com/s/mynerve/v6/P5sCzZKPdNjb4jt7xCRuiZ-uydg.ttf'
  },
  {
    label: 'Mystery Quest',
    value: 'https://fonts.gstatic.com/s/mysteryquest/v20/-nF6OG414u0E6k0wynSGlujRHwElD_9Qz9E.ttf'
  },
  {
    label: 'NTR',
    value: 'https://fonts.gstatic.com/s/ntr/v15/RLpzK5Xy0ZjiGGhs5TA4bg.ttf'
  },
  {
    label: 'Nabla',
    value: 'https://fonts.gstatic.com/s/nabla/v10/j8_D6-LI0Lvpe7Makz5UhJt9C3uqg_X_75gyGS4jAxsNIjrRNRBUFFR_198.ttf'
  },
  {
    label: 'Namdhinggo',
    value: 'https://fonts.gstatic.com/s/namdhinggo/v2/uk-mEGe3rbgg8Xzoy5-TDnWj4yxx7o8.ttf'
  },
  {
    label: 'Nanum Brush Script',
    value: 'https://fonts.gstatic.com/s/nanumbrushscript/v22/wXK2E2wfpokopxzthSqPbcR5_gVaxazyjqBr1lO97Q.ttf'
  },
  {
    label: 'Nanum Gothic',
    value: 'https://fonts.gstatic.com/s/nanumgothic/v23/PN_3Rfi-oW3hYwmKDpxS7F_z_tLfxno73g.ttf'
  },
  {
    label: 'Nanum Gothic Coding',
    value: 'https://fonts.gstatic.com/s/nanumgothiccoding/v21/8QIVdjzHisX_8vv59_xMxtPFW4IXROwsy6QxVs1X7tc.ttf'
  },
  {
    label: 'Nanum Myeongjo',
    value: 'https://fonts.gstatic.com/s/nanummyeongjo/v22/9Btx3DZF0dXLMZlywRbVRNhxy1LreHQ8juyl.ttf'
  },
  {
    label: 'Nanum Pen Script',
    value: 'https://fonts.gstatic.com/s/nanumpenscript/v19/daaDSSYiLGqEal3MvdA_FOL_3FkN2z7-aMFCcTU.ttf'
  },
  {
    label: 'Narnoor',
    value: 'https://fonts.gstatic.com/s/narnoor/v7/cIf9MaFWuVo-UTyPxCmrYGkHgIs.ttf'
  },
  {
    label: 'Neonderthaw',
    value: 'https://fonts.gstatic.com/s/neonderthaw/v6/Iure6Yx5-oWVZI0r-17AeZZJprVA4XQ0.ttf'
  },
  {
    label: 'Nerko One',
    value: 'https://fonts.gstatic.com/s/nerkoone/v16/m8JQjfZSc7OXlB3ZMOjzcJ5BZmqa3A.ttf'
  },
  {
    label: 'Neucha',
    value: 'https://fonts.gstatic.com/s/neucha/v17/q5uGsou0JOdh94bvugNsCxVEgA.ttf'
  },
  {
    label: 'Neuton',
    value: 'https://fonts.gstatic.com/s/neuton/v22/UMBTrPtMoH62xUZyyII7civlBw.ttf'
  },
  {
    label: 'New Rocker',
    value: 'https://fonts.gstatic.com/s/newrocker/v16/MwQzbhjp3-HImzcCU_cJkGMViblPtXs.ttf'
  },
  {
    label: 'New Tegomin',
    value: 'https://fonts.gstatic.com/s/newtegomin/v10/SLXMc1fV7Gd9USdBAfPlqfN0Q3ptkDMN.ttf'
  },
  {
    label: 'News Cycle',
    value: 'https://fonts.gstatic.com/s/newscycle/v23/CSR64z1Qlv-GDxkbKVQ_TOcATNt_pOU.ttf'
  },
  {
    label: 'Newsreader',
    value:
      'https://fonts.gstatic.com/s/newsreader/v20/cY9qfjOCX1hbuyalUrK49dLac06G1ZGsZBtoBCzBDXXD9JVF438weI_ADOxEPjCggA.ttf'
  },
  {
    label: 'Niconne',
    value: 'https://fonts.gstatic.com/s/niconne/v15/w8gaH2QvRug1_rTfrQut2F4OuOo.ttf'
  },
  {
    label: 'Niramit',
    value: 'https://fonts.gstatic.com/s/niramit/v10/I_uuMpWdvgLdNxVLbbRQkiCvs5Y.ttf'
  },
  {
    label: 'Nixie One',
    value: 'https://fonts.gstatic.com/s/nixieone/v16/lW-8wjkKLXjg5y2o2uUoUOFzpS-yLw.ttf'
  },
  {
    label: 'Nobile',
    value: 'https://fonts.gstatic.com/s/nobile/v17/m8JTjflSeaOVl1i2XqfXeLVdbw.ttf'
  },
  {
    label: 'Nokora',
    value: 'https://fonts.gstatic.com/s/nokora/v31/hYkIPuwgTubzaWxQOzoPovZg8Q.ttf'
  },
  {
    label: 'Norican',
    value: 'https://fonts.gstatic.com/s/norican/v15/MwQ2bhXp1eSBqjkPGJJRtGs-lbA.ttf'
  },
  {
    label: 'Nosifer',
    value: 'https://fonts.gstatic.com/s/nosifer/v22/ZGjXol5JTp0g5bxZaC1RVDNdGDs.ttf'
  },
  {
    label: 'Notable',
    value: 'https://fonts.gstatic.com/s/notable/v18/gNMEW3N_SIqx-WX9-HMoFIez5MI.ttf'
  },
  {
    label: 'Nothing You Could Do',
    value: 'https://fonts.gstatic.com/s/nothingyoucoulddo/v19/oY1B8fbBpaP5OX3DtrRYf_Q2BPB1SnfZb0OJl1ol2Ymo.ttf'
  },
  {
    label: 'Noticia Text',
    value: 'https://fonts.gstatic.com/s/noticiatext/v15/VuJ2dNDF2Yv9qppOePKYRP1GYTFZt0rNpQ.ttf'
  },
  {
    label: 'Noto Color Emoji',
    value: 'https://fonts.gstatic.com/s/notocoloremoji/v30/Yq6P-KqIXTD0t4D9z1ESnKM3-HpFab5s79iz64w.ttf'
  },
  {
    label: 'Noto Emoji',
    value: 'https://fonts.gstatic.com/s/notoemoji/v50/bMrnmSyK7YY-MEu6aWjPDs-ar6uWaGWuob-r0jwvS-FGJCMY.ttf'
  },
  {
    label: 'Noto Kufi Arabic',
    value: 'https://fonts.gstatic.com/s/notokufiarabic/v21/CSRp4ydQnPyaDxEXLFF6LZVLKrodhu8t57o1kDc5Wh5v34bPnLSmf5yD.ttf'
  },
  {
    label: 'Noto Music',
    value: 'https://fonts.gstatic.com/s/notomusic/v20/pe0rMIiSN5pO63htf1sxIteQB9Zra1U.ttf'
  },
  {
    label: 'Noto Naskh Arabic',
    value:
      'https://fonts.gstatic.com/s/notonaskharabic/v34/RrQ5bpV-9Dd1b1OAGA6M9PkyDuVBePeKNaxcsss0Y7bwvc5krK0z9_Mnuw.ttf'
  },
  {
    label: 'Noto Nastaliq Urdu',
    value:
      'https://fonts.gstatic.com/s/notonastaliqurdu/v20/LhWNMUPbN-oZdNFcBy1-DJYsEoTq5pudQ9L940pGPkB3Qt_-DK2f2-_8mEw.ttf'
  },
  {
    label: 'Noto Rashi Hebrew',
    value:
      'https://fonts.gstatic.com/s/notorashihebrew/v26/EJR_Qh82XsIK-QFmqXk4zvLwFVya0vFL-HlKM5e6C6HZB-HkRyq6Nf2pfA.ttf'
  },
  {
    label: 'Noto Sans',
    value:
      'https://fonts.gstatic.com/s/notosans/v36/o-0mIpQlx3QUlC5A4PNB6Ryti20_6n1iPHjcz6L1SoM-jCpoiyD9A99d41P6zHtY.ttf'
  },
  {
    label: 'Noto Sans Adlam',
    value: 'https://fonts.gstatic.com/s/notosansadlam/v22/neIczCCpqp0s5pPusPamd81eMfjPonvqdbYxxpgufnv0TGnBZLwhuvk.ttf'
  },
  {
    label: 'Noto Sans Adlam Unjoined',
    value:
      'https://fonts.gstatic.com/s/notosansadlamunjoined/v26/P5sszY2MYsLRsB5_ildkzPPDsLQXcOEmaFOqOGcaYrzFTIjsPam_Ye35PMEe-E3slUg.ttf'
  },
  {
    label: 'Noto Sans Anatolian Hieroglyphs',
    value:
      'https://fonts.gstatic.com/s/notosansanatolianhieroglyphs/v16/ijw9s4roRME5LLRxjsRb8A0gKPSWq4BbDmHHu6j2pEtUJzZWXybIymc5QYo.ttf'
  },
  {
    label: 'Noto Sans Arabic',
    value:
      'https://fonts.gstatic.com/s/notosansarabic/v18/nwpxtLGrOAZMl5nJ_wfgRg3DrWFZWsnVBJ_sS6tlqHHFlhQ5l3sQWIHPqzCfyGyvu3CBFQLaig.ttf'
  },
  {
    label: 'Noto Sans Armenian',
    value:
      'https://fonts.gstatic.com/s/notosansarmenian/v43/ZgN0jOZKPa7CHqq0h37c7ReDUubm2SEdFXp7ig73qtTY5idb74R9UdM3y2nZLorxb60iYy6zF3Eg.ttf'
  },
  {
    label: 'Noto Sans Avestan',
    value: 'https://fonts.gstatic.com/s/notosansavestan/v21/bWti7ejKfBziStx7lIzKOLQZKhIJkyu9SASLji8U.ttf'
  },
  {
    label: 'Noto Sans Balinese',
    value:
      'https://fonts.gstatic.com/s/notosansbalinese/v24/NaPwcYvSBuhTirw6IaFn6UrRDaqje-lpbbRtYf-Fwu2Ov7fdhE5Vd222PPY.ttf'
  },
  {
    label: 'Noto Sans Bamum',
    value: 'https://fonts.gstatic.com/s/notosansbamum/v27/uk-0EGK3o6EruUbnwovcbBTkkklK_Ya_PBHfNGTPEddO-_gLykxEkxA.ttf'
  },
  {
    label: 'Noto Sans Bassa Vah',
    value:
      'https://fonts.gstatic.com/s/notosansbassavah/v17/PN_bRee-r3f7LnqsD5sax12gjZn7mBpL5YwUpA2MBdcFn4MaAc6p34gH-GD7.ttf'
  },
  {
    label: 'Noto Sans Batak',
    value: 'https://fonts.gstatic.com/s/notosansbatak/v20/gok2H6TwAEdtF9N8-mdTCQvT-Zdgo4_PHuk74A.ttf'
  },
  {
    label: 'Noto Sans Bengali',
    value:
      'https://fonts.gstatic.com/s/notosansbengali/v20/Cn-SJsCGWQxOjaGwMQ6fIiMywrNJIky6nvd8BjzVMvJx2mcSPVFpVEqE-6KmsolLudCk8izI0lc.ttf'
  },
  {
    label: 'Noto Sans Bhaiksuki',
    value: 'https://fonts.gstatic.com/s/notosansbhaiksuki/v17/UcC63EosKniBH4iELXATsSBWdvUHXxhj8rLUdU4wh9U.ttf'
  },
  {
    label: 'Noto Sans Brahmi',
    value: 'https://fonts.gstatic.com/s/notosansbrahmi/v19/vEFK2-VODB8RrNDvZSUmQQIIByV18tK1W77HtMo.ttf'
  },
  {
    label: 'Noto Sans Buginese',
    value: 'https://fonts.gstatic.com/s/notosansbuginese/v18/esDM30ldNv-KYGGJpKGk18phe_7Da6_gtfuEXLmNtw.ttf'
  },
  {
    label: 'Noto Sans Buhid',
    value: 'https://fonts.gstatic.com/s/notosansbuhid/v22/Dxxy8jiXMW75w3OmoDXVWJD7YwzAe6tgnaFoGA.ttf'
  },
  {
    label: 'Noto Sans Canadian Aboriginal',
    value:
      'https://fonts.gstatic.com/s/notosanscanadianaboriginal/v26/4C_TLjTuEqPj-8J01CwaGkiZ9os0iGVkezM1mUT-j_Lmlzda6uH_nnX1bzigWLn_yAsg0q0uhQ.ttf'
  },
  {
    label: 'Noto Sans Carian',
    value: 'https://fonts.gstatic.com/s/notosanscarian/v16/LDIpaoiONgYwA9Yc6f0gUILeMIOgs7ob9yGLmfI.ttf'
  },
  {
    label: 'Noto Sans Caucasian Albanian',
    value:
      'https://fonts.gstatic.com/s/notosanscaucasianalbanian/v18/nKKA-HM_FYFRJvXzVXaANsU0VzsAc46QGOkWytlTs-TXrYDmoVmRSZo.ttf'
  },
  {
    label: 'Noto Sans Chakma',
    value: 'https://fonts.gstatic.com/s/notosanschakma/v17/Y4GQYbJ8VTEp4t3MKJSMjg5OIzhi4JjTQhYBeYo.ttf'
  },
  {
    label: 'Noto Sans Cham',
    value: 'https://fonts.gstatic.com/s/notosanscham/v30/pe06MIySN5pO62Z5YkFyQb_bbuRhe6D4yip43qfcERwcv7GykboaLg.ttf'
  },
  {
    label: 'Noto Sans Cherokee',
    value:
      'https://fonts.gstatic.com/s/notosanscherokee/v20/KFOPCm6Yu8uF-29fiz9vQF9YWK6Z8O10cHNA0cSkZCHYWi5PDkm5rAffjl0.ttf'
  },
  {
    label: 'Noto Sans Chorasmian',
    value: 'https://fonts.gstatic.com/s/notosanschorasmian/v3/MQpL-X6uKMC7ROPLwRnI9ULxK_7NVkf8S5vyoH7w4g9b.ttf'
  },
  {
    label: 'Noto Sans Coptic',
    value: 'https://fonts.gstatic.com/s/notosanscoptic/v21/iJWfBWmUZi_OHPqn4wq6kgqumOEd78u_VG0xR4Y.ttf'
  },
  {
    label: 'Noto Sans Cuneiform',
    value: 'https://fonts.gstatic.com/s/notosanscuneiform/v17/bMrrmTWK7YY-MF22aHGGd7H8PhJtvBDWgb9JlRQueeQ.ttf'
  },
  {
    label: 'Noto Sans Cypriot',
    value: 'https://fonts.gstatic.com/s/notosanscypriot/v19/8AtzGta9PYqQDjyp79a6f8Cj-3a3cxIsK5MPpahF.ttf'
  },
  {
    label: 'Noto Sans Cypro Minoan',
    value: 'https://fonts.gstatic.com/s/notosanscyprominoan/v1/2Eb2L_dtDUlkNmPHB_UVtEzp3ZlPGqZ_4nAGq9eSf8_eQSE.ttf'
  },
  {
    label: 'Noto Sans Deseret',
    value: 'https://fonts.gstatic.com/s/notosansdeseret/v17/MwQsbgPp1eKH6QsAVuFb9AZM6MMr2Vq9ZnJSZtQG.ttf'
  },
  {
    label: 'Noto Sans Devanagari',
    value:
      'https://fonts.gstatic.com/s/notosansdevanagari/v26/TuGoUUFzXI5FBtUq5a8bjKYTZjtRU6Sgv3NaV_SNmI0b8QQCQmHn6B2OHjbL_08AlXQly-AzoFoW4Ow.ttf'
  },
  {
    label: 'Noto Sans Display',
    value:
      'https://fonts.gstatic.com/s/notosansdisplay/v26/RLpbK4fy6r6tOBEJg0IAKzqdFZVZxpMkXJMhnB9XjO1o90LuV-PT4Doq_AKp_3cKVTGQ2iHrvWM.ttf'
  },
  {
    label: 'Noto Sans Duployan',
    value: 'https://fonts.gstatic.com/s/notosansduployan/v18/gokzH7nwAEdtF9N8-mdTDx_X9JM5wsvrFsIn6WYDvA.ttf'
  },
  {
    label: 'Noto Sans Egyptian Hieroglyphs',
    value:
      'https://fonts.gstatic.com/s/notosansegyptianhieroglyphs/v29/vEF42-tODB8RrNDvZSUmRhcQHzx1s7y_F9-j3qSzEcbEYindSVK8xRg7iw.ttf'
  },
  {
    label: 'Noto Sans Elbasan',
    value: 'https://fonts.gstatic.com/s/notosanselbasan/v16/-F6rfiZqLzI2JPCgQBnw400qp1trvHdlre4dFcFh.ttf'
  },
  {
    label: 'Noto Sans Elymaic',
    value: 'https://fonts.gstatic.com/s/notosanselymaic/v17/UqyKK9YTJW5liNMhTMqe9vUFP65ZD4AjWOT0zi2V.ttf'
  },
  {
    label: 'Noto Sans Ethiopic',
    value:
      'https://fonts.gstatic.com/s/notosansethiopic/v47/7cHPv50vjIepfJVOZZgcpQ5B9FBTH9KGNfhSTgtoow1KVnIvyBoMSzUMacb-T35OK6DjwmfeaY9u.ttf'
  },
  {
    label: 'Noto Sans Georgian',
    value:
      'https://fonts.gstatic.com/s/notosansgeorgian/v44/PlIaFke5O6RzLfvNNVSitxkr76PRHBC4Ytyq-Gof7PUs4S7zWn-8YDB09HFNdpvnzFj-f5WK0OQV.ttf'
  },
  {
    label: 'Noto Sans Glagolitic',
    value: 'https://fonts.gstatic.com/s/notosansglagolitic/v18/1q2ZY4-BBFBst88SU_tOj4J-4yuNF_HI4ERK4Amu7nM1.ttf'
  },
  {
    label: 'Noto Sans Gothic',
    value: 'https://fonts.gstatic.com/s/notosansgothic/v16/TuGKUUVzXI5FBtUq5a8bj6wRbzxTFMX40kFQRx0.ttf'
  },
  {
    label: 'Noto Sans Grantha',
    value: 'https://fonts.gstatic.com/s/notosansgrantha/v19/3y976akwcCjmsU8NDyrKo3IQfQ4o-r8cFeulHc6N.ttf'
  },
  {
    label: 'Noto Sans Gujarati',
    value:
      'https://fonts.gstatic.com/s/notosansgujarati/v25/wlpWgx_HC1ti5ViekvcxnhMlCVo3f5pv17ivlzsUB14gg1TMR2Gw4VceEl7MA_ypFwPM_OdiEH0s.ttf'
  },
  {
    label: 'Noto Sans Gunjala Gondi',
    value:
      'https://fonts.gstatic.com/s/notosansgunjalagondi/v19/bWtX7e7KfBziStx7lIzKPrcSMwcEnCv6DW7n5g0ef3PLtymzNxYL4YDE4J4vCTxEJQ.ttf'
  },
  {
    label: 'Noto Sans Gurmukhi',
    value:
      'https://fonts.gstatic.com/s/notosansgurmukhi/v26/w8g9H3EvQP81sInb43inmyN9zZ7hb7ATbSWo4q8dJ74a3cVrYFQ_bogT0-gPeG1OenbxZ_trdp7h.ttf'
  },
  {
    label: 'Noto Sans HK',
    value: 'https://fonts.gstatic.com/s/notosanshk/v31/nKKF-GM_FYFRJvXzVXaAPe97P1KHynJFP716qHB--oWTiYjNvVA.ttf'
  },
  {
    label: 'Noto Sans Hanifi Rohingya',
    value:
      'https://fonts.gstatic.com/s/notosanshanifirohingya/v28/5h17iYsoOmIC3Yu3MDXLDw3UZCgghyOEBBY7hhLNyo3tiaiuSIAqrIYY4j6vvcudK8rN.ttf'
  },
  {
    label: 'Noto Sans Hanunoo',
    value: 'https://fonts.gstatic.com/s/notosanshanunoo/v21/f0Xs0fCv8dxkDWlZSoXOj6CphMloFsEsEpgL_ix2.ttf'
  },
  {
    label: 'Noto Sans Hatran',
    value: 'https://fonts.gstatic.com/s/notosanshatran/v16/A2BBn4Ne0RgnVF3Lnko-0sOBIfL_mM83r1nwzDs.ttf'
  },
  {
    label: 'Noto Sans Hebrew',
    value:
      'https://fonts.gstatic.com/s/notosanshebrew/v46/or3HQ7v33eiDljA1IufXTtVf7V6RvEEdhQlk0LlGxCyaeNKYZC0sqk3xXGiXd4qtoiJltutR2g.ttf'
  },
  {
    label: 'Noto Sans Imperial Aramaic',
    value:
      'https://fonts.gstatic.com/s/notosansimperialaramaic/v17/a8IMNpjwKmHXpgXbMIsbTc_kvks91LlLetBr5itQrtdml3YfPNno.ttf'
  },
  {
    label: 'Noto Sans Indic Siyaq Numbers',
    value:
      'https://fonts.gstatic.com/s/notosansindicsiyaqnumbers/v16/6xK5dTJFKcWIu4bpRBjRZRpsIYHabOeZ8UZLubTzpXNHKx2WPOpVd5Iu.ttf'
  },
  {
    label: 'Noto Sans Inscriptional Pahlavi',
    value:
      'https://fonts.gstatic.com/s/notosansinscriptionalpahlavi/v17/ll8UK3GaVDuxR-TEqFPIbsR79Xxz9WEKbwsjpz7VklYlC7FCVtqVOAYK0QA.ttf'
  },
  {
    label: 'Noto Sans Inscriptional Parthian',
    value:
      'https://fonts.gstatic.com/s/notosansinscriptionalparthian/v17/k3k7o-IMPvpLmixcA63oYi-yStDkgXuXncL7dzfW3P4TAJ2yklBJ2jNkLlLr.ttf'
  },
  {
    label: 'Noto Sans JP',
    value: 'https://fonts.gstatic.com/s/notosansjp/v52/-F6jfjtqLzI2JPCgQBnw7HFyzSD-AsregP8VFBEj75vY0rw-oME.ttf'
  },
  {
    label: 'Noto Sans Javanese',
    value:
      'https://fonts.gstatic.com/s/notosansjavanese/v23/2V01KJkDAIA6Hp4zoSScDjV0Y-eoHAHT-Z3MngEefiidxJnkFFliZYWj4O8.ttf'
  },
  {
    label: 'Noto Sans KR',
    value: 'https://fonts.gstatic.com/s/notosanskr/v36/PbyxFmXiEBPT4ITbgNA5Cgms3VYcOA-vvnIzzuoyeLTq8H4hfeE.ttf'
  },
  {
    label: 'Noto Sans Kaithi',
    value: 'https://fonts.gstatic.com/s/notosanskaithi/v21/buEtppS9f8_vkXadMBJJu0tWjLwjQi0KdoZIKlo.ttf'
  },
  {
    label: 'Noto Sans Kannada',
    value:
      'https://fonts.gstatic.com/s/notosanskannada/v27/8vIs7xs32H97qzQKnzfeXycxXZyUmySvZWItmf1fe6TVmgop9ndpS-BqHEyGrDvNzSIMLsPKrkY.ttf'
  },
  {
    label: 'Noto Sans Kawi',
    value: 'https://fonts.gstatic.com/s/notosanskawi/v3/92zBtBJLNqsg7tCciW0EPHNNh1ZgbtGWiTYDjvnK4AhmCpRyMjXVsQ.ttf'
  },
  {
    label: 'Noto Sans Kayah Li',
    value:
      'https://fonts.gstatic.com/s/notosanskayahli/v21/B50nF61OpWTRcGrhOVJJwOMXdca6Yecki3E06x2jVTX3WCc3CZH4EXLuKVM.ttf'
  },
  {
    label: 'Noto Sans Kharoshthi',
    value: 'https://fonts.gstatic.com/s/notosanskharoshthi/v16/Fh4qPiLjKS30-P4-pGMMXCCfvkc5Vd7KE5z4rFyx5mR1.ttf'
  },
  {
    label: 'Noto Sans Khmer',
    value:
      'https://fonts.gstatic.com/s/notosanskhmer/v24/ijw3s5roRME5LLRxjsRb-gssOenAyendxrgV2c-Zw-9vbVUti_Z_dWgtWYuNAJz4kAbrddiA.ttf'
  },
  {
    label: 'Noto Sans Khojki',
    value: 'https://fonts.gstatic.com/s/notosanskhojki/v19/-nFnOHM29Oofr2wohFbTuPPKVWpmK_d709jy92k.ttf'
  },
  {
    label: 'Noto Sans Khudawadi',
    value: 'https://fonts.gstatic.com/s/notosanskhudawadi/v21/fdNi9t6ZsWBZ2k5ltHN73zZ5hc8HANlHIjRnVVXz9MY.ttf'
  },
  {
    label: 'Noto Sans Lao',
    value:
      'https://fonts.gstatic.com/s/notosanslao/v30/bx6lNx2Ol_ixgdYWLm9BwxM3NW6BOkuf763Clj73CiQ_J1Djx9pidOt4ccbdf5MK3riB2w.ttf'
  },
  {
    label: 'Noto Sans Lao Looped',
    value:
      'https://fonts.gstatic.com/s/notosanslaolooped/v7/a8IgNpbwKmHXpgXbMIsbSMP7-3U72qUOX5gBg6LRXExhqHIX9YPTpvqkW4UthhjomPr3M-Zw5V_T71k.ttf'
  },
  {
    label: 'Noto Sans Lepcha',
    value: 'https://fonts.gstatic.com/s/notosanslepcha/v19/0QI7MWlB_JWgA166SKhu05TekNS32AJstqBXgd4.ttf'
  },
  {
    label: 'Noto Sans Limbu',
    value: 'https://fonts.gstatic.com/s/notosanslimbu/v22/3JnlSDv90Gmq2mrzckOBBRRoNJVj0MF3OHRDnA.ttf'
  },
  {
    label: 'Noto Sans Linear A',
    value: 'https://fonts.gstatic.com/s/notosanslineara/v18/oPWS_l16kP4jCuhpgEGmwJOiA18FZj22zmHQAGQicw.ttf'
  },
  {
    label: 'Noto Sans Linear B',
    value: 'https://fonts.gstatic.com/s/notosanslinearb/v17/HhyJU4wt9vSgfHoORYOiXOckKNB737IV3BkFTq4EPw.ttf'
  },
  {
    label: 'Noto Sans Lisu',
    value: 'https://fonts.gstatic.com/s/notosanslisu/v25/uk-3EGO3o6EruUbnwovcYhz6kh57_nqbcTdjJnHP2Vwt29IlxkVdig.ttf'
  },
  {
    label: 'Noto Sans Lycian',
    value: 'https://fonts.gstatic.com/s/notosanslycian/v15/QldVNSNMqAsHtsJ7UmqxBQA9r8wA5_naCJwn00E.ttf'
  },
  {
    label: 'Noto Sans Lydian',
    value: 'https://fonts.gstatic.com/s/notosanslydian/v18/c4m71mVzGN7s8FmIukZJ1v4ZlcPReUPXMoIjEQI.ttf'
  },
  {
    label: 'Noto Sans Mahajani',
    value: 'https://fonts.gstatic.com/s/notosansmahajani/v19/-F6sfiVqLzI2JPCgQBnw60Agp0JrvD5Fh8ARHNh4zg.ttf'
  },
  {
    label: 'Noto Sans Malayalam',
    value:
      'https://fonts.gstatic.com/s/notosansmalayalam/v26/sJoi3K5XjsSdcnzn071rL37lpAOsUThnDZIfPdbeSNzVakglNM-Qw8EaeB8Nss-_RuD9BFzEr6HxEA.ttf'
  },
  {
    label: 'Noto Sans Mandaic',
    value: 'https://fonts.gstatic.com/s/notosansmandaic/v16/cIfnMbdWt1w_HgCcilqhKQBo_OsMI5_A_gMk0izH.ttf'
  },
  {
    label: 'Noto Sans Manichaean',
    value: 'https://fonts.gstatic.com/s/notosansmanichaean/v18/taiVGntiC4--qtsfi4Jp9-_GkPZZCcrfekqCNTtFCtdX.ttf'
  },
  {
    label: 'Noto Sans Marchen',
    value: 'https://fonts.gstatic.com/s/notosansmarchen/v19/aFTO7OZ_Y282EP-WyG6QTOX_C8WZMHhPk652ZaHk.ttf'
  },
  {
    label: 'Noto Sans Masaram Gondi',
    value: 'https://fonts.gstatic.com/s/notosansmasaramgondi/v17/6xK_dThFKcWIu4bpRBjRYRV7KZCbUq6n_1kPnuGe7RI9WSWX.ttf'
  },
  {
    label: 'Noto Sans Math',
    value: 'https://fonts.gstatic.com/s/notosansmath/v15/7Aump_cpkSecTWaHRlH2hyV5UHkG-V048PW0.ttf'
  },
  {
    label: 'Noto Sans Mayan Numerals',
    value:
      'https://fonts.gstatic.com/s/notosansmayannumerals/v16/PlIuFk25O6RzLfvNNVSivR09_KqYMwvvDKYjfIiE68oo6eepYQ.ttf'
  },
  {
    label: 'Noto Sans Medefaidrin',
    value:
      'https://fonts.gstatic.com/s/notosansmedefaidrin/v23/WwkzxOq6Dk-wranENynkfeVsNbRZtbOIdLb1exeM4ZeuabBfmErWlT318e5A3rw.ttf'
  },
  {
    label: 'Noto Sans Meetei Mayek',
    value:
      'https://fonts.gstatic.com/s/notosansmeeteimayek/v15/HTxAL3QyKieByqY9eZPFweO0be7M21uSphSdhqILnmrRfJ8t_1TJ_vTW5PgeFYVa.ttf'
  },
  {
    label: 'Noto Sans Mende Kikakui',
    value: 'https://fonts.gstatic.com/s/notosansmendekikakui/v28/11hRGoLHz17aKjQCWj-JHcLvu2Q5zZrnkbNCLUx_aDJLAHer.ttf'
  },
  {
    label: 'Noto Sans Meroitic',
    value: 'https://fonts.gstatic.com/s/notosansmeroitic/v18/IFS5HfRJndhE3P4b5jnZ3ITPvC6i00UDgDhTiKY9KQ.ttf'
  },
  {
    label: 'Noto Sans Miao',
    value: 'https://fonts.gstatic.com/s/notosansmiao/v17/Dxxz8jmXMW75w3OmoDXVV4zyZUjgUYVslLhx.ttf'
  },
  {
    label: 'Noto Sans Modi',
    value: 'https://fonts.gstatic.com/s/notosansmodi/v23/pe03MIySN5pO62Z5YkFyT7jeav5qWVAgVol-.ttf'
  },
  {
    label: 'Noto Sans Mongolian',
    value: 'https://fonts.gstatic.com/s/notosansmongolian/v21/VdGCAYADGIwE0EopZx8xQfHlgEAMsrToxLsg6-av1x0.ttf'
  },
  {
    label: 'Noto Sans Mono',
    value:
      'https://fonts.gstatic.com/s/notosansmono/v30/BngrUXNETWXI6LwhGYvaxZikqZqK6fBq6kPvUce2oAZcdthSBUsYck4-_FNJ49rXVEQQL8Y.ttf'
  },
  {
    label: 'Noto Sans Mro',
    value: 'https://fonts.gstatic.com/s/notosansmro/v18/qWcsB6--pZv9TqnUQMhe9b39WDzRtjkho4M.ttf'
  },
  {
    label: 'Noto Sans Multani',
    value: 'https://fonts.gstatic.com/s/notosansmultani/v20/9Bty3ClF38_RfOpe1gCaZ8p30BOFO1A0pfCs5Kos.ttf'
  },
  {
    label: 'Noto Sans Myanmar',
    value: 'https://fonts.gstatic.com/s/notosansmyanmar/v20/AlZq_y1ZtY3ymOryg38hOCSdOnFq0En23OU4o1AC.ttf'
  },
  {
    label: 'Noto Sans NKo',
    value: 'https://fonts.gstatic.com/s/notosansnko/v6/esDX31ZdNv-KYGGJpKGk2_RpMpCMHMLBrdA.ttf'
  },
  {
    label: 'Noto Sans NKo Unjoined',
    value:
      'https://fonts.gstatic.com/s/notosansnkounjoined/v2/MCo_zBjx1d3VUhJFK9MYlNCXJ6VvqwGPz3szPOBB5nuzMdWs0rvF2gEPxf5wbh3T.ttf'
  },
  {
    label: 'Noto Sans Nabataean',
    value: 'https://fonts.gstatic.com/s/notosansnabataean/v16/IFS4HfVJndhE3P4b5jnZ34DfsjO330dNoBJ9hK8kMK4.ttf'
  },
  {
    label: 'Noto Sans Nag Mundari',
    value:
      'https://fonts.gstatic.com/s/notosansnagmundari/v1/3qTAoi2hnSyU8TNFIdhZTyod3g5lBnKlQFk2kS9fr9Eq09RHRxirbUGA0whP19M.ttf'
  },
  {
    label: 'Noto Sans Nandinagari',
    value: 'https://fonts.gstatic.com/s/notosansnandinagari/v4/or38Q7733eiDljA1IufXSNFT-1KI5y10H4jVa5RXzC1KOw.ttf'
  },
  {
    label: 'Noto Sans New Tai Lue',
    value:
      'https://fonts.gstatic.com/s/notosansnewtailue/v22/H4cKBW-Pl9DZ0Xe_nHUapt7PovLXAhAnY7wqaLy-OJgU3p_pdeXAYUbghFPKzeY.ttf'
  },
  {
    label: 'Noto Sans Newa',
    value: 'https://fonts.gstatic.com/s/notosansnewa/v16/7r3fqXp6utEsO9pI4f8ok8sWg8n_qN4R5lNU.ttf'
  },
  {
    label: 'Noto Sans Nushu',
    value: 'https://fonts.gstatic.com/s/notosansnushu/v19/rnCw-xRQ3B7652emAbAe_Ai1IYaFWFAMArZKqQ.ttf'
  },
  {
    label: 'Noto Sans Ogham',
    value: 'https://fonts.gstatic.com/s/notosansogham/v17/kmKlZqk1GBDGN0mY6k5lmEmww4hrt5laQxcoCA.ttf'
  },
  {
    label: 'Noto Sans Ol Chiki',
    value:
      'https://fonts.gstatic.com/s/notosansolchiki/v29/N0b92TJNOPt-eHmFZCdQbrL32r-4CvhzDzRwlxOQYuVALWk267I6gVrz5gQ.ttf'
  },
  {
    label: 'Noto Sans Old Hungarian',
    value: 'https://fonts.gstatic.com/s/notosansoldhungarian/v18/E213_cD6hP3GwCJPEUssHEM0KqLaHJXg2PiIgRfjbg5nCYXt.ttf'
  },
  {
    label: 'Noto Sans Old Italic',
    value: 'https://fonts.gstatic.com/s/notosansolditalic/v16/TuGOUUFzXI5FBtUq5a8bh68BJxxEVam7tWlRdRhtCC4d.ttf'
  },
  {
    label: 'Noto Sans Old North Arabian',
    value:
      'https://fonts.gstatic.com/s/notosansoldnortharabian/v16/esDF30BdNv-KYGGJpKGk2tNiMt7Jar6olZDyNdr81zBQmUo_xw4ABw.ttf'
  },
  {
    label: 'Noto Sans Old Permic',
    value: 'https://fonts.gstatic.com/s/notosansoldpermic/v17/snf1s1q1-dF8pli1TesqcbUY4Mr-ElrwKLdXgv_dKYB5.ttf'
  },
  {
    label: 'Noto Sans Old Persian',
    value: 'https://fonts.gstatic.com/s/notosansoldpersian/v16/wEOjEAbNnc5caQTFG18FHrZr9Bp6-8CmIJ_tqOlQfx9CjA.ttf'
  },
  {
    label: 'Noto Sans Old Sogdian',
    value: 'https://fonts.gstatic.com/s/notosansoldsogdian/v16/3JnjSCH90Gmq2mrzckOBBhFhdrMst48aURt7neIqM-9uyg.ttf'
  },
  {
    label: 'Noto Sans Old South Arabian',
    value:
      'https://fonts.gstatic.com/s/notosansoldsoutharabian/v16/3qT5oiOhnSyU8TNFIdhZTice3hB_HWKsEnF--0XCHiKx1OtDT9HwTA.ttf'
  },
  {
    label: 'Noto Sans Old Turkic',
    value: 'https://fonts.gstatic.com/s/notosansoldturkic/v17/yMJNMJVya43H0SUF_WmcGEQVqoEMKDKbsE2RjEw-Vyws.ttf'
  },
  {
    label: 'Noto Sans Oriya',
    value:
      'https://fonts.gstatic.com/s/notosansoriya/v31/AYCppXfzfccDCstK_hrjDyADv5e9748vhj3CJBLHIARtgD6TJQS0dJT5Ivj0f6_c6LhHBRe-.ttf'
  },
  {
    label: 'Noto Sans Osage',
    value: 'https://fonts.gstatic.com/s/notosansosage/v18/oPWX_kB6kP4jCuhpgEGmw4mtAVtXRlaSxkrMCQ.ttf'
  },
  {
    label: 'Noto Sans Osmanya',
    value: 'https://fonts.gstatic.com/s/notosansosmanya/v18/8vIS7xs32H97qzQKnzfeWzUyUpOJmz6kR47NCV5Z.ttf'
  },
  {
    label: 'Noto Sans Pahawh Hmong',
    value: 'https://fonts.gstatic.com/s/notosanspahawhhmong/v18/bWtp7e_KfBziStx7lIzKKaMUOBEA3UPQDW7krzc_c48aMpM.ttf'
  },
  {
    label: 'Noto Sans Palmyrene',
    value: 'https://fonts.gstatic.com/s/notosanspalmyrene/v16/ZgNPjOdKPa7CHqq0h37c_ASCWvH93SFCPnK5ZpdNtcA.ttf'
  },
  {
    label: 'Noto Sans Pau Cin Hau',
    value: 'https://fonts.gstatic.com/s/notosanspaucinhau/v20/x3d-cl3IZKmUqiMg_9wBLLtzl22EayN7ehIdjEWqKMxsKw.ttf'
  },
  {
    label: 'Noto Sans Phags Pa',
    value: 'https://fonts.gstatic.com/s/notosansphagspa/v15/pxiZyoo6v8ZYyWh5WuPeJzMkd4SrGChkqkSsrvNXiA.ttf'
  },
  {
    label: 'Noto Sans Phoenician',
    value: 'https://fonts.gstatic.com/s/notosansphoenician/v17/jizFRF9Ksm4Bt9PvcTaEkIHiTVtxmFtS5X7Jot-p5561.ttf'
  },
  {
    label: 'Noto Sans Psalter Pahlavi',
    value:
      'https://fonts.gstatic.com/s/notosanspsalterpahlavi/v16/rP2Vp3K65FkAtHfwd-eISGznYihzggmsicPfud3w1G3KsUQBct4.ttf'
  },
  {
    label: 'Noto Sans Rejang',
    value: 'https://fonts.gstatic.com/s/notosansrejang/v21/Ktk2AKuMeZjqPnXgyqrib7DIogqwN4O3WYZB_sU.ttf'
  },
  {
    label: 'Noto Sans Runic',
    value: 'https://fonts.gstatic.com/s/notosansrunic/v17/H4c_BXWPl9DZ0Xe_nHUaus7W68WWaxpvHtgIYg.ttf'
  },
  {
    label: 'Noto Sans SC',
    value: 'https://fonts.gstatic.com/s/notosanssc/v36/k3kCo84MPvpLmixcA63oeAL7Iqp5IZJF9bmaG9_FnYxNbPzS5HE.ttf'
  },
  {
    label: 'Noto Sans Samaritan',
    value: 'https://fonts.gstatic.com/s/notosanssamaritan/v16/buEqppe9f8_vkXadMBJJo0tSmaYjFkxOUo5jNlOVMzQ.ttf'
  },
  {
    label: 'Noto Sans Saurashtra',
    value: 'https://fonts.gstatic.com/s/notosanssaurashtra/v23/ea8GacQ0Wfz_XKWXe6OtoA8w8zvmYwTef9ndjhPTSIx9.ttf'
  },
  {
    label: 'Noto Sans Sharada',
    value: 'https://fonts.gstatic.com/s/notosanssharada/v16/gok0H7rwAEdtF9N8-mdTGALG6p0kwoXLPOwr4H8a.ttf'
  },
  {
    label: 'Noto Sans Shavian',
    value: 'https://fonts.gstatic.com/s/notosansshavian/v17/CHy5V_HZE0jxJBQlqAeCKjJvQBNF4EFQSplv2Cwg.ttf'
  },
  {
    label: 'Noto Sans Siddham',
    value: 'https://fonts.gstatic.com/s/notosanssiddham/v20/OZpZg-FwqiNLe9PELUikxTWDoCCeGqndk3Ic92ZH.ttf'
  },
  {
    label: 'Noto Sans SignWriting',
    value: 'https://fonts.gstatic.com/s/notosanssignwriting/v4/Noas6VX_wIWFbTTCrYmvy9A2UnkL-2SZAWiUEVCARYQemg.ttf'
  },
  {
    label: 'Noto Sans Sinhala',
    value:
      'https://fonts.gstatic.com/s/notosanssinhala/v26/yMJ2MJBya43H0SUF_WmcBEEf4rQVO2P524V5N_MxQzQtb-tf5dJbC30Fu9zUwg2a5lgLpJwbQRM.ttf'
  },
  {
    label: 'Noto Sans Sogdian',
    value: 'https://fonts.gstatic.com/s/notosanssogdian/v16/taiQGn5iC4--qtsfi4Jp6eHPnfxQBo--Pm6KHidM.ttf'
  },
  {
    label: 'Noto Sans Sora Sompeng',
    value:
      'https://fonts.gstatic.com/s/notosanssorasompeng/v24/PlIRFkO5O6RzLfvNNVSioxM2_OTrEhPyDLolKvCsHzCxWuGkYHR818DpZXJQd4Mu.ttf'
  },
  {
    label: 'Noto Sans Soyombo',
    value: 'https://fonts.gstatic.com/s/notosanssoyombo/v17/RWmSoL-Y6-8q5LTtXs6MF6q7xsxgY0FrIFOcK25W.ttf'
  },
  {
    label: 'Noto Sans Sundanese',
    value:
      'https://fonts.gstatic.com/s/notosanssundanese/v26/FwZw7_84xUkosG2xJo2gm7nFwSLQkdymq2mkz3Gz1_b6ctxpNNHCizv7fQES.ttf'
  },
  {
    label: 'Noto Sans Syloti Nagri',
    value: 'https://fonts.gstatic.com/s/notosanssylotinagri/v20/uU9eCAQZ75uhfF9UoWDRiY3q7Sf_VFV3m4dGFVfxN87gsj0.ttf'
  },
  {
    label: 'Noto Sans Symbols',
    value:
      'https://fonts.gstatic.com/s/notosanssymbols/v43/rP2up3q65FkAtHfwd-eIS2brbDN6gxP34F9jRRCe4W3gfQ8gavVFRkzrbQ.ttf'
  },
  {
    label: 'Noto Sans Symbols 2',
    value: 'https://fonts.gstatic.com/s/notosanssymbols2/v23/I_uyMoGduATTei9eI8daxVHDyfisHr71ypPqfX71-AI.ttf'
  },
  {
    label: 'Noto Sans Syriac',
    value: 'https://fonts.gstatic.com/s/notosanssyriac/v16/Ktk7AKuMeZjqPnXgyqribqzQqgW0LYiVqV7dXcP0C-VD9MaJyZfUL_FC.ttf'
  },
  {
    label: 'Noto Sans Syriac Eastern',
    value:
      'https://fonts.gstatic.com/s/notosanssyriaceastern/v1/Noac6Vj_wIWFbTTCrYmvy8AjVU8aslWRHHvRYxS-Ro3yS0FDacnHPi-fszCL5ep1QPQ.ttf'
  },
  {
    label: 'Noto Sans TC',
    value: 'https://fonts.gstatic.com/s/notosanstc/v35/-nFuOG829Oofr2wohFbTp9ifNAn722rq0MXz76Cy_CpOtma3uNQ.ttf'
  },
  {
    label: 'Noto Sans Tagalog',
    value: 'https://fonts.gstatic.com/s/notosanstagalog/v22/J7aFnoNzCnFcV9ZI-sUYuvote1R0wwEAA8jHexnL.ttf'
  },
  {
    label: 'Noto Sans Tagbanwa',
    value: 'https://fonts.gstatic.com/s/notosanstagbanwa/v18/Y4GWYbB8VTEp4t3MKJSMmQdIKjRtt_nZRjQEaYpGoQ.ttf'
  },
  {
    label: 'Noto Sans Tai Le',
    value: 'https://fonts.gstatic.com/s/notosanstaile/v17/vEFK2-VODB8RrNDvZSUmVxEATwR58tK1W77HtMo.ttf'
  },
  {
    label: 'Noto Sans Tai Tham',
    value:
      'https://fonts.gstatic.com/s/notosanstaitham/v20/kJEbBv0U4hgtwxDUw2x9q7tbjLIfbPGHBoaVSAZ3MdLJBCUbPgquyaRGKMw.ttf'
  },
  {
    label: 'Noto Sans Tai Viet',
    value: 'https://fonts.gstatic.com/s/notosanstaiviet/v19/8QIUdj3HhN_lv4jf9vsE-9GMOLsaSPZr644fWsRO9w.ttf'
  },
  {
    label: 'Noto Sans Takri',
    value: 'https://fonts.gstatic.com/s/notosanstakri/v24/TuGJUVpzXI5FBtUq5a8bnKIOdTwQNO_W3khJXg.ttf'
  },
  {
    label: 'Noto Sans Tamil',
    value:
      'https://fonts.gstatic.com/s/notosanstamil/v27/ieVc2YdFI3GCY6SyQy1KfStzYKZgzN1z4LKDbeZce-0429tBManUktuex7vGo70RqKDt_EvT.ttf'
  },
  {
    label: 'Noto Sans Tamil Supplement',
    value:
      'https://fonts.gstatic.com/s/notosanstamilsupplement/v21/DdTz78kEtnooLS5rXF1DaruiCd_bFp_Ph4sGcn7ax_vsAeMkeq1x.ttf'
  },
  {
    label: 'Noto Sans Tangsa',
    value: 'https://fonts.gstatic.com/s/notosanstangsa/v6/z7NCdQPmcigbbZAIOl9igP26K470lICpky0-peX5Qp1YkyoRYdplyJDA.ttf'
  },
  {
    label: 'Noto Sans Telugu',
    value:
      'https://fonts.gstatic.com/s/notosanstelugu/v26/0FlxVOGZlE2Rrtr-HmgkMWJNjJ5_RyT8o8c7fHkeg-esVC5dzHkHIJQqrEntezbqQUbf-3v37w.ttf'
  },
  {
    label: 'Noto Sans Thaana',
    value: 'https://fonts.gstatic.com/s/notosansthaana/v24/C8c14dM-vnz-s-3jaEsxlxHkBH-WZOETXfoQrfQ9Y4XrbhLhnu4-tbNu.ttf'
  },
  {
    label: 'Noto Sans Thai',
    value:
      'https://fonts.gstatic.com/s/notosansthai/v25/iJWnBXeUZi_OHPqn4wq6hQ2_hbJ1xyN9wd43SofNWcd1MKVQt_So_9CdU5RtpzF-QRvzzXg.ttf'
  },
  {
    label: 'Noto Sans Thai Looped',
    value: 'https://fonts.gstatic.com/s/notosansthailooped/v14/B50RF6pOpWTRcGrhOVJJ3-oPfY7WQuFu5R3gO6ocWiHvWQ.ttf'
  },
  {
    label: 'Noto Sans Tifinagh',
    value: 'https://fonts.gstatic.com/s/notosanstifinagh/v20/I_uzMoCduATTei9eI8dawkHIwvmhCvbn6rnEcXfs4Q.ttf'
  },
  {
    label: 'Noto Sans Tirhuta',
    value: 'https://fonts.gstatic.com/s/notosanstirhuta/v16/t5t6IQYRNJ6TWjahPR6X-M-apUyby7uGUBsTrn5P.ttf'
  },
  {
    label: 'Noto Sans Ugaritic',
    value: 'https://fonts.gstatic.com/s/notosansugaritic/v16/3qTwoiqhnSyU8TNFIdhZVCwbjCpkAXXkMhoIkiazfg.ttf'
  },
  {
    label: 'Noto Sans Vai',
    value: 'https://fonts.gstatic.com/s/notosansvai/v17/NaPecZTSBuhTirw6IaFn_UrURMTsDIRSfr0.ttf'
  },
  {
    label: 'Noto Sans Vithkuqi',
    value:
      'https://fonts.gstatic.com/s/notosansvithkuqi/v1/jVyi7m77CXvQswd6WjYu9E1wN6cih2TSchUEkQgw3KTnva5SgKM7vmn0BLE.ttf'
  },
  {
    label: 'Noto Sans Wancho',
    value: 'https://fonts.gstatic.com/s/notosanswancho/v17/zrf-0GXXyfn6Fs0lH9P4cUubP0GBqAPopiRfKp8.ttf'
  },
  {
    label: 'Noto Sans Warang Citi',
    value: 'https://fonts.gstatic.com/s/notosanswarangciti/v17/EYqtmb9SzL1YtsZSScyKDXIeOv3w-zgsNvKRpeVCCXzdgA.ttf'
  },
  {
    label: 'Noto Sans Yi',
    value: 'https://fonts.gstatic.com/s/notosansyi/v19/sJoD3LFXjsSdcnzn071rO3apxVDJNVgSNg.ttf'
  },
  {
    label: 'Noto Sans Zanabazar Square',
    value:
      'https://fonts.gstatic.com/s/notosanszanabazarsquare/v19/Cn-jJsuGWQxOjaGwMQ6fOicyxLBEMRfDtkzl4uagQtJxOCEgN0Gc.ttf'
  },
  {
    label: 'Noto Serif',
    value:
      'https://fonts.gstatic.com/s/notoserif/v23/ga6iaw1J5X9T9RW6j9bNVls-hfgvz8JcMofYTa32J4wsL2JAlAhZqFCjwM0Lhq_Szw.ttf'
  },
  {
    label: 'Noto Serif Ahom',
    value: 'https://fonts.gstatic.com/s/notoserifahom/v20/FeVIS0hfp6cprmEUffAW_fUL_AN-wuYrPFiwaw.ttf'
  },
  {
    label: 'Noto Serif Armenian',
    value:
      'https://fonts.gstatic.com/s/notoserifarmenian/v27/3XFMEqMt3YoFsciDRZxptyCUKJmytZ0kVU-XvF7QaZuL85rnQ_zDNzDe5xNnKxyZi8KbxvXagGdkbg.ttf'
  },
  {
    label: 'Noto Serif Balinese',
    value: 'https://fonts.gstatic.com/s/notoserifbalinese/v18/QdVKSS0-JginysQSRvuCmUMB_wVeQAxXRbgJdhapcUU.ttf'
  },
  {
    label: 'Noto Serif Bengali',
    value:
      'https://fonts.gstatic.com/s/notoserifbengali/v19/hYkuPvggTvnzO14VSXltirUdnnkt1pwmWrprmO7RjE0a5BtdATYU1crFaM_5JfcAHnqn4LjQH8yD.ttf'
  },
  {
    label: 'Noto Serif Devanagari',
    value:
      'https://fonts.gstatic.com/s/notoserifdevanagari/v30/x3dYcl3IZKmUqiMk48ZHXJ5jwU-DZGRSaQ4Hh2dGyFzPLcQPVbnRNeFsw0xRWb6uxTA-ow-HMUe1u_dv.ttf'
  },
  {
    label: 'Noto Serif Display',
    value:
      'https://fonts.gstatic.com/s/notoserifdisplay/v24/buERppa9f8_vkXaZLAgP0G5Wi6QmA1QaeYah2sovLCDq_ZgLyt3idQfktOG-PVpd4tgKaDU9hvzC.ttf'
  },
  {
    label: 'Noto Serif Dogra',
    value: 'https://fonts.gstatic.com/s/notoserifdogra/v23/MQpP-XquKMC7ROPP3QOOlm7xPu3fGy63IbPzkns.ttf'
  },
  {
    label: 'Noto Serif Ethiopic',
    value:
      'https://fonts.gstatic.com/s/notoserifethiopic/v30/V8mjoR7-XjwJ8_Au3Ti5tXj5Rd83frpWLK4d-taxqWw2HMWjDxBAg5S_0QsrggxCzSUjkaO9UVLyiw.ttf'
  },
  {
    label: 'Noto Serif Georgian',
    value:
      'https://fonts.gstatic.com/s/notoserifgeorgian/v26/VEMXRpd8s4nv8hG_qOzL7HOAw4nt0Sl_XxyaEduNMvi7T6Y4etRnmGhyLop-R3aSTvofdzTw-FgZxQ.ttf'
  },
  {
    label: 'Noto Serif Grantha',
    value: 'https://fonts.gstatic.com/s/notoserifgrantha/v21/qkBIXuEH5NzDDvc3fLDYxPk9-Wq3WLiqFENLR7fHGw.ttf'
  },
  {
    label: 'Noto Serif Gujarati',
    value:
      'https://fonts.gstatic.com/s/notoserifgujarati/v27/hESa6WBlOixO-3OJ1FTmTsmqlBRUJBVkcgNLpdsspzP2HuYycIzuM1Kf-OJu.ttf'
  },
  {
    label: 'Noto Serif Gurmukhi',
    value:
      'https://fonts.gstatic.com/s/notoserifgurmukhi/v20/92z-tA9LNqsg7tCYlXdCV1VPnAEeDU0vLoYMbylXk0xTCr6-eRTNmqVU7y6l.ttf'
  },
  {
    label: 'Noto Serif HK',
    value: 'https://fonts.gstatic.com/s/notoserifhk/v7/BngdUXBETWXI6LwlBZGcqL-B_KuJFcgfwP_9RMd-K2RmV9Su1M6i.ttf'
  },
  {
    label: 'Noto Serif Hebrew',
    value:
      'https://fonts.gstatic.com/s/notoserifhebrew/v28/k3k0o9MMPvpLmixYH7euCwmkS9DohjX1-kRyiqyBqIxnoLbp93i9IKrXKF_qVAwSAG8_vlQxz24.ttf'
  },
  {
    label: 'Noto Serif JP',
    value: 'https://fonts.gstatic.com/s/notoserifjp/v28/xn71YHs72GKoTvER4Gn3b5eMRtWGkp6o7MjQ2bwxOubAILO5wBCU.ttf'
  },
  {
    label: 'Noto Serif KR',
    value: 'https://fonts.gstatic.com/s/notoserifkr/v27/3JnoSDn90Gmq2mr3blnHaTZXbOtLJDvui3JOncjmeM524ZvTePRu.ttf'
  },
  {
    label: 'Noto Serif Kannada',
    value:
      'https://fonts.gstatic.com/s/notoserifkannada/v27/v6-8GZHLJFKIhClqUYqXDiWqpxQxWSPoW6bz-l4hGHiNgcYDceRJ71svgcI.ttf'
  },
  {
    label: 'Noto Serif Khitan Small Script',
    value:
      'https://fonts.gstatic.com/s/notoserifkhitansmallscript/v4/jizzRFVKsm4Bt9PrbSzC4KLlQUF5lRJg5j-l5PvyhfTdd4TsZ8lb39iddA.ttf'
  },
  {
    label: 'Noto Serif Khmer',
    value:
      'https://fonts.gstatic.com/s/notoserifkhmer/v25/-F6UfidqLzI2JPCkXAO2hmogq0146FxtbwKEr951z5s6lI40sDRH_AVhUKdN6B8wXEZK9Xo4xg.ttf'
  },
  {
    label: 'Noto Serif Khojki',
    value:
      'https://fonts.gstatic.com/s/notoserifkhojki/v11/I_uHMoOduATTei9aP90ctmPGxP2rBKTM4mcQ5M3z9QMY0ghvyZ0Qtc5HAQ.ttf'
  },
  {
    label: 'Noto Serif Lao',
    value:
      'https://fonts.gstatic.com/s/notoseriflao/v24/3y9C6bYwcCjmsU8JEzCMxEwQfEBLk3f0rlSqCdaM_LlSNZ59oNw0BWH8VeMKrvOjlmyhHHQ.ttf'
  },
  {
    label: 'Noto Serif Makasar',
    value: 'https://fonts.gstatic.com/s/notoserifmakasar/v1/memjYbqtyH-NiZpFH_9zcvB_PqkfY9S7j4HTVSmevw.ttf'
  },
  {
    label: 'Noto Serif Malayalam',
    value:
      'https://fonts.gstatic.com/s/notoserifmalayalam/v28/JIAZUU5sdmdP_HMcVcZFcH7DeVBeGVgSMEk2cmVDq1ihUXL1t-xfnVwHpQVySg.ttf'
  },
  {
    label: 'Noto Serif Myanmar',
    value: 'https://fonts.gstatic.com/s/notoserifmyanmar/v13/VuJsdM7F2Yv76aBKKs-bHMQfAHUw3jn1pBrocdDqRA.ttf'
  },
  {
    label: 'Noto Serif NP Hmong',
    value:
      'https://fonts.gstatic.com/s/notoserifnphmong/v1/pONN1gItFMO79E4L1GPUi-2sixKHZyFj9Jy6_KhXPwzdvbjPhFLp3u0rVO-d.ttf'
  },
  {
    label: 'Noto Serif Old Uyghur',
    value: 'https://fonts.gstatic.com/s/notoserifolduyghur/v3/v6-KGZbLJFKIhClqUYqXDiGnrVoFRCW6JdwnKumeF2yVgA.ttf'
  },
  {
    label: 'Noto Serif Oriya',
    value: 'https://fonts.gstatic.com/s/notoseriforiya/v4/MjQQmj56u-r69izk_LDqWN7w0cYByutv9qeWYrvLaxrc_Hy-v039MF1j.ttf'
  },
  {
    label: 'Noto Serif Ottoman Siyaq',
    value: 'https://fonts.gstatic.com/s/notoserifottomansiyaq/v2/fC1yPZ9IYnzRhTrrc4s8cSvYI0eozzaFOQ01qoHLJrgA00kAdA.ttf'
  },
  {
    label: 'Noto Serif SC',
    value: 'https://fonts.gstatic.com/s/notoserifsc/v30/H4cyBXePl9DZ0Xe7gG9cyOj7uK2-n-D2rd4FY7SCqyWv847hdDWC.ttf'
  },
  {
    label: 'Noto Serif Sinhala',
    value:
      'https://fonts.gstatic.com/s/notoserifsinhala/v26/DtVEJwinQqclnZE2CnsPug9lgGC3y2F2nehQ7Eg4EdBKWxPiDxMivFLgRXs_-pGxR1MsxaLRn3W-.ttf'
  },
  {
    label: 'Noto Serif TC',
    value: 'https://fonts.gstatic.com/s/notoseriftc/v30/XLYzIZb5bJNDGYxLBibeHZ0BnHwmuanx8cUaGX9aMOpDOWYMr2OM.ttf'
  },
  {
    label: 'Noto Serif Tamil',
    value:
      'https://fonts.gstatic.com/s/notoseriftamil/v28/LYjndHr-klIgTfc40komjQ5OObazYp-6H94dBF-RX6nNRJfi-Gf55IgAecattN-R8Pz3v8Etew.ttf'
  },
  {
    label: 'Noto Serif Tangut',
    value: 'https://fonts.gstatic.com/s/notoseriftangut/v16/xn76YGc72GKoTvER4Gn3b4m9Ern7Em41fcvN2KT4.ttf'
  },
  {
    label: 'Noto Serif Telugu',
    value:
      'https://fonts.gstatic.com/s/notoseriftelugu/v26/tDbl2pCbnkEKmXNVmt2M1q6f4HWbbj6MRbYEeav7Fe9D9TCwuY2fjgrZYA.ttf'
  },
  {
    label: 'Noto Serif Thai',
    value:
      'https://fonts.gstatic.com/s/notoserifthai/v24/k3kyo80MPvpLmixYH7euCxWpSMu3-gcWGj0hHAKGvUQlUv_bCKDUSzB5L0oiF-RRCmsdu0Qx.ttf'
  },
  {
    label: 'Noto Serif Tibetan',
    value:
      'https://fonts.gstatic.com/s/notoseriftibetan/v22/gokGH7nwAEdtF9N45n0Vaz7O-pk0wsvxHeDXMfqguoCmIrYcPS7rdSy_32c.ttf'
  },
  {
    label: 'Noto Serif Toto',
    value: 'https://fonts.gstatic.com/s/notoseriftoto/v5/Ktk6ALSMeZjqPnXk1rCkHYHNtwvtHItpjRP74dHhCy3Il-aj55vdNug.ttf'
  },
  {
    label: 'Noto Serif Vithkuqi',
    value:
      'https://fonts.gstatic.com/s/notoserifvithkuqi/v1/YA94r1OY7FjTf5szakutkndpw9HH-4a4z9pklvg1IQSNcRWMdW2Cqy9A4teH.ttf'
  },
  {
    label: 'Noto Serif Yezidi',
    value:
      'https://fonts.gstatic.com/s/notoserifyezidi/v21/XLYPIYr5bJNDGYxLBibeHZAn3B5KJENnQjbfhMSVZspD2yEkrlGJgmVCqg.ttf'
  },
  {
    label: 'Noto Traditional Nushu',
    value:
      'https://fonts.gstatic.com/s/nototraditionalnushu/v17/SZcV3EDkJ7q9FaoMPlmF4Su8hlIjoGh5aj67PUZX6ADm6oa8IXus1tnPa7QoqirI.ttf'
  },
  {
    label: 'Noto Znamenny Musical Notation',
    value:
      'https://fonts.gstatic.com/s/notoznamennymusicalnotation/v3/CSRW4ylQnPyaDwAMK1U_AolTaJ4Lz41GcgaIZV9YO2rO88jvtpqqdoWa7g.ttf'
  },
  {
    label: 'Nova Cut',
    value: 'https://fonts.gstatic.com/s/novacut/v24/KFOkCnSYu8mL-39LkWxPKTM1K9nz.ttf'
  },
  {
    label: 'Nova Flat',
    value: 'https://fonts.gstatic.com/s/novaflat/v24/QdVUSTc-JgqpytEbVebEuStkm20oJA.ttf'
  },
  {
    label: 'Nova Mono',
    value: 'https://fonts.gstatic.com/s/novamono/v20/Cn-0JtiGWQ5Ajb--MRKfYGxYrdM9Sg.ttf'
  },
  {
    label: 'Nova Oval',
    value: 'https://fonts.gstatic.com/s/novaoval/v24/jAnEgHdmANHvPenMaswCMY-h3cWkWg.ttf'
  },
  {
    label: 'Nova Round',
    value: 'https://fonts.gstatic.com/s/novaround/v21/flU9Rqquw5UhEnlwTJYTYYfeeetYEBc.ttf'
  },
  {
    label: 'Nova Script',
    value: 'https://fonts.gstatic.com/s/novascript/v25/7Au7p_IpkSWSTWaFWkumvmQNEl0O0kEx.ttf'
  },
  {
    label: 'Nova Slim',
    value: 'https://fonts.gstatic.com/s/novaslim/v24/Z9XUDmZNQAuem8jyZcn-yMOInrib9Q.ttf'
  },
  {
    label: 'Nova Square',
    value: 'https://fonts.gstatic.com/s/novasquare/v24/RrQUbo9-9DV7b06QHgSWsZhARYMgGtWA.ttf'
  },
  {
    label: 'Numans',
    value: 'https://fonts.gstatic.com/s/numans/v15/SlGRmQmGupYAfH8IYRggiHVqaQ.ttf'
  },
  {
    label: 'Nunito',
    value: 'https://fonts.gstatic.com/s/nunito/v26/XRXI3I6Li01BKofiOc5wtlZ2di8HDLshRTM9jo7eTWk.ttf'
  },
  {
    label: 'Nunito Sans',
    value:
      'https://fonts.gstatic.com/s/nunitosans/v15/pe1mMImSLYBIv1o4X1M8ce2xCx3yop4tQpF_MeTm0lfGWVpNn64CL7U8upHZIbMV51Q42ptCp5F5bxqqtQ1yiU4G1ilntF8kA_Ykqw.ttf'
  },
  {
    label: 'Nuosu SIL',
    value: 'https://fonts.gstatic.com/s/nuosusil/v10/8vIK7wM3wmRn_kc4uAjeFGxbO_zo-w.ttf'
  },
  {
    label: 'Odibee Sans',
    value: 'https://fonts.gstatic.com/s/odibeesans/v18/neIPzCSooYAho6WvjeToRYkyepH9qGsf.ttf'
  },
  {
    label: 'Odor Mean Chey',
    value: 'https://fonts.gstatic.com/s/odormeanchey/v27/raxkHiKDttkTe1aOGcJMR1A_4mrY2zqUKafv.ttf'
  },
  {
    label: 'Offside',
    value: 'https://fonts.gstatic.com/s/offside/v24/HI_KiYMWKa9QrAykQ5HiRp-dhpQ.ttf'
  },
  {
    label: 'Oi',
    value: 'https://fonts.gstatic.com/s/oi/v19/w8gXH2EuRqtaut6yjBOG.ttf'
  },
  {
    label: 'Ojuju',
    value: 'https://fonts.gstatic.com/s/ojuju/v3/7r3bqXF7v9ApbrMih3jYQBVm9-n_Spk552FRLYeruQ.ttf'
  },
  {
    label: 'Old Standard TT',
    value: 'https://fonts.gstatic.com/s/oldstandardtt/v20/MwQubh3o1vLImiwAVvYawgcf2eVurVC5RHdCZg.ttf'
  },
  {
    label: 'Oldenburg',
    value: 'https://fonts.gstatic.com/s/oldenburg/v22/fC1jPY5JYWzbywv7c4V6UU6oXyndrw.ttf'
  },
  {
    label: 'Ole',
    value: 'https://fonts.gstatic.com/s/ole/v3/dFazZf6Z-rd89fw69qJ_ew.ttf'
  },
  {
    label: 'Oleo Script',
    value: 'https://fonts.gstatic.com/s/oleoscript/v14/rax5HieDvtMOe0iICsUccBhasU7Q8Cad.ttf'
  },
  {
    label: 'Oleo Script Swash Caps',
    value: 'https://fonts.gstatic.com/s/oleoscriptswashcaps/v13/Noaj6Vb-w5SFbTTAsZP_7JkCS08K-jCzDn_HMXquSY0Hg90.ttf'
  },
  {
    label: 'Onest',
    value: 'https://fonts.gstatic.com/s/onest/v6/gNMZW3F-SZuj7zOT0IfSjTS16cPh9R-ZshFMQWXgSQ.ttf'
  },
  {
    label: 'Oooh Baby',
    value: 'https://fonts.gstatic.com/s/ooohbaby/v4/2sDcZGJWgJTT2Jf76xQDb2-4C7wFZQ.ttf'
  },
  {
    label: 'Open Sans',
    value:
      'https://fonts.gstatic.com/s/opensans/v40/memSYaGs126MiZpBA-UvWbX2vVnXBbObj2OVZyOOSr4dVJWUgsjZ0C4nY1M2xLER.ttf'
  },
  {
    label: 'Oranienbaum',
    value: 'https://fonts.gstatic.com/s/oranienbaum/v15/OZpHg_txtzZKMuXLIVrx-3zn7kz3dpHc.ttf'
  },
  {
    label: 'Orbit',
    value: 'https://fonts.gstatic.com/s/orbit/v1/_LOCmz7I-uHd2mjEeqciRwRm.ttf'
  },
  {
    label: 'Orbitron',
    value: 'https://fonts.gstatic.com/s/orbitron/v31/yMJMMIlzdpvBhQQL_SC3X9yhF25-T1nyGy6xpmIyXjU1pg.ttf'
  },
  {
    label: 'Oregano',
    value: 'https://fonts.gstatic.com/s/oregano/v15/If2IXTPxciS3H4S2kZffPznO3yM.ttf'
  },
  {
    label: 'Orelega One',
    value: 'https://fonts.gstatic.com/s/orelegaone/v12/3qTpojOggD2XtAdFb-QXZGt61EcYaQ7F.ttf'
  },
  {
    label: 'Orienta',
    value: 'https://fonts.gstatic.com/s/orienta/v15/PlI9FlK4Jrl5Y9zNeyeo9HRFhcU.ttf'
  },
  {
    label: 'Original Surfer',
    value: 'https://fonts.gstatic.com/s/originalsurfer/v22/RWmQoKGZ9vIirYntXJ3_MbekzNMiDEtvAlaMKw.ttf'
  },
  {
    label: 'Oswald',
    value: 'https://fonts.gstatic.com/s/oswald/v53/TK3_WkUHHAIjg75cFRf3bXL8LICs1_FvgUFoZAaRliE.ttf'
  },
  {
    label: 'Outfit',
    value: 'https://fonts.gstatic.com/s/outfit/v11/QGYyz_MVcBeNP4NjuGObqx1XmO1I4TC1C4G-EiAou6Y.ttf'
  },
  {
    label: 'Over the Rainbow',
    value: 'https://fonts.gstatic.com/s/overtherainbow/v20/11haGoXG1k_HKhMLUWz7Mc7vvW5upvOm9NA2XG0.ttf'
  },
  {
    label: 'Overlock',
    value: 'https://fonts.gstatic.com/s/overlock/v17/Z9XVDmdMWRiN1_T9Z4Te4u2El6GC.ttf'
  },
  {
    label: 'Overlock SC',
    value: 'https://fonts.gstatic.com/s/overlocksc/v23/1cX3aUHKGZrstGAY8nwVzHGAq8Sk1PoH.ttf'
  },
  {
    label: 'Overpass',
    value: 'https://fonts.gstatic.com/s/overpass/v13/qFda35WCmI96Ajtm83upeyoaX6QPnlo6_PPrOZCLtce-og.ttf'
  },
  {
    label: 'Overpass Mono',
    value: 'https://fonts.gstatic.com/s/overpassmono/v16/_Xm5-H86tzKDdAPa-KPQZ-AC_COcRycquHlL6EXUokzzXur-SmIr.ttf'
  },
  {
    label: 'Ovo',
    value: 'https://fonts.gstatic.com/s/ovo/v17/yYLl0h7Wyfzjy4Q5_3WVxA.ttf'
  },
  {
    label: 'Oxanium',
    value: 'https://fonts.gstatic.com/s/oxanium/v19/RrQPboN_4yJ0JmiMUW7sIGjd1IA9G81JfniMBXQ7d67x.ttf'
  },
  {
    label: 'Oxygen',
    value: 'https://fonts.gstatic.com/s/oxygen/v15/2sDfZG1Wl4Lcnbu6iUcnZ0SkAg.ttf'
  },
  {
    label: 'Oxygen Mono',
    value: 'https://fonts.gstatic.com/s/oxygenmono/v14/h0GsssGg9FxgDgCjLeAd7ijfze-PPlUu.ttf'
  },
  {
    label: 'PT Mono',
    value: 'https://fonts.gstatic.com/s/ptmono/v13/9oRONYoBnWILk-9ArCg5MtPyAcg.ttf'
  },
  {
    label: 'PT Sans',
    value: 'https://fonts.gstatic.com/s/ptsans/v17/jizaRExUiTo99u79P0WOxOGMMDQ.ttf'
  },
  {
    label: 'PT Sans Caption',
    value: 'https://fonts.gstatic.com/s/ptsanscaption/v19/0FlMVP6Hrxmt7-fsUFhlFXNIlpcqfQXwQy6yxg.ttf'
  },
  {
    label: 'PT Sans Narrow',
    value: 'https://fonts.gstatic.com/s/ptsansnarrow/v18/BngRUXNadjH0qYEzV7ab-oWlsYCByxyKeuDp.ttf'
  },
  {
    label: 'PT Serif',
    value: 'https://fonts.gstatic.com/s/ptserif/v18/EJRVQgYoZZY2vCFuvDFRxL6ddjb-.ttf'
  },
  {
    label: 'PT Serif Caption',
    value: 'https://fonts.gstatic.com/s/ptserifcaption/v17/ieVl2ZhbGCW-JoW6S34pSDpqYKU059WxDCs5cvI.ttf'
  },
  {
    label: 'Pacifico',
    value: 'https://fonts.gstatic.com/s/pacifico/v22/FwZY7-Qmy14u9lezJ96A4sijpFu_.ttf'
  },
  {
    label: 'Padauk',
    value: 'https://fonts.gstatic.com/s/padauk/v16/RrQRboJg-id7OnbBa0_g3LlYbg.ttf'
  },
  {
    label: 'Padyakke Expanded One',
    value: 'https://fonts.gstatic.com/s/padyakkeexpandedone/v6/K2FvfY9El_tbR0JfHb6WWvrBaU6XAUvC4IAYOKRkpDjeoQ.ttf'
  },
  {
    label: 'Palanquin',
    value: 'https://fonts.gstatic.com/s/palanquin/v13/9XUnlJ90n1fBFg7ceXwsdlFMzLC2Zw.ttf'
  },
  {
    label: 'Palanquin Dark',
    value: 'https://fonts.gstatic.com/s/palanquindark/v14/xn75YHgl1nqmANMB-26xC7yuF_6OTEo9VtfE.ttf'
  },
  {
    label: 'Palette Mosaic',
    value: 'https://fonts.gstatic.com/s/palettemosaic/v10/AMOIz4aBvWuBFe3TohdW6YZ9MFiy4dxL4jSr.ttf'
  },
  {
    label: 'Pangolin',
    value: 'https://fonts.gstatic.com/s/pangolin/v11/cY9GfjGcW0FPpi-tWPfK5d3aiLBG.ttf'
  },
  {
    label: 'Paprika',
    value: 'https://fonts.gstatic.com/s/paprika/v21/8QIJdijZitv49rDfuIgOq7jkAOw.ttf'
  },
  {
    label: 'Parisienne',
    value: 'https://fonts.gstatic.com/s/parisienne/v13/E21i_d3kivvAkxhLEVZpcy96DuKuavM.ttf'
  },
  {
    label: 'Passero One',
    value: 'https://fonts.gstatic.com/s/passeroone/v26/JTUTjIko8DOq5FeaeEAjgE5B5Arr-s50.ttf'
  },
  {
    label: 'Passion One',
    value: 'https://fonts.gstatic.com/s/passionone/v18/PbynFmL8HhTPqbjUzux3JHuW_Frg6YoV.ttf'
  },
  {
    label: 'Passions Conflict',
    value: 'https://fonts.gstatic.com/s/passionsconflict/v7/kmKnZrcrFhfafnWX9x0GuEC-zowow5NeYRI4CN2V.ttf'
  },
  {
    label: 'Pathway Extreme',
    value:
      'https://fonts.gstatic.com/s/pathwayextreme/v3/neI6zCC3pJ0rsaH2_sD-QttXPfDPonvkQ-pxx5gufvP2VmLjiFyxGf8BLymNjYv2Oy6vkLmw4xak2N11Kyw3igP5eg.ttf'
  },
  {
    label: 'Pathway Gothic One',
    value: 'https://fonts.gstatic.com/s/pathwaygothicone/v15/MwQrbgD32-KAvjkYGNUUxAtW7pEBwx-dTFxeb80flQ.ttf'
  },
  {
    label: 'Patrick Hand',
    value: 'https://fonts.gstatic.com/s/patrickhand/v23/LDI1apSQOAYtSuYWp8ZhfYeMWcjKm7sp8g.ttf'
  },
  {
    label: 'Patrick Hand SC',
    value: 'https://fonts.gstatic.com/s/patrickhandsc/v15/0nkwC9f7MfsBiWcLtY65AWDK873ViSi6JQc7Vg.ttf'
  },
  {
    label: 'Pattaya',
    value: 'https://fonts.gstatic.com/s/pattaya/v16/ea8ZadcqV_zkHY-XNdCn92ZEmVs.ttf'
  },
  {
    label: 'Patua One',
    value: 'https://fonts.gstatic.com/s/patuaone/v20/ZXuke1cDvLCKLDcimxBI5PNvNA9LuA.ttf'
  },
  {
    label: 'Pavanam',
    value: 'https://fonts.gstatic.com/s/pavanam/v11/BXRrvF_aiezLh0xPDOtQ9Wf0QcE.ttf'
  },
  {
    label: 'Paytone One',
    value: 'https://fonts.gstatic.com/s/paytoneone/v23/0nksC9P7MfYHj2oFtYm2CiTqivr9iBq_.ttf'
  },
  {
    label: 'Peddana',
    value: 'https://fonts.gstatic.com/s/peddana/v20/aFTU7PBhaX89UcKWhh2aBYyMcKw.ttf'
  },
  {
    label: 'Peralta',
    value: 'https://fonts.gstatic.com/s/peralta/v19/hYkJPu0-RP_9d3kRGxAhrv956B8.ttf'
  },
  {
    label: 'Permanent Marker',
    value: 'https://fonts.gstatic.com/s/permanentmarker/v16/Fh4uPib9Iyv2ucM6pGQMWimMp004HaqIfrT5nlk.ttf'
  },
  {
    label: 'Petemoss',
    value: 'https://fonts.gstatic.com/s/petemoss/v7/A2BZn5tA2xgtGWHZgxkesKb9UouQ.ttf'
  },
  {
    label: 'Petit Formal Script',
    value: 'https://fonts.gstatic.com/s/petitformalscript/v17/B50TF6xQr2TXJBnGOFME6u5OR83oRP5qoHnqP4gZSiE.ttf'
  },
  {
    label: 'Petrona',
    value: 'https://fonts.gstatic.com/s/petrona/v32/mtGl4_NXL7bZo9XXq35wRLONYyOjFk6NsQRBH452Mvds.ttf'
  },
  {
    label: 'Philosopher',
    value: 'https://fonts.gstatic.com/s/philosopher/v19/vEFV2_5QCwIS4_Dhez5jcVBpRUwU08qe.ttf'
  },
  {
    label: 'Phudu',
    value: 'https://fonts.gstatic.com/s/phudu/v4/0FlJVPSHk0ya-7OUeO_U-Lwm7PkKtWzUSwWuz38Tgg.ttf'
  },
  {
    label: 'Piazzolla',
    value:
      'https://fonts.gstatic.com/s/piazzolla/v35/N0b52SlTPu5rIkWIZjVKKtYtfxYqZ4RJBFzFfYUjkSDdlqZgy7LYxnLy1AHfAAy5.ttf'
  },
  {
    label: 'Piedra',
    value: 'https://fonts.gstatic.com/s/piedra/v25/ke8kOg8aN0Bn7hTunEyHN_M3gA.ttf'
  },
  {
    label: 'Pinyon Script',
    value: 'https://fonts.gstatic.com/s/pinyonscript/v22/6xKpdSJbL9-e9LuoeQiDRQR8aOLQO4bhiDY.ttf'
  },
  {
    label: 'Pirata One',
    value: 'https://fonts.gstatic.com/s/pirataone/v22/I_urMpiDvgLdLh0fAtoftiiEr5_BdZ8.ttf'
  },
  {
    label: 'Pixelify Sans',
    value: 'https://fonts.gstatic.com/s/pixelifysans/v1/CHy2V-3HFUT7aC4iv1TxGDR9DHEserHN25py2TTp0H1Yb5JagkmX.ttf'
  },
  {
    label: 'Plaster',
    value: 'https://fonts.gstatic.com/s/plaster/v24/DdTm79QatW80eRh4Ei5JOtLOeLI.ttf'
  },
  {
    label: 'Platypi',
    value: 'https://fonts.gstatic.com/s/platypi/v4/bMromSGU7pMlaX6-PAmuwBQP4Hwe02Jz4p9juxgnYP1P.ttf'
  },
  {
    label: 'Play',
    value: 'https://fonts.gstatic.com/s/play/v19/6aez4K2oVqwIjtI8Hp8Tx3A.ttf'
  },
  {
    label: 'Playball',
    value: 'https://fonts.gstatic.com/s/playball/v20/TK3gWksYAxQ7jbsKcj8Dl-tPKo2t.ttf'
  },
  {
    label: 'Playfair',
    value:
      'https://fonts.gstatic.com/s/playfair/v2/0nkQC9D7PO4KhmUJ5_zTZ_4MYQXznAK-TUcZXKO3UMnW6VNpe4-SiiZ4b8h5G3GutPkUetgdoSMw5ifm.ttf'
  },
  {
    label: 'Playfair Display',
    value:
      'https://fonts.gstatic.com/s/playfairdisplay/v37/nuFvD-vYSZviVYUb_rj3ij__anPXJzDwcbmjWBN2PKdFvUDQZNLo_U2r.ttf'
  },
  {
    label: 'Playfair Display SC',
    value: 'https://fonts.gstatic.com/s/playfairdisplaysc/v17/ke85OhoaMkR6-hSn7kbHVoFf7ZfgMPr_pb4GEcM2M4s.ttf'
  },
  {
    label: 'Playpen Sans',
    value: 'https://fonts.gstatic.com/s/playpensans/v13/dg43_pj1p6gXP0gzAZgm4c8XQArSU7ACQSn4IvRhunQ9Ffmme0w.ttf'
  },
  {
    label: 'Playwrite AR',
    value: 'https://fonts.gstatic.com/s/playwritear/v1/VEMjRohisJz5pTCzruCNjWbfp_N-aNWqYgKS-ftfqf-ES67xIO8.ttf'
  },
  {
    label: 'Playwrite AT',
    value: 'https://fonts.gstatic.com/s/playwriteat/v1/Gw6owc7n6kfJN4fVoKON7HIEBRSfb0U2uGBm2M76d03MLIHJ-rk.ttf'
  },
  {
    label: 'Playwrite AU NSW',
    value: 'https://fonts.gstatic.com/s/playwriteaunsw/v4/6qLWKY4NtxD-qVlIPUIPenElWCCEWRgilpupBXi19xZjMb96TD2dC1gS.ttf'
  },
  {
    label: 'Playwrite AU QLD',
    value: 'https://fonts.gstatic.com/s/playwriteauqld/v4/SlGGmR-Yo5oYZX5BFVcEwSFSOXBRWADAWbgjmLBhA6-yMI2fqm1BV-XS.ttf'
  },
  {
    label: 'Playwrite AU SA',
    value: 'https://fonts.gstatic.com/s/playwriteausa/v4/YcmhsZpNS1SdgmHbGgtRuUElnR3CmSC5bVQVlrclpZgRcuVjCoV0bbE.ttf'
  },
  {
    label: 'Playwrite AU TAS',
    value: 'https://fonts.gstatic.com/s/playwriteautas/v4/GftT7u9QuxsdI_QuuctXue3ElxxmcBb3ih0opvWiLLUEH6K6Hl9WrXm4.ttf'
  },
  {
    label: 'Playwrite AU VIC',
    value: 'https://fonts.gstatic.com/s/playwriteauvic/v4/bWtu7enUfwn0Hf1zjprKOJdcDy8rxwC1ltAeNDAAd4fTabK0CBIDKNvl.ttf'
  },
  {
    label: 'Playwrite BE VLG',
    value: 'https://fonts.gstatic.com/s/playwritebevlg/v1/GFD8WBdug3mQSvrAT9AL6fd4ZkB-a2sDmg3dy2W0blL8vPCMMiyBIkNg.ttf'
  },
  {
    label: 'Playwrite BE WAL',
    value: 'https://fonts.gstatic.com/s/playwritebewal/v1/DtV1Jwq5QbIzyrA6DHdJ2BksuUmanQtEYjAlv96WFsWCGPugHxwiSZp8.ttf'
  },
  {
    label: 'Playwrite BR',
    value: 'https://fonts.gstatic.com/s/playwritebr/v4/kJEhBuMK4Q07lDHc2Xp9vYgIp-6D3QEGCpthmFOPFsfjAOVZBgs.ttf'
  },
  {
    label: 'Playwrite CA',
    value: 'https://fonts.gstatic.com/s/playwriteca/v4/z7NGdR_4cT0NOrEAIElil93uR_VhfhYaYOijHiqvYp261AIQU98.ttf'
  },
  {
    label: 'Playwrite CL',
    value: 'https://fonts.gstatic.com/s/playwritecl/v1/-zk391m7wssz_XLkGgu8hy3tqrcOhnbf6ForU8JavLq3DyPNbus.ttf'
  },
  {
    label: 'Playwrite CO',
    value: 'https://fonts.gstatic.com/s/playwriteco/v6/0FlGVP2Hl1iH-fv2BH4kJkgb8vH-rbJPTDqqx7ZIo-dOEhxX3Iw.ttf'
  },
  {
    label: 'Playwrite CU',
    value: 'https://fonts.gstatic.com/s/playwritecu/v1/VuJjdNDb2p7tvoFGLMPdf9xcahOpb9ZuoyXseRmxZeT7m2YIByI.ttf'
  },
  {
    label: 'Playwrite CZ',
    value: 'https://fonts.gstatic.com/s/playwritecz/v1/8vIa7wYp22pt_BUChSHeVxxlOPUEKoMfap_FCI4bTqT_VkAS5X4.ttf'
  },
  {
    label: 'Playwrite DE Grund',
    value:
      'https://fonts.gstatic.com/s/playwritedegrund/v4/EJR-QhwoXdccriFurnRxqv-1MFyKy696-4VufrEGGbTZz2qHZwCUOfSwZTM.ttf'
  },
  {
    label: 'Playwrite DE LA',
    value: 'https://fonts.gstatic.com/s/playwritedela/v4/oY1J8e3fprboJ2HN4ogXTpFVJ8QjJV9p0P4yukst2FnqPgcigC5Ph2w.ttf'
  },
  {
    label: 'Playwrite DE SAS',
    value: 'https://fonts.gstatic.com/s/playwritedesas/v4/1Pt4g9vaRvmWghDdrE8IDuRPVrHN_1AaFXASpbMqJTeVgu6lSyVPgRkJ.ttf'
  },
  {
    label: 'Playwrite DE VA',
    value: 'https://fonts.gstatic.com/s/playwritedeva/v4/VuJmdNPb2p7tvoFGLMPdeMxGN1pntEMhdK1XfsTyRSySv24jGyvutu4.ttf'
  },
  {
    label: 'Playwrite DK Loopet',
    value:
      'https://fonts.gstatic.com/s/playwritedkloopet/v1/memVYbuzy2qb3rtJGfM1FvY-GacDcsPvtaDfqfgbBWmV75JJwegRuQovSE8_.ttf'
  },
  {
    label: 'Playwrite DK Uloopet',
    value:
      'https://fonts.gstatic.com/s/playwritedkuloopet/v1/bWtS7e3Ufwn0Hf1zjprKPYlcDAoHknvYFjqIh8PF6jwcP5K06lUrKeng6LUzAA.ttf'
  },
  {
    label: 'Playwrite ES',
    value: 'https://fonts.gstatic.com/s/playwritees/v4/kJEhBuMK4Q07lDHc2Xp9uokIp-6D3QEGCpthmFOPFsfjAOVZBgs.ttf'
  },
  {
    label: 'Playwrite ES Deco',
    value:
      'https://fonts.gstatic.com/s/playwriteesdeco/v4/7AuWp-g3kjKKGkePXEf2jxctfDxlvGM7-RllW8uEsjJ4yrdxOQ9p5Cljpw.ttf'
  },
  {
    label: 'Playwrite FR Moderne',
    value:
      'https://fonts.gstatic.com/s/playwritefrmoderne/v4/3y9L6awucz3w5m4FFTzKolJRXhUk_u1yWtWmFCJcqUBvK5aJuAKvAl74XSpRjw.ttf'
  },
  {
    label: 'Playwrite FR Trad',
    value:
      'https://fonts.gstatic.com/s/playwritefrtrad/v6/sJot3KxJjdGLJV3vyatrJE2pkQisWWMBP23HSIVI5tvAogvNdt-fwzTS5A.ttf'
  },
  {
    label: 'Playwrite GB J',
    value: 'https://fonts.gstatic.com/s/playwritegbj/v4/k3kEo8wSPe9dzQ1UGbvobAPhY5iG-fsubxedDheGdc9HaN7X9HFR8Q.ttf'
  },
  {
    label: 'Playwrite GB S',
    value: 'https://fonts.gstatic.com/s/playwritegbs/v5/oPWb_kFkk-s1Xclhmlemy7jsNQR8TohGU_DTHWU6uhD8x5eMaTk8AQ.ttf'
  },
  {
    label: 'Playwrite HR',
    value: 'https://fonts.gstatic.com/s/playwritehr/v1/WWXAljmQYQCZM5qaU_dwQYcybAQ7GFn1mFNJPsoB9YHXAHC7tvg.ttf'
  },
  {
    label: 'Playwrite HR Lijeva',
    value:
      'https://fonts.gstatic.com/s/playwritehrlijeva/v1/gNMvW2dhS5-p7HvxrBYiWN2SsKqLWCrYiDBAvbRl82ZY0d4zBsaUy4_lxmKl.ttf'
  },
  {
    label: 'Playwrite HU',
    value: 'https://fonts.gstatic.com/s/playwritehu/v1/A2BIn59A0g0xA3zDhFw-0vfPWJtlaFKmrETx1PL6TO2dH3B2Xx4.ttf'
  },
  {
    label: 'Playwrite ID',
    value: 'https://fonts.gstatic.com/s/playwriteid/v4/Cn-kJt2YWhlY2oC4KxifKQJmrtrRm-sKkQqUl0-RB_JbFi0pLlg.ttf'
  },
  {
    label: 'Playwrite IE',
    value: 'https://fonts.gstatic.com/s/playwriteie/v4/fC1zPYtWYWnH0hvndYd6GCGWXCAxfsUebXFMyzioBpIu30AZbUY.ttf'
  },
  {
    label: 'Playwrite IN',
    value: 'https://fonts.gstatic.com/s/playwritein/v4/uk-xEGGpoLQ97mfv2J3cZzuz7CyEJhPw65lkM7mMMR8n3_Ag1kU.ttf'
  },
  {
    label: 'Playwrite IS',
    value: 'https://fonts.gstatic.com/s/playwriteis/v3/JTUFjI4o_SGg9lecLGptrD17xQYXK0vOoz6jq6R9aX9-p7K5ILg.ttf'
  },
  {
    label: 'Playwrite IT Moderna',
    value:
      'https://fonts.gstatic.com/s/playwriteitmoderna/v4/mFTbWaYCwKPK5cx6W8jy2kwDnSUe9q45vQQi5HMFnSdEx2F5Wih8ubbffHtmMw.ttf'
  },
  {
    label: 'Playwrite IT Trad',
    value:
      'https://fonts.gstatic.com/s/playwriteittrad/v4/SlG5mR6Yo5oYZX5BFVcEySBSPE50BjHDpZxuvgxzFq96u-6_gENNXvzL2Q.ttf'
  },
  {
    label: 'Playwrite MX',
    value: 'https://fonts.gstatic.com/s/playwritemx/v4/6xK9dSNbKtCe7KfhXg7RYSwyQ-oO7xNblyJr9wnd5xYfXDWXDu8.ttf'
  },
  {
    label: 'Playwrite NG Modern',
    value:
      'https://fonts.gstatic.com/s/playwritengmodern/v4/ijw-s4b2R9Qve5V5lNJb_yRhEfSep5NbFCKmKgoEeCA4V17tPAbi5GswWJNE.ttf'
  },
  {
    label: 'Playwrite NL',
    value: 'https://fonts.gstatic.com/s/playwritenl/v3/k3kCo84SPe9dzQ1UGbvoZQ37Iqp5IZJF9bmaG9_FnYxNbPzS5HE.ttf'
  },
  {
    label: 'Playwrite NO',
    value: 'https://fonts.gstatic.com/s/playwriteno/v3/nuFrD_fYSZviRJYb-P2TrQO1DRpazaZDgnw-49wgHKen-mjRVtc.ttf'
  },
  {
    label: 'Playwrite NZ',
    value: 'https://fonts.gstatic.com/s/playwritenz/v4/d6lakaOxRsyr_zZDmUYvh2TW3NCQVvjKPjPjngAUeRt5gGCzkrs.ttf'
  },
  {
    label: 'Playwrite PE',
    value: 'https://fonts.gstatic.com/s/playwritepe/v1/FwZJ7-Amxlw-50y5PJugmImRrktKJDJ4lnesO2ltTPHFdtSgb_A.ttf'
  },
  {
    label: 'Playwrite PL',
    value: 'https://fonts.gstatic.com/s/playwritepl/v3/0QIyMXVf_4C2VH-yUr5uz72U-LQiKJ_9tb1WmRfb9ZybSwcVtHQ.ttf'
  },
  {
    label: 'Playwrite PT',
    value: 'https://fonts.gstatic.com/s/playwritept/v3/6NUE8FidKwOcfRjj8ukv5Lg-wt21rkAVfXUe9qDiTfJtvlo3Qaw.ttf'
  },
  {
    label: 'Playwrite RO',
    value: 'https://fonts.gstatic.com/s/playwritero/v3/gok8H6fuA1J7QPJ04HFTGSWdk_S0czhwEf0j4a9YnZWMJnZeBS8.ttf'
  },
  {
    label: 'Playwrite SK',
    value: 'https://fonts.gstatic.com/s/playwritesk/v3/9XU3lJp0klrZDw3AZHcsJTByz7latrF9yDIlf-2cvsOzdK9OF68.ttf'
  },
  {
    label: 'Playwrite TZ',
    value: 'https://fonts.gstatic.com/s/playwritetz/v4/RLptK5rs6au7bzABmVQAOwnUbvHMbzSUU27JDWwTue1COwjVROo.ttf'
  },
  {
    label: 'Playwrite US Modern',
    value:
      'https://fonts.gstatic.com/s/playwriteusmodern/v4/H4cMBWmRlMXPhla3hmMaveiYz8nSDkIFLNIYl2TXUwK62YohNg2Da0LCgUPK.ttf'
  },
  {
    label: 'Playwrite US Trad',
    value:
      'https://fonts.gstatic.com/s/playwriteustrad/v4/fdNX9tyHsnVPjW9trmV7wQ0stdwRBYclCsCdzOb1-cd1E8tgj6Kf5uBNig.ttf'
  },
  {
    label: 'Playwrite VN',
    value: 'https://fonts.gstatic.com/s/playwritevn/v4/mtGo4_hXJqPSu8nf5RBY5i0q0yxCxtP-9TFBNUI9E-9HPWIQtD0.ttf'
  },
  {
    label: 'Playwrite ZA',
    value: 'https://fonts.gstatic.com/s/playwriteza/v4/Noag6Uzhw5CTOhXKt5-vwvhrNyaNQo1LaBq0EbLHbYUsn9T5dt0.ttf'
  },
  {
    label: 'Plus Jakarta Sans',
    value:
      'https://fonts.gstatic.com/s/plusjakartasans/v8/LDIbaomQNQcsA88c7O9yZ4KMCoOg4IA6-91aHEjcWuA_qU7NShXUEKi4Rw.ttf'
  },
  {
    label: 'Podkova',
    value: 'https://fonts.gstatic.com/s/podkova/v31/K2FufZ1EmftJSV9VQpXb1lo9vC3nZWtFzcU4EoporSHH.ttf'
  },
  {
    label: 'Poetsen One',
    value: 'https://fonts.gstatic.com/s/poetsenone/v3/ke8hOgIaMUB37xCgvCntWtIvq_KREbG9.ttf'
  },
  {
    label: 'Poiret One',
    value: 'https://fonts.gstatic.com/s/poiretone/v16/UqyVK80NJXN4zfRgbdfbk5lWVscxdKE.ttf'
  },
  {
    label: 'Poller One',
    value: 'https://fonts.gstatic.com/s/pollerone/v23/ahccv82n0TN3gia5E4Bud-lbgUS5u0s.ttf'
  },
  {
    label: 'Poltawski Nowy',
    value: 'https://fonts.gstatic.com/s/poltawskinowy/v2/flUsRq6ww480U1xsUpFXD-iDBNlSAOLkKCLnWq8KqCWnDS6V5CzCoQ.ttf'
  },
  {
    label: 'Poly',
    value: 'https://fonts.gstatic.com/s/poly/v16/MQpb-W6wKNitRLCAq2Lpris.ttf'
  },
  {
    label: 'Pompiere',
    value: 'https://fonts.gstatic.com/s/pompiere/v19/VEMyRoxis5Dwuyeov6Wt5jDtreOL.ttf'
  },
  {
    label: 'Pontano Sans',
    value: 'https://fonts.gstatic.com/s/pontanosans/v17/qFdW35GdgYR8EzR6oBLDHa3wyRf8W8eBM6XLOXLMncaMp9gzWsE.ttf'
  },
  {
    label: 'Poor Story',
    value: 'https://fonts.gstatic.com/s/poorstory/v20/jizfREFUsnUct9P6cDfd4OmnLD0Z4zM.ttf'
  },
  {
    label: 'Poppins',
    value: 'https://fonts.gstatic.com/s/poppins/v21/pxiEyp8kv8JHgFVrFJDUc1NECPY.ttf'
  },
  {
    label: 'Port Lligat Sans',
    value: 'https://fonts.gstatic.com/s/portlligatsans/v22/kmKmZrYrGBbdN1aV7Vokow6Lw4s4l7N0Tx4xEcQ.ttf'
  },
  {
    label: 'Port Lligat Slab',
    value: 'https://fonts.gstatic.com/s/portlligatslab/v25/LDIpaoiQNgArA8kR7ulhZ8P_NYOss7ob9yGLmfI.ttf'
  },
  {
    label: 'Potta One',
    value: 'https://fonts.gstatic.com/s/pottaone/v16/FeVSS05Bp6cy7xI-YfxQ3Z5nm29Gww.ttf'
  },
  {
    label: 'Pragati Narrow',
    value: 'https://fonts.gstatic.com/s/pragatinarrow/v13/vm8vdRf0T0bS1ffgsPB7WZ-mD17_ytN3M48a.ttf'
  },
  {
    label: 'Praise',
    value: 'https://fonts.gstatic.com/s/praise/v7/qkBUXvUZ-cnFXcFyDvO67L9XmQ.ttf'
  },
  {
    label: 'Prata',
    value: 'https://fonts.gstatic.com/s/prata/v20/6xKhdSpbNNCT-vWIAG_5LWwJ.ttf'
  },
  {
    label: 'Preahvihear',
    value: 'https://fonts.gstatic.com/s/preahvihear/v29/6NUS8F-dNQeEYhzj7uluxswE49FJf8Wv.ttf'
  },
  {
    label: 'Press Start 2P',
    value: 'https://fonts.gstatic.com/s/pressstart2p/v15/e3t4euO8T-267oIAQAu6jDQyK0nSgPJE4580.ttf'
  },
  {
    label: 'Pridi',
    value: 'https://fonts.gstatic.com/s/pridi/v13/2sDQZG5JnZLfkfWao2krbl29.ttf'
  },
  {
    label: 'Princess Sofia',
    value: 'https://fonts.gstatic.com/s/princesssofia/v25/qWczB6yguIb8DZ_GXZst16n7GRz7mDUoupoI.ttf'
  },
  {
    label: 'Prociono',
    value: 'https://fonts.gstatic.com/s/prociono/v26/r05YGLlR-KxAf9GGO8upyDYtStiJ.ttf'
  },
  {
    label: 'Prompt',
    value: 'https://fonts.gstatic.com/s/prompt/v10/-W__XJnvUD7dzB26Z9AcZkIzeg.ttf'
  },
  {
    label: 'Prosto One',
    value: 'https://fonts.gstatic.com/s/prostoone/v19/OpNJno4VhNfK-RgpwWWxpipfWhXD00c.ttf'
  },
  {
    label: 'Protest Guerrilla',
    value: 'https://fonts.gstatic.com/s/protestguerrilla/v2/Qw3HZR5PDSL6K3irtrY-VJB2YzARHV0koJ8y_eiS.ttf'
  },
  {
    label: 'Protest Revolution',
    value: 'https://fonts.gstatic.com/s/protestrevolution/v2/11hcGofZ0kXBbxQXFB7MJsjtqnVw6Z2s8PIzTG1nQw.ttf'
  },
  {
    label: 'Protest Riot',
    value: 'https://fonts.gstatic.com/s/protestriot/v2/d6lPkaOxWMKm7TdezXFmpkrM1_JgjmRpOA.ttf'
  },
  {
    label: 'Protest Strike',
    value: 'https://fonts.gstatic.com/s/proteststrike/v2/0QI5MXdf4Y67Rn6vBog67ZjFlpzW0gZOs7BX.ttf'
  },
  {
    label: 'Proza Libre',
    value: 'https://fonts.gstatic.com/s/prozalibre/v9/LYjGdGHgj0k1DIQRyUEyyHovftvXWYyz.ttf'
  },
  {
    label: 'Public Sans',
    value: 'https://fonts.gstatic.com/s/publicsans/v15/ijwGs572Xtc6ZYQws9YVwllKVG8qX1oyOymuFpm5ww0pX189fg.ttf'
  },
  {
    label: 'Puppies Play',
    value: 'https://fonts.gstatic.com/s/puppiesplay/v9/wlp2gwHZEV99rG6M3NR9uB9vaAJSA_JN3Q.ttf'
  },
  {
    label: 'Puritan',
    value: 'https://fonts.gstatic.com/s/puritan/v24/845YNMgkAJ2VTtIo9JrwRdaI50M.ttf'
  },
  {
    label: 'Purple Purse',
    value: 'https://fonts.gstatic.com/s/purplepurse/v23/qWctB66gv53iAp-Vfs4My6qyeBb_ujA4ug.ttf'
  },
  {
    label: 'Qahiri',
    value: 'https://fonts.gstatic.com/s/qahiri/v9/tsssAp1RZy0C_hGuU3Chrnmupw.ttf'
  },
  {
    label: 'Quando',
    value: 'https://fonts.gstatic.com/s/quando/v16/xMQVuFNaVa6YuW0pC6WzKX_QmA.ttf'
  },
  {
    label: 'Quantico',
    value: 'https://fonts.gstatic.com/s/quantico/v17/rax-HiSdp9cPL3KIF4xsLjxSmlLZ.ttf'
  },
  {
    label: 'Quattrocento',
    value: 'https://fonts.gstatic.com/s/quattrocento/v23/OZpEg_xvsDZQL_LKIF7q4jPHxGL7f4jFuA.ttf'
  },
  {
    label: 'Quattrocento Sans',
    value: 'https://fonts.gstatic.com/s/quattrocentosans/v21/va9c4lja2NVIDdIAAoMR5MfuElaRB3zOvU7eHGHJ.ttf'
  },
  {
    label: 'Questrial',
    value: 'https://fonts.gstatic.com/s/questrial/v18/QdVUSTchPBm7nuUeVf7EuStkm20oJA.ttf'
  },
  {
    label: 'Quicksand',
    value: 'https://fonts.gstatic.com/s/quicksand/v31/6xK-dSZaM9iE8KbpRA_LJ3z8mH9BOJvgkP8o18G0wx40QDw.ttf'
  },
  {
    label: 'Quintessential',
    value: 'https://fonts.gstatic.com/s/quintessential/v22/fdNn9sOGq31Yjnh3qWU14DdtjY5wS7kmAyxM.ttf'
  },
  {
    label: 'Qwigley',
    value: 'https://fonts.gstatic.com/s/qwigley/v18/1cXzaU3UGJb5tGoCuVxsi1mBmcE.ttf'
  },
  {
    label: 'Qwitcher Grypen',
    value: 'https://fonts.gstatic.com/s/qwitchergrypen/v6/pxicypclp9tDilN9RrC5BSI1dZmrSGNAom-wpw.ttf'
  },
  {
    label: 'REM',
    value: 'https://fonts.gstatic.com/s/rem/v2/WnzgHAIoSDyHbRjfsYumpRvUPMLqrToUbIqIfBU.ttf'
  },
  {
    label: 'Racing Sans One',
    value: 'https://fonts.gstatic.com/s/racingsansone/v15/sykr-yRtm7EvTrXNxkv5jfKKyDCwL3rmWpIBtA.ttf'
  },
  {
    label: 'Radio Canada',
    value:
      'https://fonts.gstatic.com/s/radiocanada/v21/XRX13ISXn0dBMcibU6jlAqr3ejLv5OLZYiYXik6db2P4jxxlsls-0nFMkQPIJOdSSfOT.ttf'
  },
  {
    label: 'Radio Canada Big',
    value: 'https://fonts.gstatic.com/s/radiocanadabig/v1/LYjUdHrinEImAoQewU0hyTsPFra4Yp-6A-YRBF-RX6nNRY3p2pcheCsG.ttf'
  },
  {
    label: 'Radley',
    value: 'https://fonts.gstatic.com/s/radley/v22/LYjDdGzinEIjCN19oAlEpVs3VQ.ttf'
  },
  {
    label: 'Rajdhani',
    value: 'https://fonts.gstatic.com/s/rajdhani/v15/LDIxapCSOBg7S-QT7q4AOeekWPrP.ttf'
  },
  {
    label: 'Rakkas',
    value: 'https://fonts.gstatic.com/s/rakkas/v19/Qw3cZQlNHiblL3j_lttPOeMcCw.ttf'
  },
  {
    label: 'Raleway',
    value: 'https://fonts.gstatic.com/s/raleway/v34/1Ptxg8zYS_SKggPN4iEgvnHyvveLxVvaooCPNLA3JC9c.ttf'
  },
  {
    label: 'Raleway Dots',
    value: 'https://fonts.gstatic.com/s/ralewaydots/v18/6NUR8FifJg6AfQvzpshgwJ8kyf9Fdty2ew.ttf'
  },
  {
    label: 'Ramabhadra',
    value: 'https://fonts.gstatic.com/s/ramabhadra/v15/EYq2maBOwqRW9P1SQ83LehNGX5uWw3o.ttf'
  },
  {
    label: 'Ramaraja',
    value: 'https://fonts.gstatic.com/s/ramaraja/v15/SlGTmQearpYAYG1CABIkqnB6aSQU.ttf'
  },
  {
    label: 'Rambla',
    value: 'https://fonts.gstatic.com/s/rambla/v13/snfrs0ip98hx6mr0I7IONthkwQ.ttf'
  },
  {
    label: 'Rammetto One',
    value: 'https://fonts.gstatic.com/s/rammettoone/v19/LhWiMV3HOfMbMetJG3lQDpp9Mvuciu-_SQ.ttf'
  },
  {
    label: 'Rampart One',
    value: 'https://fonts.gstatic.com/s/rampartone/v9/K2F1fZFGl_JSR1tAWNG9R6qgLS76ZHOM.ttf'
  },
  {
    label: 'Ranchers',
    value: 'https://fonts.gstatic.com/s/ranchers/v17/zrfm0H3Lx-P2Xvs2AoDYDC79XTHv.ttf'
  },
  {
    label: 'Rancho',
    value: 'https://fonts.gstatic.com/s/rancho/v21/46kulbzmXjLaqZRlbWXgd0RY1g.ttf'
  },
  {
    label: 'Ranga',
    value: 'https://fonts.gstatic.com/s/ranga/v21/C8ct4cYisGb28p6CLDwZwmGE.ttf'
  },
  {
    label: 'Rasa',
    value: 'https://fonts.gstatic.com/s/rasa/v22/xn76YHIn1mWmVKl8ZtAM9NrJfN5GJW41fcvN2KT4.ttf'
  },
  {
    label: 'Rationale',
    value: 'https://fonts.gstatic.com/s/rationale/v28/9XUnlJ92n0_JFxHIfHcsdlFMzLC2Zw.ttf'
  },
  {
    label: 'Ravi Prakash',
    value: 'https://fonts.gstatic.com/s/raviprakash/v19/gokpH6fsDkVrF9Bv9X8SOAKHmNZEq6TTFw.ttf'
  },
  {
    label: 'Readex Pro',
    value:
      'https://fonts.gstatic.com/s/readexpro/v21/SLXnc1bJ7HE5YDoGPuzj_dh8uc7wUy8ZQQyX2KY8TL0kGZN6blTC4USmgmsglvjkag.ttf'
  },
  {
    label: 'Recursive',
    value:
      'https://fonts.gstatic.com/s/recursive/v38/8vJN7wMr0mhh-RQChyHEH06TlXhq_gukbYrFMk1QuAIcyEwG_X-dpEfaE5YaERmK-CImKsvxvU-MXGX2fSqasNfUvz2xbXfn1uEQadCCk018vwxjDJCL.ttf'
  },
  {
    label: 'Red Hat Display',
    value: 'https://fonts.gstatic.com/s/redhatdisplay/v19/8vIf7wUr0m80wwYf0QCXZzYzUoTK8RZQvRd-D1NYbmyWckg5-Xecg3w.ttf'
  },
  {
    label: 'Red Hat Mono',
    value: 'https://fonts.gstatic.com/s/redhatmono/v11/jVyY7nDnA2uf2zVvFAhhzEs-VMSjJpBTfgjwQV3I-7HNuW4QuKI.ttf'
  },
  {
    label: 'Red Hat Text',
    value: 'https://fonts.gstatic.com/s/redhattext/v14/RrQCbohi_ic6B3yVSzGBrMx6ZI_cy1A6Ok2ML7hwVrbacYVFtIY.ttf'
  },
  {
    label: 'Red Rose',
    value: 'https://fonts.gstatic.com/s/redrose/v20/QdVISTYiLBjouPgEUajvsfWwDtc3MH8yrfsDcjSsYUVUjg.ttf'
  },
  {
    label: 'Redacted',
    value: 'https://fonts.gstatic.com/s/redacted/v8/Z9XVDmdRShme2O_7aITe4u2El6GC.ttf'
  },
  {
    label: 'Redacted Script',
    value: 'https://fonts.gstatic.com/s/redactedscript/v10/ypvBbXGRglhokR7dcC3d1-R6zmxSsWTxkZkr_g.ttf'
  },
  {
    label: 'Reddit Mono',
    value: 'https://fonts.gstatic.com/s/redditmono/v3/oPWc_kRmmu4oQ88oo13o48DHbsqn28eR20vUwCYacnnYz7yQYA.ttf'
  },
  {
    label: 'Reddit Sans',
    value: 'https://fonts.gstatic.com/s/redditsans/v4/EYqgmaFOxq1T_-ETdN7EKSlnU2dHRsBCV5uxbYxmAVfBiVMFlw.ttf'
  },
  {
    label: 'Reddit Sans Condensed',
    value:
      'https://fonts.gstatic.com/s/redditsanscondensed/v3/m8J_jepOc6WYkkm2Dey9A5QGAQXmuL3va5IfZsq2gyKtWVJro1kKUpU4u7XDIuc.ttf'
  },
  {
    label: 'Redressed',
    value: 'https://fonts.gstatic.com/s/redressed/v29/x3dickHUbrmJ7wMy9MsBfPACvy_1BA.ttf'
  },
  {
    label: 'Reem Kufi',
    value: 'https://fonts.gstatic.com/s/reemkufi/v21/2sDPZGJLip7W2J7v7wQZZE1I0yCmYzzQtuZnEGGf3qGuvM4.ttf'
  },
  {
    label: 'Reem Kufi Fun',
    value: 'https://fonts.gstatic.com/s/reemkufifun/v7/uK_m4rOFYukkmyUEbF43fIryZEk5qRZ8nrKChoYj3nCgrvqZzZXq.ttf'
  },
  {
    label: 'Reem Kufi Ink',
    value: 'https://fonts.gstatic.com/s/reemkufiink/v9/oPWJ_kJmmu8hCvB9iFumxZSnRj5dQnSX1ko.ttf'
  },
  {
    label: 'Reenie Beanie',
    value: 'https://fonts.gstatic.com/s/reeniebeanie/v20/z7NSdR76eDkaJKZJFkkjuvWxbP2_qoOgf_w.ttf'
  },
  {
    label: 'Reggae One',
    value: 'https://fonts.gstatic.com/s/reggaeone/v16/7r3DqX5msMIkeuwJwOJt_a5L5uH-mts.ttf'
  },
  {
    label: 'Rethink Sans',
    value: 'https://fonts.gstatic.com/s/rethinksans/v5/AMODz4SDuXOMCPfdoglY9JQuWHBGG0X45DmqkmFRCE7mma-aua4.ttf'
  },
  {
    label: 'Revalia',
    value: 'https://fonts.gstatic.com/s/revalia/v22/WwkexPimBE2-4ZPEeVruNIgJSNM.ttf'
  },
  {
    label: 'Rhodium Libre',
    value: 'https://fonts.gstatic.com/s/rhodiumlibre/v19/1q2AY5adA0tn_ukeHcQHqpx6pETLeo2gm2U.ttf'
  },
  {
    label: 'Ribeye',
    value: 'https://fonts.gstatic.com/s/ribeye/v25/L0x8DFMxk1MP9R3RvPCmRSlUig.ttf'
  },
  {
    label: 'Ribeye Marrow',
    value: 'https://fonts.gstatic.com/s/ribeyemarrow/v24/GFDsWApshnqMRO2JdtRZ2d0vEAwTVWgKdtw.ttf'
  },
  {
    label: 'Righteous',
    value: 'https://fonts.gstatic.com/s/righteous/v17/1cXxaUPXBpj2rGoU7C9mj3uEicG01A.ttf'
  },
  {
    label: 'Risque',
    value: 'https://fonts.gstatic.com/s/risque/v22/VdGfAZUfHosahXxoCUYVBJ-T5g.ttf'
  },
  {
    label: 'Road Rage',
    value: 'https://fonts.gstatic.com/s/roadrage/v7/6NUU8F2fKAOBKjjr4ekvtMYAwdRZfw.ttf'
  },
  {
    label: 'Roboto',
    value: 'https://fonts.gstatic.com/s/roboto/v30/KFOmCnqEu92Fr1Me5WZLCzYlKw.ttf'
  },
  {
    label: 'Roboto Condensed',
    value:
      'https://fonts.gstatic.com/s/robotocondensed/v27/ieVo2ZhZI2eCN5jzbjEETS9weq8-_d6T_POl0fRJeyWyovBJ731BKMSK.ttf'
  },
  {
    label: 'Roboto Flex',
    value:
      'https://fonts.gstatic.com/s/robotoflex/v26/NaNnepOXO_NexZs0b5QrzlOHb8wCikXpYqmZsWI-__OGbt8jZktqc2V3Zs0KvDLdBP8SBZtOs2IifRuUZQMsPJtUsR4DEK6cULNeUx9XgTnH37Ha_FIAp4Fm0PP1hw45DntW2x0wZGzhPmr1YNMYKYn9_1IQXGwJAiUJVUMdN5YUW4O8HtSoXjC1z3QSabshNFVe3e0O5j3ZjrZCu23Qd4G0EBysQNK-QKavMl1cKq3tHXtXi8mzLjaAQbGunCNCKMY.ttf'
  },
  {
    label: 'Roboto Mono',
    value: 'https://fonts.gstatic.com/s/robotomono/v23/L0xuDF4xlVMF-BfR8bXMIhJHg45mwgGEFl0_3vqPQ--5Ip2sSQ.ttf'
  },
  {
    label: 'Roboto Serif',
    value:
      'https://fonts.gstatic.com/s/robotoserif/v13/R71RjywflP6FLr3gZx7K8UyuXDs9zVwDmXCb8lxYgmuii32UGoVldX6UgfjL4-3sMM_kB_qXSEXTJQCFLH5-_bcEliotp6d2Af5fR4k.ttf'
  },
  {
    label: 'Roboto Slab',
    value: 'https://fonts.gstatic.com/s/robotoslab/v34/BngbUXZYTXPIvIBgJJSb6s3BzlRRfKOFbvjojISWaG5iddG-1A.ttf'
  },
  {
    label: 'Rochester',
    value: 'https://fonts.gstatic.com/s/rochester/v22/6ae-4KCqVa4Zy6Fif-Uy31vWNTMwoQ.ttf'
  },
  {
    label: 'Rock 3D',
    value: 'https://fonts.gstatic.com/s/rock3d/v10/yYLp0hrL0PCo651513SnwRnQyNI.ttf'
  },
  {
    label: 'Rock Salt',
    value: 'https://fonts.gstatic.com/s/rocksalt/v22/MwQ0bhv11fWD6QsAVOZbsEk7hbBWrA.ttf'
  },
  {
    label: 'RocknRoll One',
    value: 'https://fonts.gstatic.com/s/rocknrollone/v13/kmK7ZqspGAfCeUiW6FFlmEC9guVhs7tfUxc.ttf'
  },
  {
    label: 'Rokkitt',
    value: 'https://fonts.gstatic.com/s/rokkitt/v36/qFdb35qfgYFjGy5hukqqhw5XeRgdi1ryd5DLE76HvN6n.ttf'
  },
  {
    label: 'Romanesco',
    value: 'https://fonts.gstatic.com/s/romanesco/v21/w8gYH2ozQOY7_r_J7mSn3HwLqOqSBg.ttf'
  },
  {
    label: 'Ropa Sans',
    value: 'https://fonts.gstatic.com/s/ropasans/v15/EYqxmaNOzLlWtsZSScyKWjloU5KP2g.ttf'
  },
  {
    label: 'Rosario',
    value: 'https://fonts.gstatic.com/s/rosario/v31/xfuu0WDhWW_fOEoY8l_VPNZfB7jPM68YCWczd-YnOzUD.ttf'
  },
  {
    label: 'Rosarivo',
    value: 'https://fonts.gstatic.com/s/rosarivo/v22/PlI-Fl2lO6N9f8HaNAeC2nhMnNy5.ttf'
  },
  {
    label: 'Rouge Script',
    value: 'https://fonts.gstatic.com/s/rougescript/v18/LYjFdGbiklMoCIQOw1Ep3S4PVPXbUJWq9g.ttf'
  },
  {
    label: 'Rowdies',
    value: 'https://fonts.gstatic.com/s/rowdies/v17/ptRJTieMYPNBAK21zrdJwObZNQo.ttf'
  },
  {
    label: 'Rozha One',
    value: 'https://fonts.gstatic.com/s/rozhaone/v15/AlZy_zVFtYP12Zncg2khdXf4XB0Tow.ttf'
  },
  {
    label: 'Rubik',
    value: 'https://fonts.gstatic.com/s/rubik/v28/iJWZBXyIfDnIV5PNhY1KTN7Z-Yh-B4i1UE80V4bVkA.ttf'
  },
  {
    label: 'Rubik 80s Fade',
    value: 'https://fonts.gstatic.com/s/rubik80sfade/v2/U9MF6dW37nLSmnwZXyoV-uPXUhHwkbL8IHcK.ttf'
  },
  {
    label: 'Rubik Beastly',
    value: 'https://fonts.gstatic.com/s/rubikbeastly/v10/0QImMXRd5oOmSC2ZQ7o9653X07z8_ApHqqk.ttf'
  },
  {
    label: 'Rubik Broken Fax',
    value: 'https://fonts.gstatic.com/s/rubikbrokenfax/v1/NGSvv4rXG042O-GzH9sg1cUgl8w8YW-WdmGi300.ttf'
  },
  {
    label: 'Rubik Bubbles',
    value: 'https://fonts.gstatic.com/s/rubikbubbles/v3/JIA1UVdwbHFJtwA7Us1BPFbRNTENfDxyRXI.ttf'
  },
  {
    label: 'Rubik Burned',
    value: 'https://fonts.gstatic.com/s/rubikburned/v1/Jqzk5TmOVOqQHihKqPpscqniHQuaCY5ZSg.ttf'
  },
  {
    label: 'Rubik Dirt',
    value: 'https://fonts.gstatic.com/s/rubikdirt/v2/DtVmJxC7WLEj1uIXEWAdulwm6gDXvwE.ttf'
  },
  {
    label: 'Rubik Distressed',
    value: 'https://fonts.gstatic.com/s/rubikdistressed/v1/GFDxWBdsmnqAVqjtUsZf2dcrQ2ldcWAhatVBaGM.ttf'
  },
  {
    label: 'Rubik Doodle Shadow',
    value: 'https://fonts.gstatic.com/s/rubikdoodleshadow/v1/rP2bp3im_k8G_wTVdvvMdHqmXTR3lEaLyKuZ3KOY7Gw.ttf'
  },
  {
    label: 'Rubik Doodle Triangles',
    value: 'https://fonts.gstatic.com/s/rubikdoodletriangles/v1/esDA301BLOmMKxKspb3g-domRuLPeaSn2bTzdLi_slZxgWE.ttf'
  },
  {
    label: 'Rubik Gemstones',
    value: 'https://fonts.gstatic.com/s/rubikgemstones/v1/zrf90HrL0-_8Xb4DFM2rUkWbOVrOiCnGqi1GMw.ttf'
  },
  {
    label: 'Rubik Glitch',
    value: 'https://fonts.gstatic.com/s/rubikglitch/v2/qkBSXv8b_srFRYQVYrDKh9ZvmC7HONiSFQ.ttf'
  },
  {
    label: 'Rubik Glitch Pop',
    value: 'https://fonts.gstatic.com/s/rubikglitchpop/v1/tDbX2pGHhFcM0gB3hN2elZLa3G-MOwStSUrV_BE.ttf'
  },
  {
    label: 'Rubik Iso',
    value: 'https://fonts.gstatic.com/s/rubikiso/v2/x3dickHUfr-S4VAI4sABfPACvy_1BA.ttf'
  },
  {
    label: 'Rubik Lines',
    value: 'https://fonts.gstatic.com/s/rubiklines/v1/_gP81R3vsjYzVW2Y6xFF-GSxYPp7oSNy.ttf'
  },
  {
    label: 'Rubik Maps',
    value: 'https://fonts.gstatic.com/s/rubikmaps/v1/Gw6_wcjl80TZK9XxtbbejSYUChRqp9k.ttf'
  },
  {
    label: 'Rubik Marker Hatch',
    value: 'https://fonts.gstatic.com/s/rubikmarkerhatch/v1/QldTNSFQsh0B_bFXXWv6LAt-jswapJHQDL4iw0H6zw.ttf'
  },
  {
    label: 'Rubik Maze',
    value: 'https://fonts.gstatic.com/s/rubikmaze/v2/xMQRuF9ZVa2ftiJEavXSAX7inS-bxV4.ttf'
  },
  {
    label: 'Rubik Microbe',
    value: 'https://fonts.gstatic.com/s/rubikmicrobe/v2/UqyWK8oPP3hjw6ANS9rM3PsZcs8aaKgiauE.ttf'
  },
  {
    label: 'Rubik Mono One',
    value: 'https://fonts.gstatic.com/s/rubikmonoone/v18/UqyJK8kPP3hjw6ANTdfRk9YSN-8wRqQrc_j9.ttf'
  },
  {
    label: 'Rubik Moonrocks',
    value: 'https://fonts.gstatic.com/s/rubikmoonrocks/v5/845ANMAmAI2VUZMLu_W0M7HqlDHnXcD7JGy1Sw.ttf'
  },
  {
    label: 'Rubik Pixels',
    value: 'https://fonts.gstatic.com/s/rubikpixels/v2/SlGXmQOaupkIeSx4CEpB7AdSaBYRagrQrA.ttf'
  },
  {
    label: 'Rubik Puddles',
    value: 'https://fonts.gstatic.com/s/rubikpuddles/v2/1Ptog8bYX_qGnkLkrU5MJsQcJfC0wVMT-aE.ttf'
  },
  {
    label: 'Rubik Scribble',
    value: 'https://fonts.gstatic.com/s/rubikscribble/v1/snfzs0Cp48d67SuHQOpjXLsQpbqbSjORSo9W.ttf'
  },
  {
    label: 'Rubik Spray Paint',
    value: 'https://fonts.gstatic.com/s/rubikspraypaint/v1/WnzhHBAoeBPUDTB4EWR82y6EXWPH-Ro-QoaBZQxP.ttf'
  },
  {
    label: 'Rubik Storm',
    value: 'https://fonts.gstatic.com/s/rubikstorm/v1/eLGYP-_uPgO5Ag7ju9JaouL9T2Xh9NQk.ttf'
  },
  {
    label: 'Rubik Vinyl',
    value: 'https://fonts.gstatic.com/s/rubikvinyl/v1/iJWABXKIfDnIV4mQ5BfjvUXexox2ztOU.ttf'
  },
  {
    label: 'Rubik Wet Paint',
    value: 'https://fonts.gstatic.com/s/rubikwetpaint/v2/HTx0L20uMDGHgdULcpTF3Oe4d_-F-zz313DuvQ.ttf'
  },
  {
    label: 'Ruda',
    value: 'https://fonts.gstatic.com/s/ruda/v28/k3kKo8YQJOpFgHQ1mQ5VkEbUKaJFsi_-2KiSGg-H.ttf'
  },
  {
    label: 'Rufina',
    value: 'https://fonts.gstatic.com/s/rufina/v15/Yq6V-LyURyLy-aKyoxRktOdClg.ttf'
  },
  {
    label: 'Ruge Boogie',
    value: 'https://fonts.gstatic.com/s/rugeboogie/v28/JIA3UVFwbHRF_GIWSMhKNROiPzUveSxy.ttf'
  },
  {
    label: 'Ruluko',
    value: 'https://fonts.gstatic.com/s/ruluko/v21/xMQVuFNZVaODtm0pC6WzKX_QmA.ttf'
  },
  {
    label: 'Rum Raisin',
    value: 'https://fonts.gstatic.com/s/rumraisin/v22/nwpRtKu3Ih8D5avB4h2uJ3-IywA7eMM.ttf'
  },
  {
    label: 'Ruslan Display',
    value: 'https://fonts.gstatic.com/s/ruslandisplay/v26/Gw6jwczl81XcIZuckK_e3UpfdzxrldyFvm1n.ttf'
  },
  {
    label: 'Russo One',
    value: 'https://fonts.gstatic.com/s/russoone/v16/Z9XUDmZRWg6M1LvRYsH-yMOInrib9Q.ttf'
  },
  {
    label: 'Ruthie',
    value: 'https://fonts.gstatic.com/s/ruthie/v26/gokvH63sGkdqXuU9lD53Q2u_mQ.ttf'
  },
  {
    label: 'Ruwudu',
    value: 'https://fonts.gstatic.com/s/ruwudu/v4/syky-y1tj6UzRKfNlQCT9tPdpw.ttf'
  },
  {
    label: 'Rye',
    value: 'https://fonts.gstatic.com/s/rye/v15/r05XGLJT86YDFpTsXOqx4w.ttf'
  },
  {
    label: 'STIX Two Text',
    value: 'https://fonts.gstatic.com/s/stixtwotext/v12/YA9Gr02F12Xkf5whdwKf11l0jbKkeidMTtZ5Yihg2SOYWxFMN1WD.ttf'
  },
  {
    label: 'Sacramento',
    value: 'https://fonts.gstatic.com/s/sacramento/v15/buEzpo6gcdjy0EiZMBUG0CoV_NxLeiw.ttf'
  },
  {
    label: 'Sahitya',
    value: 'https://fonts.gstatic.com/s/sahitya/v17/6qLAKZkOuhnuqlJAaScFPywEDnI.ttf'
  },
  {
    label: 'Sail',
    value: 'https://fonts.gstatic.com/s/sail/v16/DPEjYwiBxwYJFBTDADYAbvw.ttf'
  },
  {
    label: 'Saira',
    value: 'https://fonts.gstatic.com/s/saira/v20/memWYa2wxmKQyPMrZX79wwYZQMhsyuShhKMjjbU9uXuA71rCosg7lwYmUVY.ttf'
  },
  {
    label: 'Saira Condensed',
    value: 'https://fonts.gstatic.com/s/sairacondensed/v11/EJROQgErUN8XuHNEtX81i9TmEkrfpeFE-IyCrw.ttf'
  },
  {
    label: 'Saira Extra Condensed',
    value: 'https://fonts.gstatic.com/s/sairaextracondensed/v13/-nFiOHYr-vcC7h8MklGBkrvmUG9rbpkisrTT70L11Ct8sw.ttf'
  },
  {
    label: 'Saira Semi Condensed',
    value: 'https://fonts.gstatic.com/s/sairasemicondensed/v13/U9MD6c-2-nnJkHxyCjRcnMHcWVWV1cWRRU8LYuceqGT-.ttf'
  },
  {
    label: 'Saira Stencil One',
    value: 'https://fonts.gstatic.com/s/sairastencilone/v16/SLXSc03I6HkvZGJ1GvvipLoYSTEL9AsMawif2YQ2.ttf'
  },
  {
    label: 'Salsa',
    value: 'https://fonts.gstatic.com/s/salsa/v21/gNMKW3FiRpKj-imY8ncKEZez.ttf'
  },
  {
    label: 'Sanchez',
    value: 'https://fonts.gstatic.com/s/sanchez/v15/Ycm2sZJORluHnXbITm5b_BwE1l0.ttf'
  },
  {
    label: 'Sancreek',
    value: 'https://fonts.gstatic.com/s/sancreek/v25/pxiHypAnsdxUm159X7D-XV9NEe-K.ttf'
  },
  {
    label: 'Sansita',
    value: 'https://fonts.gstatic.com/s/sansita/v11/QldONTRRphEb_-V7HBm7TXFf3qw.ttf'
  },
  {
    label: 'Sansita Swashed',
    value: 'https://fonts.gstatic.com/s/sansitaswashed/v18/BXR8vFfZifTZgFlDDLgNkBydPKTt3pVCeYWqJnZSW7RpbToVehmEa4Q.ttf'
  },
  {
    label: 'Sarabun',
    value: 'https://fonts.gstatic.com/s/sarabun/v15/DtVjJx26TKEr37c9WBJDnlQN9gk.ttf'
  },
  {
    label: 'Sarala',
    value: 'https://fonts.gstatic.com/s/sarala/v12/uK_y4riEZv4o1w9RCh0TMv6EXw.ttf'
  },
  {
    label: 'Sarina',
    value: 'https://fonts.gstatic.com/s/sarina/v23/-F6wfjF3ITQwasLhLkDUriBQxw.ttf'
  },
  {
    label: 'Sarpanch',
    value: 'https://fonts.gstatic.com/s/sarpanch/v13/hESy6Xt4NCpRuk6Pzh2ARIrX_20n.ttf'
  },
  {
    label: 'Sassy Frass',
    value: 'https://fonts.gstatic.com/s/sassyfrass/v7/LhWhMVrGOe0FLb97BjhsE99dGNWQg_am.ttf'
  },
  {
    label: 'Satisfy',
    value: 'https://fonts.gstatic.com/s/satisfy/v21/rP2Hp2yn6lkG50LoOZSCHBeHFl0.ttf'
  },
  {
    label: 'Sawarabi Gothic',
    value: 'https://fonts.gstatic.com/s/sawarabigothic/v12/x3d4ckfVaqqa-BEj-I9mE65u3k3NBSk3E2YljQ.ttf'
  },
  {
    label: 'Sawarabi Mincho',
    value: 'https://fonts.gstatic.com/s/sawarabimincho/v17/8QIRdiDaitzr7brc8ahpxt6GcIJTLahP46UDUw.ttf'
  },
  {
    label: 'Scada',
    value: 'https://fonts.gstatic.com/s/scada/v15/RLpxK5Pv5qumeWJoxzUobkvv.ttf'
  },
  {
    label: 'Scheherazade New',
    value: 'https://fonts.gstatic.com/s/scheherazadenew/v15/4UaZrFhTvxVnHDvUkUiHg8jprP4DCwNsOl4p5Is.ttf'
  },
  {
    label: 'Schibsted Grotesk',
    value:
      'https://fonts.gstatic.com/s/schibstedgrotesk/v3/JqzK5SSPQuCQF3t8uOwiUL-taUTtarVKQ9vZ6pJJWlMNIsEAT8JuXFGVOQ.ttf'
  },
  {
    label: 'Schoolbell',
    value: 'https://fonts.gstatic.com/s/schoolbell/v18/92zQtBZWOrcgoe-fgnJIVxIQ6mRqfiQ.ttf'
  },
  {
    label: 'Scope One',
    value: 'https://fonts.gstatic.com/s/scopeone/v14/WBLnrEXKYFlGHrOKmGD1W0_MJMGxiQ.ttf'
  },
  {
    label: 'Seaweed Script',
    value: 'https://fonts.gstatic.com/s/seaweedscript/v15/bx6cNx6Tne2pxOATYE8C_Rsoe0WJ-KcGVbLW.ttf'
  },
  {
    label: 'Secular One',
    value: 'https://fonts.gstatic.com/s/secularone/v12/8QINdiTajsj_87rMuMdKypDlMul7LJpK.ttf'
  },
  {
    label: 'Sedan',
    value: 'https://fonts.gstatic.com/s/sedan/v1/Yq6a-L-VVyD6-eOSiTpovf5b.ttf'
  },
  {
    label: 'Sedan SC',
    value: 'https://fonts.gstatic.com/s/sedansc/v2/yMJRMIlvYZ3Jn1Y30Dq8fSx5i814.ttf'
  },
  {
    label: 'Sedgwick Ave',
    value: 'https://fonts.gstatic.com/s/sedgwickave/v12/uK_04rKEYuguzAcSYRdWTJq8Xmg1Vcf5JA.ttf'
  },
  {
    label: 'Sedgwick Ave Display',
    value: 'https://fonts.gstatic.com/s/sedgwickavedisplay/v21/xfuu0XPgU3jZPUoUo3ScvmPi-NapQ8OxM2czd-YnOzUD.ttf'
  },
  {
    label: 'Sen',
    value: 'https://fonts.gstatic.com/s/sen/v9/6xK0dSxYI9_dkN18-vZKK2EISCq5H47KlD9q78A.ttf'
  },
  {
    label: 'Send Flowers',
    value: 'https://fonts.gstatic.com/s/sendflowers/v5/If2PXTjtZS-0Xqy13uCQSULvxwjjouU1iw.ttf'
  },
  {
    label: 'Sevillana',
    value: 'https://fonts.gstatic.com/s/sevillana/v23/KFOlCnWFscmDt1Bfiy1vAx05IsDqlA.ttf'
  },
  {
    label: 'Seymour One',
    value: 'https://fonts.gstatic.com/s/seymourone/v23/4iCp6Khla9xbjQpoWGGd0myIPYBvgpUI.ttf'
  },
  {
    label: 'Shadows Into Light',
    value: 'https://fonts.gstatic.com/s/shadowsintolight/v19/UqyNK9UOIntux_czAvDQx_ZcHqZXBNQDcsr4xzSMYA.ttf'
  },
  {
    label: 'Shadows Into Light Two',
    value: 'https://fonts.gstatic.com/s/shadowsintolighttwo/v17/4iC86LVlZsRSjQhpWGedwyOoW-0A6_kpsyNmlAvNGLNnIF0.ttf'
  },
  {
    label: 'Shalimar',
    value: 'https://fonts.gstatic.com/s/shalimar/v7/uU9MCBoE6I6iNWFUvTPx8PCOg0uX.ttf'
  },
  {
    label: 'Shantell Sans',
    value:
      'https://fonts.gstatic.com/s/shantellsans/v10/FeUaS0pCoLIo-lcdY7kjvNoQqWVWB0qWpl29ajppTuUTu_kJKmHesPOL-maYi4xZeHCNQ09eBlmv2QcUzJ39-rAISYR8S2i2yPwxjyRN.ttf'
  },
  {
    label: 'Shanti',
    value: 'https://fonts.gstatic.com/s/shanti/v25/t5thIREMM4uSDgzgU0ezpKfwzA.ttf'
  },
  {
    label: 'Share',
    value: 'https://fonts.gstatic.com/s/share/v18/i7dEIFliZjKNF5VNHLq2cV5d.ttf'
  },
  {
    label: 'Share Tech',
    value: 'https://fonts.gstatic.com/s/sharetech/v21/7cHtv4Uyi5K0OeZ7bohUwHoDmTcibrA.ttf'
  },
  {
    label: 'Share Tech Mono',
    value: 'https://fonts.gstatic.com/s/sharetechmono/v15/J7aHnp1uDWRBEqV98dVQztYldFc7pAsEIc3Xew.ttf'
  },
  {
    label: 'Shippori Antique',
    value: 'https://fonts.gstatic.com/s/shipporiantique/v8/-F6qfid3KC8pdMyzR0qRyFUht11v8ldPg-IUDNg.ttf'
  },
  {
    label: 'Shippori Antique B1',
    value: 'https://fonts.gstatic.com/s/shipporiantiqueb1/v8/2Eb7L_JwClR7Zl_UAKZ0mUHw3oMKd40grRFCj9-5Y8Y.ttf'
  },
  {
    label: 'Shippori Mincho',
    value: 'https://fonts.gstatic.com/s/shipporimincho/v14/VdGGAZweH5EbgHY6YExcZfDoj0BA2_-C7LoS7g.ttf'
  },
  {
    label: 'Shippori Mincho B1',
    value: 'https://fonts.gstatic.com/s/shipporiminchob1/v21/wXK2E2wCr44tulPdnn-xbIpJ9RgT9-nyjqBr1lO97Q.ttf'
  },
  {
    label: 'Shizuru',
    value: 'https://fonts.gstatic.com/s/shizuru/v10/O4ZSFGfvnxFiCA3i30IJlgUTj2A.ttf'
  },
  {
    label: 'Shojumaru',
    value: 'https://fonts.gstatic.com/s/shojumaru/v15/rax_HiWfutkLLnaKCtlMBBJek0vA8A.ttf'
  },
  {
    label: 'Short Stack',
    value: 'https://fonts.gstatic.com/s/shortstack/v15/bMrzmS2X6p0jZC6EcmPFX-SScX8D0nq6.ttf'
  },
  {
    label: 'Shrikhand',
    value: 'https://fonts.gstatic.com/s/shrikhand/v15/a8IbNovtLWfR7T7bMJwbBIiQ0zhMtA.ttf'
  },
  {
    label: 'Siemreap',
    value: 'https://fonts.gstatic.com/s/siemreap/v28/Gg82N5oFbgLvHAfNl2YbnA8DLXpe.ttf'
  },
  {
    label: 'Sigmar',
    value: 'https://fonts.gstatic.com/s/sigmar/v7/hv-XlzJgIE8a85pUbWY3MTFgVg.ttf'
  },
  {
    label: 'Sigmar One',
    value: 'https://fonts.gstatic.com/s/sigmarone/v18/co3DmWZ8kjZuErj9Ta3dk6Pjp3Di8U0.ttf'
  },
  {
    label: 'Signika',
    value: 'https://fonts.gstatic.com/s/signika/v25/vEF72_JTCgwQ5ejvMV0Ox_Kg1UwJ0tKfX4zNpD8E4ASzH1r9gTuYzTMngt4xjw.ttf'
  },
  {
    label: 'Signika Negative',
    value:
      'https://fonts.gstatic.com/s/signikanegative/v21/E21x_cfngu7HiRpPX3ZpNE4kY5zKSPmJXkF0VDD2RAqnS73st9hiuEq8.ttf'
  },
  {
    label: 'Silkscreen',
    value: 'https://fonts.gstatic.com/s/silkscreen/v4/m8JXjfVPf62XiF7kO-i9ULRvamODxdI.ttf'
  },
  {
    label: 'Simonetta',
    value: 'https://fonts.gstatic.com/s/simonetta/v27/x3dickHVYrCU5BU15c4BfPACvy_1BA.ttf'
  },
  {
    label: 'Single Day',
    value: 'https://fonts.gstatic.com/s/singleday/v17/LYjHdGDjlEgoAcF95EI5jVoFUNfeQJU.ttf'
  },
  {
    label: 'Sintony',
    value: 'https://fonts.gstatic.com/s/sintony/v15/XoHm2YDqR7-98cVUITQnu98ojjs.ttf'
  },
  {
    label: 'Sirin Stencil',
    value: 'https://fonts.gstatic.com/s/sirinstencil/v25/mem4YaWwznmLx-lzGfN7MdRydchGBq6al6o.ttf'
  },
  {
    label: 'Six Caps',
    value: 'https://fonts.gstatic.com/s/sixcaps/v20/6ae_4KGrU7VR7bNmabcS9XXaPCop.ttf'
  },
  {
    label: 'Sixtyfour',
    value:
      'https://fonts.gstatic.com/s/sixtyfour/v1/OD5vuMCT1numDm3nakXtp2h4jg463t9haG_3mBkVsV20uFT3BAE5f73YnyS5ZuOV.ttf'
  },
  {
    label: 'Skranji',
    value: 'https://fonts.gstatic.com/s/skranji/v13/OZpDg_dtriVFNerMYzuuklTm3Ek.ttf'
  },
  {
    label: 'Slabo 13px',
    value: 'https://fonts.gstatic.com/s/slabo13px/v15/11hEGp_azEvXZUdSBzzRcKer2wkYnvI.ttf'
  },
  {
    label: 'Slabo 27px',
    value: 'https://fonts.gstatic.com/s/slabo27px/v14/mFT0WbgBwKPR_Z4hGN2qsxgJ1EJ7i90.ttf'
  },
  {
    label: 'Slackey',
    value: 'https://fonts.gstatic.com/s/slackey/v28/N0bV2SdQO-5yM0-dKlRaJdbWgdY.ttf'
  },
  {
    label: 'Slackside One',
    value: 'https://fonts.gstatic.com/s/slacksideone/v10/EJRQQgMrXdcGsiBuvnRxodTwVy7VocNB6Iw.ttf'
  },
  {
    label: 'Smokum',
    value: 'https://fonts.gstatic.com/s/smokum/v28/TK3iWkUbAhopmrdGHjUHte5fKg.ttf'
  },
  {
    label: 'Smooch',
    value: 'https://fonts.gstatic.com/s/smooch/v7/o-0LIps4xW8U1xUBjqp_6hVdYg.ttf'
  },
  {
    label: 'Smooch Sans',
    value: 'https://fonts.gstatic.com/s/smoochsans/v13/c4mz1n5uGsXss2LJh1QH6b129FZvxPj6I4oiwUBodqIeNlzayg.ttf'
  },
  {
    label: 'Smythe',
    value: 'https://fonts.gstatic.com/s/smythe/v23/MwQ3bhT01--coT1BOLh_uGInjA.ttf'
  },
  {
    label: 'Sniglet',
    value: 'https://fonts.gstatic.com/s/sniglet/v17/cIf9MaFLtkE3UjaJxCmrYGkHgIs.ttf'
  },
  {
    label: 'Snippet',
    value: 'https://fonts.gstatic.com/s/snippet/v21/bWt47f7XfQH9Gupu2v_Afcp9QWc.ttf'
  },
  {
    label: 'Snowburst One',
    value: 'https://fonts.gstatic.com/s/snowburstone/v20/MQpS-WezKdujBsXY3B7I-UT7eZ-UPyacPbo.ttf'
  },
  {
    label: 'Sofadi One',
    value: 'https://fonts.gstatic.com/s/sofadione/v21/JIA2UVBxdnVBuElZaMFGcDOIETkmYDU.ttf'
  },
  {
    label: 'Sofia',
    value: 'https://fonts.gstatic.com/s/sofia/v14/8QIHdirahM3j_vu-sowsrqjk.ttf'
  },
  {
    label: 'Sofia Sans',
    value: 'https://fonts.gstatic.com/s/sofiasans/v16/Yq6E-LCVXSLy9uPBwlAThu1SY8Cx8rlT69B6sK3trvKCXl8k.ttf'
  },
  {
    label: 'Sofia Sans Condensed',
    value:
      'https://fonts.gstatic.com/s/sofiasanscondensed/v2/r05xGKVS5aVKd567NYXawnFKJaTtoAuLnK0EjiAN5s9CZwUqh-Gsl8QO3OfwQQ.ttf'
  },
  {
    label: 'Sofia Sans Extra Condensed',
    value:
      'https://fonts.gstatic.com/s/sofiasansextracondensed/v2/raxdHjafvdAIOju4GcIfJH0i7zi50X3zRtuLNiMS0d6iDr-MD5Si9NGLmmiEfzmM356GxA.ttf'
  },
  {
    label: 'Sofia Sans Semi Condensed',
    value:
      'https://fonts.gstatic.com/s/sofiasanssemicondensed/v4/46kOlaPnUDrQoNsWDCGXXxYlujh5Wv0nwP4RwxURm28cA7YLHsIVvoobEe9TGahllIhN.ttf'
  },
  {
    label: 'Solitreo',
    value: 'https://fonts.gstatic.com/s/solitreo/v2/r05YGLlS5a9KYsyNO8upyDYtStiJ.ttf'
  },
  {
    label: 'Solway',
    value: 'https://fonts.gstatic.com/s/solway/v18/AMOQz46Cs2uTAOCWgnA9kuYMUg.ttf'
  },
  {
    label: 'Sometype Mono',
    value: 'https://fonts.gstatic.com/s/sometypemono/v1/70lGu745KGk_R3uxyq0WrROhAJiJsJ_eTWllpTAMGE9agQBbs7uG.ttf'
  },
  {
    label: 'Song Myung',
    value: 'https://fonts.gstatic.com/s/songmyung/v20/1cX2aUDWAJH5-EIC7DIhr1GqhcitzeM.ttf'
  },
  {
    label: 'Sono',
    value: 'https://fonts.gstatic.com/s/sono/v6/aFT97PNiY3U2Cqf_aYEN64CYaK18YWJEsV6u-QLiOsxVtkWdEnR4qYeB4Q.ttf'
  },
  {
    label: 'Sonsie One',
    value: 'https://fonts.gstatic.com/s/sonsieone/v21/PbymFmP_EAnPqbKaoc18YVu80lbp8JM.ttf'
  },
  {
    label: 'Sora',
    value: 'https://fonts.gstatic.com/s/sora/v12/xMQOuFFYT72X5wkB_18qmnndmSdSnn-KIwNhBti0.ttf'
  },
  {
    label: 'Sorts Mill Goudy',
    value: 'https://fonts.gstatic.com/s/sortsmillgoudy/v15/Qw3GZR9MED_6PSuS_50nEaVrfzgEXH0OjpM75PE.ttf'
  },
  {
    label: 'Source Code Pro',
    value: 'https://fonts.gstatic.com/s/sourcecodepro/v23/HI_diYsKILxRpg3hIP6sJ7fM7PqPMcMnZFqUwX28DMyQhM5hTXUcdJg.ttf'
  },
  {
    label: 'Source Sans 3',
    value: 'https://fonts.gstatic.com/s/sourcesans3/v15/nwpBtKy2OAdR1K-IwhWudF-R9QMylBJAV3Bo8Ky461EN_io6npfB.ttf'
  },
  {
    label: 'Source Serif 4',
    value:
      'https://fonts.gstatic.com/s/sourceserif4/v8/vEFy2_tTDB4M7-auWDN0ahZJW3IX2ih5nk3AucvUHf6OAVIJmeUDygwjihdqrhxXD-wGvjU.ttf'
  },
  {
    label: 'Space Grotesk',
    value: 'https://fonts.gstatic.com/s/spacegrotesk/v16/V8mQoQDjQSkFtoMM3T6r8E7mF71Q-gOoraIAEj7oUUsjNsFjTDJK.ttf'
  },
  {
    label: 'Space Mono',
    value: 'https://fonts.gstatic.com/s/spacemono/v13/i7dPIFZifjKcF5UAWdDRUEZ2RFq7AwU.ttf'
  },
  {
    label: 'Special Elite',
    value: 'https://fonts.gstatic.com/s/specialelite/v18/XLYgIZbkc4JPUL5CVArUVL0nhncESXFtUsM.ttf'
  },
  {
    label: 'Spectral',
    value: 'https://fonts.gstatic.com/s/spectral/v13/rnCr-xNNww_2s0amA-M-mHnOSOuk.ttf'
  },
  {
    label: 'Spectral SC',
    value: 'https://fonts.gstatic.com/s/spectralsc/v12/KtkpALCRZonmalTgyPmRfvWi6WDfFpuc.ttf'
  },
  {
    label: 'Spicy Rice',
    value: 'https://fonts.gstatic.com/s/spicyrice/v25/uK_24rSEd-Uqwk4jY1RyGv-2WkowRcc.ttf'
  },
  {
    label: 'Spinnaker',
    value: 'https://fonts.gstatic.com/s/spinnaker/v19/w8gYH2oyX-I0_rvR6Hmn3HwLqOqSBg.ttf'
  },
  {
    label: 'Spirax',
    value: 'https://fonts.gstatic.com/s/spirax/v21/buE3poKgYNLy0F3cXktt-Csn-Q.ttf'
  },
  {
    label: 'Splash',
    value: 'https://fonts.gstatic.com/s/splash/v6/KtksAL2RZoDkbU6hpPPGNdS6wg.ttf'
  },
  {
    label: 'Spline Sans',
    value: 'https://fonts.gstatic.com/s/splinesans/v10/_6_sED73Uf-2WfU2LzycEZousNzn1a1lKWRpOFnYEtvlUfE2kw.ttf'
  },
  {
    label: 'Spline Sans Mono',
    value: 'https://fonts.gstatic.com/s/splinesansmono/v10/R70MjzAei_CDNLfgZxrW6wrZOF2WdZ6xabUGSVtNuGBiMrtVy4d4dGb1.ttf'
  },
  {
    label: 'Squada One',
    value: 'https://fonts.gstatic.com/s/squadaone/v18/BCasqZ8XsOrx4mcOk6MtWaA8WDBkHgs.ttf'
  },
  {
    label: 'Square Peg',
    value: 'https://fonts.gstatic.com/s/squarepeg/v5/y83eW48Nzw6ZlUHc-phrBDHrHHfrFPE.ttf'
  },
  {
    label: 'Sree Krushnadevaraya',
    value: 'https://fonts.gstatic.com/s/sreekrushnadevaraya/v21/R70FjzQeifmPepmyQQjQ9kvwMkWYPfTA_EWb2FhQuXir.ttf'
  },
  {
    label: 'Sriracha',
    value: 'https://fonts.gstatic.com/s/sriracha/v14/0nkrC9D4IuYBgWcI9ObYRQDioeb0.ttf'
  },
  {
    label: 'Srisakdi',
    value: 'https://fonts.gstatic.com/s/srisakdi/v16/yMJRMIlvdpDbkB0A-jq8fSx5i814.ttf'
  },
  {
    label: 'Staatliches',
    value: 'https://fonts.gstatic.com/s/staatliches/v13/HI_OiY8KO6hCsQSoAPmtMbectJG9O9PS.ttf'
  },
  {
    label: 'Stalemate',
    value: 'https://fonts.gstatic.com/s/stalemate/v22/taiIGmZ_EJq97-UfkZRpuqSs8ZQpaQ.ttf'
  },
  {
    label: 'Stalinist One',
    value: 'https://fonts.gstatic.com/s/stalinistone/v56/MQpS-WezM9W4Dd7D3B7I-UT7eZ-UPyacPbo.ttf'
  },
  {
    label: 'Stardos Stencil',
    value: 'https://fonts.gstatic.com/s/stardosstencil/v15/X7n94bcuGPC8hrvEOHXOgaKCc2TR71R3tiSx0g.ttf'
  },
  {
    label: 'Stick',
    value: 'https://fonts.gstatic.com/s/stick/v17/Qw3TZQpMCyTtJSvfvPVDMPoF.ttf'
  },
  {
    label: 'Stick No Bills',
    value: 'https://fonts.gstatic.com/s/sticknobills/v15/bWts7ffXZwHuAa9Uld-oEK4QKlxj9f9t_7uEmjcVv8Q7KriwKhcTKA.ttf'
  },
  {
    label: 'Stint Ultra Condensed',
    value: 'https://fonts.gstatic.com/s/stintultracondensed/v23/-W_gXIrsVjjeyEnPC45qD2NoFPtBE0xCh2A-qhUO2cNvdg.ttf'
  },
  {
    label: 'Stint Ultra Expanded',
    value: 'https://fonts.gstatic.com/s/stintultraexpanded/v22/CSRg4yNNh-GbW3o3JkwoDcdvMKMf0oBAd0qoATQkWwam.ttf'
  },
  {
    label: 'Stoke',
    value: 'https://fonts.gstatic.com/s/stoke/v24/z7NadRb7aTMfKONpfihK1YTV.ttf'
  },
  {
    label: 'Strait',
    value: 'https://fonts.gstatic.com/s/strait/v17/DtViJxy6WaEr1LZzeDhtkl0U7w.ttf'
  },
  {
    label: 'Style Script',
    value: 'https://fonts.gstatic.com/s/stylescript/v11/vm8xdRX3SV7Z0aPa88xzW5npeFT76NZnMw.ttf'
  },
  {
    label: 'Stylish',
    value: 'https://fonts.gstatic.com/s/stylish/v22/m8JSjfhPYriQkk7-fo35dLxEdmo.ttf'
  },
  {
    label: 'Sue Ellen Francisco',
    value: 'https://fonts.gstatic.com/s/sueellenfrancisco/v20/wXK3E20CsoJ9j1DDkjHcQ5ZL8xRaxru9ropF2lqk9H4.ttf'
  },
  {
    label: 'Suez One',
    value: 'https://fonts.gstatic.com/s/suezone/v13/taiJGmd_EZ6rqscQgNFJkIqg-I0w.ttf'
  },
  {
    label: 'Sulphur Point',
    value: 'https://fonts.gstatic.com/s/sulphurpoint/v15/RLp5K5vv8KaycDcazWFPBj2aRfkSu6EuTHo.ttf'
  },
  {
    label: 'Sumana',
    value: 'https://fonts.gstatic.com/s/sumana/v10/4UaDrE5TqRBjGj-G8Bji76zR4w.ttf'
  },
  {
    label: 'Sunflower',
    value: 'https://fonts.gstatic.com/s/sunflower/v16/RWmPoKeF8fUjqIj7Vc-0sMbiqYsGBGBzCw.ttf'
  },
  {
    label: 'Sunshiney',
    value: 'https://fonts.gstatic.com/s/sunshiney/v24/LDIwapGTLBwsS-wT4vcgE8moUePWkg.ttf'
  },
  {
    label: 'Supermercado One',
    value: 'https://fonts.gstatic.com/s/supermercadoone/v26/OpNXnpQWg8jc_xps_Gi14kVVEXOn60b3MClBRTs.ttf'
  },
  {
    label: 'Sura',
    value: 'https://fonts.gstatic.com/s/sura/v19/SZc23FL5PbyzFf5UWzXtjUM.ttf'
  },
  {
    label: 'Suranna',
    value: 'https://fonts.gstatic.com/s/suranna/v13/gokuH6ztGkFjWe58tBRZT2KmgP0.ttf'
  },
  {
    label: 'Suravaram',
    value: 'https://fonts.gstatic.com/s/suravaram/v21/_gP61R_usiY7SCym4xIAi261Qv9roQ.ttf'
  },
  {
    label: 'Suwannaphum',
    value: 'https://fonts.gstatic.com/s/suwannaphum/v31/jAnCgHV7GtDvc8jbe8hXXIWl_8C0Wg2V.ttf'
  },
  {
    label: 'Swanky and Moo Moo',
    value: 'https://fonts.gstatic.com/s/swankyandmoomoo/v22/flUlRrKz24IuWVI_WJYTYcqbEsMUZ3kUtbPkR64SYQ.ttf'
  },
  {
    label: 'Syncopate',
    value: 'https://fonts.gstatic.com/s/syncopate/v21/pe0sMIuPIYBCpEV5eFdyAv2-C99ycg.ttf'
  },
  {
    label: 'Syne',
    value: 'https://fonts.gstatic.com/s/syne/v22/8vIS7w4qzmVxsWxjBZRjr0FKM_04uT6kR47NCV5Z.ttf'
  },
  {
    label: 'Syne Mono',
    value: 'https://fonts.gstatic.com/s/synemono/v15/K2FzfZNHj_FHBmRbFvHzIqCkDyvqZA.ttf'
  },
  {
    label: 'Syne Tactile',
    value: 'https://fonts.gstatic.com/s/synetactile/v15/11hGGpna2UTQKjMCVzjAPMKh3ysdjvKU8Q.ttf'
  },
  {
    label: 'Tac One',
    value: 'https://fonts.gstatic.com/s/tacone/v4/ahcZv8Cj3zw7qDr8fO4hU-FwnU0.ttf'
  },
  {
    label: 'Tai Heritage Pro',
    value: 'https://fonts.gstatic.com/s/taiheritagepro/v6/sZlfdQid-zgaNiNIYcUzJMU3IYyNoHxSENxuLuE.ttf'
  },
  {
    label: 'Tajawal',
    value: 'https://fonts.gstatic.com/s/tajawal/v9/Iura6YBj_oCad4k1rzaLCr5IlLA.ttf'
  },
  {
    label: 'Tangerine',
    value: 'https://fonts.gstatic.com/s/tangerine/v17/IurY6Y5j_oScZZow4VOBDpxNhLBQ4Q.ttf'
  },
  {
    label: 'Tapestry',
    value: 'https://fonts.gstatic.com/s/tapestry/v4/SlGTmQecrosEYXhaGBIkqnB6aSQU.ttf'
  },
  {
    label: 'Taprom',
    value: 'https://fonts.gstatic.com/s/taprom/v27/UcCn3F82JHycULbFQyk3-0kvHg.ttf'
  },
  {
    label: 'Tauri',
    value: 'https://fonts.gstatic.com/s/tauri/v18/TwMA-IISS0AM3IpVWHU_TBqO.ttf'
  },
  {
    label: 'Taviraj',
    value: 'https://fonts.gstatic.com/s/taviraj/v13/ahcZv8Cj3ylylTXzfO4hU-FwnU0.ttf'
  },
  {
    label: 'Teachers',
    value: 'https://fonts.gstatic.com/s/teachers/v4/H4c5BXKVncXVmUGsgTwx4E9oHx2xfjBr3powY55O4AS32A.ttf'
  },
  {
    label: 'Teko',
    value: 'https://fonts.gstatic.com/s/teko/v20/LYjYdG7kmE0gV69VVPPdFl06VN8XG7Sy3TKEvkCF.ttf'
  },
  {
    label: 'Tektur',
    value: 'https://fonts.gstatic.com/s/tektur/v3/XoHN2YHtS7q969kXCjzlV0aSkS_o8OacmTe0TYlYFot8TrwuVYtOY8P7TWd0.ttf'
  },
  {
    label: 'Telex',
    value: 'https://fonts.gstatic.com/s/telex/v17/ieVw2Y1fKWmIO9fTB1piKFIf.ttf'
  },
  {
    label: 'Tenali Ramakrishna',
    value: 'https://fonts.gstatic.com/s/tenaliramakrishna/v12/raxgHj6Yt9gAN3LLKs0BZVMo8jmwn1-8KJXqUFFvtA.ttf'
  },
  {
    label: 'Tenor Sans',
    value: 'https://fonts.gstatic.com/s/tenorsans/v19/bx6ANxqUneKx06UkIXISr3JyC22IyqI.ttf'
  },
  {
    label: 'Text Me One',
    value: 'https://fonts.gstatic.com/s/textmeone/v24/i7dOIFdlayuLUvgoFvHQFWZcalayGhyV.ttf'
  },
  {
    label: 'Texturina',
    value:
      'https://fonts.gstatic.com/s/texturina/v28/c4mM1nxpEtL3pXiAulRTkY-HGmNEX1b9NspjMwhAgliHhVrXy2eYGvUg25riW1OD.ttf'
  },
  {
    label: 'Thasadith',
    value: 'https://fonts.gstatic.com/s/thasadith/v11/mtG44_1TIqPYrd_f5R1YsEkU0CWuFw.ttf'
  },
  {
    label: 'The Girl Next Door',
    value: 'https://fonts.gstatic.com/s/thegirlnextdoor/v22/pe0zMJCIMIsBjFxqYBIcZ6_OI5oFHCYIV7t7w6bE2A.ttf'
  },
  {
    label: 'The Nautigal',
    value: 'https://fonts.gstatic.com/s/thenautigal/v6/VdGZAZ8ZH51Lvng9fQV2bfKr5wVk09Se5Q.ttf'
  },
  {
    label: 'Tienne',
    value: 'https://fonts.gstatic.com/s/tienne/v20/AYCKpX7pe9YCRP0LkEPHSFNyxw.ttf'
  },
  {
    label: 'Tillana',
    value: 'https://fonts.gstatic.com/s/tillana/v13/VuJxdNvf35P4qJ1OeKbXOIFneRo.ttf'
  },
  {
    label: 'Tilt Neon',
    value:
      'https://fonts.gstatic.com/s/tiltneon/v10/E21L_d7gguXdwD9LEFY2WCeElCNtd-eBqpHp1TzrkJSmwpj5ndxquXK9WualJ9DS.ttf'
  },
  {
    label: 'Tilt Prism',
    value:
      'https://fonts.gstatic.com/s/tiltprism/v11/5h11iZgyPHoZ3YikNzWGfWey2dCAZXT-bH9V4VGn-FJ7tLI25oc_rIbwoTSrn86NKw.ttf'
  },
  {
    label: 'Tilt Warp',
    value:
      'https://fonts.gstatic.com/s/tiltwarp/v12/AlZc_zVDs5XpmO7yn3w7flUoytXJp3z29uEwmEMLEJljLXvT8UJSZTBxAVfMGOPb.ttf'
  },
  {
    label: 'Timmana',
    value: 'https://fonts.gstatic.com/s/timmana/v12/6xKvdShfL9yK-rvpCmvbKHwJUOM.ttf'
  },
  {
    label: 'Tinos',
    value: 'https://fonts.gstatic.com/s/tinos/v24/buE4poGnedXvwgX8dGVh8TI-.ttf'
  },
  {
    label: 'Tiny5',
    value: 'https://fonts.gstatic.com/s/tiny5/v1/KFOpCnmCvdGT7hw-z0hHAi88.ttf'
  },
  {
    label: 'Tiro Bangla',
    value: 'https://fonts.gstatic.com/s/tirobangla/v6/IFSgHe1Tm95E3O8b5i2V8MG9-UPeuz4i.ttf'
  },
  {
    label: 'Tiro Devanagari Hindi',
    value: 'https://fonts.gstatic.com/s/tirodevanagarihindi/v5/55xyezN7P8T4e0_CfIJrwdodg9HoYw0i-M9fSOkOfG0Y3A.ttf'
  },
  {
    label: 'Tiro Devanagari Marathi',
    value: 'https://fonts.gstatic.com/s/tirodevanagarimarathi/v5/fC1xPZBSZHrRhS3rd4M0MAPNJUHl4znXCxAkotDrDJYM2lAZ.ttf'
  },
  {
    label: 'Tiro Devanagari Sanskrit',
    value:
      'https://fonts.gstatic.com/s/tirodevanagarisanskrit/v5/MCoAzBbr09vVUgVBM8FWu_yZdZkhkg-I0nUlb59pEoEqgtOh0w.ttf'
  },
  {
    label: 'Tiro Gurmukhi',
    value: 'https://fonts.gstatic.com/s/tirogurmukhi/v6/x3dmckXSYq-Uqjc048JUF7Jvly7HAQsyA2Y.ttf'
  },
  {
    label: 'Tiro Kannada',
    value: 'https://fonts.gstatic.com/s/tirokannada/v6/CSR44ztKmvqaDxEDJFY7CIYKSPl6tOU9Eg.ttf'
  },
  {
    label: 'Tiro Tamil',
    value: 'https://fonts.gstatic.com/s/tirotamil/v10/m8JXjfVIf7OT22n3M-S_ULRvamODxdI.ttf'
  },
  {
    label: 'Tiro Telugu',
    value: 'https://fonts.gstatic.com/s/tirotelugu/v7/aFTQ7PxlZWk2EPiSymjXdKSNQqn0X0BO.ttf'
  },
  {
    label: 'Titan One',
    value: 'https://fonts.gstatic.com/s/titanone/v15/mFTzWbsGxbbS_J5cQcjykzIn2Etikg.ttf'
  },
  {
    label: 'Titillium Web',
    value: 'https://fonts.gstatic.com/s/titilliumweb/v17/NaPecZTIAOhVxoMyOr9n_E7fRMTsDIRSfr0.ttf'
  },
  {
    label: 'Tomorrow',
    value: 'https://fonts.gstatic.com/s/tomorrow/v17/WBLmrETNbFtZCeGqgSXVcWHALdio.ttf'
  },
  {
    label: 'Tourney',
    value: 'https://fonts.gstatic.com/s/tourney/v13/AlZa_ztDtYzv1tzq1wcJnbVt7xseomk-tNs7qrzTWbyt8n7GOQFyZTp1I1LcGA.ttf'
  },
  {
    label: 'Trade Winds',
    value: 'https://fonts.gstatic.com/s/tradewinds/v17/AYCPpXPpYNIIT7h8-QenM3Jq7PKP5Z_G.ttf'
  },
  {
    label: 'Train One',
    value: 'https://fonts.gstatic.com/s/trainone/v13/gyB-hwkiNtc6KnxUVjWHOqbZRY7JVQ.ttf'
  },
  {
    label: 'Trirong',
    value: 'https://fonts.gstatic.com/s/trirong/v15/7r3GqXNgp8wxdOdOr4wi2aZg-ug.ttf'
  },
  {
    label: 'Trispace',
    value:
      'https://fonts.gstatic.com/s/trispace/v24/Yq65-LKSQC3o56LxxgRrtA6yBqsrXL5GI5KI-IUZVGsxWFIlbH9roQl0zHugpt0.ttf'
  },
  {
    label: 'Trocchi',
    value: 'https://fonts.gstatic.com/s/trocchi/v17/qWcqB6WkuIDxDZLcDrtUvMeTYD0.ttf'
  },
  {
    label: 'Trochut',
    value: 'https://fonts.gstatic.com/s/trochut/v22/CHyjV-fDDlP9bDIw5nSIfVIPLns.ttf'
  },
  {
    label: 'Truculenta',
    value:
      'https://fonts.gstatic.com/s/truculenta/v22/LhWfMVvBKusVIfNYGi1-WvRVyDdZeeiySNppcu32Mb2f06y6Oa21F6XHi0VYDX_PzOupMlAiswcFHnJMMhg.ttf'
  },
  {
    label: 'Trykker',
    value: 'https://fonts.gstatic.com/s/trykker/v21/KtktALyWZJXudUPzhNnoOd2j22U.ttf'
  },
  {
    label: 'Tsukimi Rounded',
    value: 'https://fonts.gstatic.com/s/tsukimirounded/v10/sJoc3LJNksWZO0LvnZwkF3HtoB7tPXMOP5gP1A.ttf'
  },
  {
    label: 'Tulpen One',
    value: 'https://fonts.gstatic.com/s/tulpenone/v25/dFa6ZfeC474skLgesc0CWj0w_HyIRlE.ttf'
  },
  {
    label: 'Turret Road',
    value: 'https://fonts.gstatic.com/s/turretroad/v9/pxiAypMgpcBFjE84Zv-fE3tFOvODSVFF.ttf'
  },
  {
    label: 'Twinkle Star',
    value: 'https://fonts.gstatic.com/s/twinklestar/v6/pe0pMI6IL4dPoFl9LGEmY6WaA_Rue1UwVg.ttf'
  },
  {
    label: 'Ubuntu',
    value: 'https://fonts.gstatic.com/s/ubuntu/v20/4iCs6KVjbNBYlgo6eAT3v02QFg.ttf'
  },
  {
    label: 'Ubuntu Condensed',
    value: 'https://fonts.gstatic.com/s/ubuntucondensed/v16/u-4k0rCzjgs5J7oXnJcM_0kACGMtf-fVqvHoJXw.ttf'
  },
  {
    label: 'Ubuntu Mono',
    value: 'https://fonts.gstatic.com/s/ubuntumono/v17/KFOjCneDtsqEr0keqCMhbBc9AMX6lJBP.ttf'
  },
  {
    label: 'Ubuntu Sans',
    value:
      'https://fonts.gstatic.com/s/ubuntusans/v2/co3omWd6mSRtB7_9UaLWwJnCq5ALePfPu1tPcW235R53LqrCPWbujzt6CfqLVLT9mgk.ttf'
  },
  {
    label: 'Ubuntu Sans Mono',
    value: 'https://fonts.gstatic.com/s/ubuntusansmono/v1/jVyc7mzgBHrR5yE7ZyRg0QRJMKI4zAbgjc1t-pKe27Ev_kYRiqcZu3n0.ttf'
  },
  {
    label: 'Uchen',
    value: 'https://fonts.gstatic.com/s/uchen/v9/nKKZ-GokGZ1baIaSEQGodLxA.ttf'
  },
  {
    label: 'Ultra',
    value: 'https://fonts.gstatic.com/s/ultra/v23/zOLy4prXmrtY-tT6yLOD6NxF.ttf'
  },
  {
    label: 'Unbounded',
    value: 'https://fonts.gstatic.com/s/unbounded/v7/Yq6F-LOTXCb04q32xlpat-6uR42XTqtG6xjx043HgP6LR0Y.ttf'
  },
  {
    label: 'Uncial Antiqua',
    value: 'https://fonts.gstatic.com/s/uncialantiqua/v20/N0bM2S5WOex4OUbESzoESK-i-PfRS5VBBSSF.ttf'
  },
  {
    label: 'Underdog',
    value: 'https://fonts.gstatic.com/s/underdog/v23/CHygV-jCElj7diMroVSiU14GN2Il.ttf'
  },
  {
    label: 'Unica One',
    value: 'https://fonts.gstatic.com/s/unicaone/v18/DPEuYwWHyAYGVTSmalshdtffuEY7FA.ttf'
  },
  {
    label: 'UnifrakturMaguntia',
    value: 'https://fonts.gstatic.com/s/unifrakturmaguntia/v20/WWXPlieVYwiGNomYU-ciRLRvEmK7oaVun2xNNgNa1A.ttf'
  },
  {
    label: 'Unkempt',
    value: 'https://fonts.gstatic.com/s/unkempt/v21/2EbnL-Z2DFZue0DSSYYf8z2Yt_c.ttf'
  },
  {
    label: 'Unlock',
    value: 'https://fonts.gstatic.com/s/unlock/v26/7Au-p_8ykD-cDl7GKAjSwkUVOQ.ttf'
  },
  {
    label: 'Unna',
    value: 'https://fonts.gstatic.com/s/unna/v23/AYCEpXzofN0NCpgBlGHCWFM.ttf'
  },
  {
    label: 'Updock',
    value: 'https://fonts.gstatic.com/s/updock/v5/nuF4D_3dVZ70UI9SjLK3602XBw.ttf'
  },
  {
    label: 'Urbanist',
    value: 'https://fonts.gstatic.com/s/urbanist/v15/L0xjDF02iFML4hGCyOCpRdycFsGxSrqDyx4fFpOrS8SlKw.ttf'
  },
  {
    label: 'VT323',
    value: 'https://fonts.gstatic.com/s/vt323/v17/pxiKyp0ihIEF2hsYHpT2dkNE.ttf'
  },
  {
    label: 'Vampiro One',
    value: 'https://fonts.gstatic.com/s/vampiroone/v18/gokqH6DoDl5yXvJytFsdLkqnsvhIor3K.ttf'
  },
  {
    label: 'Varela',
    value: 'https://fonts.gstatic.com/s/varela/v16/DPEtYwqExx0AWHXJBBQFfvzDsQ.ttf'
  },
  {
    label: 'Varela Round',
    value: 'https://fonts.gstatic.com/s/varelaround/v20/w8gdH283Tvk__Lua32TysjIvoMGOD9gxZw.ttf'
  },
  {
    label: 'Varta',
    value: 'https://fonts.gstatic.com/s/varta/v21/Qw3AZQpJHj_6LzHUngWbrFkDH1x9tD4EirE-9PGLfQ.ttf'
  },
  {
    label: 'Vast Shadow',
    value: 'https://fonts.gstatic.com/s/vastshadow/v19/pe0qMImKOZ1V62ZwbVY9dfe6Kdpickwp.ttf'
  },
  {
    label: 'Vazirmatn',
    value: 'https://fonts.gstatic.com/s/vazirmatn/v13/Dxx78j6PP2D_kU2muijPEe1n2vVbfJRklWgzOReZ72DF_QY.ttf'
  },
  {
    label: 'Vesper Libre',
    value: 'https://fonts.gstatic.com/s/vesperlibre/v19/bx6CNxyWnf-uxPdXDHUD_Rd4D0-N2qIWVQ.ttf'
  },
  {
    label: 'Viaoda Libre',
    value: 'https://fonts.gstatic.com/s/viaodalibre/v18/vEFW2_lWCgoR6OKuRz9kcRVJb2IY2tOHXg.ttf'
  },
  {
    label: 'Vibes',
    value: 'https://fonts.gstatic.com/s/vibes/v14/QdVYSTsmIB6tmbd3HpbsuBlh.ttf'
  },
  {
    label: 'Vibur',
    value: 'https://fonts.gstatic.com/s/vibur/v23/DPEiYwmEzw0QRjTpLjoJd-Xa.ttf'
  },
  {
    label: 'Victor Mono',
    value: 'https://fonts.gstatic.com/s/victormono/v3/Yq6H-LGQWyfv-LGy7lEO09xRn-T81AVB_tCyO87NhNyOV0Y9bQ.ttf'
  },
  {
    label: 'Vidaloka',
    value: 'https://fonts.gstatic.com/s/vidaloka/v18/7cHrv4c3ipenMKlEass8yn4hnCci.ttf'
  },
  {
    label: 'Viga',
    value: 'https://fonts.gstatic.com/s/viga/v14/xMQbuFFdSaiX_QIjD4e2OX8.ttf'
  },
  {
    label: 'Vina Sans',
    value: 'https://fonts.gstatic.com/s/vinasans/v6/m8JQjfZKf6-d2273MP7zcJ5BZmqa3A.ttf'
  },
  {
    label: 'Voces',
    value: 'https://fonts.gstatic.com/s/voces/v22/-F6_fjJyLyU8d4PBBG7YpzlJ.ttf'
  },
  {
    label: 'Volkhov',
    value: 'https://fonts.gstatic.com/s/volkhov/v17/SlGQmQieoJcKemNeQTIOhHxzcD0.ttf'
  },
  {
    label: 'Vollkorn',
    value: 'https://fonts.gstatic.com/s/vollkorn/v23/0ybgGDoxxrvAnPhYGzMlQLzuMasz6Df2MHGuGWOdEbD63w.ttf'
  },
  {
    label: 'Vollkorn SC',
    value: 'https://fonts.gstatic.com/s/vollkornsc/v11/j8_v6-zQ3rXpceZj9cqnVhF5NH-iSq_E.ttf'
  },
  {
    label: 'Voltaire',
    value: 'https://fonts.gstatic.com/s/voltaire/v21/1Pttg8PcRfSblAvGvQooYKVnBOif.ttf'
  },
  {
    label: 'Vujahday Script',
    value: 'https://fonts.gstatic.com/s/vujahdayscript/v8/RWmQoKGA8fEkrIPtSZ3_J7er2dUiDEtvAlaMKw.ttf'
  },
  {
    label: 'Waiting for the Sunrise',
    value: 'https://fonts.gstatic.com/s/waitingforthesunrise/v20/WBL1rFvOYl9CEv2i1mO6KUW8RKWJ2zoXoz5JsYZQ9h_ZYk5J.ttf'
  },
  {
    label: 'Wallpoet',
    value: 'https://fonts.gstatic.com/s/wallpoet/v20/f0X10em2_8RnXVVdUNbu7cXP8L8G.ttf'
  },
  {
    label: 'Walter Turncoat',
    value: 'https://fonts.gstatic.com/s/walterturncoat/v23/snfys0Gs98ln43n0d-14ULoToe67YB2dQ5ZPqQ.ttf'
  },
  {
    label: 'Warnes',
    value: 'https://fonts.gstatic.com/s/warnes/v27/pONn1hc0GsW6sW5OpiC2o6Lkqg.ttf'
  },
  {
    label: 'Water Brush',
    value: 'https://fonts.gstatic.com/s/waterbrush/v4/AYCPpXPqc8cJWLhp4hywKHJq7PKP5Z_G.ttf'
  },
  {
    label: 'Waterfall',
    value: 'https://fonts.gstatic.com/s/waterfall/v6/MCoRzAfo293fACdFKcwY2rH8D_EZwA.ttf'
  },
  {
    label: 'Wavefont',
    value:
      'https://fonts.gstatic.com/s/wavefont/v10/L0xFDF00m0cP6hefyOCpRezQNuizSrqDyx8FHbFu21B3L4m0SEzuQYwq-f_JJ8I1WI3V07DHXKtOXOg4.ttf'
  },
  {
    label: 'Wellfleet',
    value: 'https://fonts.gstatic.com/s/wellfleet/v22/nuF7D_LfQJb3VYgX6eyT42aLDhO2HA.ttf'
  },
  {
    label: 'Wendy One',
    value: 'https://fonts.gstatic.com/s/wendyone/v18/2sDcZGJOipXfgfXV5wgDb2-4C7wFZQ.ttf'
  },
  {
    label: 'Whisper',
    value: 'https://fonts.gstatic.com/s/whisper/v5/q5uHsoqtKftx74K9milCBxxdmYU.ttf'
  },
  {
    label: 'WindSong',
    value: 'https://fonts.gstatic.com/s/windsong/v11/KR1WBsyu-P-GFEW57r95HdG6vjH3.ttf'
  },
  {
    label: 'Wire One',
    value: 'https://fonts.gstatic.com/s/wireone/v28/qFdH35Wah5htUhV75WGiWdrCwwcJ.ttf'
  },
  {
    label: 'Wittgenstein',
    value: 'https://fonts.gstatic.com/s/wittgenstein/v1/WBL3rEDOakJCHParhXGwMgvyJ8hdWNLC1kI61G4T_Bv7Z15J4ow.ttf'
  },
  {
    label: 'Wix Madefor Display',
    value:
      'https://fonts.gstatic.com/s/wixmadefordisplay/v10/SZcS3EX9IbbyeJ8aOluD52KXgUA_7Ed1I13G853Cp9duUYFhYltkv_3HQKgh.ttf'
  },
  {
    label: 'Wix Madefor Text',
    value: 'https://fonts.gstatic.com/s/wixmadefortext/v13/-W_oXI_oSymQ8Qj-Apx3HGN_Hu1RTCk5FtSDETgf0cK_NOeFgpRt9rN5.ttf'
  },
  {
    label: 'Work Sans',
    value: 'https://fonts.gstatic.com/s/worksans/v19/QGY_z_wNahGAdqQ43RhVcIgYT2Xz5u32K0nXNigDp6_cOyA.ttf'
  },
  {
    label: 'Workbench',
    value:
      'https://fonts.gstatic.com/s/workbench/v1/FeV8S05Gp6Et7FcfbPFK1rynGd_MxtkvNFmoUDFhgF2VKTGQk6vapdOL0GKqgZyb.ttf'
  },
  {
    label: 'Xanh Mono',
    value: 'https://fonts.gstatic.com/s/xanhmono/v18/R70YjykVmvKCep-vWhSYmACQXzLhTg.ttf'
  },
  {
    label: 'Yaldevi',
    value: 'https://fonts.gstatic.com/s/yaldevi/v12/cY9afj6VW0NMrDWtDNzCOwlPMq9SLpdxJzvobxLCBJkS.ttf'
  },
  {
    label: 'Yanone Kaffeesatz',
    value:
      'https://fonts.gstatic.com/s/yanonekaffeesatz/v30/3y9I6aknfjLm_3lMKjiMgmUUYBs04aUXNxt9gW2LIfto9tWpcGuLCnXkVA.ttf'
  },
  {
    label: 'Yantramanav',
    value: 'https://fonts.gstatic.com/s/yantramanav/v13/flU8Rqu5zY00QEpyWJYWN6f0V-dRCQ41.ttf'
  },
  {
    label: 'Yarndings 12',
    value: 'https://fonts.gstatic.com/s/yarndings12/v2/55xreyp2N8T5P2LJbZAlkY9c8ZLMI2VUnQ.ttf'
  },
  {
    label: 'Yarndings 12 Charted',
    value: 'https://fonts.gstatic.com/s/yarndings12charted/v2/eLGDP_DlKhO-DUfeqM4I_vDdJgmIh7hAvvbJ0t-dHaJH.ttf'
  },
  {
    label: 'Yarndings 20',
    value: 'https://fonts.gstatic.com/s/yarndings20/v2/TuGWUVlkUohEQu8l7K8b-vNFB380PMTK1w.ttf'
  },
  {
    label: 'Yarndings 20 Charted',
    value: 'https://fonts.gstatic.com/s/yarndings20charted/v2/QldRNSdbpg0G8vh0W2qxe0l-hcUPtY2VaLQm4UTqz5V9.ttf'
  },
  {
    label: 'Yatra One',
    value: 'https://fonts.gstatic.com/s/yatraone/v14/C8ch4copsHzj8p7NaF0xw1OBbRDvXw.ttf'
  },
  {
    label: 'Yellowtail',
    value: 'https://fonts.gstatic.com/s/yellowtail/v22/OZpGg_pnoDtINPfRIlLotlzNwED-b4g.ttf'
  },
  {
    label: 'Yeon Sung',
    value: 'https://fonts.gstatic.com/s/yeonsung/v21/QldMNTpbohAGtsJvUn6xSVNazqx2xg.ttf'
  },
  {
    label: 'Yeseva One',
    value: 'https://fonts.gstatic.com/s/yesevaone/v22/OpNJno4ck8vc-xYpwWWxpipfWhXD00c.ttf'
  },
  {
    label: 'Yesteryear',
    value: 'https://fonts.gstatic.com/s/yesteryear/v18/dg4g_p78rroaKl8kRKo1r7wHTwonmyw.ttf'
  },
  {
    label: 'Yomogi',
    value: 'https://fonts.gstatic.com/s/yomogi/v11/VuJwdNrS2ZL7rpoPWIz5NIh-YA.ttf'
  },
  {
    label: 'Young Serif',
    value: 'https://fonts.gstatic.com/s/youngserif/v2/3qTpojO2nS2VtkB3KtkQZ2t61EcYaQ7F.ttf'
  },
  {
    label: 'Yrsa',
    value: 'https://fonts.gstatic.com/s/yrsa/v20/wlprgwnQFlxs_wD3CFSMYmFaaCieSNNV9rRPfrKu.ttf'
  },
  {
    label: 'Ysabeau',
    value: 'https://fonts.gstatic.com/s/ysabeau/v2/kmKiZqEiBAXLcnuMvjZNI_5FGeJet7OWCDYwI8Gcw6Oi.ttf'
  },
  {
    label: 'Ysabeau Infant',
    value: 'https://fonts.gstatic.com/s/ysabeauinfant/v2/hv-ClzpqOkkV94kBTQVdX1EWI9B0V-HEmd9JmTQYFo8HK5ChLwKH6A.ttf'
  },
  {
    label: 'Ysabeau Office',
    value: 'https://fonts.gstatic.com/s/ysabeauoffice/v2/LDImapaZKhM9RuQIp8FmdYrPPNjFm07hbpKNlPPbh6MfYSfpQj7IGQ.ttf'
  },
  {
    label: 'Ysabeau SC',
    value: 'https://fonts.gstatic.com/s/ysabeausc/v2/Noai6Uro3JCIKAbW46nMorJZyP7kKRflbw98UlqEZ4EOmsT5.ttf'
  },
  {
    label: 'Yuji Boku',
    value: 'https://fonts.gstatic.com/s/yujiboku/v5/P5sAzZybeNzXsA9xj1Fkjb2r2dgvJA.ttf'
  },
  {
    label: 'Yuji Hentaigana Akari',
    value: 'https://fonts.gstatic.com/s/yujihentaiganaakari/v11/cY9bfiyVT0VB6QuhWKOrpr6z58lnb_zYFnLIRTzODYALaA.ttf'
  },
  {
    label: 'Yuji Hentaigana Akebono',
    value: 'https://fonts.gstatic.com/s/yujihentaiganaakebono/v12/EJRGQhkhRNwM-RtitGUwh930GU_f5KAlkuL0wQy9NKXRzrrF.ttf'
  },
  {
    label: 'Yuji Mai',
    value: 'https://fonts.gstatic.com/s/yujimai/v5/ZgNQjPxdJ7DEHrS0gC38hmHmNpCO.ttf'
  },
  {
    label: 'Yuji Syuku',
    value: 'https://fonts.gstatic.com/s/yujisyuku/v5/BngNUXdTV3vO6Lw5ApOPqPfgwqiA-Rk.ttf'
  },
  {
    label: 'Yusei Magic',
    value: 'https://fonts.gstatic.com/s/yuseimagic/v12/yYLt0hbAyuCmoo5wlhPkpjHR-tdfcIT_.ttf'
  },
  {
    label: 'ZCOOL KuaiLe',
    value: 'https://fonts.gstatic.com/s/zcoolkuaile/v19/tssqApdaRQokwFjFJjvM6h2WpozzoXhC2g.ttf'
  },
  {
    label: 'ZCOOL QingKe HuangYou',
    value: 'https://fonts.gstatic.com/s/zcoolqingkehuangyou/v15/2Eb5L_R5IXJEWhD3AOhSvFC554MOOahI4mRIi_28c8bHWA.ttf'
  },
  {
    label: 'ZCOOL XiaoWei',
    value: 'https://fonts.gstatic.com/s/zcoolxiaowei/v14/i7dMIFFrTRywPpUVX9_RJyM1YFKQHwyVd3U.ttf'
  },
  {
    label: 'Zen Antique',
    value: 'https://fonts.gstatic.com/s/zenantique/v12/AYCPpXPnd91Ma_Zf-Ri2JXJq7PKP5Z_G.ttf'
  },
  {
    label: 'Zen Antique Soft',
    value: 'https://fonts.gstatic.com/s/zenantiquesoft/v12/DtV4JwqzSL1q_KwnEWMc_3xfgW6ihwBmkui5HNg.ttf'
  },
  {
    label: 'Zen Dots',
    value: 'https://fonts.gstatic.com/s/zendots/v12/XRXX3ICfm00IGoesQeaETM_FcCIG.ttf'
  },
  {
    label: 'Zen Kaku Gothic Antique',
    value: 'https://fonts.gstatic.com/s/zenkakugothicantique/v15/6qLQKYkHvh-nlUpKPAdoVFBtfxDzIn1eCzpB21-g3RKjc4d7.ttf'
  },
  {
    label: 'Zen Kaku Gothic New',
    value: 'https://fonts.gstatic.com/s/zenkakugothicnew/v15/gNMYW2drQpDw0GjzrVNFf_valaDBcznOkjtiTWz5UGA.ttf'
  },
  {
    label: 'Zen Kurenaido',
    value: 'https://fonts.gstatic.com/s/zenkurenaido/v16/3XFsEr0515BK2u6UUptu_gWJZfz22PRLd0U.ttf'
  },
  {
    label: 'Zen Loop',
    value: 'https://fonts.gstatic.com/s/zenloop/v9/h0GrssK16UsnJwHsEK9zqwzX5vOG.ttf'
  },
  {
    label: 'Zen Maru Gothic',
    value: 'https://fonts.gstatic.com/s/zenmarugothic/v16/o-0SIpIxzW5b-RxT-6A8jWAtCp-k7UJmNLGG9A.ttf'
  },
  {
    label: 'Zen Old Mincho',
    value: 'https://fonts.gstatic.com/s/zenoldmincho/v11/tss0ApVaYytLwxTqcxfMyBveyYb3g31S2s8p.ttf'
  },
  {
    label: 'Zen Tokyo Zoo',
    value: 'https://fonts.gstatic.com/s/zentokyozoo/v7/NGSyv5ffC0J_BK6aFNtr6sRv8a1uRWe9amg.ttf'
  },
  {
    label: 'Zeyada',
    value: 'https://fonts.gstatic.com/s/zeyada/v19/11hAGpPTxVPUbgZDNGatWKaZ3g.ttf'
  },
  {
    label: 'Zhi Mang Xing',
    value: 'https://fonts.gstatic.com/s/zhimangxing/v17/f0Xw0ey79sErYFtWQ9a2rq-g0actfektIJ0.ttf'
  },
  {
    label: 'Zilla Slab',
    value: 'https://fonts.gstatic.com/s/zillaslab/v11/dFa6ZfeM_74wlPZtksIFWj0w_HyIRlE.ttf'
  },
  {
    label: 'Zilla Slab Highlight',
    value: 'https://fonts.gstatic.com/s/zillaslabhighlight/v19/gNMbW2BrTpK8-inLtBJgMMfbm6uNVDvRxhtIY2DwSXlM.ttf'
  }
]
