{"info": {"title": "Napster Engine API Surface", "description": "APIs for the Napster Engine application", "version": "1.0.0"}, "paths": {"/authentication": {"post": {"parameters": [{"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/authentication"}}], "responses": {"201": {"description": "created", "schema": {"$ref": "#/definitions/authentication"}}, "401": {"description": "not authenticated"}, "500": {"description": "general error"}}, "description": "Creates a new resource with data.", "summary": "", "tags": ["authentication"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}}, "/authentication/{id}": {"delete": {"parameters": [{"in": "path", "name": "id", "description": "ID of authentication to remove", "type": "integer", "required": true}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/authentication"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Removes the resource with id.", "summary": "", "tags": ["authentication"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}}, "/channel-type": {"get": {"parameters": [{"description": "Number of results to return", "in": "query", "name": "$limit", "type": "integer"}, {"description": "Number of results to skip", "in": "query", "name": "$skip", "type": "integer"}, {"description": "Property to sort results", "in": "query", "name": "$sort", "type": "string"}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/channel-type_list"}}, "401": {"description": "not authenticated"}, "500": {"description": "general error"}}, "description": "Retrieves a list of all resources from the service.", "summary": "", "tags": ["channel-type"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "post": {"parameters": [{"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/channel-type"}}], "responses": {"201": {"description": "created", "schema": {"$ref": "#/definitions/channel-type"}}, "401": {"description": "not authenticated"}, "500": {"description": "general error"}}, "description": "Creates a new resource with data.", "summary": "", "tags": ["channel-type"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}}, "/channel-type/{type}": {"get": {"parameters": [{"in": "path", "name": "type", "description": "ID of channel-type to return", "type": "integer", "required": true}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/channel-type"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Retrieves a single resource with the given id from the service.", "summary": "", "tags": ["channel-type"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "put": {"parameters": [{"in": "path", "name": "type", "description": "ID of channel-type to return", "type": "integer", "required": true}, {"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/channel-type"}}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/channel-type"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Updates the resource identified by id using data.", "summary": "", "tags": ["channel-type"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "patch": {"parameters": [{"in": "path", "name": "type", "description": "ID of channel-type to update", "type": "integer", "required": true}, {"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/channel-type"}}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/channel-type"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Updates the resource identified by id using data.", "summary": "", "tags": ["channel-type"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "delete": {"parameters": [{"in": "path", "name": "type", "description": "ID of channel-type to remove", "type": "integer", "required": true}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/channel-type"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Removes the resource with id.", "summary": "", "tags": ["channel-type"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}}, "/collection-type": {"get": {"parameters": [{"description": "Number of results to return", "in": "query", "name": "$limit", "type": "integer"}, {"description": "Number of results to skip", "in": "query", "name": "$skip", "type": "integer"}, {"description": "Property to sort results", "in": "query", "name": "$sort", "type": "string"}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/collection-type_list"}}, "401": {"description": "not authenticated"}, "500": {"description": "general error"}}, "description": "Retrieves a list of all resources from the service.", "summary": "", "tags": ["collection-type"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "post": {"parameters": [{"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/collection-type"}}], "responses": {"201": {"description": "created", "schema": {"$ref": "#/definitions/collection-type"}}, "401": {"description": "not authenticated"}, "500": {"description": "general error"}}, "description": "Creates a new resource with data.", "summary": "", "tags": ["collection-type"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}}, "/collection-type/{type}": {"get": {"parameters": [{"in": "path", "name": "type", "description": "ID of collection-type to return", "type": "integer", "required": true}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/collection-type"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Retrieves a single resource with the given id from the service.", "summary": "", "tags": ["collection-type"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "put": {"parameters": [{"in": "path", "name": "type", "description": "ID of collection-type to return", "type": "integer", "required": true}, {"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/collection-type"}}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/collection-type"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Updates the resource identified by id using data.", "summary": "", "tags": ["collection-type"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "patch": {"parameters": [{"in": "path", "name": "type", "description": "ID of collection-type to update", "type": "integer", "required": true}, {"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/collection-type"}}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/collection-type"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Updates the resource identified by id using data.", "summary": "", "tags": ["collection-type"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "delete": {"parameters": [{"in": "path", "name": "type", "description": "ID of collection-type to remove", "type": "integer", "required": true}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/collection-type"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Removes the resource with id.", "summary": "", "tags": ["collection-type"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}}, "/component-type": {"get": {"parameters": [{"description": "Number of results to return", "in": "query", "name": "$limit", "type": "integer"}, {"description": "Number of results to skip", "in": "query", "name": "$skip", "type": "integer"}, {"description": "Property to sort results", "in": "query", "name": "$sort", "type": "string"}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/component-type_list"}}, "401": {"description": "not authenticated"}, "500": {"description": "general error"}}, "description": "Retrieves a list of all resources from the service.", "summary": "", "tags": ["component-type"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "post": {"parameters": [{"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/component-type"}}], "responses": {"201": {"description": "created", "schema": {"$ref": "#/definitions/component-type"}}, "401": {"description": "not authenticated"}, "500": {"description": "general error"}}, "description": "Creates a new resource with data.", "summary": "", "tags": ["component-type"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}}, "/component-type/{type}": {"get": {"parameters": [{"in": "path", "name": "type", "description": "ID of component-type to return", "type": "integer", "required": true}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/component-type"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Retrieves a single resource with the given id from the service.", "summary": "", "tags": ["component-type"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "put": {"parameters": [{"in": "path", "name": "type", "description": "ID of component-type to return", "type": "integer", "required": true}, {"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/component-type"}}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/component-type"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Updates the resource identified by id using data.", "summary": "", "tags": ["component-type"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "patch": {"parameters": [{"in": "path", "name": "type", "description": "ID of component-type to update", "type": "integer", "required": true}, {"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/component-type"}}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/component-type"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Updates the resource identified by id using data.", "summary": "", "tags": ["component-type"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "delete": {"parameters": [{"in": "path", "name": "type", "description": "ID of component-type to remove", "type": "integer", "required": true}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/component-type"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Removes the resource with id.", "summary": "", "tags": ["component-type"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}}, "/group-user-rank": {"get": {"parameters": [{"description": "Number of results to return", "in": "query", "name": "$limit", "type": "integer"}, {"description": "Number of results to skip", "in": "query", "name": "$skip", "type": "integer"}, {"description": "Property to sort results", "in": "query", "name": "$sort", "type": "string"}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/group-user-rank_list"}}, "401": {"description": "not authenticated"}, "500": {"description": "general error"}}, "description": "Retrieves a list of all resources from the service.", "summary": "", "tags": ["group-user-rank"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "post": {"parameters": [{"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/group-user-rank"}}], "responses": {"201": {"description": "created", "schema": {"$ref": "#/definitions/group-user-rank"}}, "401": {"description": "not authenticated"}, "500": {"description": "general error"}}, "description": "Creates a new resource with data.", "summary": "", "tags": ["group-user-rank"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}}, "/group-user-rank/{rank}": {"get": {"parameters": [{"in": "path", "name": "rank", "description": "ID of group-user-rank to return", "type": "integer", "required": true}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/group-user-rank"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Retrieves a single resource with the given id from the service.", "summary": "", "tags": ["group-user-rank"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "put": {"parameters": [{"in": "path", "name": "rank", "description": "ID of group-user-rank to return", "type": "integer", "required": true}, {"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/group-user-rank"}}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/group-user-rank"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Updates the resource identified by id using data.", "summary": "", "tags": ["group-user-rank"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "patch": {"parameters": [{"in": "path", "name": "rank", "description": "ID of group-user-rank to update", "type": "integer", "required": true}, {"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/group-user-rank"}}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/group-user-rank"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Updates the resource identified by id using data.", "summary": "", "tags": ["group-user-rank"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "delete": {"parameters": [{"in": "path", "name": "rank", "description": "ID of group-user-rank to remove", "type": "integer", "required": true}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/group-user-rank"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Removes the resource with id.", "summary": "", "tags": ["group-user-rank"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}}, "/invite-type": {"get": {"parameters": [{"description": "Number of results to return", "in": "query", "name": "$limit", "type": "integer"}, {"description": "Number of results to skip", "in": "query", "name": "$skip", "type": "integer"}, {"description": "Property to sort results", "in": "query", "name": "$sort", "type": "string"}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/invite-type_list"}}, "401": {"description": "not authenticated"}, "500": {"description": "general error"}}, "description": "Retrieves a list of all resources from the service.", "summary": "", "tags": ["invite-type"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "post": {"parameters": [{"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/invite-type"}}], "responses": {"201": {"description": "created", "schema": {"$ref": "#/definitions/invite-type"}}, "401": {"description": "not authenticated"}, "500": {"description": "general error"}}, "description": "Creates a new resource with data.", "summary": "", "tags": ["invite-type"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}}, "/invite-type/{type}": {"get": {"parameters": [{"in": "path", "name": "type", "description": "ID of invite-type to return", "type": "integer", "required": true}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/invite-type"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Retrieves a single resource with the given id from the service.", "summary": "", "tags": ["invite-type"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "put": {"parameters": [{"in": "path", "name": "type", "description": "ID of invite-type to return", "type": "integer", "required": true}, {"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/invite-type"}}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/invite-type"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Updates the resource identified by id using data.", "summary": "", "tags": ["invite-type"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "patch": {"parameters": [{"in": "path", "name": "type", "description": "ID of invite-type to update", "type": "integer", "required": true}, {"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/invite-type"}}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/invite-type"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Updates the resource identified by id using data.", "summary": "", "tags": ["invite-type"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "delete": {"parameters": [{"in": "path", "name": "type", "description": "ID of invite-type to remove", "type": "integer", "required": true}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/invite-type"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Removes the resource with id.", "summary": "", "tags": ["invite-type"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}}, "/location-type": {"get": {"parameters": [{"description": "Number of results to return", "in": "query", "name": "$limit", "type": "integer"}, {"description": "Number of results to skip", "in": "query", "name": "$skip", "type": "integer"}, {"description": "Property to sort results", "in": "query", "name": "$sort", "type": "string"}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/location-type_list"}}, "401": {"description": "not authenticated"}, "500": {"description": "general error"}}, "description": "Retrieves a list of all resources from the service.", "summary": "", "tags": ["location-type"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "post": {"parameters": [{"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/location-type"}}], "responses": {"201": {"description": "created", "schema": {"$ref": "#/definitions/location-type"}}, "401": {"description": "not authenticated"}, "500": {"description": "general error"}}, "description": "Creates a new resource with data.", "summary": "", "tags": ["location-type"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}}, "/location-type/{type}": {"get": {"parameters": [{"in": "path", "name": "type", "description": "ID of location-type to return", "type": "integer", "required": true}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/location-type"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Retrieves a single resource with the given id from the service.", "summary": "", "tags": ["location-type"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "put": {"parameters": [{"in": "path", "name": "type", "description": "ID of location-type to return", "type": "integer", "required": true}, {"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/location-type"}}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/location-type"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Updates the resource identified by id using data.", "summary": "", "tags": ["location-type"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "patch": {"parameters": [{"in": "path", "name": "type", "description": "ID of location-type to update", "type": "integer", "required": true}, {"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/location-type"}}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/location-type"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Updates the resource identified by id using data.", "summary": "", "tags": ["location-type"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "delete": {"parameters": [{"in": "path", "name": "type", "description": "ID of location-type to remove", "type": "integer", "required": true}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/location-type"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Removes the resource with id.", "summary": "", "tags": ["location-type"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}}, "/seat-status": {"get": {"parameters": [{"description": "Number of results to return", "in": "query", "name": "$limit", "type": "integer"}, {"description": "Number of results to skip", "in": "query", "name": "$skip", "type": "integer"}, {"description": "Property to sort results", "in": "query", "name": "$sort", "type": "string"}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/seat-status_list"}}, "401": {"description": "not authenticated"}, "500": {"description": "general error"}}, "description": "Retrieves a list of all resources from the service.", "summary": "", "tags": ["seat-status"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "post": {"parameters": [{"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/seat-status"}}], "responses": {"201": {"description": "created", "schema": {"$ref": "#/definitions/seat-status"}}, "401": {"description": "not authenticated"}, "500": {"description": "general error"}}, "description": "Creates a new resource with data.", "summary": "", "tags": ["seat-status"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}}, "/seat-status/{status}": {"get": {"parameters": [{"in": "path", "name": "status", "description": "ID of seat-status to return", "type": "integer", "required": true}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/seat-status"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Retrieves a single resource with the given id from the service.", "summary": "", "tags": ["seat-status"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "put": {"parameters": [{"in": "path", "name": "status", "description": "ID of seat-status to return", "type": "integer", "required": true}, {"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/seat-status"}}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/seat-status"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Updates the resource identified by id using data.", "summary": "", "tags": ["seat-status"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "patch": {"parameters": [{"in": "path", "name": "status", "description": "ID of seat-status to update", "type": "integer", "required": true}, {"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/seat-status"}}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/seat-status"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Updates the resource identified by id using data.", "summary": "", "tags": ["seat-status"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "delete": {"parameters": [{"in": "path", "name": "status", "description": "ID of seat-status to remove", "type": "integer", "required": true}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/seat-status"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Removes the resource with id.", "summary": "", "tags": ["seat-status"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}}, "/static-resource-type": {"get": {"parameters": [{"description": "Number of results to return", "in": "query", "name": "$limit", "type": "integer"}, {"description": "Number of results to skip", "in": "query", "name": "$skip", "type": "integer"}, {"description": "Property to sort results", "in": "query", "name": "$sort", "type": "string"}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/static-resource-type_list"}}, "401": {"description": "not authenticated"}, "500": {"description": "general error"}}, "description": "Retrieves a list of all resources from the service.", "summary": "", "tags": ["static-resource-type"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "post": {"parameters": [{"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/static-resource-type"}}], "responses": {"201": {"description": "created", "schema": {"$ref": "#/definitions/static-resource-type"}}, "401": {"description": "not authenticated"}, "500": {"description": "general error"}}, "description": "Creates a new resource with data.", "summary": "", "tags": ["static-resource-type"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}}, "/static-resource-type/{type}": {"get": {"parameters": [{"in": "path", "name": "type", "description": "ID of static-resource-type to return", "type": "integer", "required": true}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/static-resource-type"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Retrieves a single resource with the given id from the service.", "summary": "", "tags": ["static-resource-type"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "put": {"parameters": [{"in": "path", "name": "type", "description": "ID of static-resource-type to return", "type": "integer", "required": true}, {"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/static-resource-type"}}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/static-resource-type"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Updates the resource identified by id using data.", "summary": "", "tags": ["static-resource-type"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "patch": {"parameters": [{"in": "path", "name": "type", "description": "ID of static-resource-type to update", "type": "integer", "required": true}, {"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/static-resource-type"}}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/static-resource-type"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Updates the resource identified by id using data.", "summary": "", "tags": ["static-resource-type"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "delete": {"parameters": [{"in": "path", "name": "type", "description": "ID of static-resource-type to remove", "type": "integer", "required": true}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/static-resource-type"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Removes the resource with id.", "summary": "", "tags": ["static-resource-type"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}}, "/subscription-level": {"get": {"parameters": [{"description": "Number of results to return", "in": "query", "name": "$limit", "type": "integer"}, {"description": "Number of results to skip", "in": "query", "name": "$skip", "type": "integer"}, {"description": "Property to sort results", "in": "query", "name": "$sort", "type": "string"}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/subscription-level_list"}}, "401": {"description": "not authenticated"}, "500": {"description": "general error"}}, "description": "Retrieves a list of all resources from the service.", "summary": "", "tags": ["subscription-level"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "post": {"parameters": [{"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/subscription-level"}}], "responses": {"201": {"description": "created", "schema": {"$ref": "#/definitions/subscription-level"}}, "401": {"description": "not authenticated"}, "500": {"description": "general error"}}, "description": "Creates a new resource with data.", "summary": "", "tags": ["subscription-level"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}}, "/subscription-level/{level}": {"get": {"parameters": [{"in": "path", "name": "level", "description": "ID of subscription-level to return", "type": "integer", "required": true}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/subscription-level"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Retrieves a single resource with the given id from the service.", "summary": "", "tags": ["subscription-level"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "put": {"parameters": [{"in": "path", "name": "level", "description": "ID of subscription-level to return", "type": "integer", "required": true}, {"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/subscription-level"}}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/subscription-level"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Updates the resource identified by id using data.", "summary": "", "tags": ["subscription-level"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "patch": {"parameters": [{"in": "path", "name": "level", "description": "ID of subscription-level to update", "type": "integer", "required": true}, {"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/subscription-level"}}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/subscription-level"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Updates the resource identified by id using data.", "summary": "", "tags": ["subscription-level"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "delete": {"parameters": [{"in": "path", "name": "level", "description": "ID of subscription-level to remove", "type": "integer", "required": true}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/subscription-level"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Removes the resource with id.", "summary": "", "tags": ["subscription-level"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}}, "/subscription-type": {"get": {"parameters": [{"description": "Number of results to return", "in": "query", "name": "$limit", "type": "integer"}, {"description": "Number of results to skip", "in": "query", "name": "$skip", "type": "integer"}, {"description": "Property to sort results", "in": "query", "name": "$sort", "type": "string"}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/subscription-type_list"}}, "401": {"description": "not authenticated"}, "500": {"description": "general error"}}, "description": "Retrieves a list of all resources from the service.", "summary": "", "tags": ["subscription-type"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "post": {"parameters": [{"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/subscription-type"}}], "responses": {"201": {"description": "created", "schema": {"$ref": "#/definitions/subscription-type"}}, "401": {"description": "not authenticated"}, "500": {"description": "general error"}}, "description": "Creates a new resource with data.", "summary": "", "tags": ["subscription-type"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}}, "/subscription-type/{plan}": {"get": {"parameters": [{"in": "path", "name": "plan", "description": "ID of subscription-type to return", "type": "integer", "required": true}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/subscription-type"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Retrieves a single resource with the given id from the service.", "summary": "", "tags": ["subscription-type"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "put": {"parameters": [{"in": "path", "name": "plan", "description": "ID of subscription-type to return", "type": "integer", "required": true}, {"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/subscription-type"}}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/subscription-type"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Updates the resource identified by id using data.", "summary": "", "tags": ["subscription-type"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "patch": {"parameters": [{"in": "path", "name": "plan", "description": "ID of subscription-type to update", "type": "integer", "required": true}, {"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/subscription-type"}}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/subscription-type"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Updates the resource identified by id using data.", "summary": "", "tags": ["subscription-type"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "delete": {"parameters": [{"in": "path", "name": "plan", "description": "ID of subscription-type to remove", "type": "integer", "required": true}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/subscription-type"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Removes the resource with id.", "summary": "", "tags": ["subscription-type"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}}, "/user-relationship-type": {"get": {"parameters": [{"description": "Number of results to return", "in": "query", "name": "$limit", "type": "integer"}, {"description": "Number of results to skip", "in": "query", "name": "$skip", "type": "integer"}, {"description": "Property to sort results", "in": "query", "name": "$sort", "type": "string"}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/user-relationship-type_list"}}, "401": {"description": "not authenticated"}, "500": {"description": "general error"}}, "description": "Retrieves a list of all resources from the service.", "summary": "", "tags": ["user-relationship-type"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "post": {"parameters": [{"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/user-relationship-type"}}], "responses": {"201": {"description": "created", "schema": {"$ref": "#/definitions/user-relationship-type"}}, "401": {"description": "not authenticated"}, "500": {"description": "general error"}}, "description": "Creates a new resource with data.", "summary": "", "tags": ["user-relationship-type"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}}, "/user-relationship-type/{type}": {"get": {"parameters": [{"in": "path", "name": "type", "description": "ID of user-relationship-type to return", "type": "integer", "required": true}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/user-relationship-type"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Retrieves a single resource with the given id from the service.", "summary": "", "tags": ["user-relationship-type"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "put": {"parameters": [{"in": "path", "name": "type", "description": "ID of user-relationship-type to return", "type": "integer", "required": true}, {"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/user-relationship-type"}}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/user-relationship-type"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Updates the resource identified by id using data.", "summary": "", "tags": ["user-relationship-type"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "patch": {"parameters": [{"in": "path", "name": "type", "description": "ID of user-relationship-type to update", "type": "integer", "required": true}, {"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/user-relationship-type"}}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/user-relationship-type"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Updates the resource identified by id using data.", "summary": "", "tags": ["user-relationship-type"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "delete": {"parameters": [{"in": "path", "name": "type", "description": "ID of user-relationship-type to remove", "type": "integer", "required": true}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/user-relationship-type"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Removes the resource with id.", "summary": "", "tags": ["user-relationship-type"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}}, "/attribution": {"get": {"parameters": [{"description": "Number of results to return", "in": "query", "name": "$limit", "type": "integer"}, {"description": "Number of results to skip", "in": "query", "name": "$skip", "type": "integer"}, {"description": "Property to sort results", "in": "query", "name": "$sort", "type": "string"}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/attribution_list"}}, "401": {"description": "not authenticated"}, "500": {"description": "general error"}}, "description": "Retrieves a list of all resources from the service.", "summary": "", "tags": ["attribution"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "post": {"parameters": [{"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/attribution"}}], "responses": {"201": {"description": "created", "schema": {"$ref": "#/definitions/attribution"}}, "401": {"description": "not authenticated"}, "500": {"description": "general error"}}, "description": "Creates a new resource with data.", "summary": "", "tags": ["attribution"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}}, "/attribution/{id}": {"get": {"parameters": [{"in": "path", "name": "id", "description": "ID of attribution to return", "type": "integer", "required": true}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/attribution"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Retrieves a single resource with the given id from the service.", "summary": "", "tags": ["attribution"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "put": {"parameters": [{"in": "path", "name": "id", "description": "ID of attribution to return", "type": "integer", "required": true}, {"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/attribution"}}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/attribution"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Updates the resource identified by id using data.", "summary": "", "tags": ["attribution"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "patch": {"parameters": [{"in": "path", "name": "id", "description": "ID of attribution to update", "type": "integer", "required": true}, {"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/attribution"}}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/attribution"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Updates the resource identified by id using data.", "summary": "", "tags": ["attribution"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "delete": {"parameters": [{"in": "path", "name": "id", "description": "ID of attribution to remove", "type": "integer", "required": true}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/attribution"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Removes the resource with id.", "summary": "", "tags": ["attribution"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}}, "/collection": {"get": {"parameters": [{"description": "Number of results to return", "in": "query", "name": "$limit", "type": "integer"}, {"description": "Number of results to skip", "in": "query", "name": "$skip", "type": "integer"}, {"description": "Property to sort results", "in": "query", "name": "$sort", "type": "string"}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/collection_list"}}, "401": {"description": "not authenticated"}, "500": {"description": "general error"}}, "description": "Retrieves a list of all resources from the service.", "summary": "", "tags": ["collection"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "post": {"parameters": [{"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/collection"}}], "responses": {"201": {"description": "created", "schema": {"$ref": "#/definitions/collection"}}, "401": {"description": "not authenticated"}, "500": {"description": "general error"}}, "description": "Creates a new resource with data.", "summary": "", "tags": ["collection"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}}, "/collection/{id}": {"get": {"parameters": [{"in": "path", "name": "id", "description": "ID of collection to return", "type": "integer", "required": true}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/collection"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Retrieves a single resource with the given id from the service.", "summary": "", "tags": ["collection"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "put": {"parameters": [{"in": "path", "name": "id", "description": "ID of collection to return", "type": "integer", "required": true}, {"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/collection"}}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/collection"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Updates the resource identified by id using data.", "summary": "", "tags": ["collection"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "patch": {"parameters": [{"in": "path", "name": "id", "description": "ID of collection to update", "type": "integer", "required": true}, {"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/collection"}}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/collection"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Updates the resource identified by id using data.", "summary": "", "tags": ["collection"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "delete": {"parameters": [{"in": "path", "name": "id", "description": "ID of collection to remove", "type": "integer", "required": true}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/collection"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Removes the resource with id.", "summary": "", "tags": ["collection"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}}, "/component": {"get": {"parameters": [{"description": "Number of results to return", "in": "query", "name": "$limit", "type": "integer"}, {"description": "Number of results to skip", "in": "query", "name": "$skip", "type": "integer"}, {"description": "Property to sort results", "in": "query", "name": "$sort", "type": "string"}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/component_list"}}, "401": {"description": "not authenticated"}, "500": {"description": "general error"}}, "description": "Retrieves a list of all resources from the service.", "summary": "", "tags": ["component"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "post": {"parameters": [{"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/component"}}], "responses": {"201": {"description": "created", "schema": {"$ref": "#/definitions/component"}}, "401": {"description": "not authenticated"}, "500": {"description": "general error"}}, "description": "Creates a new resource with data.", "summary": "", "tags": ["component"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}}, "/component/{id}": {"get": {"parameters": [{"in": "path", "name": "id", "description": "ID of component to return", "type": "integer", "required": true}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/component"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Retrieves a single resource with the given id from the service.", "summary": "", "tags": ["component"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "put": {"parameters": [{"in": "path", "name": "id", "description": "ID of component to return", "type": "integer", "required": true}, {"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/component"}}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/component"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Updates the resource identified by id using data.", "summary": "", "tags": ["component"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "patch": {"parameters": [{"in": "path", "name": "id", "description": "ID of component to update", "type": "integer", "required": true}, {"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/component"}}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/component"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Updates the resource identified by id using data.", "summary": "", "tags": ["component"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "delete": {"parameters": [{"in": "path", "name": "id", "description": "ID of component to remove", "type": "integer", "required": true}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/component"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Removes the resource with id.", "summary": "", "tags": ["component"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}}, "/entity": {"get": {"parameters": [{"description": "Number of results to return", "in": "query", "name": "$limit", "type": "integer"}, {"description": "Number of results to skip", "in": "query", "name": "$skip", "type": "integer"}, {"description": "Property to sort results", "in": "query", "name": "$sort", "type": "string"}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/entity_list"}}, "401": {"description": "not authenticated"}, "500": {"description": "general error"}}, "description": "Retrieves a list of all resources from the service.", "summary": "", "tags": ["entity"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "post": {"parameters": [{"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/entity"}}], "responses": {"201": {"description": "created", "schema": {"$ref": "#/definitions/entity"}}, "401": {"description": "not authenticated"}, "500": {"description": "general error"}}, "description": "Creates a new resource with data.", "summary": "", "tags": ["entity"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}}, "/entity/{id}": {"get": {"parameters": [{"in": "path", "name": "id", "description": "ID of entity to return", "type": "integer", "required": true}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/entity"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Retrieves a single resource with the given id from the service.", "summary": "", "tags": ["entity"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "put": {"parameters": [{"in": "path", "name": "id", "description": "ID of entity to return", "type": "integer", "required": true}, {"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/entity"}}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/entity"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Updates the resource identified by id using data.", "summary": "", "tags": ["entity"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "patch": {"parameters": [{"in": "path", "name": "id", "description": "ID of entity to update", "type": "integer", "required": true}, {"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/entity"}}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/entity"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Updates the resource identified by id using data.", "summary": "", "tags": ["entity"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "delete": {"parameters": [{"in": "path", "name": "id", "description": "ID of entity to remove", "type": "integer", "required": true}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/entity"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Removes the resource with id.", "summary": "", "tags": ["entity"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}}, "/group": {"get": {"parameters": [{"description": "Number of results to return", "in": "query", "name": "$limit", "type": "integer"}, {"description": "Number of results to skip", "in": "query", "name": "$skip", "type": "integer"}, {"description": "Property to sort results", "in": "query", "name": "$sort", "type": "string"}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/group_list"}}, "401": {"description": "not authenticated"}, "500": {"description": "general error"}}, "description": "Retrieves a list of all resources from the service.", "summary": "", "tags": ["group"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "post": {"parameters": [{"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/group"}}], "responses": {"201": {"description": "created", "schema": {"$ref": "#/definitions/group"}}, "401": {"description": "not authenticated"}, "500": {"description": "general error"}}, "description": "Creates a new resource with data.", "summary": "", "tags": ["group"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}}, "/group/{id}": {"get": {"parameters": [{"in": "path", "name": "id", "description": "ID of group to return", "type": "integer", "required": true}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/group"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Retrieves a single resource with the given id from the service.", "summary": "", "tags": ["group"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "put": {"parameters": [{"in": "path", "name": "id", "description": "ID of group to return", "type": "integer", "required": true}, {"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/group"}}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/group"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Updates the resource identified by id using data.", "summary": "", "tags": ["group"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "patch": {"parameters": [{"in": "path", "name": "id", "description": "ID of group to update", "type": "integer", "required": true}, {"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/group"}}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/group"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Updates the resource identified by id using data.", "summary": "", "tags": ["group"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "delete": {"parameters": [{"in": "path", "name": "id", "description": "ID of group to remove", "type": "integer", "required": true}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/group"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Removes the resource with id.", "summary": "", "tags": ["group"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}}, "/identity-provider": {"get": {"parameters": [{"description": "Number of results to return", "in": "query", "name": "$limit", "type": "integer"}, {"description": "Number of results to skip", "in": "query", "name": "$skip", "type": "integer"}, {"description": "Property to sort results", "in": "query", "name": "$sort", "type": "string"}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/identity-provider_list"}}, "401": {"description": "not authenticated"}, "500": {"description": "general error"}}, "description": "Retrieves a list of all resources from the service.", "summary": "", "tags": ["identity-provider"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "post": {"parameters": [{"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/identity-provider"}}], "responses": {"201": {"description": "created", "schema": {"$ref": "#/definitions/identity-provider"}}, "401": {"description": "not authenticated"}, "500": {"description": "general error"}}, "description": "Creates a new resource with data.", "summary": "", "tags": ["identity-provider"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}}, "/identity-provider/{id}": {"get": {"parameters": [{"in": "path", "name": "id", "description": "ID of identity-provider to return", "type": "integer", "required": true}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/identity-provider"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Retrieves a single resource with the given id from the service.", "summary": "", "tags": ["identity-provider"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "put": {"parameters": [{"in": "path", "name": "id", "description": "ID of identity-provider to return", "type": "integer", "required": true}, {"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/identity-provider"}}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/identity-provider"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Updates the resource identified by id using data.", "summary": "", "tags": ["identity-provider"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "patch": {"parameters": [{"in": "path", "name": "id", "description": "ID of identity-provider to update", "type": "integer", "required": true}, {"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/identity-provider"}}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/identity-provider"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Updates the resource identified by id using data.", "summary": "", "tags": ["identity-provider"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "delete": {"parameters": [{"in": "path", "name": "id", "description": "ID of identity-provider to remove", "type": "integer", "required": true}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/identity-provider"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Removes the resource with id.", "summary": "", "tags": ["identity-provider"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}}, "/instance": {"get": {"parameters": [{"description": "Number of results to return", "in": "query", "name": "$limit", "type": "integer"}, {"description": "Number of results to skip", "in": "query", "name": "$skip", "type": "integer"}, {"description": "Property to sort results", "in": "query", "name": "$sort", "type": "string"}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/instance_list"}}, "401": {"description": "not authenticated"}, "500": {"description": "general error"}}, "description": "Retrieves a list of all resources from the service.", "summary": "", "tags": ["instance"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "post": {"parameters": [{"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/instance"}}], "responses": {"201": {"description": "created", "schema": {"$ref": "#/definitions/instance"}}, "401": {"description": "not authenticated"}, "500": {"description": "general error"}}, "description": "Creates a new resource with data.", "summary": "", "tags": ["instance"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}}, "/instance/{id}": {"get": {"parameters": [{"in": "path", "name": "id", "description": "ID of instance to return", "type": "integer", "required": true}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/instance"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Retrieves a single resource with the given id from the service.", "summary": "", "tags": ["instance"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "put": {"parameters": [{"in": "path", "name": "id", "description": "ID of instance to return", "type": "integer", "required": true}, {"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/instance"}}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/instance"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Updates the resource identified by id using data.", "summary": "", "tags": ["instance"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "patch": {"parameters": [{"in": "path", "name": "id", "description": "ID of instance to update", "type": "integer", "required": true}, {"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/instance"}}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/instance"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Updates the resource identified by id using data.", "summary": "", "tags": ["instance"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "delete": {"parameters": [{"in": "path", "name": "id", "description": "ID of instance to remove", "type": "integer", "required": true}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/instance"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Removes the resource with id.", "summary": "", "tags": ["instance"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}}, "/invite": {"get": {"parameters": [{"description": "Number of results to return", "in": "query", "name": "$limit", "type": "integer"}, {"description": "Number of results to skip", "in": "query", "name": "$skip", "type": "integer"}, {"description": "Property to sort results", "in": "query", "name": "$sort", "type": "string"}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/invite_list"}}, "401": {"description": "not authenticated"}, "500": {"description": "general error"}}, "description": "Retrieves a list of all resources from the service.", "summary": "", "tags": ["invite"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "post": {"parameters": [{"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/invite"}}], "responses": {"201": {"description": "created", "schema": {"$ref": "#/definitions/invite"}}, "401": {"description": "not authenticated"}, "500": {"description": "general error"}}, "description": "Creates a new resource with data.", "summary": "", "tags": ["invite"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}}, "/invite/{id}": {"get": {"parameters": [{"in": "path", "name": "id", "description": "ID of invite to return", "type": "integer", "required": true}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/invite"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Retrieves a single resource with the given id from the service.", "summary": "", "tags": ["invite"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "put": {"parameters": [{"in": "path", "name": "id", "description": "ID of invite to return", "type": "integer", "required": true}, {"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/invite"}}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/invite"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Updates the resource identified by id using data.", "summary": "", "tags": ["invite"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "patch": {"parameters": [{"in": "path", "name": "id", "description": "ID of invite to update", "type": "integer", "required": true}, {"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/invite"}}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/invite"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Updates the resource identified by id using data.", "summary": "", "tags": ["invite"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "delete": {"parameters": [{"in": "path", "name": "id", "description": "ID of invite to remove", "type": "integer", "required": true}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/invite"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Removes the resource with id.", "summary": "", "tags": ["invite"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}}, "/license": {"get": {"parameters": [{"description": "Number of results to return", "in": "query", "name": "$limit", "type": "integer"}, {"description": "Number of results to skip", "in": "query", "name": "$skip", "type": "integer"}, {"description": "Property to sort results", "in": "query", "name": "$sort", "type": "string"}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/license_list"}}, "401": {"description": "not authenticated"}, "500": {"description": "general error"}}, "description": "Retrieves a list of all resources from the service.", "summary": "", "tags": ["license"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "post": {"parameters": [{"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/license"}}], "responses": {"201": {"description": "created", "schema": {"$ref": "#/definitions/license"}}, "401": {"description": "not authenticated"}, "500": {"description": "general error"}}, "description": "Creates a new resource with data.", "summary": "", "tags": ["license"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}}, "/license/{name}": {"get": {"parameters": [{"in": "path", "name": "name", "description": "ID of license to return", "type": "integer", "required": true}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/license"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Retrieves a single resource with the given id from the service.", "summary": "", "tags": ["license"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "put": {"parameters": [{"in": "path", "name": "name", "description": "ID of license to return", "type": "integer", "required": true}, {"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/license"}}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/license"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Updates the resource identified by id using data.", "summary": "", "tags": ["license"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "patch": {"parameters": [{"in": "path", "name": "name", "description": "ID of license to update", "type": "integer", "required": true}, {"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/license"}}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/license"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Updates the resource identified by id using data.", "summary": "", "tags": ["license"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "delete": {"parameters": [{"in": "path", "name": "name", "description": "ID of license to remove", "type": "integer", "required": true}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/license"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Removes the resource with id.", "summary": "", "tags": ["license"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}}, "/location": {"get": {"parameters": [{"description": "Number of results to return", "in": "query", "name": "$limit", "type": "integer"}, {"description": "Number of results to skip", "in": "query", "name": "$skip", "type": "integer"}, {"description": "Property to sort results", "in": "query", "name": "$sort", "type": "string"}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/location_list"}}, "401": {"description": "not authenticated"}, "500": {"description": "general error"}}, "description": "Retrieves a list of all resources from the service.", "summary": "", "tags": ["location"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "post": {"parameters": [{"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/location"}}], "responses": {"201": {"description": "created", "schema": {"$ref": "#/definitions/location"}}, "401": {"description": "not authenticated"}, "500": {"description": "general error"}}, "description": "Creates a new resource with data.", "summary": "", "tags": ["location"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}}, "/location/{id}": {"get": {"parameters": [{"in": "path", "name": "id", "description": "ID of location to return", "type": "integer", "required": true}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/location"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Retrieves a single resource with the given id from the service.", "summary": "", "tags": ["location"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "put": {"parameters": [{"in": "path", "name": "id", "description": "ID of location to return", "type": "integer", "required": true}, {"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/location"}}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/location"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Updates the resource identified by id using data.", "summary": "", "tags": ["location"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "patch": {"parameters": [{"in": "path", "name": "id", "description": "ID of location to update", "type": "integer", "required": true}, {"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/location"}}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/location"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Updates the resource identified by id using data.", "summary": "", "tags": ["location"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "delete": {"parameters": [{"in": "path", "name": "id", "description": "ID of location to remove", "type": "integer", "required": true}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/location"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Removes the resource with id.", "summary": "", "tags": ["location"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}}, "/location-settings": {"get": {"parameters": [{"description": "Number of results to return", "in": "query", "name": "$limit", "type": "integer"}, {"description": "Number of results to skip", "in": "query", "name": "$skip", "type": "integer"}, {"description": "Property to sort results", "in": "query", "name": "$sort", "type": "string"}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/location-settings_list"}}, "401": {"description": "not authenticated"}, "500": {"description": "general error"}}, "description": "Retrieves a list of all resources from the service.", "summary": "", "tags": ["location-settings"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "post": {"parameters": [{"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/location-settings"}}], "responses": {"201": {"description": "created", "schema": {"$ref": "#/definitions/location-settings"}}, "401": {"description": "not authenticated"}, "500": {"description": "general error"}}, "description": "Creates a new resource with data.", "summary": "", "tags": ["location-settings"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}}, "/location-settings/{id}": {"get": {"parameters": [{"in": "path", "name": "id", "description": "ID of location-settings to return", "type": "integer", "required": true}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/location-settings"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Retrieves a single resource with the given id from the service.", "summary": "", "tags": ["location-settings"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "put": {"parameters": [{"in": "path", "name": "id", "description": "ID of location-settings to return", "type": "integer", "required": true}, {"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/location-settings"}}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/location-settings"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Updates the resource identified by id using data.", "summary": "", "tags": ["location-settings"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "patch": {"parameters": [{"in": "path", "name": "id", "description": "ID of location-settings to update", "type": "integer", "required": true}, {"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/location-settings"}}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/location-settings"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Updates the resource identified by id using data.", "summary": "", "tags": ["location-settings"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "delete": {"parameters": [{"in": "path", "name": "id", "description": "ID of location-settings to remove", "type": "integer", "required": true}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/location-settings"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Removes the resource with id.", "summary": "", "tags": ["location-settings"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}}, "/location-ban": {"get": {"parameters": [{"description": "Number of results to return", "in": "query", "name": "$limit", "type": "integer"}, {"description": "Number of results to skip", "in": "query", "name": "$skip", "type": "integer"}, {"description": "Property to sort results", "in": "query", "name": "$sort", "type": "string"}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/location-ban_list"}}, "401": {"description": "not authenticated"}, "500": {"description": "general error"}}, "description": "Retrieves a list of all resources from the service.", "summary": "", "tags": ["location-ban"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "post": {"parameters": [{"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/location-ban"}}], "responses": {"201": {"description": "created", "schema": {"$ref": "#/definitions/location-ban"}}, "401": {"description": "not authenticated"}, "500": {"description": "general error"}}, "description": "Creates a new resource with data.", "summary": "", "tags": ["location-ban"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}}, "/location-ban/{id}": {"get": {"parameters": [{"in": "path", "name": "id", "description": "ID of location-ban to return", "type": "integer", "required": true}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/location-ban"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Retrieves a single resource with the given id from the service.", "summary": "", "tags": ["location-ban"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "put": {"parameters": [{"in": "path", "name": "id", "description": "ID of location-ban to return", "type": "integer", "required": true}, {"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/location-ban"}}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/location-ban"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Updates the resource identified by id using data.", "summary": "", "tags": ["location-ban"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "patch": {"parameters": [{"in": "path", "name": "id", "description": "ID of location-ban to update", "type": "integer", "required": true}, {"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/location-ban"}}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/location-ban"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Updates the resource identified by id using data.", "summary": "", "tags": ["location-ban"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "delete": {"parameters": [{"in": "path", "name": "id", "description": "ID of location-ban to remove", "type": "integer", "required": true}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/location-ban"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Removes the resource with id.", "summary": "", "tags": ["location-ban"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}}, "/login-token": {"get": {"parameters": [{"description": "Number of results to return", "in": "query", "name": "$limit", "type": "integer"}, {"description": "Number of results to skip", "in": "query", "name": "$skip", "type": "integer"}, {"description": "Property to sort results", "in": "query", "name": "$sort", "type": "string"}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/login-token_list"}}, "401": {"description": "not authenticated"}, "500": {"description": "general error"}}, "description": "Retrieves a list of all resources from the service.", "summary": "", "tags": ["login-token"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "post": {"parameters": [{"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/login-token"}}], "responses": {"201": {"description": "created", "schema": {"$ref": "#/definitions/login-token"}}, "401": {"description": "not authenticated"}, "500": {"description": "general error"}}, "description": "Creates a new resource with data.", "summary": "", "tags": ["login-token"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}}, "/login-token/{id}": {"get": {"parameters": [{"in": "path", "name": "id", "description": "ID of login-token to return", "type": "integer", "required": true}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/login-token"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Retrieves a single resource with the given id from the service.", "summary": "", "tags": ["login-token"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "put": {"parameters": [{"in": "path", "name": "id", "description": "ID of login-token to return", "type": "integer", "required": true}, {"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/login-token"}}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/login-token"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Updates the resource identified by id using data.", "summary": "", "tags": ["login-token"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "patch": {"parameters": [{"in": "path", "name": "id", "description": "ID of login-token to update", "type": "integer", "required": true}, {"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/login-token"}}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/login-token"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Updates the resource identified by id using data.", "summary": "", "tags": ["login-token"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "delete": {"parameters": [{"in": "path", "name": "id", "description": "ID of login-token to remove", "type": "integer", "required": true}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/login-token"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Removes the resource with id.", "summary": "", "tags": ["login-token"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}}, "/party": {"get": {"parameters": [{"description": "Number of results to return", "in": "query", "name": "$limit", "type": "integer"}, {"description": "Number of results to skip", "in": "query", "name": "$skip", "type": "integer"}, {"description": "Property to sort results", "in": "query", "name": "$sort", "type": "string"}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/party_list"}}, "401": {"description": "not authenticated"}, "500": {"description": "general error"}}, "description": "Retrieves a list of all resources from the service.", "summary": "", "tags": ["party"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "post": {"parameters": [{"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/party"}}], "responses": {"201": {"description": "created", "schema": {"$ref": "#/definitions/party"}}, "401": {"description": "not authenticated"}, "500": {"description": "general error"}}, "description": "Creates a new resource with data.", "summary": "", "tags": ["party"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}}, "/party/{id}": {"get": {"parameters": [{"in": "path", "name": "id", "description": "ID of party to return", "type": "integer", "required": true}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/party"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Retrieves a single resource with the given id from the service.", "summary": "", "tags": ["party"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "put": {"parameters": [{"in": "path", "name": "id", "description": "ID of party to return", "type": "integer", "required": true}, {"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/party"}}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/party"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Updates the resource identified by id using data.", "summary": "", "tags": ["party"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "patch": {"parameters": [{"in": "path", "name": "id", "description": "ID of party to update", "type": "integer", "required": true}, {"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/party"}}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/party"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Updates the resource identified by id using data.", "summary": "", "tags": ["party"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "delete": {"parameters": [{"in": "path", "name": "id", "description": "ID of party to remove", "type": "integer", "required": true}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/party"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Removes the resource with id.", "summary": "", "tags": ["party"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}}, "/scene": {"get": {"parameters": [{"description": "Number of results to return", "in": "query", "name": "$limit", "type": "integer"}, {"description": "Number of results to skip", "in": "query", "name": "$skip", "type": "integer"}, {"description": "Property to sort results", "in": "query", "name": "$sort", "type": "string"}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/scene_list"}}, "401": {"description": "not authenticated"}, "500": {"description": "general error"}}, "description": "Retrieves a list of all resources from the service.", "summary": "", "tags": ["scene"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "post": {"parameters": [{"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/scene"}}], "responses": {"201": {"description": "created", "schema": {"$ref": "#/definitions/scene"}}, "401": {"description": "not authenticated"}, "500": {"description": "general error"}}, "description": "Creates a new resource with data.", "summary": "", "tags": ["scene"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}}, "/scene/{id}": {"get": {"parameters": [{"in": "path", "name": "id", "description": "ID of scene to return", "type": "integer", "required": true}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/scene"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Retrieves a single resource with the given id from the service.", "summary": "", "tags": ["scene"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "put": {"parameters": [{"in": "path", "name": "id", "description": "ID of scene to return", "type": "integer", "required": true}, {"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/scene"}}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/scene"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Updates the resource identified by id using data.", "summary": "", "tags": ["scene"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "patch": {"parameters": [{"in": "path", "name": "id", "description": "ID of scene to update", "type": "integer", "required": true}, {"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/scene"}}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/scene"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Updates the resource identified by id using data.", "summary": "", "tags": ["scene"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "delete": {"parameters": [{"in": "path", "name": "id", "description": "ID of scene to remove", "type": "integer", "required": true}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/scene"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Removes the resource with id.", "summary": "", "tags": ["scene"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}}, "/rtc-ports": {"get": {"parameters": [{"description": "Number of results to return", "in": "query", "name": "$limit", "type": "integer"}, {"description": "Number of results to skip", "in": "query", "name": "$skip", "type": "integer"}, {"description": "Property to sort results", "in": "query", "name": "$sort", "type": "string"}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/rtc-ports_list"}}, "401": {"description": "not authenticated"}, "500": {"description": "general error"}}, "description": "Retrieves a list of all resources from the service.", "summary": "", "tags": ["rtc-ports"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "post": {"parameters": [{"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/rtc-ports"}}], "responses": {"201": {"description": "created", "schema": {"$ref": "#/definitions/rtc-ports"}}, "401": {"description": "not authenticated"}, "500": {"description": "general error"}}, "description": "Creates a new resource with data.", "summary": "", "tags": ["rtc-ports"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}}, "/rtc-ports/{id}": {"get": {"parameters": [{"in": "path", "name": "id", "description": "ID of rtc-ports to return", "type": "integer", "required": true}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/rtc-ports"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Retrieves a single resource with the given id from the service.", "summary": "", "tags": ["rtc-ports"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "put": {"parameters": [{"in": "path", "name": "id", "description": "ID of rtc-ports to return", "type": "integer", "required": true}, {"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/rtc-ports"}}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/rtc-ports"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Updates the resource identified by id using data.", "summary": "", "tags": ["rtc-ports"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "patch": {"parameters": [{"in": "path", "name": "id", "description": "ID of rtc-ports to update", "type": "integer", "required": true}, {"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/rtc-ports"}}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/rtc-ports"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Updates the resource identified by id using data.", "summary": "", "tags": ["rtc-ports"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "delete": {"parameters": [{"in": "path", "name": "id", "description": "ID of rtc-ports to remove", "type": "integer", "required": true}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/rtc-ports"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Removes the resource with id.", "summary": "", "tags": ["rtc-ports"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}}, "/seat": {"get": {"parameters": [{"description": "Number of results to return", "in": "query", "name": "$limit", "type": "integer"}, {"description": "Number of results to skip", "in": "query", "name": "$skip", "type": "integer"}, {"description": "Property to sort results", "in": "query", "name": "$sort", "type": "string"}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/seat_list"}}, "401": {"description": "not authenticated"}, "500": {"description": "general error"}}, "description": "Retrieves a list of all resources from the service.", "summary": "", "tags": ["seat"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "post": {"parameters": [{"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/seat"}}], "responses": {"201": {"description": "created", "schema": {"$ref": "#/definitions/seat"}}, "401": {"description": "not authenticated"}, "500": {"description": "general error"}}, "description": "Creates a new resource with data.", "summary": "", "tags": ["seat"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}}, "/seat/{id}": {"get": {"parameters": [{"in": "path", "name": "id", "description": "ID of seat to return", "type": "integer", "required": true}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/seat"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Retrieves a single resource with the given id from the service.", "summary": "", "tags": ["seat"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "put": {"parameters": [{"in": "path", "name": "id", "description": "ID of seat to return", "type": "integer", "required": true}, {"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/seat"}}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/seat"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Updates the resource identified by id using data.", "summary": "", "tags": ["seat"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "patch": {"parameters": [{"in": "path", "name": "id", "description": "ID of seat to update", "type": "integer", "required": true}, {"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/seat"}}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/seat"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Updates the resource identified by id using data.", "summary": "", "tags": ["seat"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "delete": {"parameters": [{"in": "path", "name": "id", "description": "ID of seat to remove", "type": "integer", "required": true}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/seat"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Removes the resource with id.", "summary": "", "tags": ["seat"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}}, "/static-resource": {"get": {"parameters": [{"description": "Number of results to return", "in": "query", "name": "$limit", "type": "integer"}, {"description": "Number of results to skip", "in": "query", "name": "$skip", "type": "integer"}, {"description": "Property to sort results", "in": "query", "name": "$sort", "type": "string"}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/static-resource_list"}}, "401": {"description": "not authenticated"}, "500": {"description": "general error"}}, "description": "Retrieves a list of all resources from the service.", "summary": "", "tags": ["static-resource"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "post": {"parameters": [{"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/static-resource"}}], "responses": {"201": {"description": "created", "schema": {"$ref": "#/definitions/static-resource"}}, "401": {"description": "not authenticated"}, "500": {"description": "general error"}}, "description": "Creates a new resource with data.", "summary": "", "tags": ["static-resource"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}}, "/static-resource/{id}": {"get": {"parameters": [{"in": "path", "name": "id", "description": "ID of static-resource to return", "type": "integer", "required": true}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/static-resource"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Retrieves a single resource with the given id from the service.", "summary": "", "tags": ["static-resource"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "put": {"parameters": [{"in": "path", "name": "id", "description": "ID of static-resource to return", "type": "integer", "required": true}, {"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/static-resource"}}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/static-resource"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Updates the resource identified by id using data.", "summary": "", "tags": ["static-resource"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "patch": {"parameters": [{"in": "path", "name": "id", "description": "ID of static-resource to update", "type": "integer", "required": true}, {"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/static-resource"}}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/static-resource"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Updates the resource identified by id using data.", "summary": "", "tags": ["static-resource"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "delete": {"parameters": [{"in": "path", "name": "id", "description": "ID of static-resource to remove", "type": "integer", "required": true}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/static-resource"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Removes the resource with id.", "summary": "", "tags": ["static-resource"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}}, "/subscription": {"get": {"parameters": [{"description": "Number of results to return", "in": "query", "name": "$limit", "type": "integer"}, {"description": "Number of results to skip", "in": "query", "name": "$skip", "type": "integer"}, {"description": "Property to sort results", "in": "query", "name": "$sort", "type": "string"}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/subscription_list"}}, "401": {"description": "not authenticated"}, "500": {"description": "general error"}}, "description": "Retrieves a list of all resources from the service.", "summary": "", "tags": ["subscription"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "post": {"parameters": [{"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/subscription"}}], "responses": {"201": {"description": "created", "schema": {"$ref": "#/definitions/subscription"}}, "401": {"description": "not authenticated"}, "500": {"description": "general error"}}, "description": "Creates a new resource with data.", "summary": "", "tags": ["subscription"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}}, "/subscription/{id}": {"get": {"parameters": [{"in": "path", "name": "id", "description": "ID of subscription to return", "type": "integer", "required": true}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/subscription"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Retrieves a single resource with the given id from the service.", "summary": "", "tags": ["subscription"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "put": {"parameters": [{"in": "path", "name": "id", "description": "ID of subscription to return", "type": "integer", "required": true}, {"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/subscription"}}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/subscription"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Updates the resource identified by id using data.", "summary": "", "tags": ["subscription"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "patch": {"parameters": [{"in": "path", "name": "id", "description": "ID of subscription to update", "type": "integer", "required": true}, {"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/subscription"}}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/subscription"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Updates the resource identified by id using data.", "summary": "", "tags": ["subscription"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "delete": {"parameters": [{"in": "path", "name": "id", "description": "ID of subscription to remove", "type": "integer", "required": true}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/subscription"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Removes the resource with id.", "summary": "", "tags": ["subscription"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}}, "/user": {"get": {"parameters": [{"description": "Number of results to return", "in": "query", "name": "$limit", "type": "integer"}, {"description": "Number of results to skip", "in": "query", "name": "$skip", "type": "integer"}, {"description": "Property to sort results", "in": "query", "name": "$sort", "type": "string"}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/user_list"}}, "401": {"description": "not authenticated"}, "500": {"description": "general error"}}, "description": "Retrieves a list of all resources from the service.", "summary": "", "tags": ["user"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "post": {"parameters": [{"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/user"}}], "responses": {"201": {"description": "created", "schema": {"$ref": "#/definitions/user"}}, "401": {"description": "not authenticated"}, "500": {"description": "general error"}}, "description": "Creates a new resource with data.", "summary": "", "tags": ["user"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}}, "/user/{id}": {"get": {"parameters": [{"in": "path", "name": "id", "description": "ID of user to return", "type": "integer", "required": true}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/user"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Retrieves a single resource with the given id from the service.", "summary": "", "tags": ["user"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "put": {"parameters": [{"in": "path", "name": "id", "description": "ID of user to return", "type": "integer", "required": true}, {"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/user"}}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/user"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Updates the resource identified by id using data.", "summary": "", "tags": ["user"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "patch": {"parameters": [{"in": "path", "name": "id", "description": "ID of user to update", "type": "integer", "required": true}, {"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/user"}}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/user"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Updates the resource identified by id using data.", "summary": "", "tags": ["user"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "delete": {"parameters": [{"in": "path", "name": "id", "description": "ID of user to remove", "type": "integer", "required": true}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/user"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Removes the resource with id.", "summary": "", "tags": ["user"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}}, "/user-relationship": {"get": {"parameters": [{"description": "Number of results to return", "in": "query", "name": "$limit", "type": "integer"}, {"description": "Number of results to skip", "in": "query", "name": "$skip", "type": "integer"}, {"description": "Property to sort results", "in": "query", "name": "$sort", "type": "string"}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/user-relationship_list"}}, "401": {"description": "not authenticated"}, "500": {"description": "general error"}}, "description": "Retrieves a list of all resources from the service.", "summary": "", "tags": ["user-relationship"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "post": {"parameters": [{"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/user-relationship"}}], "responses": {"201": {"description": "created", "schema": {"$ref": "#/definitions/user-relationship"}}, "401": {"description": "not authenticated"}, "500": {"description": "general error"}}, "description": "Creates a new resource with data.", "summary": "", "tags": ["user-relationship"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}}, "/user-relationship/{id}": {"get": {"parameters": [{"in": "path", "name": "id", "description": "ID of user-relationship to return", "type": "integer", "required": true}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/user-relationship"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Retrieves a single resource with the given id from the service.", "summary": "", "tags": ["user-relationship"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "put": {"parameters": [{"in": "path", "name": "id", "description": "ID of user-relationship to return", "type": "integer", "required": true}, {"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/user-relationship"}}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/user-relationship"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Updates the resource identified by id using data.", "summary": "", "tags": ["user-relationship"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "patch": {"parameters": [{"in": "path", "name": "id", "description": "ID of user-relationship to update", "type": "integer", "required": true}, {"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/user-relationship"}}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/user-relationship"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Updates the resource identified by id using data.", "summary": "", "tags": ["user-relationship"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "delete": {"parameters": [{"in": "path", "name": "id", "description": "ID of user-relationship to remove", "type": "integer", "required": true}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/user-relationship"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Removes the resource with id.", "summary": "", "tags": ["user-relationship"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}}, "/user-role": {"get": {"parameters": [{"description": "Number of results to return", "in": "query", "name": "$limit", "type": "integer"}, {"description": "Number of results to skip", "in": "query", "name": "$skip", "type": "integer"}, {"description": "Property to sort results", "in": "query", "name": "$sort", "type": "string"}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/user-role_list"}}, "401": {"description": "not authenticated"}, "500": {"description": "general error"}}, "description": "Retrieves a list of all resources from the service.", "summary": "", "tags": ["user-role"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "post": {"parameters": [{"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/user-role"}}], "responses": {"201": {"description": "created", "schema": {"$ref": "#/definitions/user-role"}}, "401": {"description": "not authenticated"}, "500": {"description": "general error"}}, "description": "Creates a new resource with data.", "summary": "", "tags": ["user-role"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}}, "/user-role/{role}": {"get": {"parameters": [{"in": "path", "name": "role", "description": "ID of user-role to return", "type": "integer", "required": true}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/user-role"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Retrieves a single resource with the given id from the service.", "summary": "", "tags": ["user-role"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "put": {"parameters": [{"in": "path", "name": "role", "description": "ID of user-role to return", "type": "integer", "required": true}, {"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/user-role"}}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/user-role"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Updates the resource identified by id using data.", "summary": "", "tags": ["user-role"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "patch": {"parameters": [{"in": "path", "name": "role", "description": "ID of user-role to update", "type": "integer", "required": true}, {"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/user-role"}}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/user-role"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Updates the resource identified by id using data.", "summary": "", "tags": ["user-role"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "delete": {"parameters": [{"in": "path", "name": "role", "description": "ID of user-role to remove", "type": "integer", "required": true}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/user-role"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Removes the resource with id.", "summary": "", "tags": ["user-role"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}}, "/user-settings": {"get": {"parameters": [{"description": "Number of results to return", "in": "query", "name": "$limit", "type": "integer"}, {"description": "Number of results to skip", "in": "query", "name": "$skip", "type": "integer"}, {"description": "Property to sort results", "in": "query", "name": "$sort", "type": "string"}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/user-settings_list"}}, "401": {"description": "not authenticated"}, "500": {"description": "general error"}}, "description": "Retrieves a list of all resources from the service.", "summary": "", "tags": ["user-settings"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "post": {"parameters": [{"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/user-settings"}}], "responses": {"201": {"description": "created", "schema": {"$ref": "#/definitions/user-settings"}}, "401": {"description": "not authenticated"}, "500": {"description": "general error"}}, "description": "Creates a new resource with data.", "summary": "", "tags": ["user-settings"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}}, "/user-settings/{id}": {"get": {"parameters": [{"in": "path", "name": "id", "description": "ID of user-settings to return", "type": "integer", "required": true}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/user-settings"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Retrieves a single resource with the given id from the service.", "summary": "", "tags": ["user-settings"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "put": {"parameters": [{"in": "path", "name": "id", "description": "ID of user-settings to return", "type": "integer", "required": true}, {"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/user-settings"}}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/user-settings"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Updates the resource identified by id using data.", "summary": "", "tags": ["user-settings"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "patch": {"parameters": [{"in": "path", "name": "id", "description": "ID of user-settings to update", "type": "integer", "required": true}, {"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/user-settings"}}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/user-settings"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Updates the resource identified by id using data.", "summary": "", "tags": ["user-settings"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "delete": {"parameters": [{"in": "path", "name": "id", "description": "ID of user-settings to remove", "type": "integer", "required": true}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/user-settings"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Removes the resource with id.", "summary": "", "tags": ["user-settings"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}}, "/party-user": {"get": {"parameters": [{"description": "Number of results to return", "in": "query", "name": "$limit", "type": "integer"}, {"description": "Number of results to skip", "in": "query", "name": "$skip", "type": "integer"}, {"description": "Property to sort results", "in": "query", "name": "$sort", "type": "string"}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/party-user_list"}}, "401": {"description": "not authenticated"}, "500": {"description": "general error"}}, "description": "Retrieves a list of all resources from the service.", "summary": "", "tags": ["party-user"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "post": {"parameters": [{"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/party-user"}}], "responses": {"201": {"description": "created", "schema": {"$ref": "#/definitions/party-user"}}, "401": {"description": "not authenticated"}, "500": {"description": "general error"}}, "description": "Creates a new resource with data.", "summary": "", "tags": ["party-user"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}}, "/party-user/{id}": {"get": {"parameters": [{"in": "path", "name": "id", "description": "ID of party-user to return", "type": "integer", "required": true}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/party-user"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Retrieves a single resource with the given id from the service.", "summary": "", "tags": ["party-user"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "put": {"parameters": [{"in": "path", "name": "id", "description": "ID of party-user to return", "type": "integer", "required": true}, {"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/party-user"}}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/party-user"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Updates the resource identified by id using data.", "summary": "", "tags": ["party-user"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "patch": {"parameters": [{"in": "path", "name": "id", "description": "ID of party-user to update", "type": "integer", "required": true}, {"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/party-user"}}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/party-user"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Updates the resource identified by id using data.", "summary": "", "tags": ["party-user"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "delete": {"parameters": [{"in": "path", "name": "id", "description": "ID of party-user to remove", "type": "integer", "required": true}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/party-user"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Removes the resource with id.", "summary": "", "tags": ["party-user"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}}, "/group-user": {"get": {"parameters": [{"description": "Number of results to return", "in": "query", "name": "$limit", "type": "integer"}, {"description": "Number of results to skip", "in": "query", "name": "$skip", "type": "integer"}, {"description": "Property to sort results", "in": "query", "name": "$sort", "type": "string"}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/group-user_list"}}, "401": {"description": "not authenticated"}, "500": {"description": "general error"}}, "description": "Retrieves a list of all resources from the service.", "summary": "", "tags": ["group-user"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "post": {"parameters": [{"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/group-user"}}], "responses": {"201": {"description": "created", "schema": {"$ref": "#/definitions/group-user"}}, "401": {"description": "not authenticated"}, "500": {"description": "general error"}}, "description": "Creates a new resource with data.", "summary": "", "tags": ["group-user"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}}, "/group-user/{id}": {"get": {"parameters": [{"in": "path", "name": "id", "description": "ID of group-user to return", "type": "integer", "required": true}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/group-user"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Retrieves a single resource with the given id from the service.", "summary": "", "tags": ["group-user"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "put": {"parameters": [{"in": "path", "name": "id", "description": "ID of group-user to return", "type": "integer", "required": true}, {"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/group-user"}}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/group-user"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Updates the resource identified by id using data.", "summary": "", "tags": ["group-user"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "patch": {"parameters": [{"in": "path", "name": "id", "description": "ID of group-user to update", "type": "integer", "required": true}, {"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/group-user"}}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/group-user"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Updates the resource identified by id using data.", "summary": "", "tags": ["group-user"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "delete": {"parameters": [{"in": "path", "name": "id", "description": "ID of group-user to remove", "type": "integer", "required": true}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/group-user"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Removes the resource with id.", "summary": "", "tags": ["group-user"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}}, "/location-admin": {"get": {"parameters": [{"description": "Number of results to return", "in": "query", "name": "$limit", "type": "integer"}, {"description": "Number of results to skip", "in": "query", "name": "$skip", "type": "integer"}, {"description": "Property to sort results", "in": "query", "name": "$sort", "type": "string"}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/location-admin_list"}}, "401": {"description": "not authenticated"}, "500": {"description": "general error"}}, "description": "Retrieves a list of all resources from the service.", "summary": "", "tags": ["location-admin"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "post": {"parameters": [{"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/location-admin"}}], "responses": {"201": {"description": "created", "schema": {"$ref": "#/definitions/location-admin"}}, "401": {"description": "not authenticated"}, "500": {"description": "general error"}}, "description": "Creates a new resource with data.", "summary": "", "tags": ["location-admin"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}}, "/location-admin/{id}": {"get": {"parameters": [{"in": "path", "name": "id", "description": "ID of location-admin to return", "type": "integer", "required": true}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/location-admin"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Retrieves a single resource with the given id from the service.", "summary": "", "tags": ["location-admin"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "put": {"parameters": [{"in": "path", "name": "id", "description": "ID of location-admin to return", "type": "integer", "required": true}, {"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/location-admin"}}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/location-admin"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Updates the resource identified by id using data.", "summary": "", "tags": ["location-admin"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "patch": {"parameters": [{"in": "path", "name": "id", "description": "ID of location-admin to update", "type": "integer", "required": true}, {"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/location-admin"}}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/location-admin"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Updates the resource identified by id using data.", "summary": "", "tags": ["location-admin"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "delete": {"parameters": [{"in": "path", "name": "id", "description": "ID of location-admin to remove", "type": "integer", "required": true}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/location-admin"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Removes the resource with id.", "summary": "", "tags": ["location-admin"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}}, "/a-i": {"get": {"parameters": [{"description": "Number of results to return", "in": "query", "name": "$limit", "type": "integer"}, {"description": "Number of results to skip", "in": "query", "name": "$skip", "type": "integer"}, {"description": "Property to sort results", "in": "query", "name": "$sort", "type": "string"}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/a-i_list"}}, "401": {"description": "not authenticated"}, "500": {"description": "general error"}}, "description": "Retrieves a list of all resources from the service.", "summary": "", "tags": ["a-i"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "post": {"parameters": [{"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/a-i"}}], "responses": {"201": {"description": "created", "schema": {"$ref": "#/definitions/a-i"}}, "401": {"description": "not authenticated"}, "500": {"description": "general error"}}, "description": "Creates a new resource with data.", "summary": "", "tags": ["a-i"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}}, "/a-i/{id}": {"get": {"parameters": [{"in": "path", "name": "id", "description": "ID of a-i to return", "type": "integer", "required": true}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/a-i"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Retrieves a single resource with the given id from the service.", "summary": "", "tags": ["a-i"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "put": {"parameters": [{"in": "path", "name": "id", "description": "ID of a-i to return", "type": "integer", "required": true}, {"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/a-i"}}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/a-i"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Updates the resource identified by id using data.", "summary": "", "tags": ["a-i"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "patch": {"parameters": [{"in": "path", "name": "id", "description": "ID of a-i to update", "type": "integer", "required": true}, {"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/a-i"}}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/a-i"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Updates the resource identified by id using data.", "summary": "", "tags": ["a-i"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "delete": {"parameters": [{"in": "path", "name": "id", "description": "ID of a-i to remove", "type": "integer", "required": true}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/a-i"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Removes the resource with id.", "summary": "", "tags": ["a-i"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}}, "/authManagement": {"post": {"parameters": [{"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/authManagement"}}], "responses": {"201": {"description": "created", "schema": {"$ref": "#/definitions/authManagement"}}, "401": {"description": "not authenticated"}, "500": {"description": "general error"}}, "description": "Creates a new resource with data.", "summary": "", "tags": ["authManagement"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}}, "/channel": {"get": {"parameters": [{"description": "Number of results to return", "in": "query", "name": "$limit", "type": "integer"}, {"description": "Number of results to skip", "in": "query", "name": "$skip", "type": "integer"}, {"description": "Property to sort results", "in": "query", "name": "$sort", "type": "string"}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/channel_list"}}, "401": {"description": "not authenticated"}, "500": {"description": "general error"}}, "description": "Retrieves a list of all resources from the service.", "summary": "", "tags": ["channel"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "post": {"parameters": [{"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/channel"}}], "responses": {"201": {"description": "created", "schema": {"$ref": "#/definitions/channel"}}, "401": {"description": "not authenticated"}, "500": {"description": "general error"}}, "description": "Creates a new resource with data.", "summary": "", "tags": ["channel"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}}, "/channel/{id}": {"get": {"parameters": [{"in": "path", "name": "id", "description": "ID of channel to return", "type": "integer", "required": true}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/channel"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Retrieves a single resource with the given id from the service.", "summary": "", "tags": ["channel"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "put": {"parameters": [{"in": "path", "name": "id", "description": "ID of channel to return", "type": "integer", "required": true}, {"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/channel"}}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/channel"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Updates the resource identified by id using data.", "summary": "", "tags": ["channel"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "patch": {"parameters": [{"in": "path", "name": "id", "description": "ID of channel to update", "type": "integer", "required": true}, {"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/channel"}}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/channel"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Updates the resource identified by id using data.", "summary": "", "tags": ["channel"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "delete": {"parameters": [{"in": "path", "name": "id", "description": "ID of channel to remove", "type": "integer", "required": true}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/channel"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Removes the resource with id.", "summary": "", "tags": ["channel"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}}, "/email": {"post": {"parameters": [{"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/email"}}], "responses": {"201": {"description": "created", "schema": {"$ref": "#/definitions/email"}}, "401": {"description": "not authenticated"}, "500": {"description": "general error"}}, "description": "Creates a new resource with data.", "summary": "", "tags": ["email"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}}, "/instance-provision": {"get": {"parameters": [{"description": "Number of results to return", "in": "query", "name": "$limit", "type": "integer"}, {"description": "Number of results to skip", "in": "query", "name": "$skip", "type": "integer"}, {"description": "Property to sort results", "in": "query", "name": "$sort", "type": "string"}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/instance-provision_list"}}, "401": {"description": "not authenticated"}, "500": {"description": "general error"}}, "description": "Retrieves a list of all resources from the service.", "summary": "", "tags": ["instance-provision"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "post": {"parameters": [{"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/instance-provision"}}], "responses": {"201": {"description": "created", "schema": {"$ref": "#/definitions/instance-provision"}}, "401": {"description": "not authenticated"}, "500": {"description": "general error"}}, "description": "Creates a new resource with data.", "summary": "", "tags": ["instance-provision"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}}, "/instance-provision/{id}": {"get": {"parameters": [{"in": "path", "name": "id", "description": "ID of instance-provision to return", "type": "integer", "required": true}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/instance-provision"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Retrieves a single resource with the given id from the service.", "summary": "", "tags": ["instance-provision"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "put": {"parameters": [{"in": "path", "name": "id", "description": "ID of instance-provision to return", "type": "integer", "required": true}, {"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/instance-provision"}}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/instance-provision"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Updates the resource identified by id using data.", "summary": "", "tags": ["instance-provision"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "patch": {"parameters": [{"in": "path", "name": "id", "description": "ID of instance-provision to update", "type": "integer", "required": true}, {"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/instance-provision"}}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/instance-provision"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Updates the resource identified by id using data.", "summary": "", "tags": ["instance-provision"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "delete": {"parameters": [{"in": "path", "name": "id", "description": "ID of instance-provision to remove", "type": "integer", "required": true}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/instance-provision"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Removes the resource with id.", "summary": "", "tags": ["instance-provision"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}}, "/login": {"get": {"parameters": [{"description": "Number of results to return", "in": "query", "name": "$limit", "type": "integer"}, {"description": "Number of results to skip", "in": "query", "name": "$skip", "type": "integer"}, {"description": "Property to sort results", "in": "query", "name": "$sort", "type": "string"}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/login_list"}}, "401": {"description": "not authenticated"}, "500": {"description": "general error"}}, "description": "Retrieves a list of all resources from the service.", "summary": "", "tags": ["login"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "post": {"parameters": [{"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/login"}}], "responses": {"201": {"description": "created", "schema": {"$ref": "#/definitions/login"}}, "401": {"description": "not authenticated"}, "500": {"description": "general error"}}, "description": "Creates a new resource with data.", "summary": "", "tags": ["login"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}}, "/login/{id}": {"get": {"parameters": [{"in": "path", "name": "id", "description": "ID of login to return", "type": "integer", "required": true}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/login"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Retrieves a single resource with the given id from the service.", "summary": "", "tags": ["login"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "put": {"parameters": [{"in": "path", "name": "id", "description": "ID of login to return", "type": "integer", "required": true}, {"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/login"}}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/login"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Updates the resource identified by id using data.", "summary": "", "tags": ["login"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "patch": {"parameters": [{"in": "path", "name": "id", "description": "ID of login to update", "type": "integer", "required": true}, {"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/login"}}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/login"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Updates the resource identified by id using data.", "summary": "", "tags": ["login"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "delete": {"parameters": [{"in": "path", "name": "id", "description": "ID of login to remove", "type": "integer", "required": true}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/login"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Removes the resource with id.", "summary": "", "tags": ["login"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}}, "/magic-link": {"get": {"parameters": [{"description": "Number of results to return", "in": "query", "name": "$limit", "type": "integer"}, {"description": "Number of results to skip", "in": "query", "name": "$skip", "type": "integer"}, {"description": "Property to sort results", "in": "query", "name": "$sort", "type": "string"}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/magic-link_list"}}, "401": {"description": "not authenticated"}, "500": {"description": "general error"}}, "description": "Retrieves a list of all resources from the service.", "summary": "", "tags": ["magic-link"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "post": {"parameters": [{"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/magic-link"}}], "responses": {"201": {"description": "created", "schema": {"$ref": "#/definitions/magic-link"}}, "401": {"description": "not authenticated"}, "500": {"description": "general error"}}, "description": "Creates a new resource with data.", "summary": "", "tags": ["magic-link"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}}, "/magic-link/{id}": {"get": {"parameters": [{"in": "path", "name": "id", "description": "ID of magic-link to return", "type": "integer", "required": true}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/magic-link"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Retrieves a single resource with the given id from the service.", "summary": "", "tags": ["magic-link"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "put": {"parameters": [{"in": "path", "name": "id", "description": "ID of magic-link to return", "type": "integer", "required": true}, {"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/magic-link"}}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/magic-link"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Updates the resource identified by id using data.", "summary": "", "tags": ["magic-link"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "patch": {"parameters": [{"in": "path", "name": "id", "description": "ID of magic-link to update", "type": "integer", "required": true}, {"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/magic-link"}}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/magic-link"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Updates the resource identified by id using data.", "summary": "", "tags": ["magic-link"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "delete": {"parameters": [{"in": "path", "name": "id", "description": "ID of magic-link to remove", "type": "integer", "required": true}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/magic-link"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Removes the resource with id.", "summary": "", "tags": ["magic-link"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}}, "/message": {"get": {"parameters": [{"description": "Number of results to return", "in": "query", "name": "$limit", "type": "integer"}, {"description": "Number of results to skip", "in": "query", "name": "$skip", "type": "integer"}, {"description": "Property to sort results", "in": "query", "name": "$sort", "type": "string"}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/message_list"}}, "401": {"description": "not authenticated"}, "500": {"description": "general error"}}, "description": "Retrieves a list of all resources from the service.", "summary": "", "tags": ["message"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "post": {"parameters": [{"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/message"}}], "responses": {"201": {"description": "created", "schema": {"$ref": "#/definitions/message"}}, "401": {"description": "not authenticated"}, "500": {"description": "general error"}}, "description": "Creates a new resource with data.", "summary": "", "tags": ["message"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}}, "/message/{id}": {"get": {"parameters": [{"in": "path", "name": "id", "description": "ID of message to return", "type": "integer", "required": true}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/message"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Retrieves a single resource with the given id from the service.", "summary": "", "tags": ["message"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "put": {"parameters": [{"in": "path", "name": "id", "description": "ID of message to return", "type": "integer", "required": true}, {"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/message"}}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/message"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Updates the resource identified by id using data.", "summary": "", "tags": ["message"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "patch": {"parameters": [{"in": "path", "name": "id", "description": "ID of message to update", "type": "integer", "required": true}, {"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/message"}}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/message"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Updates the resource identified by id using data.", "summary": "", "tags": ["message"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "delete": {"parameters": [{"in": "path", "name": "id", "description": "ID of message to remove", "type": "integer", "required": true}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/message"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Removes the resource with id.", "summary": "", "tags": ["message"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}}, "/message-status": {"get": {"parameters": [{"description": "Number of results to return", "in": "query", "name": "$limit", "type": "integer"}, {"description": "Number of results to skip", "in": "query", "name": "$skip", "type": "integer"}, {"description": "Property to sort results", "in": "query", "name": "$sort", "type": "string"}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/message-status_list"}}, "401": {"description": "not authenticated"}, "500": {"description": "general error"}}, "description": "Retrieves a list of all resources from the service.", "summary": "", "tags": ["message-status"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "post": {"parameters": [{"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/message-status"}}], "responses": {"201": {"description": "created", "schema": {"$ref": "#/definitions/message-status"}}, "401": {"description": "not authenticated"}, "500": {"description": "general error"}}, "description": "Creates a new resource with data.", "summary": "", "tags": ["message-status"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}}, "/message-status/{id}": {"get": {"parameters": [{"in": "path", "name": "id", "description": "ID of message-status to return", "type": "integer", "required": true}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/message-status"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Retrieves a single resource with the given id from the service.", "summary": "", "tags": ["message-status"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "put": {"parameters": [{"in": "path", "name": "id", "description": "ID of message-status to return", "type": "integer", "required": true}, {"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/message-status"}}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/message-status"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Updates the resource identified by id using data.", "summary": "", "tags": ["message-status"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "patch": {"parameters": [{"in": "path", "name": "id", "description": "ID of message-status to update", "type": "integer", "required": true}, {"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/message-status"}}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/message-status"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Updates the resource identified by id using data.", "summary": "", "tags": ["message-status"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "delete": {"parameters": [{"in": "path", "name": "id", "description": "ID of message-status to remove", "type": "integer", "required": true}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/message-status"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Removes the resource with id.", "summary": "", "tags": ["message-status"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}}, "/sms": {"get": {"parameters": [{"description": "Number of results to return", "in": "query", "name": "$limit", "type": "integer"}, {"description": "Number of results to skip", "in": "query", "name": "$skip", "type": "integer"}, {"description": "Property to sort results", "in": "query", "name": "$sort", "type": "string"}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/sms_list"}}, "401": {"description": "not authenticated"}, "500": {"description": "general error"}}, "description": "Retrieves a list of all resources from the service.", "summary": "", "tags": ["sms"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "post": {"parameters": [{"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/sms"}}], "responses": {"201": {"description": "created", "schema": {"$ref": "#/definitions/sms"}}, "401": {"description": "not authenticated"}, "500": {"description": "general error"}}, "description": "Creates a new resource with data.", "summary": "", "tags": ["sms"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}}, "/sms/{id}": {"get": {"parameters": [{"in": "path", "name": "id", "description": "ID of sms to return", "type": "integer", "required": true}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/sms"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Retrieves a single resource with the given id from the service.", "summary": "", "tags": ["sms"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "put": {"parameters": [{"in": "path", "name": "id", "description": "ID of sms to return", "type": "integer", "required": true}, {"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/sms"}}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/sms"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Updates the resource identified by id using data.", "summary": "", "tags": ["sms"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "patch": {"parameters": [{"in": "path", "name": "id", "description": "ID of sms to update", "type": "integer", "required": true}, {"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/sms"}}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/sms"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Updates the resource identified by id using data.", "summary": "", "tags": ["sms"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "delete": {"parameters": [{"in": "path", "name": "id", "description": "ID of sms to remove", "type": "integer", "required": true}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/sms"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Removes the resource with id.", "summary": "", "tags": ["sms"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}}, "/tag": {"get": {"parameters": [{"description": "Number of results to return", "in": "query", "name": "$limit", "type": "integer"}, {"description": "Number of results to skip", "in": "query", "name": "$skip", "type": "integer"}, {"description": "Property to sort results", "in": "query", "name": "$sort", "type": "string"}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/tag_list"}}, "401": {"description": "not authenticated"}, "500": {"description": "general error"}}, "description": "Retrieves a list of all resources from the service.", "summary": "", "tags": ["tag"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "post": {"parameters": [{"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/tag"}}], "responses": {"201": {"description": "created", "schema": {"$ref": "#/definitions/tag"}}, "401": {"description": "not authenticated"}, "500": {"description": "general error"}}, "description": "Creates a new resource with data.", "summary": "", "tags": ["tag"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}}, "/tag/{tag}": {"get": {"parameters": [{"in": "path", "name": "tag", "description": "ID of tag to return", "type": "integer", "required": true}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/tag"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Retrieves a single resource with the given id from the service.", "summary": "", "tags": ["tag"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "put": {"parameters": [{"in": "path", "name": "tag", "description": "ID of tag to return", "type": "integer", "required": true}, {"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/tag"}}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/tag"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Updates the resource identified by id using data.", "summary": "", "tags": ["tag"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "patch": {"parameters": [{"in": "path", "name": "tag", "description": "ID of tag to update", "type": "integer", "required": true}, {"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/tag"}}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/tag"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Updates the resource identified by id using data.", "summary": "", "tags": ["tag"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "delete": {"parameters": [{"in": "path", "name": "tag", "description": "ID of tag to remove", "type": "integer", "required": true}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/tag"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Removes the resource with id.", "summary": "", "tags": ["tag"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}}, "/subscription-confirm": {"get": {"parameters": [{"description": "Number of results to return", "in": "query", "name": "$limit", "type": "integer"}, {"description": "Number of results to skip", "in": "query", "name": "$skip", "type": "integer"}, {"description": "Property to sort results", "in": "query", "name": "$sort", "type": "string"}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/subscription-confirm_list"}}, "401": {"description": "not authenticated"}, "500": {"description": "general error"}}, "description": "Retrieves a list of all resources from the service.", "summary": "", "tags": ["subscription-confirm"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "post": {"parameters": [{"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/subscription-confirm"}}], "responses": {"201": {"description": "created", "schema": {"$ref": "#/definitions/subscription-confirm"}}, "401": {"description": "not authenticated"}, "500": {"description": "general error"}}, "description": "Creates a new resource with data.", "summary": "", "tags": ["subscription-confirm"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}}, "/subscription-confirm/{id}": {"get": {"parameters": [{"in": "path", "name": "id", "description": "ID of subscription-confirm to return", "type": "integer", "required": true}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/subscription-confirm"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Retrieves a single resource with the given id from the service.", "summary": "", "tags": ["subscription-confirm"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "put": {"parameters": [{"in": "path", "name": "id", "description": "ID of subscription-confirm to return", "type": "integer", "required": true}, {"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/subscription-confirm"}}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/subscription-confirm"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Updates the resource identified by id using data.", "summary": "", "tags": ["subscription-confirm"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "patch": {"parameters": [{"in": "path", "name": "id", "description": "ID of subscription-confirm to update", "type": "integer", "required": true}, {"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/subscription-confirm"}}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/subscription-confirm"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Updates the resource identified by id using data.", "summary": "", "tags": ["subscription-confirm"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "delete": {"parameters": [{"in": "path", "name": "id", "description": "ID of subscription-confirm to remove", "type": "integer", "required": true}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/subscription-confirm"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Removes the resource with id.", "summary": "", "tags": ["subscription-confirm"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}}, "/upload/{id}": {"get": {"parameters": [{"in": "path", "name": "id", "description": "ID of upload to return", "type": "integer", "required": true}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/upload"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Retrieves a single resource with the given id from the service.", "summary": "", "tags": ["upload"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "delete": {"parameters": [{"in": "path", "name": "id", "description": "ID of upload to remove", "type": "integer", "required": true}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/upload"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Removes the resource with id.", "summary": "", "tags": ["upload"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}}, "/upload": {"post": {"parameters": [{"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/upload"}}], "responses": {"201": {"description": "created", "schema": {"$ref": "#/definitions/upload"}}, "401": {"description": "not authenticated"}, "500": {"description": "general error"}}, "description": "Creates a new resource with data.", "summary": "", "tags": ["upload"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}}, "/media-search": {"get": {"parameters": [{"description": "Number of results to return", "in": "query", "name": "$limit", "type": "integer"}, {"description": "Number of results to skip", "in": "query", "name": "$skip", "type": "integer"}, {"description": "Property to sort results", "in": "query", "name": "$sort", "type": "string"}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/media-search_list"}}, "401": {"description": "not authenticated"}, "500": {"description": "general error"}}, "description": "Retrieves a list of all resources from the service.", "summary": "", "tags": ["media-search"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "post": {"parameters": [{"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/media-search"}}], "responses": {"201": {"description": "created", "schema": {"$ref": "#/definitions/media-search"}}, "401": {"description": "not authenticated"}, "500": {"description": "general error"}}, "description": "Creates a new resource with data.", "summary": "", "tags": ["media-search"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}}, "/media-search/{id}": {"get": {"parameters": [{"in": "path", "name": "id", "description": "ID of media-search to return", "type": "integer", "required": true}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/media-search"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Retrieves a single resource with the given id from the service.", "summary": "", "tags": ["media-search"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "put": {"parameters": [{"in": "path", "name": "id", "description": "ID of media-search to return", "type": "integer", "required": true}, {"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/media-search"}}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/media-search"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Updates the resource identified by id using data.", "summary": "", "tags": ["media-search"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "patch": {"parameters": [{"in": "path", "name": "id", "description": "ID of media-search to update", "type": "integer", "required": true}, {"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/media-search"}}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/media-search"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Updates the resource identified by id using data.", "summary": "", "tags": ["media-search"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "delete": {"parameters": [{"in": "path", "name": "id", "description": "ID of media-search to remove", "type": "integer", "required": true}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/media-search"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Removes the resource with id.", "summary": "", "tags": ["media-search"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}}, "/meta": {"get": {"parameters": [{"description": "Number of results to return", "in": "query", "name": "$limit", "type": "integer"}, {"description": "Number of results to skip", "in": "query", "name": "$skip", "type": "integer"}, {"description": "Property to sort results", "in": "query", "name": "$sort", "type": "string"}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/meta_list"}}, "401": {"description": "not authenticated"}, "500": {"description": "general error"}}, "description": "Retrieves a list of all resources from the service.", "summary": "", "tags": ["meta"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "post": {"parameters": [{"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/meta"}}], "responses": {"201": {"description": "created", "schema": {"$ref": "#/definitions/meta"}}, "401": {"description": "not authenticated"}, "500": {"description": "general error"}}, "description": "Creates a new resource with data.", "summary": "", "tags": ["meta"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}}, "/meta/{id}": {"get": {"parameters": [{"in": "path", "name": "id", "description": "ID of meta to return", "type": "integer", "required": true}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/meta"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Retrieves a single resource with the given id from the service.", "summary": "", "tags": ["meta"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "put": {"parameters": [{"in": "path", "name": "id", "description": "ID of meta to return", "type": "integer", "required": true}, {"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/meta"}}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/meta"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Updates the resource identified by id using data.", "summary": "", "tags": ["meta"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "patch": {"parameters": [{"in": "path", "name": "id", "description": "ID of meta to update", "type": "integer", "required": true}, {"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/meta"}}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/meta"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Updates the resource identified by id using data.", "summary": "", "tags": ["meta"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "delete": {"parameters": [{"in": "path", "name": "id", "description": "ID of meta to remove", "type": "integer", "required": true}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/meta"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Removes the resource with id.", "summary": "", "tags": ["meta"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}}, "/media/{id}": {"get": {"parameters": [{"in": "path", "name": "id", "description": "ID of media to return", "type": "integer", "required": true}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/media"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Retrieves a single resource with the given id from the service.", "summary": "", "tags": ["media"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "delete": {"parameters": [{"in": "path", "name": "id", "description": "ID of media to remove", "type": "integer", "required": true}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/media"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Removes the resource with id.", "summary": "", "tags": ["media"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}}, "/media": {"post": {"parameters": [{"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/media"}}], "responses": {"201": {"description": "created", "schema": {"$ref": "#/definitions/media"}}, "401": {"description": "not authenticated"}, "500": {"description": "general error"}}, "description": "Creates a new resource with data.", "summary": "", "tags": ["media"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}}, "/publish-scene": {"get": {"parameters": [{"description": "Number of results to return", "in": "query", "name": "$limit", "type": "integer"}, {"description": "Number of results to skip", "in": "query", "name": "$skip", "type": "integer"}, {"description": "Property to sort results", "in": "query", "name": "$sort", "type": "string"}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/publish-scene_list"}}, "401": {"description": "not authenticated"}, "500": {"description": "general error"}}, "description": "Retrieves a list of all resources from the service.", "summary": "", "tags": ["publish-scene"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "post": {"parameters": [{"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/publish-scene"}}], "responses": {"201": {"description": "created", "schema": {"$ref": "#/definitions/publish-scene"}}, "401": {"description": "not authenticated"}, "500": {"description": "general error"}}, "description": "Creates a new resource with data.", "summary": "", "tags": ["publish-scene"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}}, "/publish-scene/{id}": {"get": {"parameters": [{"in": "path", "name": "id", "description": "ID of publish-scene to return", "type": "integer", "required": true}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/publish-scene"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Retrieves a single resource with the given id from the service.", "summary": "", "tags": ["publish-scene"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "put": {"parameters": [{"in": "path", "name": "id", "description": "ID of publish-scene to return", "type": "integer", "required": true}, {"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/publish-scene"}}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/publish-scene"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Updates the resource identified by id using data.", "summary": "", "tags": ["publish-scene"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "patch": {"parameters": [{"in": "path", "name": "id", "description": "ID of publish-scene to update", "type": "integer", "required": true}, {"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/publish-scene"}}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/publish-scene"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Updates the resource identified by id using data.", "summary": "", "tags": ["publish-scene"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "delete": {"parameters": [{"in": "path", "name": "id", "description": "ID of publish-scene to remove", "type": "integer", "required": true}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/publish-scene"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Removes the resource with id.", "summary": "", "tags": ["publish-scene"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}}, "/resolve-media": {"get": {"parameters": [{"description": "Number of results to return", "in": "query", "name": "$limit", "type": "integer"}, {"description": "Number of results to skip", "in": "query", "name": "$skip", "type": "integer"}, {"description": "Property to sort results", "in": "query", "name": "$sort", "type": "string"}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/resolve-media_list"}}, "401": {"description": "not authenticated"}, "500": {"description": "general error"}}, "description": "Retrieves a list of all resources from the service.", "summary": "", "tags": ["resolve-media"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "post": {"parameters": [{"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/resolve-media"}}], "responses": {"201": {"description": "created", "schema": {"$ref": "#/definitions/resolve-media"}}, "401": {"description": "not authenticated"}, "500": {"description": "general error"}}, "description": "Creates a new resource with data.", "summary": "", "tags": ["resolve-media"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}}, "/resolve-media/{id}": {"get": {"parameters": [{"in": "path", "name": "id", "description": "ID of resolve-media to return", "type": "integer", "required": true}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/resolve-media"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Retrieves a single resource with the given id from the service.", "summary": "", "tags": ["resolve-media"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "put": {"parameters": [{"in": "path", "name": "id", "description": "ID of resolve-media to return", "type": "integer", "required": true}, {"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/resolve-media"}}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/resolve-media"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Updates the resource identified by id using data.", "summary": "", "tags": ["resolve-media"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "patch": {"parameters": [{"in": "path", "name": "id", "description": "ID of resolve-media to update", "type": "integer", "required": true}, {"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/resolve-media"}}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/resolve-media"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Updates the resource identified by id using data.", "summary": "", "tags": ["resolve-media"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}, "delete": {"parameters": [{"in": "path", "name": "id", "description": "ID of resolve-media to remove", "type": "integer", "required": true}], "responses": {"200": {"description": "success", "schema": {"$ref": "#/definitions/resolve-media"}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Removes the resource with id.", "summary": "", "tags": ["resolve-media"], "consumes": ["application/json"], "produces": ["application/json"], "security": []}}}, "definitions": {}, "swagger": "2.0", "schemes": ["http"], "tags": [{"name": "authentication", "description": "A authentication service"}, {"name": "channel-type", "description": "A channel-type service"}, {"name": "collection-type", "description": "A collection-type service"}, {"name": "component-type", "description": "A component-type service"}, {"name": "group-user-rank", "description": "A group-user-rank service"}, {"name": "invite-type", "description": "A invite-type service"}, {"name": "location-type", "description": "A location-type service"}, {"name": "seat-status", "description": "A seat-status service"}, {"name": "static-resource-type", "description": "A static-resource-type service"}, {"name": "subscription-level", "description": "A subscription-level service"}, {"name": "subscription-type", "description": "A subscription-type service"}, {"name": "user-relationship-type", "description": "A user-relationship-type service"}, {"name": "attribution", "description": "A attribution service"}, {"name": "collection", "description": "A collection service"}, {"name": "component", "description": "A component service"}, {"name": "entity", "description": "A entity service"}, {"name": "group", "description": "A group service"}, {"name": "identity-provider", "description": "A identity-provider service"}, {"name": "instance", "description": "A instance service"}, {"name": "invite", "description": "A invite service"}, {"name": "license", "description": "A license service"}, {"name": "location", "description": "A location service"}, {"name": "location-settings", "description": "A location-settings service"}, {"name": "location-ban", "description": "A location-ban service"}, {"name": "login-token", "description": "A login-token service"}, {"name": "party", "description": "A party service"}, {"name": "scene", "description": "A scene service"}, {"name": "rtc-ports", "description": "A rtc-ports service"}, {"name": "seat", "description": "A seat service"}, {"name": "static-resource", "description": "A static-resource service"}, {"name": "subscription", "description": "A subscription service"}, {"name": "user", "description": "A user service"}, {"name": "user-relationship", "description": "A user-relationship service"}, {"name": "user-role", "description": "A user-role service"}, {"name": "user-settings", "description": "A user-settings service"}, {"name": "party-user", "description": "A party-user service"}, {"name": "group-user", "description": "A group-user service"}, {"name": "location-admin", "description": "A location-admin service"}, {"name": "a-i", "description": "A a-i service"}, {"name": "authManagement", "description": "A authManagement service"}, {"name": "channel", "description": "A channel service"}, {"name": "email", "description": "A email service"}, {"name": "instance-provision", "description": "A instance-provision service"}, {"name": "login", "description": "A login service"}, {"name": "magic-link", "description": "A magic-link service"}, {"name": "message", "description": "A message service"}, {"name": "message-status", "description": "A message-status service"}, {"name": "sms", "description": "A sms service"}, {"name": "tag", "description": "A tag service"}, {"name": "subscription-confirm", "description": "A subscription-confirm service"}, {"name": "upload", "description": "A upload service"}, {"name": "media-search", "description": "A media-search service"}, {"name": "meta", "description": "A meta service"}, {"name": "media", "description": "A media service"}, {"name": "publish-scene", "description": "A publish-scene service"}, {"name": "resolve-media", "description": "A resolve-media service"}], "basePath": "/", "consumes": ["application/json"], "produces": ["application/json"]}