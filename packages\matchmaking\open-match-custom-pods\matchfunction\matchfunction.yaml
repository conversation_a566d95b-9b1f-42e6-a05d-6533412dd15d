# Copyright 2019 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

apiVersion: v1
kind: Pod
metadata:
  name: ir-engine-matchmaking-matchfunction
  namespace: ir-engine-matchmaking
  labels:
    app: ir-engine-matchmaking
    component: matchfunction
spec:
  containers:
  - name: ir-engine-matchmaking-matchfunction
    image: REGISTRY_PLACEHOLDER/ir-engine-matchmaking-matchfunction:latest
    imagePullPolicy: Always
    ports:
    - name: grpc
      containerPort: 50502
---
kind: Service
apiVersion: v1
metadata:
  name: ir-engine-matchmaking-matchfunction
  namespace: ir-engine-matchmaking
  labels:
    app: ir-engine-matchmaking
    component: matchfunction
spec:
  selector:
    app: ir-engine-matchmaking
    component: matchfunction
  clusterIP: None
  type: ClusterIP
  ports:
  - name: grpc
    protocol: TCP
    port: 50502
