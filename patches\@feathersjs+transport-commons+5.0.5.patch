diff --git a/node_modules/@feathersjs/transport-commons/lib/http.js b/node_modules/@feathersjs/transport-commons/lib/http.js
index f80c1b8..5d96ed5 100644
--- a/node_modules/@feathersjs/transport-commons/lib/http.js
+++ b/node_modules/@feathersjs/transport-commons/lib/http.js
@@ -49,15 +49,15 @@ function getStatusCode(context, body, location) {
     if (http.status) {
         return http.status;
     }
-    if (context.method === 'create') {
-        return exports.statusCodes.created;
-    }
     if (location !== undefined) {
         return exports.statusCodes.seeOther;
     }
     if (!body) {
         return exports.statusCodes.noContent;
     }
+    if (context.method === 'create') {
+        return exports.statusCodes.created;
+    }
     return exports.statusCodes.success;
 }
 exports.getStatusCode = getStatusCode;
diff --git a/node_modules/@feathersjs/transport-commons/lib/http.js.map b/node_modules/@feathersjs/transport-commons/lib/http.js.map
index 35cb7ab..c146c43 100644
--- a/node_modules/@feathersjs/transport-commons/lib/http.js.map
+++ b/node_modules/@feathersjs/transport-commons/lib/http.js.map
@@ -1 +1 @@
-{"version":3,"file":"http.js","sourceRoot":"","sources":["../src/http.ts"],"names":[],"mappings":";;;;;;AAAA,gDAAyD;AAEzD,0DAAiC;AAEpB,QAAA,aAAa,GAAG,kBAAkB,CAAA;AAQlC,QAAA,WAAW,GAAG;IACzB,OAAO,EAAE,GAAG;IACZ,SAAS,EAAE,GAAG;IACd,gBAAgB,EAAE,GAAG;IACrB,OAAO,EAAE,GAAG;IACZ,QAAQ,EAAE,GAAG;CACd,CAAA;AAEY,QAAA,YAAY,GAA8B;IACrD,IAAI,EAAE,QAAQ;IACd,KAAK,EAAE,OAAO;IACd,GAAG,EAAE,QAAQ;IACb,MAAM,EAAE,QAAQ;CACjB,CAAA;AAED,SAAgB,gBAAgB,CAAC,WAAmB,EAAE,EAAW,EAAE,cAAuB;IACxF,MAAM,UAAU,GAAG,WAAW,CAAC,WAAW,EAAE,CAAA;IAE5C,IAAI,UAAU,KAAK,MAAM,IAAI,cAAc,EAAE;QAC3C,OAAO,cAAc,CAAA;KACtB;IAED,MAAM,YAAY,GAAG,oBAAY,CAAC,UAAU,CAAC,CAAA;IAE7C,IAAI,YAAY,EAAE;QAChB,OAAO,YAAY,CAAA;KACpB;IAED,IAAI,UAAU,KAAK,KAAK,EAAE;QACxB,OAAO,EAAE,KAAK,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAA;KACpC;IAED,MAAM,IAAI,sBAAgB,CAAC,UAAU,WAAW,cAAc,CAAC,CAAA;AACjE,CAAC;AAlBD,4CAkBC;AAEY,QAAA,YAAY,GAAG;IAC1B,GAAG,EAAE,CAAC,EAAE,EAAE,EAAE,MAAM,EAAiB,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,MAAM,CAAC;IACpD,IAAI,EAAE,CAAC,EAAE,MAAM,EAAiB,EAAE,EAAE,CAAC,CAAC,MAAM,CAAC;IAC7C,MAAM,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAiB,EAAE,EAAE,CAAC,CAAC,IAAI,EAAE,MAAM,CAAC;IAC3D,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,IAAI,EAAE,MAAM,EAAiB,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,IAAI,EAAE,MAAM,CAAC;IACnE,KAAK,EAAE,CAAC,EAAE,EAAE,EAAE,IAAI,EAAE,MAAM,EAAiB,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,IAAI,EAAE,MAAM,CAAC;IAClE,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,MAAM,EAAiB,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,MAAM,CAAC;IACvD,OAAO,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAiB,EAAE,EAAE,CAAC,CAAC,IAAI,EAAE,MAAM,CAAC;CAC7D,CAAA;AAED,SAAgB,aAAa,CAAC,OAAoB,EAAE,IAAS,EAAE,QAA2B;IACxF,MAAM,EAAE,IAAI,GAAG,EAAE,EAAE,GAAG,OAAO,CAAA;IAE7B,IAAI,IAAI,CAAC,MAAM,EAAE;QACf,OAAO,IAAI,CAAC,MAAM,CAAA;KACnB;IAED,IAAI,OAAO,CAAC,MAAM,KAAK,QAAQ,EAAE;QAC/B,OAAO,mBAAW,CAAC,OAAO,CAAA;KAC3B;IAED,IAAI,QAAQ,KAAK,SAAS,EAAE;QAC1B,OAAO,mBAAW,CAAC,QAAQ,CAAA;KAC5B;IAED,IAAI,CAAC,IAAI,EAAE;QACT,OAAO,mBAAW,CAAC,SAAS,CAAA;KAC7B;IAED,OAAO,mBAAW,CAAC,OAAO,CAAA;AAC5B,CAAC;AApBD,sCAoBC;AAED,SAAgB,WAAW,CAAC,OAAoB;IAC9C,MAAM,EAAE,IAAI,GAAG,EAAE,EAAE,GAAG,OAAO,CAAA;IAC7B,MAAM,IAAI,GAAG,OAAO,CAAC,QAAQ,KAAK,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAA;IAE/E,IAAI,OAAO,GAAG,IAAI,CAAC,OAAO,IAAI,EAAE,CAAA;IAChC,IAAI,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAA;IAE/B,IAAI,IAAI,CAAC,QAAQ,KAAK,SAAS,EAAE;QAC/B,QAAQ,GAAG,IAAA,mBAAS,EAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;QACnC,OAAO,GAAG,EAAE,GAAG,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,CAAA;KAC7C;IAED,MAAM,MAAM,GAAG,aAAa,CAAC,OAAO,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAA;IAErD,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,CAAA;AAClC,CAAC;AAfD,kCAeC"}
\ No newline at end of file
+{"version":3,"file":"http.js","sourceRoot":"","sources":["../src/http.ts"],"names":[],"mappings":";;;;;;AAAA,gDAAyD;AAEzD,0DAAiC;AAEpB,QAAA,aAAa,GAAG,kBAAkB,CAAA;AAQlC,QAAA,WAAW,GAAG;IACzB,OAAO,EAAE,GAAG;IACZ,SAAS,EAAE,GAAG;IACd,gBAAgB,EAAE,GAAG;IACrB,OAAO,EAAE,GAAG;IACZ,QAAQ,EAAE,GAAG;CACd,CAAA;AAEY,QAAA,YAAY,GAA8B;IACrD,IAAI,EAAE,QAAQ;IACd,KAAK,EAAE,OAAO;IACd,GAAG,EAAE,QAAQ;IACb,MAAM,EAAE,QAAQ;CACjB,CAAA;AAED,SAAgB,gBAAgB,CAAC,WAAmB,EAAE,EAAW,EAAE,cAAuB;IACxF,MAAM,UAAU,GAAG,WAAW,CAAC,WAAW,EAAE,CAAA;IAE5C,IAAI,UAAU,KAAK,MAAM,IAAI,cAAc,EAAE;QAC3C,OAAO,cAAc,CAAA;KACtB;IAED,MAAM,YAAY,GAAG,oBAAY,CAAC,UAAU,CAAC,CAAA;IAE7C,IAAI,YAAY,EAAE;QAChB,OAAO,YAAY,CAAA;KACpB;IAED,IAAI,UAAU,KAAK,KAAK,EAAE;QACxB,OAAO,EAAE,KAAK,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAA;KACpC;IAED,MAAM,IAAI,sBAAgB,CAAC,UAAU,WAAW,cAAc,CAAC,CAAA;AACjE,CAAC;AAlBD,4CAkBC;AAEY,QAAA,YAAY,GAAG;IAC1B,GAAG,EAAE,CAAC,EAAE,EAAE,EAAE,MAAM,EAAiB,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,MAAM,CAAC;IACpD,IAAI,EAAE,CAAC,EAAE,MAAM,EAAiB,EAAE,EAAE,CAAC,CAAC,MAAM,CAAC;IAC7C,MAAM,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAiB,EAAE,EAAE,CAAC,CAAC,IAAI,EAAE,MAAM,CAAC;IAC3D,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,IAAI,EAAE,MAAM,EAAiB,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,IAAI,EAAE,MAAM,CAAC;IACnE,KAAK,EAAE,CAAC,EAAE,EAAE,EAAE,IAAI,EAAE,MAAM,EAAiB,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,IAAI,EAAE,MAAM,CAAC;IAClE,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,MAAM,EAAiB,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,MAAM,CAAC;IACvD,OAAO,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAiB,EAAE,EAAE,CAAC,CAAC,IAAI,EAAE,MAAM,CAAC;CAC7D,CAAA;AAED,SAAgB,aAAa,CAAC,OAAoB,EAAE,IAAS,EAAE,QAA2B;IACxF,MAAM,EAAE,IAAI,GAAG,EAAE,EAAE,GAAG,OAAO,CAAA;IAE7B,IAAI,IAAI,CAAC,MAAM,EAAE;QACf,OAAO,IAAI,CAAC,MAAM,CAAA;KACnB;IAED,IAAI,QAAQ,KAAK,SAAS,EAAE;QAC1B,OAAO,mBAAW,CAAC,QAAQ,CAAA;KAC5B;IAED,IAAI,CAAC,IAAI,EAAE;QACT,OAAO,mBAAW,CAAC,SAAS,CAAA;KAC7B;IAED,IAAI,OAAO,CAAC,MAAM,KAAK,QAAQ,EAAE;QAC/B,OAAO,mBAAW,CAAC,OAAO,CAAA;KAC3B;IAED,OAAO,mBAAW,CAAC,OAAO,CAAA;AAC5B,CAAC;AApBD,sCAoBC;AAED,SAAgB,WAAW,CAAC,OAAoB;IAC9C,MAAM,EAAE,IAAI,GAAG,EAAE,EAAE,GAAG,OAAO,CAAA;IAC7B,MAAM,IAAI,GAAG,OAAO,CAAC,QAAQ,KAAK,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAA;IAE/E,IAAI,OAAO,GAAG,IAAI,CAAC,OAAO,IAAI,EAAE,CAAA;IAChC,IAAI,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAA;IAE/B,IAAI,IAAI,CAAC,QAAQ,KAAK,SAAS,EAAE;QAC/B,QAAQ,GAAG,IAAA,mBAAS,EAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;QACnC,OAAO,GAAG,EAAE,GAAG,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,CAAA;KAC7C;IAED,MAAM,MAAM,GAAG,aAAa,CAAC,OAAO,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAA;IAErD,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,CAAA;AAClC,CAAC;AAfD,kCAeC"}
\ No newline at end of file
