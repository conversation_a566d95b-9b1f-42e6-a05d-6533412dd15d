/*
CPAL-1.0 License

The contents of this file are subject to the Common Public Attribution License
Version 1.0. (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at
https://github.com/ir-engine/ir-engine/blob/dev/LICENSE.
The License is based on the Mozilla Public License Version 1.1, but Sections 14
and 15 have been added to cover use of software over a computer network and
provide for limited attribution for the Original Developer. In addition,
Exhibit A has been modified to be consistent with Exhibit B.

Software distributed under the License is distributed on an "AS IS" basis,
WITHOUT WARRANTY OF ANY KIND, either express or implied. See the License for the
specific language governing rights and limitations under the License.

The Original Code is Infinite Reality Engine.

The Original Developer is the Initial Developer. The Initial Developer of the
Original Code is the Infinite Reality Engine team.

All portions of the code written by the Infinite Reality Engine team are Copyright © 2021-2025
Infinite Reality Engine. All Rights Reserved.
*/

import { InputField, InputFieldProps } from '@ir-engine/ui/viewer'
import React, { useRef } from 'react'

interface FieldItemProps extends InputFieldProps {
  label: string
  className?: string
}

const FieldItem: React.FC<FieldItemProps> = ({
  label,
  className = '',
  value,
  onChange,
  isDirty,
  onReset,
  ...inputProps
}) => {
  const ref = useRef<HTMLInputElement>(null)
  return (
    <div
      onClick={(e) => {
        ref.current?.focus()
        if (e.currentTarget === e.target) {
          ref.current?.setSelectionRange(value.length, value.length)
        }
      }}
      className={`flex w-full items-center justify-between bg-black/10 px-4 py-3.5 text-white/90 transition-colors hover:bg-black/5 ${className}`}
    >
      <span className="text-sm font-medium">{label}</span>
      <InputField
        ref={ref}
        label="Display Name"
        value={value}
        onChange={onChange}
        isDirty={isDirty}
        onReset={onReset}
        {...inputProps}
      />
    </div>
  )
}

export default FieldItem
