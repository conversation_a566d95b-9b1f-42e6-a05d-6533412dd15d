/*
CPAL-1.0 License

The contents of this file are subject to the Common Public Attribution License
Version 1.0. (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at
https://github.com/ir-engine/ir-engine/blob/dev/LICENSE.
The License is based on the Mozilla Public License Version 1.1, but Sections 14
and 15 have been added to cover use of software over a computer network and 
provide for limited attribution for the Original Developer. In addition, 
Exhibit A has been modified to be consistent with Exhibit B.

Software distributed under the License is distributed on an "AS IS" basis,
WITHOUT WARRANTY OF ANY KIND, either express or implied. See the License for the
specific language governing rights and limitations under the License.

The Original Code is Infinite Reality Engine.

The Original Developer is the Initial Developer. The Initial Developer of the
Original Code is the Infinite Reality Engine team.

All portions of the code written by the Infinite Reality Engine team are Copyright © 2021-2025
Infinite Reality Engine. All Rights Reserved.
*/

import '../patchEngineNode'

import assert from 'assert'
import fs from 'fs'
import path from 'path/posix'
import { afterAll, beforeAll, describe, expect, it } from 'vitest'

import { createEngine, destroyEngine } from '@ir-engine/ecs/src/Engine'

import { projectsRootFolder } from './file-browser/file-browser.class'
import { copyRecursiveSync, getIncrementalName, isValidFileType } from './FileUtil'
import LocalStorage from './storageprovider/local.storage'

const TEST_DIR = 'FileUtil-test-project'

describe('FileUtil functions', () => {
  let PROJECT_PATH: string
  let STORAGE_PATH: string
  let store: LocalStorage
  beforeAll(() => {
    createEngine()
    store = new LocalStorage()
    PROJECT_PATH = path.join(projectsRootFolder, TEST_DIR)
    STORAGE_PATH = path.join(store.PATH_PREFIX, TEST_DIR)
    if (fs.existsSync(PROJECT_PATH)) fs.rmSync(PROJECT_PATH, { force: true, recursive: true })

    fs.mkdirSync(PROJECT_PATH)
    fs.mkdirSync(STORAGE_PATH)
  })

  describe('copyRecursiveSync', () => {
    it('should not throw any error if path does not exists', () => {
      assert.doesNotThrow(() => {
        copyRecursiveSync(path.join(PROJECT_PATH, 'Test_Path'), path.join(PROJECT_PATH, 'Test_Path_2'))
      })
    })

    it('should make copy of given file path', () => {
      const fileName = 'FileUtil_Copy_File_Test_' + Math.round(Math.random() * 100) + '.txt'
      const newFileName = 'FileUtil_Copy_File_Test_' + Math.round(Math.random() * 1000) + '.txt'
      const filePath = path.join(PROJECT_PATH, fileName)
      const newFilePath = path.join(PROJECT_PATH, newFileName)
      fs.writeFileSync(filePath, 'Hello world')

      copyRecursiveSync(filePath, newFilePath)

      assert(fs.existsSync(filePath))
      assert(fs.existsSync(newFilePath))
      assert(fs.readFileSync(filePath).toString() === fs.readFileSync(newFilePath).toString())

      fs.unlinkSync(filePath)
      fs.unlinkSync(newFilePath)
    })

    it('should make copy of given dirctory path recursively', () => {
      const dirVersion = Math.round(Math.random() * 100)
      const dirName = 'FileUtil_Copy_Dir_Test_' + dirVersion
      const newDirName = 'FileUtil_Copy_Dir_Test_' + (dirVersion + 1)
      const subdirName = 'FileUtil_Copy_Dir_Subdir_Test_' + Math.round(Math.random() * 100)
      const fileName = 'FileUtil_Copy_File_Test_' + Math.round(Math.random() * 100) + '.txt'
      const dirPath = path.join(PROJECT_PATH, dirName)
      const newDirPath = path.join(PROJECT_PATH, newDirName)

      fs.mkdirSync(dirPath)
      fs.mkdirSync(path.join(dirPath, subdirName))

      fs.writeFileSync(path.join(dirPath, fileName), 'Hello world')
      fs.writeFileSync(path.join(dirPath, subdirName, fileName), 'Hello world')

      copyRecursiveSync(dirPath, newDirPath)

      assert(fs.existsSync(dirPath))
      assert(fs.existsSync(newDirPath))

      assert(fs.existsSync(path.join(dirPath, subdirName)))
      assert(fs.existsSync(path.join(newDirPath, subdirName)))

      assert(fs.existsSync(path.join(dirPath, fileName)))
      assert(fs.existsSync(path.join(newDirPath, fileName)))
      assert(
        fs.readFileSync(path.join(dirPath, fileName)).toString() ===
          fs.readFileSync(path.join(newDirPath, fileName)).toString()
      )

      assert(fs.existsSync(path.join(dirPath, subdirName, fileName)))
      assert(fs.existsSync(path.join(newDirPath, subdirName, fileName)))
      assert(
        fs.readFileSync(path.join(dirPath, subdirName, fileName)).toString() ===
          fs.readFileSync(path.join(newDirPath, subdirName, fileName)).toString()
      )

      fs.rmSync(dirPath, { force: true, recursive: true })
      fs.rmSync(newDirPath, { force: true, recursive: true })
    })
  })

  describe('getIncrementalName', () => {
    it('should return given name if provided path does not exist', async () => {
      const fileName = 'FileUtil_Incremental_Name_File_Test_' + Math.round(Math.random() * 100) + '.txt'
      assert((await getIncrementalName(fileName, TEST_DIR, store)) === fileName)
    })

    it('should return incremental name for file if it exist already', async () => {
      const fileName = 'FileUtil_Incremental_Name_File_Test.txt'
      const fileName_1 = 'FileUtil_Incremental_Name_File_Test_1.txt'
      const fileName_2 = 'FileUtil_Incremental_Name_File_Test_2.txt'

      fs.writeFileSync(path.join(STORAGE_PATH, fileName), 'Hello world')

      let name = await getIncrementalName(fileName, TEST_DIR, store)
      assert.equal(name, fileName_1)

      fs.writeFileSync(path.join(STORAGE_PATH, fileName_1), 'Hello world')
      name = await getIncrementalName(fileName, TEST_DIR, store)
      assert.equal(name, fileName_2)

      fs.unlinkSync(path.join(STORAGE_PATH, fileName))
      fs.unlinkSync(path.join(STORAGE_PATH, fileName_1))
    })

    it('should return incremental name for directory if it exist already', async () => {
      const dirName = 'FileUtil_Incremental_Name_Dir_Test'
      const dirName_1 = 'FileUtil_Incremental_Name_Dir_Test_1'
      const dirName_2 = 'FileUtil_Incremental_Name_Dir_Test_2'

      fs.mkdirSync(path.join(STORAGE_PATH, dirName))

      let name = await getIncrementalName(dirName, TEST_DIR, store, true)
      assert.equal(name, dirName_1)

      fs.mkdirSync(path.join(STORAGE_PATH, dirName_1))
      name = await getIncrementalName(dirName, TEST_DIR, store, true)
      assert.equal(name, dirName_2)

      fs.rmdirSync(path.join(STORAGE_PATH, dirName))
      fs.rmdirSync(path.join(STORAGE_PATH, dirName_1))
    })

    it('should handle singular and plural directory names correctly', async () => {
      const singularDirName = 'testdir'
      const pluralDirName = 'testdirs'

      // ensure directories don't exist before starting
      if (fs.existsSync(path.join(STORAGE_PATH, singularDirName))) {
        fs.rmdirSync(path.join(STORAGE_PATH, singularDirName))
      }
      if (fs.existsSync(path.join(STORAGE_PATH, pluralDirName))) {
        fs.rmdirSync(path.join(STORAGE_PATH, pluralDirName))
      }

      // create 'testdirs' directory
      fs.mkdirSync(path.join(STORAGE_PATH, pluralDirName))

      // try to create 'testdir' directory
      let name = await getIncrementalName(singularDirName, TEST_DIR, store, true)
      assert.equal(name, singularDirName, "Should return 'testdir' as it doesn't exist")

      // create 'testdir' directory
      fs.mkdirSync(path.join(STORAGE_PATH, singularDirName))

      // try to create another 'testdir' directory
      name = await getIncrementalName(singularDirName, TEST_DIR, store, true)
      assert.equal(name, `${singularDirName}_1`, "Should return 'testdir_1' as 'testdir' already exists")

      // try to create 'testdirs' directory
      name = await getIncrementalName(pluralDirName, TEST_DIR, store, true)
      assert.equal(name, `${pluralDirName}_1`, "Should return 'testdirs_1' as 'testdirs' already exists")

      fs.rmdirSync(path.join(STORAGE_PATH, singularDirName))
      fs.rmdirSync(path.join(STORAGE_PATH, pluralDirName))
    })
  })

  describe('isValidFileType', () => {
    it('returns true for valid image mime types', () => {
      const fileType: string = 'image/png'
      const fileName: string = 'file.png'
      expect(isValidFileType(fileType, fileName)).toBe(true)
    })

    it('returns true for valid audio mime types', () => {
      const fileType: string = 'audio/mpeg'
      const fileName: string = 'song.mp3'
      expect(isValidFileType(fileType, fileName)).toBe(true)
    })

    it('returns true for valid video mime types', () => {
      const fileType: string = 'video/mp4'
      const fileName: string = 'video.mp4'
      expect(isValidFileType(fileType, fileName)).toBe(true)
    })

    it('returns true for valid model mime types', () => {
      expect(isValidFileType('model/gltf+json', 'model.gltf')).toBe(true)
      expect(isValidFileType('model/gltf-binary', 'model.glb')).toBe(true)
      expect(isValidFileType('model/vrm', 'model.vrm')).toBe(true)
      expect(isValidFileType('model/fbx', 'model.fbx')).toBe(true)
      expect(isValidFileType('model/usdz', 'model.usdz')).toBe(true)
    })

    it('returns true for application/octet-stream with valid extensions', () => {
      expect(isValidFileType('application/octet-stream', 'model.gltf')).toBe(true)
      expect(isValidFileType('application/octet-stream', 'model.glb')).toBe(true)
      expect(isValidFileType('application/octet-stream', 'model.bin')).toBe(true)
    })

    it('returns true for application/macbinary with .bin extension', () => {
      expect(isValidFileType('application/macbinary', 'macupload.bin')).toBe(true)
    })

    it('returns false for application/octet-stream with invalid extension', () => {
      expect(isValidFileType('application/octet-stream', 'file.txt')).toBe(false)
    })

    it('returns false for application/macbinary with invalid extension', () => {
      expect(isValidFileType('application/macbinary', 'file.doc')).toBe(false)
    })

    it('returns false for unrelated mime types', () => {
      expect(isValidFileType('application/pdf', 'doc.pdf')).toBe(false)
      expect(isValidFileType('text/plain', 'notes.txt')).toBe(false)
    })
  })

  afterAll(() => {
    fs.rmSync(PROJECT_PATH, { force: true, recursive: true })
    fs.rmSync(STORAGE_PATH, { force: true, recursive: true })
    return destroyEngine()
  })
})
