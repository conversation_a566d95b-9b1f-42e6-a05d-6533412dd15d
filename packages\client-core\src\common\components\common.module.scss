.inputField {
  background: var(--inputBackground);
  border-radius: 4px;
  flex-grow: 1;

  /**
  * This below style is added to fix background issues with chrome autofill.
  * https://github.com/ir-engine/ir-engine/pull/6448#pullrequestreview-1018811200
  * https://stackoverflow.com/a/14205976/2077741
  */
  input:-webkit-autofill,
  input:-webkit-autofill:hover,
  input:-webkit-autofill:focus,
  input:-webkit-autofill:active {
    -webkit-box-shadow: 0 0 0 30px var(--inputBackground) inset !important;
  }

  :global {
    input:placeholder-shown {
      font-style: italic;
    }
  }
}

.preview {
  color: var(--textColor);
  border: 1px solid var(--buttonOutlined);
  border-radius: 8px;
  box-shadow: 0 0 10px var(--buttonOutlined);
  position: relative;
}

.previewText {
  position: absolute;
  top: 0;
  display: flex;
  text-align: center;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;
}

.bold {
  font-weight: bold;
}

.chip {
  color: var(--textColor);
  border-color: var(--textColor);
}

.divider {
  margin: 30px 70px 20px;
  border: 0.2px solid var(--buttonOutlined);
  border-bottom: none;
}

@media (max-width: 899px) {
  .hideSm {
    display: none;
  }
}
