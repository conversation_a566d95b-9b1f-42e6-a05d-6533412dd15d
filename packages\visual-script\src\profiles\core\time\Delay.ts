/*
CPAL-1.0 License

The contents of this file are subject to the Common Public Attribution License
Version 1.0. (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at
https://github.com/ir-engine/ir-engine/blob/dev/LICENSE.
The License is based on the Mozilla Public License Version 1.1, but Sections 14
and 15 have been added to cover use of software over a computer network and 
provide for limited attribution for the Original Developer. In addition, 
Exhibit A has been modified to be consistent with Exhibit B.

Software distributed under the License is distributed on an "AS IS" basis,
WITHOUT WARRANTY OF ANY KIND, either express or implied. See the License for the
specific language governing rights and limitations under the License.

The Original Code is Infinite Reality Engine.

The Original Developer is the Initial Developer. The Initial Developer of the
Original Code is the Infinite Reality Engine team.

All portions of the code written by the Infinite Reality Engine team are Copyright © 2021-2025
Infinite Reality Engine. All Rights Reserved.
*/

// ASYNC - asynchronous evaluation
// also called "delay"

import {
  AsyncNode,
  IGraph,
  NodeDescription,
  NodeDescription2,
  Socket,
  VisualScriptEngine
} from '../../../VisualScriptModule'

export class Delay extends AsyncNode {
  public static Description = new NodeDescription2({
    typeName: 'flow/time/delay',
    category: 'Time',
    label: 'Delay',
    factory: (description, graph) => new Delay(description, graph)
  })

  constructor(description: NodeDescription, graph: IGraph) {
    super(
      description,
      graph,
      [new Socket('flow', 'flow'), new Socket('float', 'duration', 1)],
      [new Socket('flow', 'flow')]
    )
  }

  private timeoutPending = false

  triggered(engine: VisualScriptEngine, triggeringSocketName: string, finished: () => void) {
    // if there is a valid timeout running, leave it.
    if (this.timeoutPending) {
      return
    }

    // otherwise start it.
    this.timeoutPending = true
    setTimeout(
      () => {
        // check if cancelled
        if (!this.timeoutPending) return
        this.timeoutPending = false
        engine.commitToNewFiber(this, 'flow')
        finished()
      },
      this.readInput<number>('duration') * 1000
    )
  }

  dispose() {
    this.timeoutPending = false
  }
}
