/**
 * DL-Engine API Gateway 代理控制器
 * 
 * 功能：
 * - 请求转发和代理
 * - 负载均衡
 * - 请求/响应处理
 * - 错误处理和重试
 */

import { All, Controller, Req, Res, Next } from '@nestjs/common'
import { Request, Response, NextFunction } from 'express'
import { ProxyService } from './proxy.service'
import { LoggerService } from '../common/logger.service'

@Controller('*')
export class ProxyController {
  constructor(
    private readonly proxyService: ProxyService,
    private readonly logger: LoggerService
  ) {}

  @All()
  async handleRequest(
    @Req() req: Request,
    @Res() res: Response,
    @Next() next: NextFunction
  ) {
    try {
      // 记录请求开始时间
      const startTime = Date.now()
      
      // 获取请求信息
      const { method, path, headers, body } = req
      const clientIp = req.ip || req.connection.remoteAddress
      
      this.logger.log(`${method} ${path} from ${clientIp}`)

      // 检查是否为网关管理API
      if (path.startsWith('/api/gateway/')) {
        return next()
      }

      // 检查是否为健康检查或监控端点
      if (path === '/health' || path === '/metrics') {
        return next()
      }

      // 代理请求
      await this.proxyService.proxyRequest(req, res, {
        onProxyReq: (proxyReq, req, res) => {
          // 请求预处理
          this.logger.debug(`Proxying ${req.method} ${req.path}`)
        },
        onProxyRes: (proxyRes, req, res) => {
          // 响应后处理
          const endTime = Date.now()
          const duration = endTime - startTime
          
          this.logger.log(
            `${req.method} ${req.path} -> ${proxyRes.statusCode} (${duration}ms)`
          )
        },
        onError: (err, req, res) => {
          // 错误处理
          const endTime = Date.now()
          const duration = endTime - startTime
          
          this.logger.error(
            `Proxy error for ${req.method} ${req.path}: ${err.message} (${duration}ms)`
          )
          
          if (!res.headersSent) {
            res.status(502).json({
              error: 'Bad Gateway',
              message: 'Service temporarily unavailable',
              timestamp: new Date().toISOString(),
              path: req.path
            })
          }
        }
      })

    } catch (error) {
      this.logger.error(`Request handling error: ${error.message}`)
      
      if (!res.headersSent) {
        res.status(500).json({
          error: 'Internal Server Error',
          message: 'An unexpected error occurred',
          timestamp: new Date().toISOString(),
          path: req.path
        })
      }
    }
  }
}
