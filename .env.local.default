# Note:- Replace localhost with '127.0.0.1' for Mac OSX
#        Linux and Windows will work fine with 'localhost'.

#        For running on some static IP, replace localhost with IP to use (Usually the network IP assigned to your device).

# General ------------------------
LOCAL=true
APP_ENV=development
DEPLOY_STAGE=local
RELEASE_NAME=
NOSSL=false
CERT=certs/cert.pem
KEY=certs/key.pem
# --------------------------------

# ALLOW_OUT_OF_DATE_PROJECTS=false

# Client variables ---------------
APP_TITLE="Napster 3D Studio"
APP_LOGO=https://localhost:3000/static/app_logo.svg
APP_URL=https://localhost:3000
APP_HOST=localhost:3000
APP_PORT=3000

VITE_HMR=false

# Vite variables - Exposed to client side
VITE_GA_MEASUREMENT_ID=
VITE_SENTRY_DSN=
VITE_APP_HOST=localhost
VITE_APP_PORT=3000
VITE_ZENDESK_ENABLED=false
VITE_ZENDESK_KEY=
VITE_ZENDESK_AUTHENTICATION_ENABLED=false
# Use following value for minio s3 provider
#VITE_FILE_SERVER=https://localhost:9000/ir-engine-static-resources
#VITE_TEST_FILE_SERVER=https://localhost:9000/ir-engine-static-resources-test
# Use following value for local file server
VITE_FILE_SERVER=https://localhost:8642
VITE_TEST_FILE_SERVER=https://localhost:8642
VITE_SERVER_HOST=localhost
VITE_SERVER_PORT=3030
VITE_CORS_SERVER_PORT=3035
VITE_INSTANCESERVER_HOST=localhost
VITE_INSTANCESERVER_PORT=3031
VITE_FEATHERS_STORE_KEY=IREngine-Auth-Store
VITE_EMAILJS_SERVICE_ID=
VITE_EMAILJS_TEMPLATE_ID=
VITE_EMAILJS_USER_ID=
VITE_ROOT_REDIRECT=false
VITE_READY_PLAYER_ME_URL=https://xre.readyplayer.me/avatar?frameApi
VITE_ROOT_DOMAIN_ENABLED=false
VITE_MAX_FILE_SIZE_TO_UPLOAD_MB='1000'

# WebSocket Configuration --------
VITE_WEBSOCKET_PING_TIMEOUT=30000
VITE_WEBSOCKET_PING_INTERVAL=10000

VITE_AVATURN_URL="https://demo.avaturn.dev" #using public one
VITE_AVATURN_API=https://api.avaturn.me/
VITE_PWA_ENABLED=false
VITE_SOURCEMAPS=true
# CHAPI Mediator URI
VITE_MEDIATOR_SERVER=https://authn.io
# Enable 'Login with Wallet'? (Disables other login methods)
VITE_LOGIN_WITH_WALLET=false
VITE_8TH_WALL=
# Use following true value to send client logs to server
#VITE_FORCE_CLIENT_LOG_AGGREGATE=true
VITE_FORCE_CLIENT_LOG_AGGREGATE=false
VITE_DISABLE_LOG=false

# --------------------------------

LOG_TO_FILE=false

# DB variables -------------------
MYSQL_USER=server
MYSQL_PASSWORD=password
MYSQL_DATABASE=ir-engine
MYSQL_HOST=127.0.0.1
MYSQL_PORT=3306
MYSQL_URL=

MYSQL_TEST_USER=server
MYSQL_TEST_PASSWORD=password
MYSQL_TEST_DATABASE=ir-engine
MYSQL_TEST_HOST=127.0.0.1
MYSQL_TEST_PORT=3305
MYSQL_TEST_URL=

# PostgreSQL Vector Database variables
VECTORDB_ENABLED=false
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres
POSTGRES_DATABASE=vector-db
POSTGRES_HOST=127.0.0.1
POSTGRES_PORT=5432
POSTGRES_URL=
POSTGRES_POOL_MAX=5

# PostgreSQL Test variables
POSTGRES_TEST_USER=postgres
POSTGRES_TEST_PASSWORD=postgres
POSTGRES_TEST_DATABASE=vector-db
POSTGRES_TEST_HOST=127.0.0.1
POSTGRES_TEST_PORT=5433
POSTGRES_TEST_URL=
# --------------------------------

# Server variables ---------------
SERVER_MODE=local
SERVER_HOST=localhost
SERVER_PORT=3030
SERVER_URL=https://localhost:3030
CORS_SERVER_PORT=3029
REQUIRE_AGE_VERIFICATION=false # defaults to true

# Switch to `true` to disable logging (useful for unit tests)
DISABLE_SERVER_LOG=false

SERVER_PUBLIC_DIR=
PERFORM_DRY_RUN=false
# Storage Provider can be s3, local, ipfs
# Use following value for minio s3 provider
#STORAGE_PROVIDER=s3
# Use following value for local file server
STORAGE_PROVIDER=local
LOCAL_STORAGE_PROVIDER=localhost:8642
LOCAL_STORAGE_PROVIDER_PORT=8642
HUB_ENDPOINT=https://ir-engine.io
INSTANCESERVER_UNREACHABLE_TIMEOUT_SECONDS=10

MATCHMAKER_EMULATION_MODE=true
# --------------------------------

# Task server variables ---------------
TASKSERVER_PORT=3033
# --------------------------------

# Kubernates ---------------------
KUBERNETES=false
KUBERNETES_SERVICE_HOST=
KUBERNETES_PORT_443_TCP_PORT=
# --------------------------------

# Game server variables ----------
INSTANCESERVER_DOMAIN=localhost
RTC_START_PORT=40000
RTC_END_PORT=49999
RTC_PORT_BLOCK_SIZE=50
INSTANCESERVER_PORT=3031
INSTANCESERVER_MODE=local
INSTANCESERVER_SHUTDOWN_DELAY_MS=30000
INSTANCESERVER_MAX_USERS_PER_INSTANCE=5
# --------------------------------

# P2P Instance variables ----------
P2P_INSTANCE_ENABLED=true
P2P_INSTANCE_MAX_CONNECTIONS=6

# Email variables ----------------
SMTP_HOST=email-smtp.us-west-1.amazonaws.com
SMTP_PORT=465
SMTP_SECURE=true
SMTP_USER=AKIARQM6EGKHDSORVC6Z
SMTP_PASS=
SMTP_FROM_NAME=noreply
SMTP_FROM_EMAIL=ENTER_SENDER_EMAIL
# --------------------------------

# Authentication -----------------
AUTH_SECRET=test

DISCORD_CALLBACK_URL=https://localhost:3000/auth/oauth/discord
DISCORD_CLIENT_ID=928436900031787030
DISCORD_CLIENT_SECRET=

FACEBOOK_CALLBACK_URL=https://localhost:3000/auth/oauth/facebook
FACEBOOK_CLIENT_ID=262344435023143
FACEBOOK_CLIENT_SECRET=

GITHUB_CALLBACK_URL=https://localhost:3000/auth/oauth/github
GITHUB_CLIENT_ID=
GITHUB_CLIENT_SECRET=

GOOGLE_CALLBACK_URL=https://localhost:3000/auth/oauth/google
GOOGLE_CLIENT_ID=502422558160-5co6malq1jaj7qnnf1jjreia2pr9jjvm.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=

LINKEDIN_CALLBACK_URL=https://localhost:3000/auth/oauth/linkedin
LINKEDIN_CLIENT_ID=
LINKEDIN_CLIENT_SECRET=

TWITTER_CALLBACK_URL=https://localhost:3000/auth/oauth/twitter
TWITTER_CLIENT_ID=
TWITTER_CLIENT_SECRET=
# --------------------------------

# AWS variables
# - SMS
AWS_SMS_ACCESS_KEY_ID=AKIARQM6EGKHAEN2K47Y
AWS_SMS_REGION=us-west-1
AWS_SMS_SECRET_ACCESS_KEY=
AWS_SMS_TOPIC_ARN=arn:aws:sns:us-west-1:103947711118:theoverlay
AWS_SMS_APPLICATION_ID=
AWS_SMS_SENDER_ID=

# - Cloud front
STORAGE_CDN_DOMAIN=resources.ir-engine.io
STORAGE_CLOUDFRONT_DISTRIBUTION_ID=E3E9EQTR0OYMND
STORAGE_CLOUDFRONT_REGION=us-west-1

# - S3
STORAGE_REGION=us-west-1
STORAGE_STATIC_RESOURCE_BUCKET=ir-engine-static-resources
STORAGE_S3_TEST_RESOURCE_BUCKET=ir-engine-static-resources-test
STORAGE_S3_AVATAR_DIRECTORY=avatars
STORAGE_S3_ENDPOINT=https://localhost:9000

# Possible values:
# local - for local development,
# dev - for live development environment,
# <empty or not defined> - for production environment,
STORAGE_S3_DEV_MODE=local

# - keys
STORAGE_AWS_ACCESS_KEY_ID=server
STORAGE_AWS_ACCESS_KEY_SECRET=password
# --------------------------------

# Chargebee variables ------------
CHARGEBEE_SITE=ir-engine-test
CHARGEBEE_API_KEY=
# --------------------------------

# Redis variables ---------------
REDIS_ENABLED=true
REDIS_ADDRESS=localhost
REDIS_PORT=6379
# REDIS_PASSWORD=
# --------------------------------

FRONTEND_SERVICE_URL=https://local-matchmaking.ir-engine.io/v1/frontendservice

#define logging url
ELASTIC_HOST=http://localhost:9200

# Has to be set to production for OAuth redirect addresses to be constructed properly in local environment
NODE_ENV=production

#OpenSearch
OPENSEARCH_USER=admin
OPENSEARCH_PASSWORD=admin
OPENSEARCH_HOST=http://localhost:9200

# Switch to `true` to enable local file system operations
FS_PROJECT_SYNC_ENABLED=true

# Metabase variables
METABASE_SITE_URL=
METABASE_SECRET_KEY=
METABASE_CRASH_DASHBOARD_ID=
METABASE_EXPIRATION=

# Zendesk key for user authentication
ZENDESK_KEY_NAME=
ZENDESK_SECRET=
ZENDESK_KID=

# IP Geolocation Service
IP_GEOLOCATION_API_URL=https://api.ipinfo.io/lite
IP_GEOLOCATION_API_TOKEN=

# Mailchimp settings for mailing lists
MAILCHIMP_KEY=
MAILCHIMP_SERVER=
MAILCHIMP_AUDIENCE_ID=
MAILCHIMP_GROUP_ID=

# Google Tag Manager Settings
GOOGLE_TAG_MANAGER_CONTAINER_ID=
GOOGLE_TAG_MANAGER_AUTH=
GOOGLE_TAG_MANAGER_PREVIEW=

# PGVector variables
PGVECTOR_VECTOR_SIZE=1024

# Ollama API endpoint
OLLAMA_API_URL='http://localhost:11434'

# Big Query Analytics Settings
BQ_PROJECT_ID=
BQ_DATASET_ID=
BQ_TABLE_ID=

# Docker controls. Please know what you are disabling
DC_minio=true
DC_test=true
DC_minikube=true

# HubSpot settings for email marketing
HUBSPOT_ACCESS_TOKEN=
HUBSPOT_BUSINESS_UNIT_ID=
# Email marketing provider selection (false = Mailchimp, true = HubSpot)
USE_HUBSPOT=
