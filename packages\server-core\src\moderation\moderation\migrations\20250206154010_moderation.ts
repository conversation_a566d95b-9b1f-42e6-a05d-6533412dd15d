/*
CPAL-1.0 License

The contents of this file are subject to the Common Public Attribution License
Version 1.0. (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at
https://github.com/ir-engine/ir-engine/blob/dev/LICENSE.
The License is based on the Mozilla Public License Version 1.1, but Sections 14
and 15 have been added to cover use of software over a computer network and 
provide for limited attribution for the Original Developer. In addition, 
Exhibit A has been modified to be consistent with Exhibit B.

Software distributed under the License is distributed on an "AS IS" basis,
WITHOUT WARRANTY OF ANY KIND, either express or implied. See the License for the
specific language governing rights and limitations under the License.

The Original Code is Infinite Reality Engine.

The Original Developer is the Initial Developer. The Initial Developer of the
Original Code is the Infinite Reality Engine team.

All portions of the code written by the Infinite Reality Engine team are Copyright © 2021-2025
Infinite Reality Engine. All Rights Reserved.
*/
import { moderationPath } from '@ir-engine/common/src/schemas/moderation/moderation.schema'
import type { Knex } from 'knex'

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
export async function up(knex: Knex): Promise<void> {
  await knex.schema.createTable(moderationPath, (table) => {
    //@ts-ignore
    table.uuid('id').collate('utf8mb4_bin').primary()
    table.string('reportDetails', 1050).notNullable()
    table.string('abuseReason', 255).notNullable()
    table.string('type', 255).notNullable()
    //@ts-ignore
    table.uuid('reportedUserId', 36).collate('utf8mb4_bin')

    //@ts-ignore
    table.string('reportedLocationId', 255).collate('utf8mb4_bin')
    table.string('status', 255).notNullable().defaultTo('open')
    table.string('ipAddress', 255).notNullable()

    //@ts-ignore
    table.uuid('updatedBy', 36).collate('utf8mb4_bin').index()
    //@ts-ignore
    table.uuid('createdBy', 36).collate('utf8mb4_bin').index()

    table.dateTime('reportedAt').notNullable()
    table.dateTime('createdAt').notNullable()
    table.dateTime('updatedAt').notNullable()

    table.foreign('reportedUserId').references('id').inTable('user').onDelete('SET NULL').onUpdate('CASCADE')
    table.foreign('reportedLocationId').references('id').inTable('location').onDelete('SET NULL').onUpdate('CASCADE')
    table.foreign('updatedBy').references('id').inTable('user').onDelete('SET NULL').onUpdate('CASCADE')
    table.foreign('createdBy').references('id').inTable('user').onDelete('SET NULL').onUpdate('CASCADE')
  })
}

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
export async function down(knex: Knex): Promise<void> {
  await knex.schema.dropTableIfExists(moderationPath)
}
