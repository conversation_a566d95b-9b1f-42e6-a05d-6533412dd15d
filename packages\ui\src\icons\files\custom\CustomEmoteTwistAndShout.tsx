/*
CPAL-1.0 License

The contents of this file are subject to the Common Public Attribution License
Version 1.0. (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at
https://github.com/ir-engine/ir-engine/blob/dev/LICENSE.
The License is based on the Mozilla Public License Version 1.1, but Sections 14
and 15 have been added to cover use of software over a computer network and
provide for limited attribution for the Original Developer. In addition,
Exhibit A has been modified to be consistent with Exhibit B.

Software distributed under the License is distributed on an "AS IS" basis,
WITHOUT WARRANTY OF ANY KIND, either express or implied. See the License for the
specific language governing rights and limitations under the License.

The Original Code is Infinite Reality Engine.

The Original Developer is the Initial Developer. The Initial Developer of the
Original Code is the Infinite Reality Engine team.

All portions of the code written by the Infinite Reality Engine team are Copyright © 2021-2025
Infinite Reality Engine. All Rights Reserved.
*/

import React, { forwardRef, Ref, SVGProps } from 'react'

const Icon = (props: SVGProps<SVGSVGElement>, ref: Ref<SVGSVGElement>) => (
  <svg
    width="1em"
    height="1em"
    viewBox="0 0 23 23"
    xmlns="http://www.w3.org/2000/svg"
    role="img"
    fill="none"
    stroke="none"
    ref={ref}
    {...props}
  >
    <path
      d="M15.193 7.87129V12.023C15.7687 13.7503 16.6476 17.4475 14.8899 20.084C14.6171 20.5082 13.9808 20.5688 13.5868 20.2052C13.2838 19.9324 13.2534 19.4779 13.4656 19.1445C14.6475 17.3263 14.1323 14.5383 13.4656 12.4775L11.6474 17.7808C11.4352 18.4475 10.5867 18.6899 10.0412 18.2354L7.19258 15.7505C6.82896 15.4474 6.79862 14.9322 7.10171 14.5686C7.40472 14.2049 7.95022 14.1746 8.28357 14.4776L10.2533 16.1444L11.2231 12.5079V8.05316C10.3442 8.02283 9.3745 7.99249 8.46538 7.96222C7.79868 7.93189 7.28352 7.35612 7.34412 6.65915L7.73807 2.02258C7.85928 1.68923 8.22297 1.4771 8.55632 1.4771C9.10175 1.4771 9.55631 2.02258 9.40483 2.56806L9.10175 5.02273C9.04114 5.44695 9.40483 5.84091 9.82906 5.84091H14.8293C15.0717 5.84091 15.2838 5.75004 15.4051 5.56816C16.1021 4.65904 16.5566 3.20443 16.799 2.14377C16.9809 1.59832 17.3445 1.4771 17.7385 1.4771C18.2234 1.53771 18.5567 2.02258 18.4658 2.50746C18.1324 4.02268 17.2536 6.99251 15.193 7.87129Z"
      fill="#F7F8FA"
    />
    <path
      d="M11.4655 4.05318C10.9806 3.32587 10.8898 2.50763 11.2837 1.81065C11.6171 1.14396 12.3141 0.75 13.0414 0.75C14.1324 0.75 15.0415 1.65913 15.0415 2.75011C15.0415 3.47742 14.6172 4.26533 14.0111 4.65928C13.7081 4.87136 13.3747 4.9623 13.0414 4.9623C12.7989 4.9623 12.5868 4.90169 12.3444 4.81075C11.9807 4.65928 11.6777 4.4168 11.4655 4.05318Z"
      fill="#F7F8FA"
    />
    <path
      d="M18.4089 19.3259H18.9298C19.1778 19.3259 19.3616 19.2639 19.481 19.1399C19.6005 19.0159 19.6603 18.8513 19.6603 18.6462C19.6603 18.4478 19.6005 18.2933 19.481 18.1828C19.3638 18.0724 19.2015 18.0172 18.9941 18.0172C18.8069 18.0172 18.6502 18.069 18.524 18.1727C18.3977 18.2742 18.3346 18.4072 18.3346 18.5717H17.3572C17.3572 18.3148 17.426 18.0848 17.5635 17.8819C17.7033 17.6767 17.8972 17.5166 18.1452 17.4016C18.3954 17.2867 18.6705 17.2292 18.9704 17.2292C19.4911 17.2292 19.8992 17.3543 20.1946 17.6046C20.49 17.8525 20.6376 18.1952 20.6376 18.6326C20.6376 18.8581 20.5688 19.0655 20.4314 19.2549C20.2938 19.4443 20.1135 19.5897 19.8902 19.6912C20.1675 19.7904 20.3739 19.9391 20.5091 20.1376C20.6466 20.3359 20.7154 20.5705 20.7154 20.841C20.7154 21.2784 20.5553 21.629 20.2352 21.8928C19.9173 22.1565 19.4957 22.2885 18.9704 22.2885C18.4789 22.2885 18.0764 22.1588 17.763 21.8995C17.4519 21.6403 17.2963 21.2976 17.2963 20.8715H18.2737C18.2737 21.0563 18.3425 21.2074 18.48 21.3246C18.6198 21.4418 18.7911 21.5005 18.9941 21.5005C19.2263 21.5005 19.4078 21.4396 19.5385 21.3178C19.6716 21.1939 19.7381 21.0303 19.7381 20.8275C19.7381 20.3359 19.4675 20.0902 18.9264 20.0902H18.4089V19.3259Z"
      fill="#F7F8FA"
    />
    <path
      d="M5.91224 11.8317C5.47956 11.8317 5.09011 11.6117 4.78724 11.2596C4.61415 11.0396 4.48434 10.7316 4.48434 10.4675C4.48434 10.4675 4.48434 10.4236 4.48434 10.3795V6.24312L3.22952 7.29921C3.05644 7.4752 2.75355 7.43122 2.62375 7.25523C2.49394 7.07917 2.49394 6.77117 2.71028 6.63916L4.65744 5.01098C4.78724 4.87897 4.96032 4.87897 5.1334 4.967C5.30648 5.05496 5.39299 5.18697 5.39299 5.36303V9.10338C5.43628 9.10338 5.47956 9.0594 5.56607 9.0594C5.99874 8.92739 6.47477 9.01542 6.82087 9.27945C7.16703 9.54347 7.3834 9.98348 7.3834 10.4236C7.34011 11.1716 6.69107 11.8317 5.91224 11.8317Z"
      fill="#F7F8FA"
    />
  </svg>
)

const ForwardRef = forwardRef(Icon)
export default ForwardRef
