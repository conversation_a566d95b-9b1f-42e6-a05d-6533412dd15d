import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToOne,
  JoinColumn
} from 'typeorm'
import { User } from './user.entity'

/**
 * 性别枚举
 */
export enum Gender {
  MALE = 'male',
  FEMALE = 'female',
  OTHER = 'other',
  PREFER_NOT_TO_SAY = 'prefer_not_to_say'
}

/**
 * 用户资料实体
 * 
 * 存储用户的详细个人信息
 */
@Entity('user_profiles')
export class UserProfile {
  @PrimaryGeneratedColumn('uuid')
  id: string

  /**
   * 关联的用户ID
   */
  @Column({ type: 'uuid' })
  userId: string

  /**
   * 真实姓名
   */
  @Column({ type: 'varchar', length: 100, nullable: true })
  realName?: string

  /**
   * 昵称
   */
  @Column({ type: 'varchar', length: 100, nullable: true })
  nickname?: string

  /**
   * 性别
   */
  @Column({
    type: 'enum',
    enum: Gender,
    nullable: true
  })
  gender?: Gender

  /**
   * 出生日期
   */
  @Column({ type: 'date', nullable: true })
  birthDate?: Date

  /**
   * 年龄（计算字段）
   */
  get age(): number | null {
    if (!this.birthDate) return null
    const today = new Date()
    const birth = new Date(this.birthDate)
    let age = today.getFullYear() - birth.getFullYear()
    const monthDiff = today.getMonth() - birth.getMonth()
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
      age--
    }
    return age
  }

  /**
   * 所在国家
   */
  @Column({ type: 'varchar', length: 100, nullable: true })
  country?: string

  /**
   * 所在省份/州
   */
  @Column({ type: 'varchar', length: 100, nullable: true })
  province?: string

  /**
   * 所在城市
   */
  @Column({ type: 'varchar', length: 100, nullable: true })
  city?: string

  /**
   * 详细地址
   */
  @Column({ type: 'text', nullable: true })
  address?: string

  /**
   * 邮政编码
   */
  @Column({ type: 'varchar', length: 20, nullable: true })
  postalCode?: string

  /**
   * 时区
   */
  @Column({ type: 'varchar', length: 50, default: 'Asia/Shanghai' })
  timezone: string

  /**
   * 语言偏好
   */
  @Column({ type: 'varchar', length: 10, default: 'zh-CN' })
  language: string

  /**
   * 个人简介
   */
  @Column({ type: 'text', nullable: true })
  bio?: string

  /**
   * 个人网站
   */
  @Column({ type: 'varchar', length: 255, nullable: true })
  website?: string

  /**
   * 社交媒体链接（JSON格式）
   */
  @Column({ type: 'json', nullable: true })
  socialLinks?: {
    wechat?: string
    qq?: string
    weibo?: string
    github?: string
    linkedin?: string
    twitter?: string
    [key: string]: string
  }

  /**
   * 兴趣爱好（标签数组）
   */
  @Column({ type: 'json', nullable: true })
  interests?: string[]

  /**
   * 技能标签
   */
  @Column({ type: 'json', nullable: true })
  skills?: string[]

  /**
   * 职业
   */
  @Column({ type: 'varchar', length: 100, nullable: true })
  occupation?: string

  /**
   * 公司/组织
   */
  @Column({ type: 'varchar', length: 200, nullable: true })
  organization?: string

  /**
   * 职位
   */
  @Column({ type: 'varchar', length: 100, nullable: true })
  position?: string

  /**
   * 工作经验年数
   */
  @Column({ type: 'int', nullable: true })
  experienceYears?: number

  /**
   * 是否公开资料
   */
  @Column({ type: 'boolean', default: false })
  isPublic: boolean

  /**
   * 资料完整度（百分比）
   */
  get completeness(): number {
    const fields = [
      'realName', 'gender', 'birthDate', 'country', 'city',
      'bio', 'occupation', 'organization'
    ]
    const filledFields = fields.filter(field => this[field] != null).length
    return Math.round((filledFields / fields.length) * 100)
  }

  /**
   * 创建时间
   */
  @CreateDateColumn()
  createdAt: Date

  /**
   * 更新时间
   */
  @UpdateDateColumn()
  updatedAt: Date

  // 关联关系
  @OneToOne(() => User, user => user.profile)
  @JoinColumn({ name: 'userId' })
  user: User
}
