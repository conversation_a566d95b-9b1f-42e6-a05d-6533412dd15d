/**
 * DL-Engine API Gateway 主入口
 * 
 * 功能特性：
 * - 统一API路由管理
 * - 请求限流和负载均衡
 * - 中间件管道处理
 * - 服务发现和健康检查
 * - 监控和日志记录
 * - 安全防护和认证
 */

import { NestFactory } from '@nestjs/core'
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger'
import { ValidationPipe } from '@nestjs/common'
import helmet from 'helmet'
import compression from 'compression'
import { AppModule } from './app.module'
import { LoggerService } from './common/logger.service'
import { ConfigService } from './config/config.service'

async function bootstrap() {
  const app = await NestFactory.create(AppModule, {
    logger: new LoggerService()
  })

  const configService = app.get(ConfigService)
  const logger = app.get(LoggerService)

  // 安全中间件
  app.use(helmet({
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        styleSrc: ["'self'", "'unsafe-inline'"],
        scriptSrc: ["'self'"],
        imgSrc: ["'self'", "data:", "https:"],
        connectSrc: ["'self'", "wss:", "ws:"]
      }
    },
    crossOriginEmbedderPolicy: false
  }))

  // 压缩中间件
  app.use(compression())

  // CORS配置
  app.enableCors({
    origin: configService.getAllowedOrigins(),
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
  })

  // 全局验证管道
  app.useGlobalPipes(new ValidationPipe({
    transform: true,
    whitelist: true,
    forbidNonWhitelisted: true
  }))

  // API文档配置
  const config = new DocumentBuilder()
    .setTitle('DL-Engine API Gateway')
    .setDescription('数字化学习引擎统一API网关')
    .setVersion('1.0.0')
    .addBearerAuth()
    .addTag('gateway', 'API网关管理')
    .addTag('routing', '路由管理')
    .addTag('middleware', '中间件管理')
    .addTag('monitoring', '监控管理')
    .build()

  const document = SwaggerModule.createDocument(app, config)
  SwaggerModule.setup('api/docs', app, document, {
    customSiteTitle: 'DL-Engine API Gateway Documentation',
    customCss: '.swagger-ui .topbar { display: none }'
  })

  // 启动服务器
  const port = configService.getPort()
  await app.listen(port)

  logger.log(`🚀 DL-Engine API Gateway started on port ${port}`)
  logger.log(`📚 API Documentation: http://localhost:${port}/api/docs`)
  logger.log(`🔍 Health Check: http://localhost:${port}/health`)
}

// 错误处理
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason)
  process.exit(1)
})

process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error)
  process.exit(1)
})

bootstrap().catch(error => {
  console.error('Failed to start application:', error)
  process.exit(1)
})
