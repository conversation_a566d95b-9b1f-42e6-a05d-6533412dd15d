{"name": "@ir-engine/engine", "author": {"name": "Infinite Reality Engine Collective", "email": "<EMAIL>", "url": "http://github.com/ir-engine"}, "bugs": {"url": "https://github.com/ir-engine/ir-engine/issues"}, "publishConfig": {"access": "public"}, "description": "A declarative way to build 3D/VR/AR/XR apps that scale. Built with tooling web developers will love, using conventions that game developers and creative coders will find straightforward familiar.", "homepage": "https://github.com/ir-engine/ir-engine#readme", "license": "CPAL", "main": "index.ts", "repository": {"type": "git", "url": "git+https://github.com/ir-engine/ir-engine.git"}, "scripts": {"check-errors": "tsc --noemit && npx cycle-import-check src || true", "test": "cross-env TEST=true vitest run --config=../../vitest.client.config.ts", "test-coverage": "npm run test-coverage-generate ; npm run test-coverage-launch", "test-coverage-generate": "npm run test -- --coverage --silent", "test-coverage-launch": "vite preview --open --outDir coverage", "validate": "npm run test", "generate-doc": "node_modules/.bin/typedoc"}, "version": "1.0.3", "dependencies": {"@dimforge/rapier3d-compat": "0.11.2", "@gltf-transform/core": "4.0.10", "@gltf-transform/extensions": "4.0.10", "@gltf-transform/functions": "4.0.10", "@ir-engine/ecs": "^1.0.3", "@ir-engine/hyperflux": "^1.0.3", "@ir-engine/spatial": "^1.0.3", "@ir-engine/xrui": "^1.0.3", "@mediapipe/tasks-vision": "0.10.12", "@tweenjs/tween.js": "^23.1.2", "draco3dgltf": "^1.5.6", "fflate": "0.7.4", "hls.js": "^1.3.5", "js-sha3": "^0.8.0", "lodash": "4.17.21", "noisejs": "2.1.0", "postprocessing": "6.37.3", "property-graph": "^2.0.0", "react": "18.2.0", "react-dom": "18.2.0", "three": "0.176.0", "three.quarks": "^0.16.0", "troika-three-text": "^0.52.4", "rfc6902": "^5.1.2", "ts-matches": "5.3.0", "uuid": "9.0.0", "web-worker": "1.2.0"}, "devDependencies": {"@types/mock-require": "2.0.1", "@types/offscreencanvas": "2019.7.0", "@types/sinon": "10.0.13", "@types/three": "0.176.0", "@types/webxr": "0.5.13", "mock-require": "3.0.3", "sinon": "15.0.2", "trace-unhandled": "2.0.1", "typedoc": "0.23.28", "typescript": "5.6.3"}, "gitHead": "2313453697ca7c6b8d36b3b166b5a6445fe1c851"}