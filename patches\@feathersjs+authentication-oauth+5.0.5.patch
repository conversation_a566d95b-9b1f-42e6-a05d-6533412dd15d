diff --git a/node_modules/@feathersjs/authentication-oauth/lib/index.js b/node_modules/@feathersjs/authentication-oauth/lib/index.js
index af730be..b8095d3 100644
--- a/node_modules/@feathersjs/authentication-oauth/lib/index.js
+++ b/node_modules/@feathersjs/authentication-oauth/lib/index.js
@@ -24,14 +24,22 @@ const oauth = (settings = {}) => (app) => {
     const grantConfig = (0, utils_1.getGrantConfig)(authService);
     const serviceOptions = (0, utils_1.authenticationServiceOptions)(authService, oauthOptions);
     const servicePath = `${grantConfig.defaults.prefix || 'oauth'}/:provider`;
-    app.use(servicePath, new service_1.OAuthService(authService, oauthOptions), serviceOptions);
-    const oauthService = app.service(servicePath);
-    oauthService.hooks({
+    const callbackServicePath = `${servicePath}/callback`;
+    const oauthService = new service_1.OAuthService(authService, oauthOptions);
+    app.use(servicePath, oauthService, serviceOptions);
+    app.use(callbackServicePath, new service_1.OAuthCallbackService(oauthService), serviceOptions);
+    app.service(servicePath).hooks({
         around: { all: [(0, schema_1.resolveDispatch)(), (0, service_1.redirectHook)()] }
     });
-    if (typeof oauthService.publish === 'function') {
+    app.service(callbackServicePath).hooks({
+        around: { all: [(0, schema_1.resolveDispatch)(), (0, service_1.redirectHook)()] }
+    });
+    if (typeof app.service(servicePath) === 'function') {
         app.service(servicePath).publish(() => null);
     }
+    if (typeof app.service(callbackServicePath).publish === 'function') {
+        app.service(callbackServicePath).publish(() => null);
+    }
 };
 exports.oauth = oauth;
 //# sourceMappingURL=index.js.map
\ No newline at end of file
diff --git a/node_modules/@feathersjs/authentication-oauth/lib/index.js.map b/node_modules/@feathersjs/authentication-oauth/lib/index.js.map
index 12ee71a..14e9e98 100644
--- a/node_modules/@feathersjs/authentication-oauth/lib/index.js.map
+++ b/node_modules/@feathersjs/authentication-oauth/lib/index.js.map
@@ -1 +1 @@
-{"version":3,"file":"index.js","sourceRoot":"","sources":["../src/index.ts"],"names":[],"mappings":";;;AACA,iDAAiD;AACjD,+CAAoD;AAEpD,yCAAwD;AAM3B,8FANpB,wBAAa,OAMoB;AAL1C,uCAAsD;AACtD,mCAA0F;AAE1F,MAAM,KAAK,GAAG,IAAA,qBAAW,EAAC,kCAAkC,CAAC,CAAA;AAItD,MAAM,KAAK,GAChB,CAAC,WAAwC,EAAE,EAAE,EAAE,CAC/C,CAAC,GAAgB,EAAE,EAAE;IACnB,MAAM,WAAW,GAAG,GAAG,CAAC,qBAAqB,CAAC,CAAC,CAAC,GAAG,CAAC,qBAAqB,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,CAAA;IAEtG,IAAI,CAAC,WAAW,EAAE;QAChB,MAAM,IAAI,KAAK,CACb,0FAA0F,CAC3F,CAAA;KACF;IAED,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,KAAK,EAAE;QACpC,KAAK,CAAC,qFAAqF,CAAC,CAAA;QAC5F,OAAM;KACP;IAED,MAAM,YAAY,GAAG;QACnB,YAAY,EAAE,KAAK;QACnB,GAAG,QAAQ;KACZ,CAAA;IAED,MAAM,WAAW,GAAG,IAAA,sBAAc,EAAC,WAAW,CAAC,CAAA;IAC/C,MAAM,cAAc,GAAG,IAAA,oCAA4B,EAAC,WAAW,EAAE,YAAY,CAAC,CAAA;IAC9E,MAAM,WAAW,GAAG,GAAG,WAAW,CAAC,QAAQ,CAAC,MAAM,IAAI,OAAO,YAAY,CAAA;IAEzE,GAAG,CAAC,GAAG,CAAC,WAAW,EAAE,IAAI,sBAAY,CAAC,WAAW,EAAE,YAAY,CAAC,EAAE,cAAc,CAAC,CAAA;IAEjF,MAAM,YAAY,GAAG,GAAG,CAAC,OAAO,CAAC,WAAW,CAAC,CAAA;IAE7C,YAAY,CAAC,KAAK,CAAC;QACjB,MAAM,EAAE,EAAE,GAAG,EAAE,CAAC,IAAA,wBAAe,GAAE,EAAE,IAAA,sBAAY,GAAE,CAAC,EAAE;KACrD,CAAC,CAAA;IAEF,IAAI,OAAO,YAAY,CAAC,OAAO,KAAK,UAAU,EAAE;QAC9C,GAAG,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,CAAA;KAC7C;AACH,CAAC,CAAA;AApCU,QAAA,KAAK,SAoCf"}
\ No newline at end of file
+{"version":3,"file":"index.js","sourceRoot":"","sources":["../src/index.ts"],"names":[],"mappings":";;;AACA,iDAAiD;AACjD,+CAAoD;AAEpD,yCAAwD;AAM3B,8FANpB,wBAAa,OAMoB;AAL1C,uCAA2E;AAC3E,mCAA0F;AAE1F,MAAM,KAAK,GAAG,IAAA,qBAAW,EAAC,kCAAkC,CAAC,CAAA;AAItD,MAAM,KAAK,GAChB,CAAC,WAAwC,EAAE,EAAE,EAAE,CAC/C,CAAC,GAAgB,EAAE,EAAE;IACnB,MAAM,WAAW,GAAG,GAAG,CAAC,qBAAqB,CAAC,CAAC,CAAC,GAAG,CAAC,qBAAqB,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,CAAA;IAEtG,IAAI,CAAC,WAAW,EAAE;QAChB,MAAM,IAAI,KAAK,CACb,0FAA0F,CAC3F,CAAA;KACF;IAED,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,KAAK,EAAE;QACpC,KAAK,CAAC,qFAAqF,CAAC,CAAA;QAC5F,OAAM;KACP;IAED,MAAM,YAAY,GAAG;QACnB,YAAY,EAAE,KAAK;QACnB,GAAG,QAAQ;KACZ,CAAA;IAED,MAAM,WAAW,GAAG,IAAA,sBAAc,EAAC,WAAW,CAAC,CAAA;IAC/C,MAAM,cAAc,GAAG,IAAA,oCAA4B,EAAC,WAAW,EAAE,YAAY,CAAC,CAAA;IAC9E,MAAM,WAAW,GAAG,GAAG,WAAW,CAAC,QAAQ,CAAC,MAAM,IAAI,OAAO,YAAY,CAAA;IACzE,MAAM,mBAAmB,GAAG,GAAG,WAAW,WAAW,CAAA;IACrD,MAAM,YAAY,GAAG,IAAI,sBAAY,CAAC,WAAW,EAAE,YAAY,CAAC,CAAA;IAEhE,GAAG,CAAC,GAAG,CAAC,WAAW,EAAE,YAAY,EAAE,cAAc,CAAC,CAAA;IAClD,GAAG,CAAC,GAAG,CAAC,mBAAmB,EAAE,IAAI,8BAAoB,CAAC,YAAY,CAAC,EAAE,cAAc,CAAC,CAAA;IACpF,GAAG,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,KAAK,CAAC;QAC7B,MAAM,EAAE,EAAE,GAAG,EAAE,CAAC,IAAA,wBAAe,GAAE,EAAE,IAAA,sBAAY,GAAE,CAAC,EAAE;KACrD,CAAC,CAAA;IACF,GAAG,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC,KAAK,CAAC;QACrC,MAAM,EAAE,EAAE,GAAG,EAAE,CAAC,IAAA,wBAAe,GAAE,EAAE,IAAA,sBAAY,GAAE,CAAC,EAAE;KACrD,CAAC,CAAA;IAEF,IAAI,OAAO,GAAG,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK,UAAU,EAAE;QAClD,GAAG,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,CAAA;KAC7C;IAED,IAAI,OAAO,GAAG,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC,OAAO,KAAK,UAAU,EAAE;QAClE,GAAG,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,CAAA;KACrD;AACH,CAAC,CAAA;AA3CU,QAAA,KAAK,SA2Cf"}
\ No newline at end of file
diff --git a/node_modules/@feathersjs/authentication-oauth/lib/service.d.ts b/node_modules/@feathersjs/authentication-oauth/lib/service.d.ts
index 0fe61e9..e152427 100644
--- a/node_modules/@feathersjs/authentication-oauth/lib/service.d.ts
+++ b/node_modules/@feathersjs/authentication-oauth/lib/service.d.ts
@@ -29,8 +29,16 @@ export declare class OAuthService {
         location: string;
     }>;
     find(params: OAuthParams): Promise<GrantResponse>;
-    get(override: string, params: OAuthParams): Promise<{
+    get(override: string, params: OAuthParams): Promise<GrantResponse>;
+    create(data: any, params: OAuthParams): Promise<GrantResponse>;
+}
+export declare class OAuthCallbackService {
+    service: OAuthService;
+    constructor(service: OAuthService);
+    find(params: OAuthParams): Promise<{
+        location: string;
+    }>;
+    create(data: any, params: OAuthParams): Promise<{
         location: string;
     }>;
-    create(data: any, params: OAuthParams): Promise<GrantResponse>;
 }
diff --git a/node_modules/@feathersjs/authentication-oauth/lib/service.js b/node_modules/@feathersjs/authentication-oauth/lib/service.js
index 7c1cba4..ebf35c0 100644
--- a/node_modules/@feathersjs/authentication-oauth/lib/service.js
+++ b/node_modules/@feathersjs/authentication-oauth/lib/service.js
@@ -3,7 +3,7 @@ var __importDefault = (this && this.__importDefault) || function (mod) {
     return (mod && mod.__esModule) ? mod : { "default": mod };
 };
 Object.defineProperty(exports, "__esModule", { value: true });
-exports.OAuthService = exports.redirectHook = exports.OAuthError = void 0;
+exports.OAuthCallbackService = exports.OAuthService = exports.redirectHook = exports.OAuthError = void 0;
 const commons_1 = require("@feathersjs/commons");
 const errors_1 = require("@feathersjs/errors");
 // eslint-disable-next-line @typescript-eslint/ban-ts-comment
@@ -130,9 +130,6 @@ class OAuthService {
     }
     async get(override, params) {
         const result = await this.handler('GET', params, {}, override);
-        if (override === 'callback') {
-            return this.authenticate(params, result);
-        }
         return result;
     }
     async create(data, params) {
@@ -140,4 +137,18 @@ class OAuthService {
     }
 }
 exports.OAuthService = OAuthService;
+class OAuthCallbackService {
+    constructor(service) {
+        this.service = service;
+    }
+    async find(params) {
+        const result = await this.service.handler('GET', params, {}, 'callback');
+        return this.service.authenticate(params, result);
+    }
+    async create(data, params) {
+        const result = await this.service.handler('POST', params, data, 'callback');
+        return this.service.authenticate(params, result);
+    }
+}
+exports.OAuthCallbackService = OAuthCallbackService;
 //# sourceMappingURL=service.js.map
\ No newline at end of file
diff --git a/node_modules/@feathersjs/authentication-oauth/lib/service.js.map b/node_modules/@feathersjs/authentication-oauth/lib/service.js.map
index cddabab..0575998 100644
--- a/node_modules/@feathersjs/authentication-oauth/lib/service.js.map
+++ b/node_modules/@feathersjs/authentication-oauth/lib/service.js.map
@@ -1 +1 @@
-{"version":3,"file":"service.js","sourceRoot":"","sources":["../src/service.ts"],"names":[],"mappings":";;;;;;AAAA,iDAAiD;AAEjD,+CAAkD;AAClD,6DAA6D;AAC7D,YAAY;AACZ,4DAAmC;AAGnC,mCAA4D;AAE5D,MAAM,KAAK,GAAG,IAAA,qBAAW,EAAC,2CAA2C,CAAC,CAAA;AAgBtE,MAAa,UAAW,SAAQ,sBAAa;IAC3C,YAAY,OAAe,EAAE,IAAS,EAAS,QAAgB;QAC7D,KAAK,CAAC,OAAO,EAAE,kBAAkB,EAAE,GAAG,EAAE,mBAAmB,EAAE,IAAI,CAAC,CAAA;QADrB,aAAQ,GAAR,QAAQ,CAAQ;IAE/D,CAAC;CACF;AAJD,gCAIC;AAEM,MAAM,YAAY,GAAG,GAAG,EAAE,CAAC,KAAK,EAAE,OAAoB,EAAE,IAAkB,EAAE,EAAE;IACnF,IAAI;QACF,MAAM,IAAI,EAAE,CAAA;QAEZ,MAAM,EAAE,QAAQ,EAAE,GAAG,OAAO,CAAC,MAAM,CAAA;QAEnC,KAAK,CAAC,qBAAqB,QAAQ,EAAE,CAAC,CAAA;QAEtC,IAAI,QAAQ,EAAE;YACZ,OAAO,CAAC,IAAI,GAAG;gBACb,GAAG,OAAO,CAAC,IAAI;gBACf,QAAQ;aACT,CAAA;SACF;KACF;IAAC,OAAO,KAAU,EAAE;QACnB,IAAI,KAAK,CAAC,QAAQ,EAAE;YAClB,OAAO,CAAC,IAAI,GAAG;gBACb,GAAG,OAAO,CAAC,IAAI;gBACf,QAAQ,EAAE,KAAK,CAAC,QAAQ;aACzB,CAAA;YACD,OAAO,CAAC,MAAM,GAAG,OAAO,KAAK,CAAC,MAAM,KAAK,UAAU,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,KAAK,CAAA;SAC7E;aAAM;YACL,MAAM,KAAK,CAAA;SACZ;KACF;AACH,CAAC,CAAA;AAzBY,QAAA,YAAY,gBAyBxB;AAED,MAAa,YAAY;IAGvB,YAAmB,OAA8B,EAAS,QAA4B;QAAnE,YAAO,GAAP,OAAO,CAAuB;QAAS,aAAQ,GAAR,QAAQ,CAAoB;QACpF,MAAM,MAAM,GAAG,IAAA,sBAAc,EAAC,OAAO,CAAC,CAAA;QAEtC,IAAI,CAAC,KAAK,GAAG,IAAA,eAAK,EAAC,EAAE,MAAM,EAAE,CAAC,CAAA;IAChC,CAAC;IAED,KAAK,CAAC,OAAO,CAAC,MAAc,EAAE,MAAmB,EAAE,IAAU,EAAE,QAAiB;QAC9E,MAAM,EACJ,OAAO,EACP,KAAK,EACL,KAAK,EACL,KAAK,EAAE,EAAE,QAAQ,EAAE,EACpB,GAAG,MAAM,CAAA;QAEV,MAAM,MAAM,GAAkB,MAAM,IAAI,CAAC,KAAK,CAAC;YAC7C,MAAM,EAAE,EAAE,QAAQ,EAAE,QAAQ,EAAE;YAC9B,KAAK,EAAE,KAAK,CAAC,KAAK;YAClB,OAAO,EAAE,OAAO,CAAC,KAAK;YACtB,KAAK;YACL,MAAM;YACN,IAAI;SACL,CAAC,CAAA;QAEF,OAAO,CAAC,KAAK,GAAG,MAAM,CAAC,OAAO,CAAA;QAC9B,KAAK,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,CAAA;QAE1B,OAAO,MAAM,CAAA;IACf,CAAC;IAED,KAAK,CAAC,YAAY,CAAC,MAAmB,EAAE,MAAqB;;QAC3D,MAAM,IAAI,GAAG,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAA;QAClC,MAAM,EAAE,YAAY,EAAE,WAAW,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAA;QACnD,MAAM,EAAE,WAAW,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,GAAG,EAAE,EAAE,QAAQ,EAAE,GAAG,MAAM,CAAC,OAAO,CAAA;QAC5E,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAkB,CAAA;QAChE,MAAM,UAAU,GAAG;YACjB,GAAG,MAAM;YACT,OAAO;YACP,cAAc,EAAE,CAAC,IAAI,CAAC;YACtB,cAAc,EAAE,WAAW;gBACzB,CAAC,CAAC;oBACE,QAAQ,EAAE,YAAY;oBACtB,WAAW;iBACZ;gBACH,CAAC,CAAC,IAAI;YACR,KAAK;YACL,QAAQ;SACT,CAAA;QACD,MAAM,OAAO,GAAG,CAAA,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,QAAQ,MAAI,MAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,OAAO,0CAAE,QAAQ,CAAA,KAAI,MAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,KAAK,0CAAE,QAAQ,CAAA,IAAI,MAAM,CAAC,KAAK,CAAA;QACvG,MAAM,cAAc,GAAG;YACrB,QAAQ,EAAE,IAAI;YACd,GAAG,OAAO;SACX,CAAA;QAED,IAAI;YACF,KAAK,CAAC,WAAW,WAAW,wCAAwC,IAAI,EAAE,CAAC,CAAA;YAE3E,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,cAAc,EAAE,UAAU,CAAC,CAAA;YAExE,KAAK,CAAC,mDAAmD,CAAC,CAAA;YAE1D,MAAM,QAAQ,GAAG,MAAM,QAAQ,CAAC,WAAW,CAAC,UAAU,EAAE,UAAU,CAAC,CAAA;YAEnE,IAAI,OAAO,MAAM,CAAC,OAAO,CAAC,OAAO,KAAK,UAAU,EAAE;gBAChD,MAAM,MAAM,CAAC,OAAO,CAAC,OAAO,EAAE,CAAA;aAC/B;YAED,OAAO;gBACL,GAAG,UAAU;gBACb,QAAQ;aACT,CAAA;SACF;QAAC,OAAO,KAAU,EAAE;YACnB,MAAM,QAAQ,GAAG,MAAM,QAAQ,CAAC,WAAW,CAAC,KAAK,EAAE,UAAU,CAAC,CAAA;YAC9D,MAAM,CAAC,GAAG,IAAI,UAAU,CAAC,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAA;YAE7D,IAAI,OAAO,MAAM,CAAC,OAAO,CAAC,OAAO,KAAK,UAAU,EAAE;gBAChD,MAAM,MAAM,CAAC,OAAO,CAAC,OAAO,EAAE,CAAA;aAC/B;YAED,CAAC,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,CAAA;YACrB,MAAM,CAAC,CAAA;SACR;IACH,CAAC;IAED,KAAK,CAAC,IAAI,CAAC,MAAmB;QAC5B,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,MAAM,CAAA;QAC1C,MAAM,EAAE,cAAc,EAAE,QAAQ,EAAE,GAAG,SAAS,EAAE,GAAG,KAAK,CAAA;QACxD,MAAM,aAAa,GAAG;YACpB,GAAG,MAAM;YACT,KAAK,EAAE,SAAS;SACjB,CAAA;QAED,IAAI,cAAc,EAAE;YAClB,KAAK,CAAC,qDAAqD,EAAE,cAAc,CAAC,CAAA;YAC5E,OAAO,CAAC,WAAW,GAAG,cAAc,CAAA;SACrC;QAED,OAAO,CAAC,QAAQ,GAAG,QAAQ,CAAA;QAC3B,OAAO,CAAC,KAAK,GAAG,SAAS,CAAA;QACzB,OAAO,CAAC,OAAO,GAAG,OAAO,CAAA;QAEzB,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,aAAa,EAAE,EAAE,CAAC,CAAA;IAC/C,CAAC;IAED,KAAK,CAAC,GAAG,CAAC,QAAgB,EAAE,MAAmB;QAC7C,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,MAAM,EAAE,EAAE,EAAE,QAAQ,CAAC,CAAA;QAE9D,IAAI,QAAQ,KAAK,UAAU,EAAE;YAC3B,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,MAAM,CAAC,CAAA;SACzC;QAED,OAAO,MAAM,CAAA;IACf,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,IAAS,EAAE,MAAmB;QACzC,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,MAAM,EAAE,IAAI,CAAC,CAAA;IAC3C,CAAC;CACF;AAvHD,oCAuHC"}
\ No newline at end of file
+{"version":3,"file":"service.js","sourceRoot":"","sources":["../src/service.ts"],"names":[],"mappings":";;;;;;AAAA,iDAAiD;AAEjD,+CAAkD;AAClD,6DAA6D;AAC7D,YAAY;AACZ,4DAAmC;AAGnC,mCAA4D;AAE5D,MAAM,KAAK,GAAG,IAAA,qBAAW,EAAC,2CAA2C,CAAC,CAAA;AAgBtE,MAAa,UAAW,SAAQ,sBAAa;IAC3C,YAAY,OAAe,EAAE,IAAS,EAAS,QAAgB;QAC7D,KAAK,CAAC,OAAO,EAAE,kBAAkB,EAAE,GAAG,EAAE,mBAAmB,EAAE,IAAI,CAAC,CAAA;QADrB,aAAQ,GAAR,QAAQ,CAAQ;IAE/D,CAAC;CACF;AAJD,gCAIC;AAEM,MAAM,YAAY,GAAG,GAAG,EAAE,CAAC,KAAK,EAAE,OAAoB,EAAE,IAAkB,EAAE,EAAE;IACnF,IAAI;QACF,MAAM,IAAI,EAAE,CAAA;QAEZ,MAAM,EAAE,QAAQ,EAAE,GAAG,OAAO,CAAC,MAAM,CAAA;QAEnC,KAAK,CAAC,qBAAqB,QAAQ,EAAE,CAAC,CAAA;QAEtC,IAAI,QAAQ,EAAE;YACZ,OAAO,CAAC,IAAI,GAAG;gBACb,GAAG,OAAO,CAAC,IAAI;gBACf,QAAQ;aACT,CAAA;SACF;KACF;IAAC,OAAO,KAAU,EAAE;QACnB,IAAI,KAAK,CAAC,QAAQ,EAAE;YAClB,OAAO,CAAC,IAAI,GAAG;gBACb,GAAG,OAAO,CAAC,IAAI;gBACf,QAAQ,EAAE,KAAK,CAAC,QAAQ;aACzB,CAAA;YACD,OAAO,CAAC,MAAM,GAAG,OAAO,KAAK,CAAC,MAAM,KAAK,UAAU,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,KAAK,CAAA;SAC7E;aAAM;YACL,MAAM,KAAK,CAAA;SACZ;KACF;AACH,CAAC,CAAA;AAzBY,QAAA,YAAY,gBAyBxB;AAED,MAAa,YAAY;IAGvB,YAAmB,OAA8B,EAAS,QAA4B;QAAnE,YAAO,GAAP,OAAO,CAAuB;QAAS,aAAQ,GAAR,QAAQ,CAAoB;QACpF,MAAM,MAAM,GAAG,IAAA,sBAAc,EAAC,OAAO,CAAC,CAAA;QAEtC,IAAI,CAAC,KAAK,GAAG,IAAA,eAAK,EAAC,EAAE,MAAM,EAAE,CAAC,CAAA;IAChC,CAAC;IAED,KAAK,CAAC,OAAO,CAAC,MAAc,EAAE,MAAmB,EAAE,IAAU,EAAE,QAAiB;QAC9E,MAAM,EACJ,OAAO,EACP,KAAK,EACL,KAAK,EACL,KAAK,EAAE,EAAE,QAAQ,EAAE,EACpB,GAAG,MAAM,CAAA;QAEV,MAAM,MAAM,GAAkB,MAAM,IAAI,CAAC,KAAK,CAAC;YAC7C,MAAM,EAAE,EAAE,QAAQ,EAAE,QAAQ,EAAE;YAC9B,KAAK,EAAE,KAAK,CAAC,KAAK;YAClB,OAAO,EAAE,OAAO,CAAC,KAAK;YACtB,KAAK;YACL,MAAM;YACN,IAAI;SACL,CAAC,CAAA;QAEF,OAAO,CAAC,KAAK,GAAG,MAAM,CAAC,OAAO,CAAA;QAC9B,KAAK,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,CAAA;QAE1B,OAAO,MAAM,CAAA;IACf,CAAC;IAED,KAAK,CAAC,YAAY,CAAC,MAAmB,EAAE,MAAqB;;QAC3D,MAAM,IAAI,GAAG,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAA;QAClC,MAAM,EAAE,YAAY,EAAE,WAAW,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAA;QACnD,MAAM,EAAE,WAAW,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,GAAG,EAAE,EAAE,QAAQ,EAAE,GAAG,MAAM,CAAC,OAAO,CAAA;QAC5E,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAkB,CAAA;QAChE,MAAM,UAAU,GAAG;YACjB,GAAG,MAAM;YACT,OAAO;YACP,cAAc,EAAE,CAAC,IAAI,CAAC;YACtB,cAAc,EAAE,WAAW;gBACzB,CAAC,CAAC;oBACE,QAAQ,EAAE,YAAY;oBACtB,WAAW;iBACZ;gBACH,CAAC,CAAC,IAAI;YACR,KAAK;YACL,QAAQ;SACT,CAAA;QACD,MAAM,OAAO,GAAG,CAAA,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,QAAQ,MAAI,MAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,OAAO,0CAAE,QAAQ,CAAA,KAAI,MAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,KAAK,0CAAE,QAAQ,CAAA,IAAI,MAAM,CAAC,KAAK,CAAA;QACvG,MAAM,cAAc,GAAG;YACrB,QAAQ,EAAE,IAAI;YACd,GAAG,OAAO;SACX,CAAA;QAED,IAAI;YACF,KAAK,CAAC,WAAW,WAAW,wCAAwC,IAAI,EAAE,CAAC,CAAA;YAE3E,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,cAAc,EAAE,UAAU,CAAC,CAAA;YAExE,KAAK,CAAC,mDAAmD,CAAC,CAAA;YAE1D,MAAM,QAAQ,GAAG,MAAM,QAAQ,CAAC,WAAW,CAAC,UAAU,EAAE,UAAU,CAAC,CAAA;YAEnE,IAAI,OAAO,MAAM,CAAC,OAAO,CAAC,OAAO,KAAK,UAAU,EAAE;gBAChD,MAAM,MAAM,CAAC,OAAO,CAAC,OAAO,EAAE,CAAA;aAC/B;YAED,OAAO;gBACL,GAAG,UAAU;gBACb,QAAQ;aACT,CAAA;SACF;QAAC,OAAO,KAAU,EAAE;YACnB,MAAM,QAAQ,GAAG,MAAM,QAAQ,CAAC,WAAW,CAAC,KAAK,EAAE,UAAU,CAAC,CAAA;YAC9D,MAAM,CAAC,GAAG,IAAI,UAAU,CAAC,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAA;YAE7D,IAAI,OAAO,MAAM,CAAC,OAAO,CAAC,OAAO,KAAK,UAAU,EAAE;gBAChD,MAAM,MAAM,CAAC,OAAO,CAAC,OAAO,EAAE,CAAA;aAC/B;YAED,CAAC,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,CAAA;YACrB,MAAM,CAAC,CAAA;SACR;IACH,CAAC;IAED,KAAK,CAAC,IAAI,CAAC,MAAmB;QAC5B,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,MAAM,CAAA;QAC1C,MAAM,EAAE,cAAc,EAAE,QAAQ,EAAE,GAAG,SAAS,EAAE,GAAG,KAAK,CAAA;QACxD,MAAM,aAAa,GAAG;YACpB,GAAG,MAAM;YACT,KAAK,EAAE,SAAS;SACjB,CAAA;QAED,IAAI,cAAc,EAAE;YAClB,KAAK,CAAC,qDAAqD,EAAE,cAAc,CAAC,CAAA;YAC5E,OAAO,CAAC,WAAW,GAAG,cAAc,CAAA;SACrC;QAED,OAAO,CAAC,QAAQ,GAAG,QAAQ,CAAA;QAC3B,OAAO,CAAC,KAAK,GAAG,SAAS,CAAA;QACzB,OAAO,CAAC,OAAO,GAAG,OAAO,CAAA;QAEzB,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,aAAa,EAAE,EAAE,CAAC,CAAA;IAC/C,CAAC;IAED,KAAK,CAAC,GAAG,CAAC,QAAgB,EAAE,MAAmB;QAC7C,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,MAAM,EAAE,EAAE,EAAE,QAAQ,CAAC,CAAA;QAG9D,OAAO,MAAM,CAAA;IACf,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,IAAS,EAAE,MAAmB;QACzC,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,MAAM,EAAE,IAAI,CAAC,CAAA;IAC3C,CAAC;CACF;AApHD,oCAoHC;AAED,MAAa,oBAAoB;IAC/B,YAAmB,OAAqB;QAArB,YAAO,GAAP,OAAO,CAAc;IAAG,CAAC;IAE5C,KAAK,CAAC,IAAI,CAAC,MAAmB;QAC5B,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,EAAE,MAAM,EAAE,EAAE,EAAE,UAAU,CAAC,CAAA;QAExE,OAAO,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,MAAM,EAAE,MAAM,CAAC,CAAA;IAClD,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,IAAS,EAAE,MAAmB;QACzC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,UAAU,CAAC,CAAA;QAE3E,OAAO,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,MAAM,EAAE,MAAM,CAAC,CAAA;IAClD,CAAC;CACF;AAdD,oDAcC"}
\ No newline at end of file
