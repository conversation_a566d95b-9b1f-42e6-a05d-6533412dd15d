/*
CPAL-1.0 License

The contents of this file are subject to the Common Public Attribution License
Version 1.0. (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at
https://github.com/ir-engine/ir-engine/blob/dev/LICENSE.
The License is based on the Mozilla Public License Version 1.1, but Sections 14
and 15 have been added to cover use of software over a computer network and 
provide for limited attribution for the Original Developer. In addition, 
Exhibit A has been modified to be consistent with Exhibit B.

Software distributed under the License is distributed on an "AS IS" basis,
WITHOUT WARRANTY OF ANY KIND, either express or implied. See the License for the
specific language governing rights and limitations under the License.

The Original Code is Infinite Reality Engine.

The Original Developer is the Initial Developer. The Initial Developer of the
Original Code is the Infinite Reality Engine team.

All portions of the code written by the Infinite Reality Engine team are Copyright © 2021-2025
Infinite Reality Engine. All Rights Reserved.
*/

import AcceptInvite from '../user/accept-invite/accept-invite'
import Avatar from './avatar/avatar'
import DiscordBotAuth from './discord-bot-auth/discord-bot-auth'
import Email from './email/email'
import GenerateToken from './generate-token/generate-token'
import GithubRepoAccessRefresh from './github-repo-access-refresh/github-repo-access-refresh'
import GithubRepoAccessWebhook from './github-repo-access-webhook/github-repo-access-webhook'
import GithubRepoAccess from './github-repo-access/github-repo-access'
import IdentityProvider from './identity-provider/identity-provider'
import JwtPublicKey from './jwt-public-key/jwt-public-key'
import LoginToken from './login-token/login-token'
import Login from './login/login'
import MagicLink from './magic-link/magic-link'
import SMS from './sms/sms'
import UserApiKey from './user-api-key/user-api-key'
import UserAvatar from './user-avatar/user-avatar'
import UserKick from './user-kick/user-kick'
import UserLogin from './user-login/user-login'
import UserRelationshipType from './user-relationship-type/user-relationship-type'
import UserRelationship from './user-relationship/user-relationship'
import UserSettings from './user-setting/user-setting'
import User from './user/user'

export default [
  UserApiKey,
  User,
  UserAvatar,
  UserSettings,
  UserKick,
  UserLogin,
  IdentityProvider,
  UserRelationshipType,
  UserRelationship,
  AcceptInvite,
  Avatar,
  Login,
  LoginToken,
  MagicLink,
  Email,
  SMS,
  DiscordBotAuth,
  GithubRepoAccess,
  GithubRepoAccessRefresh,
  GithubRepoAccessWebhook,
  GenerateToken,
  JwtPublicKey
]
