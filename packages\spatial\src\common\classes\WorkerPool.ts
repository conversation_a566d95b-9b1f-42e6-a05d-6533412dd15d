/*
CPAL-1.0 License

The contents of this file are subject to the Common Public Attribution License
Version 1.0. (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at
https://github.com/ir-engine/ir-engine/blob/dev/LICENSE.
The License is based on the Mozilla Public License Version 1.1, but Sections 14
and 15 have been added to cover use of software over a computer network and 
provide for limited attribution for the Original Developer. In addition, 
Exhibit A has been modified to be consistent with Exhibit B.

Software distributed under the License is distributed on an "AS IS" basis,
WITHOUT WARRANTY OF ANY KIND, either express or implied. See the License for the
specific language governing rights and limitations under the License.

The Original Code is Infinite Reality Engine.

The Original Developer is the Initial Developer. The Initial Developer of the
Original Code is the Infinite Reality Engine team.

All portions of the code written by the Infinite Reality Engine team are Copyright © 2021-2025
Infinite Reality Engine. All Rights Reserved.
*/

/**
 * <AUTHOR> / https://github.com/deepkolos
 */

export class WorkerPool {
  limit: number
  queue = [] as Array<{ resolve: any; msg: any; transfer: Transferable[]; signal?: AbortSignal }>
  workers = [] as Worker[]
  workersResolve = [] as Array<any>
  workerStatus = 0

  private workerCreator?: () => Worker

  constructor(pool = 1) {
    this.limit = pool
  }

  _initWorker(workerId: number) {
    if (!this.workers[workerId]) {
      const worker = this.workerCreator!()
      worker.addEventListener('message', this._onMessage.bind(this, workerId))
      this.workers[workerId] = worker
    }
  }

  _getIdleWorker() {
    for (let i = 0; i < this.limit; i++) if (!(this.workerStatus & (1 << i))) return i

    return -1
  }

  _onMessage(workerId: number, msg: MessageEvent) {
    const resolve = this.workersResolve[workerId]
    resolve && resolve(msg)
    this.workersResolve[workerId] = null

    if (this.queue.length) {
      this._postWorker(workerId)
    } else {
      this.workerStatus ^= 1 << workerId
    }
  }

  _postWorker(workerId: number) {
    const { resolve, msg, transfer, signal } = this.queue.shift() as any
    if (signal?.aborted) {
      if (this.queue.length) this._postWorker(workerId)
      return
    }
    this.workersResolve[workerId] = resolve
    this.workers[workerId].postMessage(msg, transfer)
  }

  setWorkerCreator(workerCreator: () => Worker) {
    this.workerCreator = workerCreator
  }

  setWorkerLimit(pool: number) {
    this.limit = pool
  }

  postMessage<T = any>(msg: any, transfer: Transferable[], signal?: AbortSignal): Promise<MessageEvent<T>> {
    return new Promise((resolve) => {
      const workerId = this._getIdleWorker()

      if (workerId !== -1) {
        this._initWorker(workerId)
        this.workerStatus |= 1 << workerId
        this.workersResolve[workerId] = resolve
        this.workers[workerId].postMessage(msg, transfer)
      } else {
        this.queue.push({ resolve, msg, transfer, signal })
      }
    })
  }

  dispose() {
    this.workers.forEach((worker) => worker.terminate())
    this.workersResolve.length = 0
    this.workers.length = 0
    this.queue.length = 0
    this.workerStatus = 0
  }
}
