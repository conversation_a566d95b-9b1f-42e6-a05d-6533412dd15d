# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.



**Note:** Version bump only for package @etherealengine/server-core







**Note:** Version bump only for package @etherealengine/server-core







**Note:** Version bump only for package @etherealengine/server-core







**Note:** Version bump only for package @etherealengine/server-core







**Note:** Version bump only for package @etherealengine/server-core







**Note:** Version bump only for package @etherealengine/server-core







**Note:** Version bump only for package @etherealengine/server-core







**Note:** Version bump only for package @xrengine/server-core







**Note:** Version bump only for package @xrengine/server-core







**Note:** Version bump only for package @xrengine/server-core







**Note:** Version bump only for package @xrengine/server-core







**Note:** Version bump only for package @xrengine/server-core







**Note:** Version bump only for package @xrengine/server-core







**Note:** Version bump only for package @xrengine/server-core







**Note:** Version bump only for package @xrengine/server-core







**Note:** Version bump only for package @xrengine/server-core







**Note:** Version bump only for package @xrengine/server-core







**Note:** Version bump only for package @xrengine/server-core







**Note:** Version bump only for package @xrengine/server-core







**Note:** Version bump only for package @xrengine/server-core







**Note:** Version bump only for package @xrengine/server-core







**Note:** Version bump only for package @xrengine/server-core







**Note:** Version bump only for package @xrengine/server-core







**Note:** Version bump only for package @xrengine/server-core







**Note:** Version bump only for package @xrengine/server-core







**Note:** Version bump only for package @xrengine/server-core





## [0.5.2](https://github.com/XRFoundation/XREngine/compare/v0.5.1...v0.5.2) (2022-04-07)

**Note:** Version bump only for package @xrengine/server-core







**Note:** Version bump only for package @xrengine/server-core







**Note:** Version bump only for package @xrengine/server-core







**Note:** Version bump only for package @xrengine/server-core







**Note:** Version bump only for package @xrengine/server-core







**Note:** Version bump only for package @xrengine/server-core







**Note:** Version bump only for package @xrengine/server-core







**Note:** Version bump only for package @xrengine/server-core







**Note:** Version bump only for package @xrengine/server-core







**Note:** Version bump only for package @xrengine/server-core







**Note:** Version bump only for package @xrengine/server-core







**Note:** Version bump only for package @xrengine/server-core







**Note:** Version bump only for package @xrengine/server-core







**Note:** Version bump only for package @xrengine/server-core







**Note:** Version bump only for package @xrengine/server-core







**Note:** Version bump only for package @xrengine/server-core







**Note:** Version bump only for package @xrengine/server-core







**Note:** Version bump only for package @xrengine/server-core







**Note:** Version bump only for package @xrengine/server-core







**Note:** Version bump only for package @xrengine/server-core







**Note:** Version bump only for package @xrengine/server-core







**Note:** Version bump only for package @xrengine/server-core







**Note:** Version bump only for package @xrengine/server-core







**Note:** Version bump only for package @xrengine/server-core







**Note:** Version bump only for package @xrengine/server-core







**Note:** Version bump only for package @xrengine/server-core







**Note:** Version bump only for package @xrengine/server-core







**Note:** Version bump only for package @xrengine/server-core







**Note:** Version bump only for package @xrengine/server-core







**Note:** Version bump only for package @xrengine/server-core







**Note:** Version bump only for package @xrengine/server-core





## 0.2.36 (2021-06-25)

**Note:** Version bump only for package @xrengine/server-core





## 0.2.35 (2021-06-25)

**Note:** Version bump only for package @xrengine/server-core





## 0.2.34 (2021-06-25)

**Note:** Version bump only for package @xrengine/server-core





## 0.2.33 (2021-06-25)

**Note:** Version bump only for package @xrengine/server-core





## 0.2.32 (2021-06-25)

**Note:** Version bump only for package @xrengine/server-core





## 0.2.31 (2021-06-25)

**Note:** Version bump only for package @xrengine/server-core





## 0.2.30 (2021-06-25)

**Note:** Version bump only for package @xrengine/server-core





## [0.2.29](https://github.com/barankyle/xr3ngine/compare/v0.2.28...v0.2.29) (2021-06-24)

**Note:** Version bump only for package @xrengine/server-core





## [0.2.28](https://github.com/barankyle/xr3ngine/compare/v0.2.27...v0.2.28) (2021-06-22)

**Note:** Version bump only for package @xrengine/server-core





## [0.2.27](https://github.com/barankyle/xrengine/compare/v0.2.26...v0.2.27) (2021-05-13)

**Note:** Version bump only for package @xrengine/server-core





## [0.2.26](https://github.com/barankyle/xrengine/compare/v0.2.24...v0.2.26) (2021-05-12)

**Note:** Version bump only for package @xrengine/server-core





## [0.2.25](https://github.com/barankyle/xrengine/compare/v0.2.24...v0.2.25) (2021-05-12)

**Note:** Version bump only for package @xrengine/server-core





## [0.2.24](https://github.com/barankyle/xrengine/compare/v0.2.23...v0.2.24) (2021-05-12)

**Note:** Version bump only for package @xrengine/server-core





## [0.2.23](https://github.com/barankyle/xrengine/compare/v0.2.22...v0.2.23) (2021-05-12)

**Note:** Version bump only for package @xrengine/server-core





## [0.2.22](https://github.com/xrengine/xrengine/compare/v0.2.21...v0.2.22) (2021-05-05)

**Note:** Version bump only for package @xrengine/server-core





## [0.2.21](https://github.com/barankyle/xrengine/compare/v0.2.20...v0.2.21) (2021-05-05)

**Note:** Version bump only for package @xrengine/server-core





## [0.2.20](https://github.com/barankyle/xrengine/compare/v0.2.18...v0.2.20) (2021-05-04)


### Bug Fixes

* **deps:** update dependency @feathersjs/hooks to v0.6.4 ([df63c37](https://github.com/barankyle/xrengine/commit/df63c37dcf4eb61a8e9ed4bdcfa2053d60164d8b))





## 0.2.18 (2021-04-22)


### Bug Fixes

* **deps:** update dependency @feathersjs/hooks to v0.6.4 ([e4afbb4](https://github.com/XRFoundation/XREngine/commit/e4afbb4e1f3f085855393eea997453c6002aaedb))





## [0.2.11](https://github.com/XRFoundation/XREngine/compare/v0.2.10...v0.2.11) (2021-04-08)

**Note:** Version bump only for package @xrengine/server-core
