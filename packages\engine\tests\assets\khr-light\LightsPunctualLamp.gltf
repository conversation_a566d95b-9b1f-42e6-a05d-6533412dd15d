{"accessors": [{"bufferView": 0, "componentType": 5123, "count": 12210, "max": [3211], "min": [0], "type": "SCALAR"}, {"bufferView": 1, "componentType": 5126, "count": 3212, "max": [0.8149782419204712, 1.8746249675750732, 0.3229551613330841], "min": [-0.12269512563943863, 0.013025385327637196, -0.3239322900772095], "type": "VEC3"}, {"bufferView": 2, "componentType": 5126, "count": 3212, "max": [0.999445378780365, 0.9985229969024658], "min": [0.0005546413594856858, 0.001477026497013867], "type": "VEC2"}, {"bufferView": 3, "componentType": 5126, "count": 3212, "max": [0.9972318410873413, 0.9999950528144836, 0.9997968077659607], "min": [-0.9996182918548584, -0.9999997019767761, -0.9999534487724304], "type": "VEC3"}, {"bufferView": 4, "componentType": 5126, "count": 3212, "max": [0.9997625946998596, 0.9990524053573608, 0.999835193157196, 1], "min": [-0.9995244741439819, -0.9999517798423767, -0.9999256134033203, 1], "type": "VEC4"}, {"bufferView": 5, "componentType": 5123, "count": 42, "max": [17], "min": [0], "type": "SCALAR"}, {"bufferView": 6, "componentType": 5126, "count": 18, "max": [0.3158707022666931, 0.01179451122879982, 0.17449036240577698], "min": [0.09883859008550644, 0.0110623212531209, -0.04127493128180504], "type": "VEC3"}, {"bufferView": 7, "componentType": 5126, "count": 18, "max": [0.9983924031257629, 0.9992263913154602], "min": [0.001607581740245223, 0.0007736252155154943], "type": "VEC2"}, {"bufferView": 8, "componentType": 5126, "count": 18, "max": [0.0008380493381991982, 0.9999973773956299, 0.004318808205425739], "min": [-0.0010681741405278444, -0.9999967813491821, -0.0038908792193979025], "type": "VEC3"}, {"bufferView": 9, "componentType": 5126, "count": 18, "max": [0.597588062286377, 0.004002281930297613, 0.8212687969207764, 1], "min": [-0.9242449998855591, 0.0003600451163947582, 0.38179951906204224, 1], "type": "VEC4"}, {"bufferView": 10, "componentType": 5123, "count": 5748, "max": [1324], "min": [0], "type": "SCALAR"}, {"bufferView": 11, "componentType": 5126, "count": 1325, "max": [0.4034070372581482, 1.4182039499282837, 0.35968679189682007], "min": [-0.3120085895061493, 0.9798182249069214, -0.3619171977043152], "type": "VEC3"}, {"bufferView": 12, "componentType": 5126, "count": 1325, "max": [0.9974830150604248, 0.9955689311027527], "min": [0.0016639239620417356, 0.046032872051000595], "type": "VEC2"}, {"bufferView": 13, "componentType": 5126, "count": 1325, "max": [0.9976866245269775, 0.9996117949485779, 0.9984883069992065], "min": [-0.9993720650672913, -0.23119865357875824, -0.9988537430763245], "type": "VEC3"}, {"bufferView": 14, "componentType": 5126, "count": 1325, "max": [0.9998758435249329, 0.9048672914505005, 0.9960941076278687, 1], "min": [-0.9987566471099854, -0.9998937845230103, -0.9994584918022156, 1], "type": "VEC4"}], "asset": {"generator": "Created by RapidCompact v5.4.0-rc4 | www.rapidcompact.com", "version": "2.0"}, "bufferViews": [{"buffer": 0, "byteLength": 24420, "byteOffset": 0, "target": 34963}, {"buffer": 0, "byteLength": 38544, "byteOffset": 24420, "byteStride": 12, "target": 34962}, {"buffer": 0, "byteLength": 25696, "byteOffset": 62964, "byteStride": 8, "target": 34962}, {"buffer": 0, "byteLength": 38544, "byteOffset": 88660, "byteStride": 12, "target": 34962}, {"buffer": 0, "byteLength": 51392, "byteOffset": 127204, "byteStride": 16, "target": 34962}, {"buffer": 0, "byteLength": 84, "byteOffset": 178596, "target": 34963}, {"buffer": 0, "byteLength": 216, "byteOffset": 178680, "byteStride": 12, "target": 34962}, {"buffer": 0, "byteLength": 144, "byteOffset": 178896, "byteStride": 8, "target": 34962}, {"buffer": 0, "byteLength": 216, "byteOffset": 179040, "byteStride": 12, "target": 34962}, {"buffer": 0, "byteLength": 288, "byteOffset": 179256, "byteStride": 16, "target": 34962}, {"buffer": 0, "byteLength": 11496, "byteOffset": 179544, "target": 34963}, {"buffer": 0, "byteLength": 15900, "byteOffset": 191040, "byteStride": 12, "target": 34962}, {"buffer": 0, "byteLength": 10600, "byteOffset": 206940, "byteStride": 8, "target": 34962}, {"buffer": 0, "byteLength": 15900, "byteOffset": 217540, "byteStride": 12, "target": 34962}, {"buffer": 0, "byteLength": 21200, "byteOffset": 233440, "byteStride": 16, "target": 34962}], "buffers": [{"byteLength": 254640, "uri": "LightsPunctualLamp.data.bin"}], "extensions": {"KHR_lights_punctual": {"lights": [{"color": [1, 0.6318749785423279, 0.23909975588321689], "intensity": 15, "name": "Point", "type": "point"}, {"intensity": 1.5, "name": "Point.002", "type": "point"}, {"color": [0.21223080158233645, 0.5906190276145935, 0.5583405494689941], "intensity": 80, "name": "Point.003", "type": "point"}, {"color": [0.21223080158233645, 0.5906190276145935, 0.5583405494689941], "intensity": 80, "name": "Point.001", "type": "point"}, {"color": [1, 0.6278365254402161, 0.5012403726577759], "intensity": 180, "name": "Point.004", "type": "point"}]}}, "extensionsUsed": ["KHR_materials_transmission", "KHR_lights_punctual"], "images": [{"uri": "material0_basecolor.jpeg"}, {"uri": "material0_normal.png"}, {"uri": "material0_emissive.jpeg"}, {"uri": "material0_metallic_roughness.jpeg"}, {"uri": "material1_basecolor.png"}, {"uri": "material1_normal.png"}, {"uri": "material2_transmission.jpeg"}], "materials": [{"doubleSided": true, "emissiveFactor": [1, 1, 1], "emissiveTexture": {"index": 2}, "normalTexture": {"index": 1}, "pbrMetallicRoughness": {"baseColorTexture": {"index": 0}, "metallicRoughnessTexture": {"index": 3}}}, {"alphaMode": "BLEND", "doubleSided": true, "normalTexture": {"index": 5}, "pbrMetallicRoughness": {"baseColorTexture": {"index": 4}, "metallicFactor": 0, "roughnessFactor": 0.5}}, {"doubleSided": true, "emissiveFactor": [1, 1, 1], "emissiveTexture": {"index": 2}, "extensions": {"KHR_materials_transmission": {"transmissionFactor": 1, "transmissionTexture": {"index": 6}}}, "normalTexture": {"index": 1}, "pbrMetallicRoughness": {"baseColorTexture": {"index": 0}, "metallicRoughnessTexture": {"index": 3}}}], "meshes": [{"primitives": [{"attributes": {"NORMAL": 3, "POSITION": 1, "TANGENT": 4, "TEXCOORD_0": 2}, "indices": 0, "material": 0, "mode": 4}]}, {"primitives": [{"attributes": {"NORMAL": 8, "POSITION": 6, "TANGENT": 9, "TEXCOORD_0": 7}, "indices": 5, "material": 1, "mode": 4}]}, {"primitives": [{"attributes": {"NORMAL": 13, "POSITION": 11, "TANGENT": 14, "TEXCOORD_0": 12}, "indices": 10, "material": 2, "mode": 4}]}], "nodes": [{"mesh": 0}, {"mesh": 1}, {"mesh": 2}, {"extensions": {"KHR_lights_punctual": {"light": 0}}, "matrix": [1, 0, 0, 0, 0, 3.422854177870249e-08, -0.9999999657714582, 0, 0, 0.9999999657714582, 3.422854177870249e-08, 0, 0.04622355476021767, 0.9077973365783693, 0.006696629337966442, 1], "name": "Point_Orientation"}, {"extensions": {"KHR_lights_punctual": {"light": 1}}, "matrix": [1, 0, 0, 0, 0, 3.422854177870249e-08, -0.9999999657714582, 0, 0, 0.9999999657714582, 3.422854177870249e-08, 0, 0.1754562258720398, -0.7642198801040649, -0.005704082548618317, 1], "name": "Point.002_Orientation"}, {"extensions": {"KHR_lights_punctual": {"light": 2}}, "matrix": [1, 0, 0, 0, 0, 3.422854177870249e-08, -0.9999999657714582, 0, 0, 0.9999999657714582, 3.422854177870249e-08, 0, 0.13761821389198303, 2.066983699798584, -1.178986430168152, 1], "name": "Point.001_Orientation"}, {"extensions": {"KHR_lights_punctual": {"light": 3}}, "matrix": [1, 0, 0, 0, 0, 3.422854177870249e-08, -0.9999999657714582, 0, 0, 0.9999999657714582, 3.422854177870249e-08, 0, 0.0442890003323555, 0.25436437129974365, -1.209032416343689, 1], "name": "Point.003_Orientation"}, {"extensions": {"KHR_lights_punctual": {"light": 4}}, "matrix": [1, 0, 0, 0, 0, 3.422854177870249e-08, -0.9999999657714582, 0, 0, 0.9999999657714582, 3.422854177870249e-08, 0, 0.2920210361480713, 1.0323998928070068, 1.5589159727096558, 1], "name": "Point.004_Orientation"}], "samplers": [{"wrapS": 33071, "wrapT": 33071}], "scene": 0, "scenes": [{"nodes": [0, 1, 2, 3, 4, 5, 6, 7]}], "textures": [{"sampler": 0, "source": 0}, {"sampler": 0, "source": 1}, {"sampler": 0, "source": 2}, {"sampler": 0, "source": 3}, {"sampler": 0, "source": 4}, {"sampler": 0, "source": 5}, {"sampler": 0, "source": 6}]}