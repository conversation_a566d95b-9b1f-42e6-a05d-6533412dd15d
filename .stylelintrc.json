{"extends": ["stylelint-config-standard-scss"], "plugins": ["stylelint-scss"], "overrides": [{"files": ["packages/**/*.scss"], "customSyntax": "postcss-scss"}], "rules": {"scss/at-import-partial-extension": null, "scss/no-global-function-names": null, "at-rule-no-unknown": null, "no-descending-specificity": null, "indentation": 2, "number-leading-zero": null, "property-no-vendor-prefix": null, "font-family-no-missing-generic-family-keyword": null, "selector-pseudo-class-no-unknown": null, "selector-class-pattern": ["(^[a-z][a-zA-Z0-9]+$)|(^([a-z][a-z0-9]*)(-[a-z0-9]+)*$)|(^([A-Z][a-zA-Z0-9]*)(-[a-zA-Z0-9]+)*$)"], "keyframes-name-pattern": "^[a-z][a-zA-Z0-9]+$", "custom-property-pattern": null, "scss/dollar-variable-pattern": "^[A-Z][a-zA-Z0-9]+$", "number-max-precision": [4, {"severity": "warning"}]}}