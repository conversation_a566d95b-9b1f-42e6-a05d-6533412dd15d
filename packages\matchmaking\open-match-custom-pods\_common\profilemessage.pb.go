// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.25.0-devel
// 	protoc        v3.14.0
// source: profilemessage.proto

package common

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ProfileDataMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Mode     string `protobuf:"bytes,1,opt,name=mode,proto3" json:"mode,omitempty"`
	TeamSize uint32 `protobuf:"varint,2,opt,name=team_size,json=teamSize,proto3" json:"team_size,omitempty"`
}

func (x *ProfileDataMessage) Reset() {
	*x = ProfileDataMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_profilemessage_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProfileDataMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProfileDataMessage) ProtoMessage() {}

func (x *ProfileDataMessage) ProtoReflect() protoreflect.Message {
	mi := &file_profilemessage_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProfileDataMessage.ProtoReflect.Descriptor instead.
func (*ProfileDataMessage) Descriptor() ([]byte, []int) {
	return file_profilemessage_proto_rawDescGZIP(), []int{0}
}

func (x *ProfileDataMessage) GetMode() string {
	if x != nil {
		return x.Mode
	}
	return ""
}

func (x *ProfileDataMessage) GetTeamSize() uint32 {
	if x != nil {
		return x.TeamSize
	}
	return 0
}

var File_profilemessage_proto protoreflect.FileDescriptor

var file_profilemessage_proto_rawDesc = []byte{
	0x0a, 0x14, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x06, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x22, 0x45,
	0x0a, 0x12, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x44, 0x61, 0x74, 0x61, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6d, 0x6f, 0x64, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x74, 0x65, 0x61, 0x6d,
	0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x74, 0x65, 0x61,
	0x6d, 0x53, 0x69, 0x7a, 0x65, 0x42, 0x0a, 0x5a, 0x08, 0x2e, 0x3b, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_profilemessage_proto_rawDescOnce sync.Once
	file_profilemessage_proto_rawDescData = file_profilemessage_proto_rawDesc
)

func file_profilemessage_proto_rawDescGZIP() []byte {
	file_profilemessage_proto_rawDescOnce.Do(func() {
		file_profilemessage_proto_rawDescData = protoimpl.X.CompressGZIP(file_profilemessage_proto_rawDescData)
	})
	return file_profilemessage_proto_rawDescData
}

var file_profilemessage_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_profilemessage_proto_goTypes = []interface{}{
	(*ProfileDataMessage)(nil), // 0: common.ProfileDataMessage
}
var file_profilemessage_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_profilemessage_proto_init() }
func file_profilemessage_proto_init() {
	if File_profilemessage_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_profilemessage_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProfileDataMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_profilemessage_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_profilemessage_proto_goTypes,
		DependencyIndexes: file_profilemessage_proto_depIdxs,
		MessageInfos:      file_profilemessage_proto_msgTypes,
	}.Build()
	File_profilemessage_proto = out.File
	file_profilemessage_proto_rawDesc = nil
	file_profilemessage_proto_goTypes = nil
	file_profilemessage_proto_depIdxs = nil
}
