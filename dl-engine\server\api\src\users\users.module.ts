import { Module } from '@nestjs/common'
import { TypeOrmModule } from '@nestjs/typeorm'
import { ConfigModule } from '@nestjs/config'

import { User } from './entities/user.entity'
import { UserProfile } from './entities/user-profile.entity'
import { UserPreference } from './entities/user-preference.entity'
import { UserPrivacy } from './entities/user-privacy.entity'
import { UserEducation } from './entities/user-education.entity'

import { UsersController } from './controllers/users.controller'
import { UserProfileController } from './controllers/user-profile.controller'
import { UserPreferenceController } from './controllers/user-preference.controller'
import { UserPrivacyController } from './controllers/user-privacy.controller'
import { UserEducationController } from './controllers/user-education.controller'

import { UsersService } from './services/users.service'
import { UserProfileService } from './services/user-profile.service'
import { UserPreferenceService } from './services/user-preference.service'
import { UserPrivacyService } from './services/user-privacy.service'
import { UserEducationService } from './services/user-education.service'
import { UserValidationService } from './services/user-validation.service'
import { UserNotificationService } from './services/user-notification.service'

import { AuthModule } from '../auth/auth.module'
import { NotificationModule } from '../notifications/notification.module'

/**
 * 用户管理模块
 * 
 * 功能包括：
 * - 用户注册和资料管理
 * - 用户偏好设置
 * - 隐私控制
 * - 教育信息管理
 * - 用户验证和通知
 */
@Module({
  imports: [
    ConfigModule,
    TypeOrmModule.forFeature([
      User,
      UserProfile,
      UserPreference,
      UserPrivacy,
      UserEducation
    ]),
    AuthModule,
    NotificationModule
  ],
  controllers: [
    UsersController,
    UserProfileController,
    UserPreferenceController,
    UserPrivacyController,
    UserEducationController
  ],
  providers: [
    UsersService,
    UserProfileService,
    UserPreferenceService,
    UserPrivacyService,
    UserEducationService,
    UserValidationService,
    UserNotificationService
  ],
  exports: [
    UsersService,
    UserProfileService,
    UserPreferenceService,
    UserPrivacyService,
    UserEducationService
  ]
})
export class UsersModule {}
