{"name": "@ir-engine/instanceserver", "description": "Multiplayer Instanceserver for Infinite Reality Engine", "version": "1.0.3", "private": true, "homepage": "", "main": "./src", "keywords": ["three", "three.js", "ecs", "webgl", "mmo", "game engine", "webrtc", "productivity", "xr", "vr"], "author": {"name": "Infinite Reality Engine Collective", "email": "<EMAIL>"}, "contributors": [], "bugs": {}, "directories": {"lib": "src/", "config": "config/"}, "engines": {"node": ">= 22.11.0"}, "scripts": {"check-errors": "tsc --noemit && npx cycle-import-check src || true", "start": "cross-env APP_ENV=production ts-node --swc src/index.ts", "start-channel": "cross-env APP_ENV=production INSTANCESERVER_PORT=3032 DEV_CHANNEL=true ts-node --swc src/index.ts", "dev": "cross-env APP_ENV=development NODE_OPTIONS='--inspect=2995' ts-node --swc src/index.ts", "dev-channel": " cross-env APP_ENV=development NODE_OPTIONS='--inspect=2996' DEV_CHANNEL=true INSTANCESERVER_PORT=3032 ts-node --swc src/index.ts", "dev-nossl": "cross-env NOSSL=true ts-node --swc src/index.ts", "test": "cross-env TEST=true vitest run --config=../../vitest.server.config.ts", "validate": "npm run build && npm run test"}, "types": "lib/", "dependencies": {"@ir-engine/common": "^1.0.3", "@ir-engine/ecs": "^1.0.3", "@ir-engine/engine": "^1.0.3", "@ir-engine/hyperflux": "^1.0.3", "@ir-engine/projects": "^1.0.3", "@ir-engine/server-core": "^1.0.3", "@ir-engine/spatial": "^1.0.3", "@feathersjs/feathers": "5.0.5", "@feathersjs/koa": "5.0.5", "@feathersjs/primus-client": "4.5.15", "@feathersjs/transport-commons": "5.0.5", "@google-cloud/agones-sdk": "1.30.0", "cross-env": "7.0.3", "detect-port": "^1.5.1", "ffmpeg-static": "^5.1.0", "jsonwebtoken": "^9.0.0", "lodash": "^4.17.21", "mediasoup": "3.14.1", "msgpackr": "^1.9.2", "primus": "^8.0.7", "ps-list": "7.2.0", "trace-unhandled": "2.0.1", "typescript": "5.6.3", "uuid": "9.0.0"}, "devDependencies": {"@types/node": "18.15.5"}, "gitHead": "66449f6ffba4d32c424b16b4f0667fe0ad36562c"}