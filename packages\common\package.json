{"name": "@ir-engine/common", "version": "1.0.3", "publishConfig": {"access": "public"}, "main": "./index.ts", "description": "Shared types for other Infinite Reality Engine packages", "scripts": {"check-errors": "tsc --noemit && npx cycle-import-check src || true", "test": "cross-env TEST=true vitest run --config=../../vitest.client.config.ts"}, "repository": "http://github.com/ir-engine/ir-engine", "author": "Infinite Reality Engine", "license": "CPAL", "dependencies": {"@ir-engine/hyperflux": "1.0.3", "@ir-engine/ecs": "1.0.3", "@ir-engine/spatial": "1.0.3", "@ir-engine/engine": "1.0.3", "cross-fetch": "^3.1.5", "node-cache": "^5.1.2", "node-schedule": "^2.1.1"}, "devDependencies": {"@types/node": "18.15.5", "rimraf": "4.4.0"}, "gitHead": "2313453697ca7c6b8d36b3b166b5a6445fe1c851"}