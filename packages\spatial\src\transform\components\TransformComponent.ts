/*
CPAL-1.0 License

The contents of this file are subject to the Common Public Attribution License
Version 1.0. (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at
https://github.com/ir-engine/ir-engine/blob/dev/LICENSE.
The License is based on the Mozilla Public License Version 1.1, but Sections 14
and 15 have been added to cover use of software over a computer network and
provide for limited attribution for the Original Developer. In addition,
Exhibit A has been modified to be consistent with Exhibit B.

Software distributed under the License is distributed on an "AS IS" basis,
WITHOUT WARRANTY OF ANY KIND, either express or implied. See the License for the
specific language governing rights and limitations under the License.

The Original Code is Infinite Reality Engine.

The Original Developer is the Initial Developer. The Initial Developer of the
Original Code is the Infinite Reality Engine team.

All portions of the code written by the Infinite Reality Engine team are Copyright © 2021-2025
Infinite Reality Engine. All Rights Reserved.
*/

import { Matrix4, Quaternion, Vector3 } from 'three'

import { EntityTreeComponent, getAncestorWithComponents, hasComponent, S } from '@ir-engine/ecs'
import { defineComponent, getComponent, getOptionalComponent } from '@ir-engine/ecs/src/ComponentFunctions'
import { Entity } from '@ir-engine/ecs/src/Entity'
import { ComputedTransformComponent } from './ComputedTransformComponent'

import { createResizableTypeArray } from '@ir-engine/ecs/src/bitecsLegacy'
import { isZero } from '../../common/functions/MathFunctions'
import { proxifyQuaternionWithDirty, proxifyVector3WithDirty } from '../../common/proxies/createThreejsProxy'
import { SceneComponent } from '../../renderer/components/SceneComponents'
import { T } from '../../schema/schemaFunctions'

export type TransformComponentType = {
  position: Vector3
  rotation: Quaternion
  scale: Vector3
  matrix: Matrix4
  matrixWorld: Matrix4
}

const assignPosition = (entity: Entity): Vector3 =>
  proxifyVector3WithDirty(TransformComponent.position, entity, TransformComponent.dirty)

const assignRotation = (entity: Entity): Quaternion =>
  proxifyQuaternionWithDirty(TransformComponent.rotation, entity, TransformComponent.dirty)

const assignScale = (entity: Entity): Vector3 =>
  proxifyVector3WithDirty(TransformComponent.scale, entity, TransformComponent.dirty, new Vector3(1, 1, 1))

export const TransformComponent = defineComponent({
  name: 'TransformComponent',

  jsonID: 'IR_transform',

  schema: S.Object({
    position: T.Vec3(),
    rotation: T.Quaternion(),
    scale: T.Vec3(),
    matrix: T.Mat4(undefined, { serialized: false }),
    matrixWorld: T.Mat4(undefined, { serialized: false })
  }),

  storage: {
    position: {
      x: createResizableTypeArray(Float64Array),
      y: createResizableTypeArray(Float64Array),
      z: createResizableTypeArray(Float64Array)
    },
    rotation: {
      x: createResizableTypeArray(Float64Array),
      y: createResizableTypeArray(Float64Array),
      z: createResizableTypeArray(Float64Array),
      w: createResizableTypeArray(Float64Array)
    },
    scale: {
      x: createResizableTypeArray(Float64Array),
      y: createResizableTypeArray(Float64Array),
      z: createResizableTypeArray(Float64Array)
    },
    dirty: createResizableTypeArray(Uint8Array)
  },

  onInit(entity, initial) {
    initial.position = assignPosition(entity)
    initial.rotation = assignRotation(entity)
    initial.scale = assignScale(entity)
    return initial
  },

  onSet: (entity, component, json) => {
    if (!json) return
    if (json.position) component.position.value.copy(json.position)
    if (json.rotation) component.rotation.value.copy(json.rotation)
    if (json.scale && !isZero(json.scale)) component.scale.value.copy(json.scale)

    composeMatrix(entity)
    const entityTree = getOptionalComponent(entity, EntityTreeComponent)
    const parentEntity = entityTree?.parentEntity
    if (parentEntity) {
      const parentTransform = getOptionalComponent(parentEntity, TransformComponent)
      if (parentTransform)
        component.matrixWorld.value.multiplyMatrices(parentTransform.matrixWorld, component.matrix.value as Matrix4)
    } else {
      component.matrixWorld.value.copy(component.matrix.value as Matrix4)
    }
  },

  onRemove: (entity, component) => {
    TransformComponent.dirty[entity] = 0
  },

  toJSON: (component) => {
    return {
      position: component.position,
      rotation: component.rotation,
      scale: component.scale
    }
  },

  getWorldPosition: (entity: Entity, vec3: Vector3) => {
    const transform = getComponent(entity, TransformComponent)
    vec3.x = transform.matrixWorld.elements[12]
    vec3.y = transform.matrixWorld.elements[13]
    vec3.z = transform.matrixWorld.elements[14]
    return vec3
  },

  getMatrixRelativeToEntity: (entity: Entity, relativeEntity: Entity, outMatrix: Matrix4) => {
    const transform = getComponent(entity, TransformComponent)
    const relativeTransform = getComponent(relativeEntity, TransformComponent)
    return outMatrix.copy(relativeTransform.matrixWorld).invert().multiply(transform.matrixWorld)
  },

  getMatrixRelativeToScene: (entity: Entity, outMatrix: Matrix4) => {
    const relativeEntity = getAncestorWithComponents(entity, [SceneComponent])
    if (!relativeEntity) return outMatrix.copy(getComponent(entity, TransformComponent).matrixWorld)
    /** @todo We should force updating the parent chain between entity and relativeEntity (for completeness) */
    return TransformComponent.getMatrixRelativeToEntity(entity, relativeEntity, outMatrix)
  },

  // this method is essentially equivalent to Matrix4.decompose
  getWorldRotation: (entity: Entity, quaternion: Quaternion) => {
    const transform = getComponent(entity, TransformComponent)
    const te = transform.matrixWorld.elements

    let sx = _v1.set(te[0], te[1], te[2]).length()
    const sy = _v1.set(te[4], te[5], te[6]).length()
    const sz = _v1.set(te[8], te[9], te[10]).length()

    // if determine is negative, we need to invert one scale
    const det = transform.matrixWorld.determinant()
    if (det < 0) sx = -sx

    // scale the rotation part
    _m1.copy(transform.matrixWorld)

    const invSX = 1 / sx
    const invSY = 1 / sy
    const invSZ = 1 / sz

    _m1.elements[0] *= invSX
    _m1.elements[1] *= invSX
    _m1.elements[2] *= invSX

    _m1.elements[4] *= invSY
    _m1.elements[5] *= invSY
    _m1.elements[6] *= invSY

    _m1.elements[8] *= invSZ
    _m1.elements[9] *= invSZ
    _m1.elements[10] *= invSZ

    quaternion.setFromRotationMatrix(_m1)

    return quaternion
  },

  getWorldScale: (entity: Entity, vec3: Vector3) => {
    const transform = getComponent(entity, TransformComponent)
    const te = transform.matrixWorld.elements

    let sx = _v1.set(te[0], te[1], te[2]).length()
    const sy = _v1.set(te[4], te[5], te[6]).length()
    const sz = _v1.set(te[8], te[9], te[10]).length()

    // if determine is negative, we need to invert one scale
    const det = transform.matrixWorld.determinant()
    if (det < 0) sx = -sx

    vec3.x = sx
    vec3.y = sy
    vec3.z = sz

    return vec3
  },

  getScenePosition: (entity: Entity, vec3: Vector3) => {
    const sceneEntity = getAncestorWithComponents(entity, [SceneComponent])
    if (!sceneEntity) return vec3.set(0, 0, 0)

    TransformComponent.getMatrixRelativeToEntity(entity, sceneEntity, _m1)
    const te = _m1.elements

    vec3.x = te[12]
    vec3.y = te[13]
    vec3.z = te[14]

    return vec3
  },

  getSceneScale: (entity: Entity, vec3: Vector3) => {
    const sceneEntity = getAncestorWithComponents(entity, [SceneComponent])
    if (!sceneEntity) return vec3.set(1, 1, 1)

    TransformComponent.getMatrixRelativeToEntity(entity, sceneEntity, _m1)
    const te = _m1.elements

    let sx = _v1.set(te[0], te[1], te[2]).length()
    const sy = _v1.set(te[4], te[5], te[6]).length()
    const sz = _v1.set(te[8], te[9], te[10]).length()

    // if determine is negative, we need to invert one scale
    const det = _m1.determinant()
    if (det < 0) sx = -sx

    vec3.x = sx
    vec3.y = sy
    vec3.z = sz

    return vec3
  },

  /**
   * Updates the local matrix and transform from the matrixWorld property of the transform component
   * @param entity
   */
  updateFromWorldMatrix: (entity: Entity) => {
    const transform = getComponent(entity, TransformComponent)
    const parentEntity = getComponent(entity, EntityTreeComponent)?.parentEntity
    if (parentEntity) {
      const parentTransform = getComponent(parentEntity, TransformComponent)
      mat4.copy(parentTransform.matrixWorld).invert()
      transform.matrix.multiplyMatrices(mat4, transform.matrixWorld)
    } else {
      transform.matrix.copy(transform.matrixWorld)
    }
    decomposeMatrix(entity)
    TransformComponent.dirty[entity] = 1
  },

  /**
   * Updates the position aspect of the matrixWorld property of the transform component
   * @param entity
   * @param position
   */
  setWorldPosition: (entity: Entity, position: Vector3) => {
    const transform = getComponent(entity, TransformComponent)
    transform.matrixWorld.elements[12] = position.x
    transform.matrixWorld.elements[13] = position.y
    transform.matrixWorld.elements[14] = position.z
  },

  /**
   * Updates the rotation aspect of the matrixWorld property of the transform component
   * @param entity
   * @param quaternion
   */
  setWorldRotation: (entity: Entity, quaternion: Quaternion) => {
    const transform = getComponent(entity, TransformComponent)
    transform.matrixWorld.decompose(vec3, quat, vec3_2)
    transform.matrixWorld.compose(vec3, quaternion, vec3_2)
  },

  /**
   * Updates the scale aspect of the matrixWorld property of the transform component
   * @param entity
   * @param scale
   */
  setWorldScale: (entity: Entity, scale: Vector3) => {
    const transform = getComponent(entity, TransformComponent)
    transform.matrixWorld.decompose(vec3, quat, vec3_2)
    transform.matrixWorld.compose(vec3, quat, scale)
  },

  /**Transforms forward vector*/
  forward: (entity: Entity, outVector: Vector3) => {
    const matrixElements = getComponent(entity, TransformComponent).matrixWorld.elements
    outVector.set(matrixElements[8], matrixElements[9], matrixElements[10]).normalize()
    return outVector
  },

  /**Transforms back vector*/
  back: (entity: Entity, outVector: Vector3) => {
    const matrixElements = getComponent(entity, TransformComponent).matrixWorld.elements
    outVector.set(matrixElements[8], matrixElements[9], matrixElements[10]).normalize().negate()
    return outVector
  },

  /**Transforms up vector*/
  up: (entity: Entity, outVector: Vector3) => {
    const matrixElements = getComponent(entity, TransformComponent).matrixWorld.elements
    outVector.set(matrixElements[4], matrixElements[5], matrixElements[6]).normalize()
    return outVector
  },

  /**Transforms down vector*/
  down: (entity: Entity, outVector: Vector3) => {
    const matrixElements = getComponent(entity, TransformComponent).matrixWorld.elements
    outVector.set(matrixElements[4], matrixElements[5], matrixElements[6]).normalize().negate()
    return outVector
  },

  /**Transforms right vector*/
  right: (entity: Entity, outVector: Vector3) => {
    const matrixElements = getComponent(entity, TransformComponent).matrixWorld.elements
    outVector.set(matrixElements[0], matrixElements[1], matrixElements[2]).normalize()
    return outVector
  },

  /**Transforms left vector*/
  left: (entity: Entity, outVector: Vector3) => {
    const matrixElements = getComponent(entity, TransformComponent).matrixWorld.elements
    outVector.set(matrixElements[0], matrixElements[1], matrixElements[2]).normalize().negate()
    return outVector
  },

  transformsNeedSorting: false,

  /**
   * Computes the transform matrix for an entity
   * @param entity
   */
  computeTransformMatrix: (entity: Entity) => {
    const transform = getComponent(entity, TransformComponent)
    getOptionalComponent(entity, ComputedTransformComponent)?.computeFunction()
    composeMatrix(entity)

    const entityTree = getOptionalComponent(entity, EntityTreeComponent)
    const parentEntity = entityTree?.parentEntity
    if (parentEntity) {
      const parentTransform = getOptionalComponent(parentEntity, TransformComponent)
      if (parentTransform) transform.matrixWorld.multiplyMatrices(parentTransform.matrixWorld, transform.matrix)
    } else {
      transform.matrixWorld.copy(transform.matrix)
    }
  },

  /**
   * Computes the transform matrix for an entity and all its children
   * @param entity
   */
  computeTransformMatrixWithChildren: (entity: Entity) => {
    hasComponent(entity, TransformComponent) && TransformComponent.computeTransformMatrix(entity)
    for (const child of getOptionalComponent(entity, EntityTreeComponent)?.children ?? []) {
      TransformComponent.computeTransformMatrixWithChildren(child)
    }
  },

  getDistanceSquaredFromTarget: (entity: Entity, targetPosition: Vector3) => {
    return TransformComponent.getWorldPosition(entity, _tempDistSqrVec3).distanceToSquared(targetPosition)
  },

  getLocalPositionRelativeToEntity: (worldPosition: Vector3, relativeEntity: Entity, outVector: Vector3) => {
    const relativeTransform = getComponent(relativeEntity, TransformComponent)
    const inverseMatrix = new Matrix4().copy(relativeTransform.matrixWorld).invert()
    return outVector.copy(worldPosition).applyMatrix4(inverseMatrix)
  }
})

const _tempDistSqrVec3 = new Vector3()

const vec3 = new Vector3()
const vec3_2 = new Vector3()
const quat = new Quaternion()
const mat4 = new Matrix4()

const _v1 = new Vector3()
const _m1 = new Matrix4()

export const composeMatrix = (entity: Entity) => {
  const te = getComponent(entity, TransformComponent).matrix.elements

  const x = TransformComponent.rotation.x[entity]
  const y = TransformComponent.rotation.y[entity]
  const z = TransformComponent.rotation.z[entity]
  const w = TransformComponent.rotation.w[entity]

  const x2 = x + x,
    y2 = y + y,
    z2 = z + z
  const xx = x * x2,
    xy = x * y2,
    xz = x * z2
  const yy = y * y2,
    yz = y * z2,
    zz = z * z2
  const wx = w * x2,
    wy = w * y2,
    wz = w * z2

  const sx = TransformComponent.scale.x[entity]
  const sy = TransformComponent.scale.y[entity]
  const sz = TransformComponent.scale.z[entity]

  te[0] = (1 - (yy + zz)) * sx
  te[1] = (xy + wz) * sx
  te[2] = (xz - wy) * sx
  te[3] = 0

  te[4] = (xy - wz) * sy
  te[5] = (1 - (xx + zz)) * sy
  te[6] = (yz + wx) * sy
  te[7] = 0

  te[8] = (xz + wy) * sz
  te[9] = (yz - wx) * sz
  te[10] = (1 - (xx + yy)) * sz
  te[11] = 0

  te[12] = TransformComponent.position.x[entity]
  te[13] = TransformComponent.position.y[entity]
  te[14] = TransformComponent.position.z[entity]
  te[15] = 1
}

export const decomposeMatrix = (entity: Entity) => {
  const matrix = getComponent(entity, TransformComponent).matrix
  const te = matrix.elements

  let sx = _v1.set(te[0], te[1], te[2]).length()
  const sy = _v1.set(te[4], te[5], te[6]).length()
  const sz = _v1.set(te[8], te[9], te[10]).length()

  // if determine is negative, we need to invert one scale
  const det = matrix.determinant()
  if (det < 0) sx = -sx

  TransformComponent.position.x[entity] = te[12]
  TransformComponent.position.y[entity] = te[13]
  TransformComponent.position.z[entity] = te[14]

  // scale the rotation part
  _m1.copy(matrix)

  const invSX = 1 / sx
  const invSY = 1 / sy
  const invSZ = 1 / sz

  _m1.elements[0] *= invSX
  _m1.elements[1] *= invSX
  _m1.elements[2] *= invSX

  _m1.elements[4] *= invSY
  _m1.elements[5] *= invSY
  _m1.elements[6] *= invSY

  _m1.elements[8] *= invSZ
  _m1.elements[9] *= invSZ
  _m1.elements[10] *= invSZ

  setFromRotationMatrix(entity, _m1)

  TransformComponent.scale.x[entity] = sx
  TransformComponent.scale.y[entity] = sy
  TransformComponent.scale.z[entity] = sz
}

export const setFromRotationMatrix = (entity: Entity, m: Matrix4) => {
  // http://www.euclideanspace.com/maths/geometry/rotations/conversions/matrixToQuaternion/index.htm

  // assumes the upper 3x3 of m is a pure rotation matrix (i.e, unscaled)

  const te = m.elements,
    m11 = te[0],
    m12 = te[4],
    m13 = te[8],
    m21 = te[1],
    m22 = te[5],
    m23 = te[9],
    m31 = te[2],
    m32 = te[6],
    m33 = te[10],
    trace = m11 + m22 + m33

  if (trace > 0) {
    const s = 0.5 / Math.sqrt(trace + 1.0)

    TransformComponent.rotation.w[entity] = 0.25 / s
    TransformComponent.rotation.x[entity] = (m32 - m23) * s
    TransformComponent.rotation.y[entity] = (m13 - m31) * s
    TransformComponent.rotation.z[entity] = (m21 - m12) * s
  } else if (m11 > m22 && m11 > m33) {
    const s = 2.0 * Math.sqrt(1.0 + m11 - m22 - m33)

    TransformComponent.rotation.w[entity] = (m32 - m23) / s
    TransformComponent.rotation.x[entity] = 0.25 * s
    TransformComponent.rotation.y[entity] = (m12 + m21) / s
    TransformComponent.rotation.z[entity] = (m13 + m31) / s
  } else if (m22 > m33) {
    const s = 2.0 * Math.sqrt(1.0 + m22 - m11 - m33)

    TransformComponent.rotation.w[entity] = (m13 - m31) / s
    TransformComponent.rotation.x[entity] = (m12 + m21) / s
    TransformComponent.rotation.y[entity] = 0.25 * s
    TransformComponent.rotation.z[entity] = (m23 + m32) / s
  } else {
    const s = 2.0 * Math.sqrt(1.0 + m33 - m11 - m22)

    TransformComponent.rotation.w[entity] = (m21 - m12) / s
    TransformComponent.rotation.x[entity] = (m13 + m31) / s
    TransformComponent.rotation.y[entity] = (m23 + m32) / s
    TransformComponent.rotation.z[entity] = 0.25 * s
  }
}

export const TransformGizmoTagComponent = defineComponent({ name: 'TransformGizmoTagComponent' })

/**
 * @description
 * Sets the `@param entity` as dirty and recursively sets all children entities as dirty.
 *
 * @param entity Entity Node where traversal will start
 */
export function setChildrenDirtyFast(entity: Entity) {
  TransformComponent.dirty[entity] = 1
  const children = getComponent(entity, EntityTreeComponent).children
  for (const child of children) {
    setChildrenDirtyFast(child)
  }
}
