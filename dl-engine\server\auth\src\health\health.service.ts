/**
 * DL-Engine 认证服务健康检查服务
 */

import { Injectable } from '@nestjs/common'
import { InjectConnection } from '@nestjs/typeorm'
import { Connection } from 'typeorm'
import { RedisService } from '../redis/redis.service'
import { ConfigService } from '../config/config.service'

@Injectable()
export class HealthService {
  constructor(
    @InjectConnection()
    private readonly connection: Connection,
    private readonly redisService: RedisService,
    private readonly configService: ConfigService
  ) {}

  async getHealth() {
    const [dbHealth, redisHealth] = await Promise.all([
      this.checkDatabaseHealth(),
      this.checkRedisHealth()
    ])

    const isHealthy = dbHealth && redisHealth

    return {
      status: isHealthy ? 'healthy' : 'unhealthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      version: '1.0.0',
      services: {
        database: dbHealth ? 'healthy' : 'unhealthy',
        redis: redisHealth ? 'healthy' : 'unhealthy'
      }
    }
  }

  async getDetailedHealth() {
    const basicHealth = await this.getHealth()
    
    return {
      ...basicHealth,
      system: {
        memory: process.memoryUsage(),
        cpu: process.cpuUsage(),
        platform: process.platform,
        nodeVersion: process.version
      },
      configuration: {
        environment: this.configService.getEnvironment(),
        smsProvider: this.configService.getSmsProvider()
      }
    }
  }

  private async checkDatabaseHealth(): Promise<boolean> {
    try {
      await this.connection.query('SELECT 1')
      return true
    } catch (error) {
      return false
    }
  }

  private async checkRedisHealth(): Promise<boolean> {
    try {
      await this.redisService.set('health:check', 'ok')
      const result = await this.redisService.get('health:check')
      await this.redisService.del('health:check')
      return result === 'ok'
    } catch (error) {
      return false
    }
  }
}
