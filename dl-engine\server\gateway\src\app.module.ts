/**
 * DL-Engine API Gateway 应用模块
 * 
 * 集成所有核心模块：
 * - 路由管理模块
 * - 中间件模块
 * - 限流模块
 * - 负载均衡模块
 * - 监控模块
 * - 配置模块
 */

import { Module } from '@nestjs/common'
import { ThrottlerModule } from '@nestjs/throttler'
import { ConfigModule } from './config/config.module'
import { LoggerModule } from './common/logger.module'
import { HealthModule } from './health/health.module'
import { RoutingModule } from './routing/routing.module'
import { MiddlewareModule } from './middleware/middleware.module'
import { LoadBalancerModule } from './load-balancer/load-balancer.module'
import { MonitoringModule } from './monitoring/monitoring.module'
import { SecurityModule } from './security/security.module'
import { ProxyModule } from './proxy/proxy.module'

@Module({
  imports: [
    // 配置模块 - 必须首先加载
    ConfigModule.forRoot(),

    // 日志模块
    LoggerModule,

    // 限流模块
    ThrottlerModule.forRoot([
      {
        name: 'short',
        ttl: 1000,  // 1秒
        limit: 10,  // 每秒最多10个请求
      },
      {
        name: 'medium',
        ttl: 10000, // 10秒
        limit: 50,  // 每10秒最多50个请求
      },
      {
        name: 'long',
        ttl: 60000, // 1分钟
        limit: 200, // 每分钟最多200个请求
      }
    ]),

    // 健康检查模块
    HealthModule,

    // 路由管理模块
    RoutingModule,

    // 中间件模块
    MiddlewareModule,

    // 负载均衡模块
    LoadBalancerModule,

    // 监控模块
    MonitoringModule,

    // 安全模块
    SecurityModule,

    // 代理模块
    ProxyModule
  ],
  controllers: [],
  providers: []
})
export class AppModule {}
