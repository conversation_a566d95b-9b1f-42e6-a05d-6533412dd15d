{"compilerOptions": {"outDir": "dist", "target": "ESNext", "useDefineForClassFields": true, "module": "es2020", "lib": ["ESNext", "DOM", "DOM.Iterable"], "skipLibCheck": true, "moduleResolution": "node", "moduleDetection": "force", "allowJs": true, "forceConsistentCasingInFileNames": true, "strict": false, "strictNullChecks": true, "strictBindCallApply": true, "noImplicitAny": false, "resolveJsonModule": true, "esModuleInterop": true, "sourceMap": true, "jsx": "react", "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "emitDecoratorMetadata": true, "declaration": true, "types": ["@types/node"]}, "exclude": ["./src/*.test.ts", "./src/*.test.tsx"], "include": ["../../__global.d.ts", "./src", "./index.ts"]}