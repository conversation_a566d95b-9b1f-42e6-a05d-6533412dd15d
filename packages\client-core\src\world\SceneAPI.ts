/*
CPAL-1.0 License

The contents of this file are subject to the Common Public Attribution License
Version 1.0. (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at
https://github.com/ir-engine/ir-engine/blob/dev/LICENSE.
The License is based on the Mozilla Public License Version 1.1, but Sections 14
and 15 have been added to cover use of software over a computer network and 
provide for limited attribution for the Original Developer. In addition, 
Exhibit A has been modified to be consistent with Exhibit B.

Software distributed under the License is distributed on an "AS IS" basis,
WITHOUT WARRANTY OF ANY KIND, either express or implied. See the License for the
specific language governing rights and limitations under the License.

The Original Code is Infinite Reality Engine.

The Original Developer is the Initial Developer. The Initial Developer of the
Original Code is the Infinite Reality Engine team.

All portions of the code written by the Infinite Reality Engine team are Copyright © 2021-2025
Infinite Reality Engine. All Rights Reserved.
*/

import { Params } from '@feathersjs/feathers'
import { API } from '@ir-engine/common'
import config from '@ir-engine/common/src/config'
import multiLogger from '@ir-engine/common/src/logger'
import { StaticResourceType, fileBrowserPath } from '@ir-engine/common/src/schema.type.module'

const logger = multiLogger.child({ component: 'client-core:SceneAPI' })

/**
 * deleteScene used to delete project using projectId.
 *
 * @param  {string}  sceneId
 * @return {Promise}
 */
export const deleteScene = async (sceneKey: string): Promise<any> => {
  try {
    await API.instance.service(fileBrowserPath).remove(sceneKey)
  } catch (error) {
    logger.error(error, 'Error in deleting project')
    throw error
  }
  return true
}

export const cloneScene = async (
  resource: StaticResourceType,
  newKey: string,
  oldProject: string,
  newProject: string,
  params?: Params
) => {
  const oldKeySplit = resource.key.split('/')
  const newKeySplit = newKey.split('/')
  const oldName = oldKeySplit.splice(oldKeySplit.length - 1)[0]
  const newName = newKeySplit.splice(newKeySplit.length - 1)[0]
  const oldPath = oldKeySplit.join('/')
  const newPath = newKeySplit.join('/')

  try {
    return await API.instance.service(fileBrowserPath).update(
      null,
      {
        oldProject,
        newProject,
        oldPath,
        newPath,
        oldName,
        newName,
        isCopy: true
      },
      params
    )
  } catch (error) {
    logger.error(error, 'Error in cloning project')
    throw error
  }
}

export const renameScene = async (
  resource: StaticResourceType,
  newKey: string,
  projectName: string,
  params?: Params
) => {
  const oldKeySplit = resource.key.split('/')
  const newKeySplit = newKey.split('/')
  const oldName = oldKeySplit.splice(oldKeySplit.length - 1)[0]
  const newName = newKeySplit.splice(newKeySplit.length - 1)[0]
  const oldPath = oldKeySplit.join('/')
  const newPath = newKeySplit.join('/')
  try {
    return await API.instance
      .service(fileBrowserPath)
      .update(null, { oldProject: projectName, newProject: projectName, oldPath, newPath, oldName, newName }, params)
  } catch (error) {
    logger.error(error, 'Error in renaming project')
    throw error
  }
}

export const createScene = async (
  projectName: string,
  templateURL = config.client.fileServer + '/projects/ir-engine/default-project/public/scenes/default.gltf'
) => {
  const sceneData = await API.instance.service(fileBrowserPath).patch(null, {
    project: projectName,
    type: 'scene',
    body: templateURL,
    path: 'public/scenes/New-Scene.gltf',
    unique: true
  })
  return sceneData
}
