{"name": "@ir-engine/matchmaking", "version": "1.0.3", "author": {"name": "Infinite Reality Engine Collective", "email": "<EMAIL>"}, "publishConfig": {"access": "public"}, "description": "", "homepage": "", "license": "CPAL", "main": "lib/engine.umd.js", "module": "lib/engine.es.js", "npmClient": "npm", "repository": {"type": "git", "url": "git+https://github.com/ir-engine/ir-engine.git"}, "scripts": {"check-errors": "tsc --noemit && npx cycle-import-check src || true", "cross-env": "7.0.3", "test": "exit 0", "validate": "npm run test", "local-minikube-start": "minikube start", "local-minikube-stop": "minikube stop", "open-frontend-service-port": "kubectl port-forward --namespace open-match service/open-match-frontend 51504:51504"}, "dependencies": {"abort-controller": "^3.0.0", "axios": "1.3.4", "node-fetch": "2.6.9", "typescript": "5.6.3"}, "devDependencies": {"nock": "13.3.0"}}