# ARCHIVED

This document is archived. Please refer to release tags for more info https://github.com/ir-engine/ir-engine/tags

## Author Archive 

Updated to Q1 2022

| Core Contributor | Contact | GitHub ID |
| --- | ----------- | ----- |
| <PERSON> | <<EMAIL>>  | [@HexaField](https://github.com/HexaField) |
| <PERSON> | <<EMAIL>>  | [@barankyle](https://github.com/barankyle) |
| Gheric Speiginer | <<EMAIL>>  | [@speigg](https://github.com/speigg) |
| <PERSON> | <<EMAIL>>  | [@mrhegemon](https://github.com/mrhegemon) |
| <PERSON><PERSON><PERSON> | <<EMAIL>>  | [@dmitrizagidulin](https://github.com/dmitrizagidulin) |
| <PERSON> | <<EMAIL>>  | [@dinomut1](https://github.com/dinomut1) |
| <PERSON><PERSON><PERSON> |  | [@hanzlamateen](https://github.com/hanzlamateen) |



| Contributor | Contact | GitHub ID |
| ----------- | ------- | --------- |
| Abhinav Avkumar | <<EMAIL>> | |
| Abhishek Pathak | <<EMAIL>> | [@abhishek-pathak-here](https://github.com/abhishek-pathak-here) |
| Adrew Blashchuk | <<EMAIL>> | |
| Aleks Mazur | <<EMAIL>> | [@aleksmaz](https://github.com/aleksmaz) |
| Alexander Shvets | <<EMAIL>> | |
| Andrey Pashentsev | <<EMAIL>> | |
| Andrii Blashchuk | <<EMAIL>>  | |
| Ayushi | <<EMAIL>>  | |
| Bohdan | <<EMAIL>>  | |
| Fabrice Irankunda | <<EMAIL>>  | [@FabriceIRANKUNDA](https://github.com/FabriceIRANKUNDA)  |
| Fouad Mohammad Hassan | <<EMAIL>>  | |
| Gleb Ordinsky | <<EMAIL>>  | [@glebordinski](https://github.com/glebordinski) |
| Hamza Mushtaq | <<EMAIL>>  | [@hamzamm](https://github.com/hamzamm)  |
| Hanzla Mateen | <<EMAIL>>  | [@hanzlamateen](https://github.com/hanzlamateen) |
| Kevin Kimenyi | <<EMAIL>>  | [@kimenyikevin](https://github.com/kimenyikevin) |
| Kinjal Raval | <<EMAIL>>  | [@kinjalravel](https://github.com/kinjalravel) |
| Mohsen Heydari | <<EMAIL>>  | [@mohsenheydari](https://github.com/mohsenheydari)  |
| Mustafa Saber | <<EMAIL>>  | [@Mustafa1845](https://github.com/Mustafa1845) |
| Nayankumar Patel | <<EMAIL>>  | [@NPatel10](https://github.com/NPatel10) |
| Patrick Canfield | <<EMAIL>>  | [@pscale](https://github.com/pscale) |
| Ron Oyama | | [@rondoor124](https://github.com/rondoor124) |
| Saad Ahmed Siddiqui | <<EMAIL>>  | |
| Sergiy Vovk | <<EMAIL>>  | |
| Shaw Walters | <<EMAIL>>  | [@lalalune](https://github.com/lalalune) |
| Slava Solovjov | <<EMAIL>>  | |
| Tanya Vykliuk | <<EMAIL>>  | [@tanyavykliuk](https://github.com/tanyavykliuk) |
| Thomas Mohr | <<EMAIL>>  | [@mohrtw](https://github.com/mohrtw) |
| Vyacheslav Solovjov | <<EMAIL>>  | [@XiaNi](https://github.com/XiaNi) |
| Yaroslav Koval | <<EMAIL>>  | [@HydraFire](https://github.com/HydraFire) |
| Zulqarnain Hanif  |  | [@zulqarnainhanif](https://github.com/zulqarnainhanif) |




# Release History

#### [HISTORY RELOCATED HERE](https://github.com/XRFoundation/XREngine/releases)

- https://github.com/XRFoundation/XREngine/releases/tag/v0.5.5
- https://github.com/XRFoundation/XREngine/releases/tag/v0.5.4
- https://github.com/XRFoundation/XREngine/releases/tag/v0.5.3
- https://github.com/XRFoundation/XREngine/releases/tag/v0.5.2
- https://github.com/XRFoundation/XREngine/releases/tag/v0.5.1
- https://github.com/XRFoundation/XREngine/releases/tag/v0.5.0
- https://github.com/XRFoundation/XREngine/releases/tag/v0.4.13

# Archived Release History

For various reasons we have decided to archive and reset XREngine Git History.
- Considerations as we combine Git histories from smaller packages as XREngine has moved to a larger and larger monorepo of many packages.
- Intellectual property issues from old Assets and Code from previous contributions needed to be removed.
- Massive file size of git history (5.6+ GB) made 'deep clone' impossible and was generally bogging down development.
- Massive history duplications and rootless forks needed to be cleaned up that were making it impossible to trace code authorship. 


# 2022

June 9 2022 - Renamed to Ethereal Engine - etherealengine.org

**v0.5.0**

Feb, 1st, 2022



* Matchmaking									Kyle & Vyachelsav
* Editor uses the Engine								Nayan
* Editor scene loading is isomorphic with runtime					Nayan
* Equippables & Grabbables								Hamza
* Invite to location with nearby spawning						Hamza
* Comprehensive Typescript Error Checking						Josh
* Configurable Server App Settings via Admin Panel	 				Kyle & Zulquarnain
* Networked Cached Actions 							Gheric
* Simplify Incoming & Outgoing Action Networking					Gheric
* Safe ECS exit queries & entity removal/recycling tags					Gheric
* 3x Optimized Logic & Scene Render						Nate, Josh & Gheric
* 5x Optimized Netcode via ECS Serialization						Nate
* CORS Proxy									Ron & Kyle
* Bone Matching for better avatar support 						Ron
* Overhauled Animation & Camera System (towards true VR IK)				Mohsen
* First Person Head Decapitation							Mohsen
* Locomotion foot sync								Mohsen	
* User Inventory & Trade via BIAB Integration						Swarup’s Team, Kinjal, Zain, Josh
* Project API MVP 									Josh
* Dynamic System Injection								Josh, Gheric
* XRUI MVP									Gheric
* XRUI Loading Screen								Gheric, Josh, Zain, Dhara
* Testing Bot & Benchmark Deployment QA CI/CD Integration MVP			Hanzla
* Github Repository Auth for Private Project Install					Ron
* Engine State Refactor								Abhishek
* Finalised Performance Auto Tuning							Josh, Gheric
* Discord Oauth									Kyle
* Refactor nextjs public runtime config to simple process.env				Kyle, Josh
* Separate Docker Images for API, Instance, Analytics servers and Client			Kyle
* More granular deployment configuration						Kyle
* Use google STUN servers								Kyle
* Client UI QA & Refactor								Zulquarnain
* Admin UI QA & Refactor								Zulquarnain & Kimenyi
* Harmony UI QA & Refactor							Kimenyi
* Editor UI QA & Refactor								Nayan & Fabrice


# 2021

**v0.4.13 **

 Nov 23, 2021 55a600b



* Added file browser to editor							Abhishek
* Animation system refactor								Abhishek
* Portals MVP									Josh
* Move from Redux to Hookstate							Kinjal, Josh
* IK Refactor									Moshen
* UI Revamp									Zulquarnain

v0.4.12 

 Nov 23, 2021 4afc6ca

v0.4.11 

 Nov 23, 2021 e74cb09

v0.4.10 

 Oct 17, 2021 d2b659c

v0.4.9 

 Oct 13, 2021 67ca97d

v0.4.8 

 Sep 27, 2021 8cb718e

v0.4.7 

 Sep 24, 2021 932214f

v0.4.7-beta1 

 Sep 24, 2021 fdfc16c

v0.4.6 

 Sep 17, 2021 64d5033

v0.4.5 

 Sep 16, 2021 68482fb

v0.4.5-beta4 

 Sep 16, 2021 71e2796

v0.4.5-beta3 

 Sep 16, 2021 7782877

v0.4.5-beta2 

 Sep 16, 2021 eaf8b5b

v0.4.5-beta1 

 Sep 16, 2021 7782877

v0.4.4 

 Sep 16, 2021 e803ea8

v0.4.3 

 Sep 16, 2021 86543f5

v0.4.2 

 Sep 16, 2021 67f2d96

v0.4.1 

 Sep 15, 2021 bfc9cfb

**v0.4.0 **

 Aug 26, 2021 a984cec



* Content Packs MVP								Kyle, Josh
* Separate Media and data streams for WebRTC					Kyle
* WebXR hands support								Nayan
* Cascade Shadow Map MVP							Nayan
* Added typedocs									Abhinav
* Added Docker, NPM build chain							Kyle
* ECS MVP									Josh, Nate
* ECS Networking MVP								Nate
* VolCap MVP									Vyacheslav, Ron
* Cloud component nodes								Mohsen
* Admin system refactor								Kimenyi
* Map component nodes								Patrick
* Added environment map support 							Abhishek
* Editor Rework									Zulquarnain

v0.3.37 

 Aug 24, 2021 b6937a9

v0.3.36 

 Aug 24, 2021 ee670b8

v0.3.35 

 Aug 24, 2021 7f41eaa

v0.3.35-pre.3 

 Aug 24, 2021 c627324

v0.3.35-pre.2 

 Aug 24, 2021 c725370

v0.3.35-pre.1 

 Aug 24, 2021 dd38334

v0.0.3.35-pre1 

 Aug 24, 2021 dd38334

v0.3.34 

 Aug 20, 2021 b1a6f76

v0.3.33 

 Aug 18, 2021 97057aa

v0.3.33-test4 

 Aug 18, 2021 3c1189b

v0.3.33-test2 

 Aug 18, 2021 cdd09b4

v0.3.33-test 

 Aug 18, 2021 438bb47

v0.3.33-test3 

 Aug 18, 2021 fdaa8fd

v0.3.32 

 Aug 9, 2021 e4f393f

v0.3.31 

 Jul 7, 2021 62815cd

v0.3.30 

 Jul 7, 2021 c25b280

v0.3.29 

 Jul 2, 2021 e9d42f9

v0.3.28 

 Jul 2, 2021 3fb33a7

v0.3.27 

 Jul 2, 2021 6792302

v0.3.26 

 Jul 2, 2021 74d9798

v0.3.25 

 Jun 29, 2021 de38c74

v0.3.25-beta8 

 Jun 29, 2021 241129e

v0.3.25-beta7 

 Jun 29, 2021 f25e221

v0.3.25-beta6 

 Jun 29, 2021 a043997

v0.3.25-beta5 

 Jun 29, 2021 34ab782

v0.3.25-beta4 

 Jun 29, 2021 8881ff2

v0.3.25-beta3 

 Jun 29, 2021 ee99b9a

v0.3.25-beta2 

 Jun 29, 2021 ef15aae

v0.3.25-beta1 

 Jun 29, 2021 954fc40

v0.3.24 

 Jun 28, 2021 6a3c4d7

v0.3.23 

 Jun 28, 2021 09db12b

v0.3.22 

 Jun 28, 2021 91b1198

v0.2.35 

 Jun 29, 2021 1541d5a

v03.14 

 Jun 28, 2021 92bebfa

v0.3.21 

 Jun 28, 2021 62a4275

v0.3.20 

 Jun 28, 2021 2d74758

v0.3.19 

 Jun 28, 2021 b519a0e

v0.3.18 

 Jun 28, 2021 0721bb9

v0.3.17 

 Jun 28, 2021 619ee07

v0.3.16 

 Jun 28, 2021 25b9441

v0.3.15 

 Jun 28, 2021 92bebfa

v0.3.14 

 Jun 28, 2021 92bebfa

v0.3.13 

 Jun 28, 2021 1714da8

v0.3.12 

 Jun 28, 2021 b28f7d8

v0.3.11 

 Jun 28, 2021 8f55a5c

v0.3.10 

 Jun 28, 2021 5baba0c

v0.3.9 

 Jun 28, 2021 5c7ed03

v0.3.8 

 Jun 28, 2021 3b89bf9

v0.3.7 

 Jun 28, 2021 ac6556c

v0.3.6 

 Jun 28, 2021 78f1f7f

v0.3.5 

 Jun 28, 2021 7f2571b

v0.3.4 

 Jun 28, 2021 7f2571b

v0.3.3 

 Jun 28, 2021 6122273

v0.3.2 

 Jun 28, 2021 02195d9

v0.3.1 

 Jun 28, 2021 c18c4fd

**v0.3.0 **

 Jun 28, 2021 fdd2845



* Audio refactor										Josh
* Added recorded event support								Josh
* First, 3rd, Overhead camera								Josh
* Native App support for iOS / Android via Capacitor						Gheric
* Move client to Vite									Gheric, Josh
* Animation refactor									Nayan
* Emote System										Nayan, Shaw
* Abstracted file driver system 								Kyle
* Added Bot API										Kyle
* Bot testing framework via puppeteer							Kyle
* Dynamic CI / CD system									Kyle
* Added WebXR to native Capacitor Tooling							Gheric
* Navigation mesh support									Patrick, Vyacheslav
* Navigation mesh walk system								Patrick, Vyacheslav
* Video component node									Shaw
* UI rework										Shaw
* Added types to all TS									Abhishek
* XR Wallet MVP										Dmitri, Liam

v0.2.47 

 Jun 28, 2021 241dc3a

v0.2.46 

 Jun 28, 2021 1e294fb

v0.2.45 

 Jun 28, 2021 501bc3c

v0.2.44 

 Jun 28, 2021 489e058

v0.2.43 

 Jun 28, 2021 73971a1

v0.2.42 

 Jun 28, 2021 e477b93

v0.2.41 

 Jun 28, 2021 56022f5

v0.2.40 

 Jun 28, 2021 f33edc3

v0.2.39 

 Jun 28, 2021 de400d4

v0.2.38 

 Jun 28, 2021 308a32b

v0.2.37 

 Jun 28, 2021 2126d65

v0.2.29

 Jun 24, 2021 650fbff

v0.2.28

 Jun 22, 2021 da52daf

v0.2.27

 May 13, 2021 a859091

v0.2.26

 May 12, 2021 200f7b1

v0.2.25

 May 12, 2021 6f6e373

v0.2.24

 May 12, 2021 4d24448

v0.2.23

 May 12, 2021 f77b6de

v0.2.22

 May 5, 2021 1e5003a

v0.2.21

 May 4, 2021 c99299b

v0.2.20

 May 4, 2021 52fe626

v0.2.18

 Apr 22, 2021 b5051f1

v0.2.17

 Apr 17, 2021 8d18e01

v0.2.16

 Apr 17, 2021 5ff55be

v0.2.15

 Apr 17, 2021 f53c70d

v0.2.14

 Apr 8, 2021 afdcb19

v0.2.10

 Mar 31, 2021 66449f6

v0.2.9

 Mar 31, 2021 0ceca74

v0.2.8

 Mar 31, 2021 4ac4004

v0.2.6

 Mar 31, 2021 794e4e3

v0.2.5

 Mar 31, 2021 6d580e5

**v0.2.0 **

 Mar 16, 2021 76b6728



* Character selector									Shaw
* Navigation mesh walk system								Josh, Vyacheslav

v0.0.2 

 Mar 16, 2021 76b6728

rc-0.2.2 

 Mar 16, 2021 76b6728

rc-0.2.1 

 Mar 16, 2021 76b6728

v0.1.7 

 Mar 12, 2021 e373597

**v0.1.0 **

 Mar 12, 2021 e373597


## Move to Open Code Collective - XR Foundation

rc-c0.1.7 

 Mar 12, 2021 e373597

rc-0.1.7 

 Mar 12, 2021 e373597

rc-0.1.6 

 Mar 12, 2021 e373597

rc-0.1.5 

 Mar 12, 2021 e373597

rc-0.2.0 

 Feb 1, 2021 927fe4d


# 2020

**rc-0.1.4 **

 May 27, 2020 c609950



* CMS MVP									Kyle
* Admin panel MVP								Kyle
* Added Hand, Face, Body Webcam IK tracking					Shaw
* Onboard UI tutorial								Tanya
* Move from Vue to React								Shaw, Thomas
* Move from Aframe to Three.js							Thomas, Josh, Shaw
* Removed WebRTC p2p								Thomas
* Login System: SMS, eMail, Social							Kyle, Dmitri
* System separated into smaller services						Kyle, Liam, Dmitri
* Added smart Database Seeding							Kyle
* Friends, Groups, Parties								Kyle, Shaw

rc-0.1.2 

 May 26, 2020 c3459e1

rc-0.1.1 

 May 24, 2020 a8787b3

rc-0.1.0 

 May 24, 2020 282155a

rc-0.0.14 

 May 19, 2020 463c4d2

rc-0.0.13.2 

 May 12, 2020 f7d875c

rc-0.0.13.1 

 May 12, 2020 85f7f94

rc-0.0.13 

 May 12, 2020 d30f9b3

rc-0.0.12 

 Apr 27, 2020 7a728b7

rc-0.0.11 

 Apr 27, 2020 0b415e6

rc-0.0.10 

 Apr 27, 2020 d5183f8

rc-0.0.9 

 Apr 21, 2020 739d7e2

rc-0.0.8 

 Apr 21, 2020 ab37f13

rc-0.0.6 

 Apr 16, 2020 28543cf

rc-0.0.4 

 Apr 15, 2020 2b0278f

rc-0.0.3 

 Apr 15, 2020 796ae88

v0.0.1 

 Apr 14, 2020 9970825

rc-0.0.2 

 Apr 13, 2020 5c0ce4b

rc-0.0.1 

 Apr 13, 2020 3d1c1b6
