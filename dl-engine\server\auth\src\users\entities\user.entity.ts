/**
 * DL-Engine 用户实体
 */

import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, Index } from 'typeorm'

@Entity('users')
@Index(['phone'])
@Index(['email'])
@Index(['status'])
export class User {
  @PrimaryGeneratedColumn('uuid')
  id: string

  @Column({ type: 'varchar', length: 20, unique: true, nullable: true, comment: '手机号码（包含国家代码）' })
  phone: string

  @Column({ type: 'varchar', length: 100, unique: true, nullable: true, comment: '邮箱地址' })
  email: string

  @Column({ type: 'varchar', length: 50, comment: '用户昵称' })
  nickname: string

  @Column({ type: 'varchar', length: 255, nullable: true, comment: '密码哈希' })
  password: string

  @Column({ type: 'varchar', length: 500, nullable: true, comment: '头像URL' })
  avatar: string

  @Column({ 
    type: 'enum', 
    enum: ['male', 'female', 'other'],
    nullable: true,
    comment: '性别'
  })
  gender: string

  @Column({ type: 'date', nullable: true, comment: '生日' })
  birthday: Date

  @Column({ type: 'varchar', length: 100, nullable: true, comment: '所在地区' })
  location: string

  @Column({ type: 'text', nullable: true, comment: '个人简介' })
  bio: string

  @Column({ type: 'boolean', default: false, comment: '手机号是否已验证' })
  phoneVerified: boolean

  @Column({ type: 'boolean', default: false, comment: '邮箱是否已验证' })
  emailVerified: boolean

  @Column({ 
    type: 'enum', 
    enum: ['active', 'inactive', 'suspended', 'deleted'],
    default: 'active',
    comment: '账户状态'
  })
  status: string

  @Column({ 
    type: 'enum', 
    enum: ['student', 'teacher', 'admin', 'super_admin'],
    default: 'student',
    comment: '用户角色'
  })
  role: string

  @Column({ type: 'json', nullable: true, comment: '用户偏好设置' })
  preferences: Record<string, any>

  @Column({ type: 'json', nullable: true, comment: '用户元数据' })
  metadata: Record<string, any>

  @Column({ type: 'datetime', nullable: true, comment: '最后登录时间' })
  lastLoginAt: Date

  @Column({ type: 'varchar', length: 45, nullable: true, comment: '最后登录IP' })
  lastLoginIp: string

  @Column({ type: 'int', default: 0, comment: '登录次数' })
  loginCount: number

  @Column({ type: 'datetime', nullable: true, comment: '注册时间' })
  registeredAt: Date

  @CreateDateColumn({ comment: '创建时间' })
  createdAt: Date

  @UpdateDateColumn({ comment: '更新时间' })
  updatedAt: Date
}
