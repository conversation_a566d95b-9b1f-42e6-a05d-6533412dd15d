.buttonContainer {
  width: 100%;
  padding: 10px;
  display: flex;
  cursor: pointer;
  font-size: 1rem;
  font-weight: bold;
  flex-direction: row;
  align-items: center;
  color: var(--textColor);
  justify-content: center;

  &.filled {
    background-color: var(--buttonFilled);
  }

  &.outlined {
    color: var(--buttonOutlined);
    border: solid 1px var(--buttonOutlined);
  }

  &.gradient {
    background-color: linear-gradient(90deg, var(--buttonGradientStart), var(--buttonGradientEnd));
  }

  svg {
    width: 15px;
    height: 15px;
    color: var(--textColor);

    path {
      fill: var(--textColor);
    }
  }
}

button[disabled] {
  background: var(--disabled);
  opacity: 1 !important;
}

button:hover,
button:focus {
  outline: none;
  opacity: 0.7;
}

.uploadInput {
  display: none;
}
