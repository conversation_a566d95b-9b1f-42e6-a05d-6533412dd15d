{"name": "@ir-engine/taskserver", "description": "Task server for IREngine platform", "version": "1.0.3", "repository": {"type": "git", "url": "git://github.com/ir-engine/ir-engine.git"}, "homepage": "a", "publishConfig": {"access": "public"}, "keywords": ["three", "three.js", "ecs", "webgl", "mmo", "game engine", "webrtc", "productivity", "xr", "vr"], "author": {"name": "Infinite Reality Engine Collective", "email": "<EMAIL>"}, "contributors": [], "bugs": {}, "engines": {"node": ">= 22.11.0"}, "scripts": {"check-errors": "tsc --noemit && npx cycle-import-check src || true", "start": "cross-env APP_ENV=production ts-node --swc src/index.ts", "dev": "cross-env APP_ENV=development NODE_OPTIONS='--inspect' ts-node --swc src/index.ts", "test": "exit 0", "validate": "npm run build && npm run test"}, "dependencies": {"@ir-engine/server-core": "^1.0.3", "cross-env": "7.0.3", "typescript": "5.6.3"}, "devDependencies": {"@types/node": "18.15.5"}}