/*
CPAL-1.0 License

The contents of this file are subject to the Common Public Attribution License
Version 1.0. (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at
https://github.com/ir-engine/ir-engine/blob/dev/LICENSE.
The License is based on the Mozilla Public License Version 1.1, but Sections 14
and 15 have been added to cover use of software over a computer network and 
provide for limited attribution for the Original Developer. In addition, 
Exhibit A has been modified to be consistent with Exhibit B.

Software distributed under the License is distributed on an "AS IS" basis,
WITHOUT WARRANTY OF ANY KIND, either express or implied. See the License for the
specific language governing rights and limitations under the License.

The Original Code is Infinite Reality Engine.

The Original Developer is the Initial Developer. The Initial Developer of the
Original Code is the Infinite Reality Engine team.

All portions of the code written by the Infinite Reality Engine team are Copyright © 2021-2025
Infinite Reality Engine. All Rights Reserved.
*/

import { useEffect, useLayoutEffect } from 'react'
import { Mesh, MeshLambertMaterial, MeshStandardMaterial, PlaneGeometry, ShadowMaterial } from 'three'

import { EntityID, EntityTreeComponent, EntityUUID, useEntityContext, UUIDComponent } from '@ir-engine/ecs'
import {
  createEntity,
  defineComponent,
  removeComponent,
  removeEntity,
  setComponent,
  useComponent,
  useOptionalComponent
} from '@ir-engine/ecs/src/ComponentFunctions'
import { S } from '@ir-engine/ecs/src/schemas/JSONSchemas'
import { State } from '@ir-engine/hyperflux'
import { NameComponent } from '@ir-engine/spatial/src/common/NameComponent'
import { ColliderComponent } from '@ir-engine/spatial/src/physics/components/ColliderComponent'
import { RigidBodyComponent } from '@ir-engine/spatial/src/physics/components/RigidBodyComponent'
import { CollisionGroups } from '@ir-engine/spatial/src/physics/enums/CollisionGroups'
import { BodyTypes, Shapes } from '@ir-engine/spatial/src/physics/types/PhysicsTypes'
import { MeshComponent } from '@ir-engine/spatial/src/renderer/components/MeshComponent'
import { ObjectLayerMaskComponent } from '@ir-engine/spatial/src/renderer/components/ObjectLayerComponent'
import { ObjectLayerMasks } from '@ir-engine/spatial/src/renderer/constants/ObjectLayers'
import {
  MaterialInstanceComponent,
  MaterialStateComponent
} from '@ir-engine/spatial/src/renderer/materials/MaterialComponent'
import { T } from '@ir-engine/spatial/src/schema/schemaFunctions'

export const GroundPlaneComponent = defineComponent({
  name: 'GroundPlaneComponent',
  jsonID: 'EE_ground_plane',

  schema: S.Object({
    color: T.Color(0xffffff),
    visible: S.Bool({ default: true })
  }),

  reactor: function () {
    const entity = useEntityContext()

    const component = useComponent(entity, GroundPlaneComponent)

    const source = UUIDComponent.getAsSourceID(entity)
    const materialID = 'ground-plane-material' as EntityID

    useLayoutEffect(() => {
      setComponent(entity, ObjectLayerMaskComponent, ObjectLayerMasks.Scene)
      setComponent(entity, RigidBodyComponent, { type: BodyTypes.Fixed })
      setComponent(entity, ColliderComponent, {
        shape: Shapes.Plane,
        collisionLayer: CollisionGroups.Ground,
        collisionMask: CollisionGroups.Default | CollisionGroups.Avatars
      })
      return () => {
        removeComponent(entity, RigidBodyComponent)
        removeComponent(entity, ColliderComponent)
      }
    }, [])

    useEffect(() => {
      const materialEntity = createEntity()
      setComponent(materialEntity, NameComponent, 'GroundPlaneMaterial')

      const materialObject = new MeshStandardMaterial()
      materialObject.polygonOffset = true
      materialObject.polygonOffsetFactor = -0.01
      materialObject.polygonOffsetUnits = 1
      setComponent(materialEntity, UUIDComponent, {
        entitySourceID: source,
        entityID: materialID
      })
      setComponent(materialEntity, EntityTreeComponent, { parentEntity: entity })
      setComponent(materialEntity, MaterialStateComponent, {
        material: materialObject
      })

      const mesh = new Mesh(new PlaneGeometry(10000, 10000))
      mesh.geometry.rotateX(-Math.PI / 2)
      mesh.name = 'GroundPlaneMesh'

      setComponent(entity, MeshComponent, mesh)
      setComponent(entity, MaterialInstanceComponent, {
        entities: [materialEntity]
      })

      return () => {
        removeComponent(entity, MeshComponent)
        removeEntity(materialEntity)
      }
    }, [component.visible.value])

    const meshComponent = useOptionalComponent(entity, MeshComponent) as any as State<
      Mesh<any, MeshLambertMaterial | ShadowMaterial>
    >

    const material = useOptionalComponent(
      UUIDComponent.useEntityByUUID((source + materialID) as EntityUUID),
      MaterialStateComponent
    )?.material

    useLayoutEffect(() => {
      if (!meshComponent || !material) return
      const color = component.color.value
      if (meshComponent.material.color.value == color) return
      meshComponent.material.color.value.set(component.color.value)
    }, [component.color, material])

    return null
  }
})
