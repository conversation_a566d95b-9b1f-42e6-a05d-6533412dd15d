/**
 * DL-Engine Redis服务
 */

import { Injectable, OnModuleInit, OnModuleDestroy } from '@nestjs/common'
import { createClient, RedisClientType } from 'redis'
import { ConfigService } from '../config/config.service'
import { LoggerService } from '../common/logger.service'

@Injectable()
export class RedisService implements OnModuleInit, OnModuleDestroy {
  private client: RedisClientType

  constructor(
    private readonly configService: ConfigService,
    private readonly logger: LoggerService
  ) {}

  async onModuleInit() {
    const redisConfig = this.configService.getRedisConfig()
    
    this.client = createClient({
      socket: {
        host: redisConfig.host,
        port: redisConfig.port
      },
      password: redisConfig.password,
      database: redisConfig.db
    })

    this.client.on('error', (error) => {
      this.logger.error(`Redis connection error: ${error.message}`)
    })

    this.client.on('connect', () => {
      this.logger.log('Connected to Redis')
    })

    await this.client.connect()
  }

  async onModuleDestroy() {
    if (this.client) {
      await this.client.disconnect()
    }
  }

  async get(key: string): Promise<string | null> {
    const fullKey = this.getFullKey(key)
    return this.client.get(fullKey)
  }

  async set(key: string, value: string): Promise<void> {
    const fullKey = this.getFullKey(key)
    await this.client.set(fullKey, value)
  }

  async setex(key: string, seconds: number, value: string): Promise<void> {
    const fullKey = this.getFullKey(key)
    await this.client.setEx(fullKey, seconds, value)
  }

  async del(key: string): Promise<number> {
    const fullKey = this.getFullKey(key)
    return this.client.del(fullKey)
  }

  async incr(key: string): Promise<number> {
    const fullKey = this.getFullKey(key)
    return this.client.incr(fullKey)
  }

  async expire(key: string, seconds: number): Promise<boolean> {
    const fullKey = this.getFullKey(key)
    return this.client.expire(fullKey, seconds)
  }

  async exists(key: string): Promise<number> {
    const fullKey = this.getFullKey(key)
    return this.client.exists(fullKey)
  }

  async ttl(key: string): Promise<number> {
    const fullKey = this.getFullKey(key)
    return this.client.ttl(fullKey)
  }

  private getFullKey(key: string): string {
    const prefix = this.configService.getRedisConfig().keyPrefix
    return `${prefix}${key}`
  }
}
